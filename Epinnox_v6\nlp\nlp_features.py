"""
NLP Feature Extraction Module
This module extracts meaningful features from text data for trading signal enhancement.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass
import re
from collections import Counter
import math

from .sentiment_analyzer import SentimentScore
from .news_scraper import NewsArticle
from .social_monitor import SocialPost

logger = logging.getLogger(__name__)

@dataclass
class NLPFeatures:
    """NLP-derived features for trading"""
    sentiment_momentum: float
    news_volume_score: float
    social_buzz_score: float
    fear_greed_index: float
    keyword_intensity: float
    influencer_sentiment: float
    viral_potential: float
    controversy_score: float
    urgency_score: float
    confidence_score: float
    
    # Metadata
    feature_timestamp: datetime
    symbol: str
    data_quality_score: float

class KeywordAnalyzer:
    """Analyzes keywords and their market impact"""
    
    def __init__(self):
        # Market-moving keywords with impact weights
        self.bullish_keywords = {
            'moon': 3.0, 'rocket': 2.5, 'pump': 2.0, 'bullish': 1.5, 'buy': 1.0,
            'breakthrough': 2.0, 'adoption': 1.8, 'partnership': 1.5, 'integration': 1.3,
            'milestone': 1.5, 'surge': 1.8, 'rally': 1.6, 'breakout': 2.0,
            'institutional': 1.7, 'mainstream': 1.4, 'upgrade': 1.3, 'launch': 1.2
        }
        
        self.bearish_keywords = {
            'crash': -2.5, 'dump': -2.0, 'bearish': -1.5, 'sell': -1.0,
            'correction': -1.3, 'decline': -1.2, 'fall': -1.1, 'drop': -1.4,
            'resistance': -0.8, 'rejection': -1.2, 'concern': -1.0, 'risk': -0.9,
            'regulation': -1.3, 'ban': -2.0, 'crackdown': -1.8, 'investigation': -1.5
        }
        
        self.urgency_keywords = {
            'breaking': 3.0, 'urgent': 2.5, 'alert': 2.0, 'now': 1.5,
            'immediately': 2.0, 'emergency': 2.5, 'critical': 2.0, 'warning': 1.8
        }
        
        self.uncertainty_keywords = {
            'might': -0.5, 'could': -0.4, 'possibly': -0.6, 'maybe': -0.5,
            'uncertain': -0.8, 'unclear': -0.7, 'speculation': -1.0, 'rumor': -1.2
        }

    def analyze_keywords(self, text: str) -> Dict[str, float]:
        """
        Analyze keywords in text and calculate impact scores
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with keyword analysis scores
        """
        text_lower = text.lower()
        words = re.findall(r'\b\w+\b', text_lower)
        
        # Calculate keyword scores
        bullish_score = sum(self.bullish_keywords.get(word, 0) for word in words)
        bearish_score = sum(self.bearish_keywords.get(word, 0) for word in words)
        urgency_score = sum(self.urgency_keywords.get(word, 0) for word in words)
        uncertainty_score = sum(self.uncertainty_keywords.get(word, 0) for word in words)
        
        # Normalize by text length
        word_count = len(words)
        if word_count > 0:
            bullish_score /= word_count
            bearish_score /= word_count
            urgency_score /= word_count
            uncertainty_score /= word_count
        
        return {
            'bullish_intensity': bullish_score,
            'bearish_intensity': abs(bearish_score),
            'net_sentiment': bullish_score + bearish_score,
            'urgency_score': urgency_score,
            'uncertainty_score': abs(uncertainty_score)
        }

class ViralityAnalyzer:
    """Analyzes content virality potential"""
    
    def __init__(self):
        # Viral indicators
        self.viral_indicators = [
            r'🚀+', r'💎+', r'🌙+', r'📈+', r'💰+',  # Emojis
            r'\bmoon\b', r'\brocket\b', r'\bdiamond\b', r'\bhands\b',
            r'\bhodl\b', r'\byolo\b', r'\bfomo\b', r'\bto the moon\b'
        ]
        
        self.engagement_patterns = [
            r'!{2,}', r'\?{2,}', r'[A-Z]{3,}',  # Multiple punctuation, caps
            r'#\w+', r'@\w+',  # Hashtags and mentions
        ]
    
    def calculate_virality_score(self, content: str, engagement_metrics: Dict[str, Any] = None) -> float:
        """
        Calculate virality potential score
        
        Args:
            content: Text content
            engagement_metrics: Platform-specific engagement metrics
            
        Returns:
            Virality score (0.0 to 1.0)
        """
        score = 0.0
        
        # Content-based virality signals
        for pattern in self.viral_indicators:
            matches = len(re.findall(pattern, content, re.IGNORECASE))
            score += matches * 0.1
        
        for pattern in self.engagement_patterns:
            matches = len(re.findall(pattern, content))
            score += matches * 0.05
        
        # Engagement-based signals
        if engagement_metrics:
            # Reddit metrics
            if 'upvote_ratio' in engagement_metrics:
                ratio = engagement_metrics['upvote_ratio']
                if ratio > 0.9:
                    score += 0.3
                elif ratio > 0.8:
                    score += 0.2
            
            if 'score' in engagement_metrics:
                reddit_score = engagement_metrics['score']
                if reddit_score > 1000:
                    score += 0.3
                elif reddit_score > 100:
                    score += 0.2
                elif reddit_score > 10:
                    score += 0.1
            
            # Twitter metrics
            if 'retweet_count' in engagement_metrics:
                retweets = engagement_metrics['retweet_count']
                if retweets > 100:
                    score += 0.3
                elif retweets > 10:
                    score += 0.2
                elif retweets > 1:
                    score += 0.1
        
        return min(1.0, score)

class ControversyDetector:
    """Detects controversial or polarizing content"""
    
    def __init__(self):
        self.controversy_indicators = [
            r'\bscam\b', r'\bfraud\b', r'\bponzi\b', r'\bmanipulation\b',
            r'\bdebate\b', r'\bargument\b', r'\bdispute\b', r'\bcontroversy\b',
            r'\bcriticism\b', r'\bbacklash\b', r'\boutrage\b', r'\bprotest\b'
        ]
        
        self.polarizing_terms = [
            r'\bhate\b', r'\blove\b', r'\bterrible\b', r'\bamazing\b',
            r'\bworst\b', r'\bbest\b', r'\bstupid\b', r'\bgenius\b'
        ]
    
    def detect_controversy(self, content: str, engagement_metrics: Dict[str, Any] = None) -> float:
        """
        Detect controversy level in content
        
        Args:
            content: Text content
            engagement_metrics: Platform-specific engagement metrics
            
        Returns:
            Controversy score (0.0 to 1.0)
        """
        score = 0.0
        content_lower = content.lower()
        
        # Content-based controversy signals
        for pattern in self.controversy_indicators:
            if re.search(pattern, content_lower):
                score += 0.3
        
        for pattern in self.polarizing_terms:
            if re.search(pattern, content_lower):
                score += 0.1
        
        # Engagement-based controversy signals
        if engagement_metrics:
            # High comment-to-like ratio indicates controversy
            if 'num_comments' in engagement_metrics and 'score' in engagement_metrics:
                comments = engagement_metrics['num_comments']
                score_val = engagement_metrics['score']
                if score_val > 0:
                    comment_ratio = comments / score_val
                    if comment_ratio > 0.5:  # Many comments relative to score
                        score += 0.2
            
            # Low upvote ratio indicates controversy
            if 'upvote_ratio' in engagement_metrics:
                ratio = engagement_metrics['upvote_ratio']
                if ratio < 0.6:  # Controversial on Reddit
                    score += 0.3
        
        return min(1.0, score)

class NLPFeatureExtractor:
    """Main class for extracting NLP features"""
    
    def __init__(self):
        self.keyword_analyzer = KeywordAnalyzer()
        self.virality_analyzer = ViralityAnalyzer()
        self.controversy_detector = ControversyDetector()
    
    def calculate_sentiment_momentum(self, sentiment_scores: List[SentimentScore], hours: int = 24) -> float:
        """
        Calculate sentiment momentum over time
        
        Args:
            sentiment_scores: List of sentiment scores over time
            hours: Time window for momentum calculation
            
        Returns:
            Sentiment momentum score (-1 to 1)
        """
        if len(sentiment_scores) < 2:
            return 0.0
        
        # Filter recent scores
        cutoff_time = datetime.now() - pd.Timedelta(hours=hours)
        recent_scores = [s for s in sentiment_scores if s.timestamp >= cutoff_time]
        
        if len(recent_scores) < 2:
            return 0.0
        
        # Sort by timestamp
        recent_scores.sort(key=lambda x: x.timestamp)
        
        # Calculate momentum using linear regression
        timestamps = [(s.timestamp - recent_scores[0].timestamp).total_seconds() for s in recent_scores]
        sentiments = [s.compound_score for s in recent_scores]
        
        if len(timestamps) < 2:
            return 0.0
        
        # Simple linear regression
        n = len(timestamps)
        sum_x = sum(timestamps)
        sum_y = sum(sentiments)
        sum_xy = sum(x * y for x, y in zip(timestamps, sentiments))
        sum_x2 = sum(x * x for x in timestamps)
        
        if n * sum_x2 - sum_x * sum_x == 0:
            return 0.0
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        
        # Normalize slope to [-1, 1]
        momentum = np.tanh(slope * 10000)  # Scale factor for time units
        
        return float(momentum)
    
    def calculate_news_volume_score(self, articles: List[NewsArticle], symbol: str) -> float:
        """
        Calculate news volume score for a symbol
        
        Args:
            articles: List of news articles
            symbol: Target symbol
            
        Returns:
            News volume score (0.0 to 1.0)
        """
        if not articles:
            return 0.0
        
        # Filter articles for symbol (case insensitive)
        symbol_lower = symbol.lower()
        relevant_articles = [
            article for article in articles
            if symbol_lower in article.title.lower() or 
               symbol_lower in article.content.lower() or
               symbol.upper() in article.symbols_mentioned
        ]
        
        # Calculate volume score based on:
        # 1. Number of articles
        # 2. Article importance
        # 3. Recency
        
        volume_score = 0.0
        current_time = datetime.now()
        
        for article in relevant_articles:
            # Base score from existence
            article_score = 0.1
            
            # Importance boost
            article_score += article.importance_score * 0.3
            
            # Recency boost
            age_hours = (current_time - article.published_at).total_seconds() / 3600
            if age_hours <= 1:
                recency_boost = 0.5
            elif age_hours <= 6:
                recency_boost = 0.3
            elif age_hours <= 24:
                recency_boost = 0.1
            else:
                recency_boost = 0.0
            
            article_score += recency_boost
            volume_score += article_score
        
        # Normalize to [0, 1]
        return min(1.0, volume_score / 10.0)
    
    def calculate_social_buzz_score(self, posts: List[SocialPost], symbol: str) -> float:
        """
        Calculate social media buzz score for a symbol
        
        Args:
            posts: List of social media posts
            symbol: Target symbol
            
        Returns:
            Social buzz score (0.0 to 1.0)
        """
        if not posts:
            return 0.0
        
        # Filter posts for symbol
        symbol_lower = symbol.lower()
        relevant_posts = [
            post for post in posts
            if symbol_lower in post.content.lower() or
               symbol.upper() in post.symbols_mentioned
        ]
        
        buzz_score = 0.0
        current_time = datetime.now()
        
        for post in relevant_posts:
            # Base score
            post_score = 0.05
            
            # Engagement boost
            post_score += post.engagement_score * 0.2
            
            # Influence boost
            post_score += post.influence_score * 0.1
            
            # Recency boost
            age_hours = (current_time - post.published_at).total_seconds() / 3600
            if age_hours <= 1:
                recency_boost = 0.3
            elif age_hours <= 6:
                recency_boost = 0.2
            elif age_hours <= 24:
                recency_boost = 0.1
            else:
                recency_boost = 0.0
            
            post_score += recency_boost
            buzz_score += post_score
        
        # Normalize to [0, 1]
        return min(1.0, buzz_score / 5.0)
    
    def calculate_fear_greed_index(self, sentiment_scores: List[SentimentScore]) -> float:
        """
        Calculate fear and greed index based on sentiment distribution
        
        Args:
            sentiment_scores: List of sentiment scores
            
        Returns:
            Fear-Greed index (0 = Extreme Fear, 1 = Extreme Greed)
        """
        if not sentiment_scores:
            return 0.5  # Neutral
        
        # Categorize sentiments
        very_bearish = sum(1 for s in sentiment_scores if s.compound_score <= -0.6)
        bearish = sum(1 for s in sentiment_scores if -0.6 < s.compound_score <= -0.2)
        neutral = sum(1 for s in sentiment_scores if -0.2 < s.compound_score < 0.2)
        bullish = sum(1 for s in sentiment_scores if 0.2 <= s.compound_score < 0.6)
        very_bullish = sum(1 for s in sentiment_scores if s.compound_score >= 0.6)
        
        total_count = len(sentiment_scores)
        
        # Calculate weighted score
        fear_greed_score = (
            very_bearish * 0.0 +
            bearish * 0.25 +
            neutral * 0.5 +
            bullish * 0.75 +
            very_bullish * 1.0
        ) / total_count
        
        return fear_greed_score
    
    def calculate_keyword_intensity(self, texts: List[str]) -> float:
        """
        Calculate overall keyword intensity from multiple texts
        
        Args:
            texts: List of text content
            
        Returns:
            Keyword intensity score (0.0 to 1.0)
        """
        if not texts:
            return 0.0
        
        total_intensity = 0.0
        
        for text in texts:
            keyword_analysis = self.keyword_analyzer.analyze_keywords(text)
            text_intensity = (
                keyword_analysis['bullish_intensity'] +
                keyword_analysis['bearish_intensity'] +
                keyword_analysis['urgency_score']
            )
            total_intensity += text_intensity
        
        # Average and normalize
        avg_intensity = total_intensity / len(texts)
        return min(1.0, avg_intensity * 2)  # Scale factor
    
    def calculate_influencer_sentiment(self, posts: List[SocialPost]) -> float:
        """
        Calculate sentiment from influential users
        
        Args:
            posts: List of social media posts
            
        Returns:
            Influencer sentiment score (-1 to 1)
        """
        influential_posts = [post for post in posts if post.influence_score > 0.7]
        
        if not influential_posts:
            return 0.0
        
        # Calculate weighted sentiment
        total_weight = 0.0
        weighted_sentiment = 0.0
        
        for post in influential_posts:
            weight = post.influence_score
            # Assume sentiment score is available or calculate it
            post_sentiment = 0.0  # Would need sentiment analysis here
            
            weighted_sentiment += post_sentiment * weight
            total_weight += weight
        
        if total_weight > 0:
            return weighted_sentiment / total_weight
        
        return 0.0
    
    def extract_features(self, symbol: str,
                        sentiment_scores: List[SentimentScore] = None,
                        news_articles: List[NewsArticle] = None,
                        social_posts: List[SocialPost] = None) -> NLPFeatures:
        """
        Extract comprehensive NLP features for trading
        
        Args:
            symbol: Trading symbol
            sentiment_scores: Historical sentiment scores
            news_articles: News articles
            social_posts: Social media posts
            
        Returns:
            NLPFeatures object
        """
        # Initialize default values
        sentiment_momentum = 0.0
        news_volume_score = 0.0
        social_buzz_score = 0.0
        fear_greed_index = 0.5
        keyword_intensity = 0.0
        influencer_sentiment = 0.0
        viral_potential = 0.0
        controversy_score = 0.0
        urgency_score = 0.0
        confidence_score = 0.5
        
        # Calculate features if data is available
        if sentiment_scores:
            sentiment_momentum = self.calculate_sentiment_momentum(sentiment_scores)
            fear_greed_index = self.calculate_fear_greed_index(sentiment_scores)
            confidence_score = np.mean([s.confidence for s in sentiment_scores])
        
        if news_articles:
            news_volume_score = self.calculate_news_volume_score(news_articles, symbol)
            
            # Extract urgency from news titles
            news_texts = [article.title for article in news_articles]
            if news_texts:
                keyword_analysis = self.keyword_analyzer.analyze_keywords(' '.join(news_texts))
                urgency_score = keyword_analysis['urgency_score']
        
        if social_posts:
            social_buzz_score = self.calculate_social_buzz_score(social_posts, symbol)
            influencer_sentiment = self.calculate_influencer_sentiment(social_posts)
            
            # Calculate virality and controversy
            if social_posts:
                viral_scores = []
                controversy_scores = []
                
                for post in social_posts:
                    viral_score = self.virality_analyzer.calculate_virality_score(
                        post.content,
                        {'upvote_ratio': 0.8, 'score': 100}  # Mock metrics
                    )
                    viral_scores.append(viral_score)
                    
                    controversy_score_post = self.controversy_detector.detect_controversy(
                        post.content,
                        {'upvote_ratio': 0.8, 'num_comments': 10, 'score': 100}
                    )
                    controversy_scores.append(controversy_score_post)
                
                if viral_scores:
                    viral_potential = np.mean(viral_scores)
                if controversy_scores:
                    controversy_score = np.mean(controversy_scores)
        
        # Calculate overall keyword intensity
        all_texts = []
        if news_articles:
            all_texts.extend([article.title + ' ' + article.content for article in news_articles])
        if social_posts:
            all_texts.extend([post.content for post in social_posts])
        
        if all_texts:
            keyword_intensity = self.calculate_keyword_intensity(all_texts)
        
        # Calculate data quality score
        data_sources = 0
        if sentiment_scores:
            data_sources += 1
        if news_articles:
            data_sources += 1
        if social_posts:
            data_sources += 1
        
        data_quality_score = data_sources / 3.0  # Max 3 sources
        
        return NLPFeatures(
            sentiment_momentum=sentiment_momentum,
            news_volume_score=news_volume_score,
            social_buzz_score=social_buzz_score,
            fear_greed_index=fear_greed_index,
            keyword_intensity=keyword_intensity,
            influencer_sentiment=influencer_sentiment,
            viral_potential=viral_potential,
            controversy_score=controversy_score,
            urgency_score=urgency_score,
            confidence_score=confidence_score,
            feature_timestamp=datetime.now(),
            symbol=symbol,
            data_quality_score=data_quality_score
        )

# Example usage
def main():
    """Example usage of the NLP feature extractor"""
    
    # Initialize extractor
    extractor = NLPFeatureExtractor()
    
    # Mock some data
    from .sentiment_analyzer import SentimentScore, SentimentLabel
    
    mock_sentiment_scores = [
        SentimentScore(
            label=SentimentLabel.BULLISH,
            confidence=0.8,
            compound_score=0.6,
            positive=0.7,
            negative=0.1,
            neutral=0.2,
            reasoning="Test bullish sentiment",
            source="test",
            timestamp=datetime.now(),
            text_length=100,
            keywords=['bullish', 'moon']
        )
    ]
    
    # Extract features
    features = extractor.extract_features(
        symbol='DOGE',
        sentiment_scores=mock_sentiment_scores
    )
    
    print("NLP Features for DOGE:")
    print(f"Sentiment Momentum: {features.sentiment_momentum:.3f}")
    print(f"News Volume Score: {features.news_volume_score:.3f}")
    print(f"Social Buzz Score: {features.social_buzz_score:.3f}")
    print(f"Fear-Greed Index: {features.fear_greed_index:.3f}")
    print(f"Keyword Intensity: {features.keyword_intensity:.3f}")
    print(f"Viral Potential: {features.viral_potential:.3f}")
    print(f"Controversy Score: {features.controversy_score:.3f}")
    print(f"Data Quality: {features.data_quality_score:.3f}")

if __name__ == "__main__":
    main()
