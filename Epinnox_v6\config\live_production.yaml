# Epinnox Live Production Trading Configuration
# Account Balance: $50 USD
# Trading Mode: Live with Real Funds

# Core Trading Settings
trading_mode: live
initial_balance: 50.0

# Dynamic Symbol Selection - Automatically select best performing USDT-M futures
dynamic_symbol_selection:
  enabled: true
  mode: scalping                    # Optimized for high-frequency trading
  update_interval: 30               # Update symbol selection every 30 seconds
  max_symbols: 8                    # Maximum symbols to monitor
  min_symbols: 3                    # Minimum symbols to maintain

# Symbol Pool - USDT-M futures pairs for dynamic selection
symbol_pool:
  - BTC/USDT:USDT                   # Bitcoin futures
  - ETH/USDT:USDT                   # Ethereum futures
  - DOGE/USDT:USDT                  # Dogecoin futures
  - ADA/USDT:USDT                   # Cardano futures
  - SOL/USDT:USDT                   # Solana futures
  - MATIC/USDT:USDT                 # Polygon futures
  - AVAX/USDT:USDT                  # Avalanche futures
  - DOT/USDT:USDT                   # Polkadot futures
  - LINK/USDT:USDT                  # Chainlink futures
  - UNI/USDT:USDT                   # Uniswap futures
  - LTC/USDT:USDT                   # Litecoin futures
  - BCH/USDT:USDT                   # Bitcoin Cash futures
  - XRP/USDT:USDT                   # Ripple futures
  - TRX/USDT:USDT                   # Tron futures
  - FTM/USDT:USDT                   # Fantom futures

# Symbol Scanner Configuration
symbol_scanner:
  # Metrics weights for symbol scoring (optimized for live trading)
  metrics_weights:
    spread_score: 0.30              # Higher weight on low spreads for live trading
    tick_atr_score: 0.25            # Volatility for trading opportunities
    flow_score: 0.10                # Market flow balance
    depth_score: 0.25               # Order book depth for execution
    volume_score: 0.10              # 24h volume for liquidity

  # Quality filters for symbol selection
  filters:
    min_volume_24h: 10000000        # Minimum $10M daily volume
    max_spread_pct: 0.05            # Maximum 0.05% spread
    min_market_cap: 100000000       # Minimum $100M market cap (estimated)
    min_depth_usd: 50000            # Minimum $50k order book depth
    max_volatility: 0.15            # Maximum 15% daily volatility

  # Performance thresholds
  performance:
    min_score: 60.0                 # Minimum composite score to trade
    score_decay_time: 300           # Seconds before rescanning if score drops
    switch_threshold: 10.0          # Score difference to switch symbols

# Fallback symbols if dynamic selection fails
fallback_symbols:
  - BTC/USDT:USDT
  - ETH/USDT:USDT

# Risk Management Settings (Conservative for $50 account)
max_daily_loss: -10.0          # 20% of account balance
max_position_size: 15.0        # 30% of account balance  
max_leverage: 3.0              # Conservative leverage
max_open_positions: 2          # Maximum 2 concurrent positions
min_account_balance: 5.0       # 10% of account as minimum threshold
max_drawdown: -40.0            # 40% maximum drawdown

# Position Sizing Strategy
position_sizing:
  base_size_pct: 0.15          # 15% of account per position base
  confidence_multiplier: 1.5   # Multiply by confidence (max 1.5x)
  min_position_usd: 5.0        # Minimum $5 position
  max_position_usd: 15.0       # Maximum $15 position

# Exchange Configuration
exchange:
  name: htx                    # HTX exchange
  demo_mode: false             # LIVE TRADING MODE
  futures_enabled: true        # Enable futures trading
  api_credentials:
    # API credentials will be loaded from environment variables
    # HTX_API_KEY, HTX_SECRET_KEY, HTX_PASSPHRASE
    use_env_vars: true

# LLM Configuration - Conservative settings
llm_config:
  model: phi-3.1-mini
  temperature: 0.2             # Lower temperature for more conservative decisions
  max_tokens: 256              # Shorter responses for faster processing
  confidence_threshold: 0.7    # Higher confidence threshold for live trading

# Trading Strategy Parameters (Dynamic Symbol Aware)
strategy:
  decision_confidence_min: 0.65    # Minimum confidence to execute trades
  risk_reward_ratio_min: 1.5       # Minimum 1.5:1 risk/reward ratio
  max_correlation_threshold: 0.7   # Avoid highly correlated positions
  stop_loss_pct: 0.02             # 2% stop loss
  take_profit_pct: 0.04            # 4% take profit (2:1 ratio)

  # Dynamic symbol strategy settings
  symbol_switch_cooldown: 60       # Minimum 60 seconds between symbol switches
  position_close_on_switch: true   # Close positions when switching symbols
  max_symbol_switches_per_hour: 6  # Limit symbol switching frequency
  symbol_performance_window: 300   # 5-minute window for symbol performance evaluation

# Safety and Monitoring Configuration (Enhanced for Dynamic Symbols)
monitoring:
  health_check_interval: 15        # Check system health every 15 seconds
  metrics_collection_interval: 5   # Collect metrics every 5 seconds
  alert_processing_interval: 3     # Process alerts every 3 seconds
  balance_check_interval: 10       # Check account balance every 10 seconds

  # Dynamic symbol monitoring
  symbol_scanner_interval: 30      # Scan symbols every 30 seconds
  symbol_performance_tracking: true # Track performance of selected symbols
  symbol_switch_logging: true      # Log all symbol switches
  market_condition_monitoring: true # Monitor overall market conditions
  
# Alert Thresholds
alerts:
  critical_balance_threshold: 10.0     # Alert if balance drops below $10
  high_loss_threshold: -5.0            # Alert if daily loss exceeds $5
  position_size_warning: 12.0          # Warn if position size exceeds $12
  leverage_warning: 2.5                # Warn if leverage exceeds 2.5x

# Emergency Procedures
emergency:
  auto_stop_on_daily_loss: true        # Auto-stop if daily loss limit hit
  auto_stop_on_low_balance: true       # Auto-stop if balance too low
  auto_stop_on_connection_loss: true   # Auto-stop if exchange connection lost
  emergency_close_all_positions: true  # Close all positions on emergency
  
# Logging Configuration (Enhanced for Dynamic Symbol Selection)
logging:
  level: INFO
  log_trades: true
  log_decisions: true
  log_performance: true
  log_safety_events: true
  log_symbol_selection: true       # Log dynamic symbol selection events
  log_symbol_switches: true        # Log symbol switches with reasons
  log_scanner_metrics: true        # Log symbol scanner metrics
  log_market_analysis: true        # Log market condition analysis
  max_log_files: 15                # Increased for additional logging
  log_rotation_mb: 50

# Performance Tracking
performance:
  track_pnl: true
  track_win_rate: true
  track_sharpe_ratio: true
  track_max_drawdown: true
  daily_performance_report: true

# Data and Connectivity
data:
  websocket_enabled: true
  backup_rest_api: true
  data_timeout_seconds: 30
  reconnect_attempts: 5
  reconnect_delay_seconds: 10

# Trading Hours (24/7 for crypto)
trading_hours:
  enabled: true
  start_hour: 0
  end_hour: 24
  timezone: UTC
  
# Backup and Recovery
backup:
  save_state_interval: 300     # Save state every 5 minutes
  backup_trades: true
  backup_performance: true
