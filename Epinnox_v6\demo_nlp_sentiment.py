#!/usr/bin/env python3
"""
NLP Sentiment Analysis Demo
Demonstrates the complete NLP pipeline for market sentiment analysis.
"""

import sys
import os
import asyncio
import pandas as pd
from datetime import datetime
sys.path.append(os.path.dirname(__file__))

from nlp.sentiment_analyzer import <PERSON>timentAnaly<PERSON>
from nlp.news_scraper import NewsScraperManager, NewsSource
from nlp.social_monitor import SocialMediaMonitor, SocialPlatform
from nlp.market_sentiment import MarketSentimentAggregator
from nlp.nlp_features import NLPFeatureExtractor

print("🚀 EPINNOX v6 - NLP SENTIMENT ANALYSIS DEMO")
print("=" * 60)

async def demo_nlp_sentiment():
    """Demonstrate the full NLP sentiment analysis pipeline"""
    
    print("🧠 Initializing NLP Components...")
    
    # Initialize components
    analyzer = SentimentAnalyzer(use_transformer=False)
    news_scraper = NewsScraperManager(
        sources=[NewsSource.COINDESK, NewsSource.COINTELEGRAPH],
        max_articles_per_source=5
    )
    social_monitor = SocialMediaMonitor(platforms=[SocialPlatform.REDDIT])
    
    sentiment_aggregator = MarketSentimentAggregator(
        sentiment_analyzer=analyzer,
        news_scraper=news_scraper,
        social_monitor=social_monitor
    )
    
    print("✅ NLP components initialized")
    
    # Demo with sample data (since we can't scrape in demo)
    print("\n📊 Demo Sentiment Analysis with Sample Data:")    # Create sample news articles
    from nlp.news_scraper import NewsArticle
    sample_articles = [
        NewsArticle(
            title="DOGE Surges 15% on Elon Musk Tweet",
            content="Dogecoin rallied strongly today following positive comments from Elon Musk about cryptocurrency adoption.",
            url="https://example.com/1",
            source=NewsSource.COINDESK,
            published_at=datetime.now(),
            author="Crypto Reporter",
            tags=["DOGE", "cryptocurrency", "Elon Musk"],
            symbols_mentioned=["DOGE"],
            sentiment_score=None,
            importance_score=0.8,
            article_hash="hash1",
            scraped_at=datetime.now()
        ),
        NewsArticle(
            title="Bitcoin Faces Resistance at $50K",
            content="Bitcoin struggled to break through the $50,000 resistance level as selling pressure increased.",
            url="https://example.com/2", 
            source=NewsSource.COINTELEGRAPH,
            published_at=datetime.now(),
            author="Market Analyst",
            tags=["BTC", "resistance", "technical analysis"],
            symbols_mentioned=["BTC"],
            sentiment_score=None,
            importance_score=0.7,
            article_hash="hash2",
            scraped_at=datetime.now()
        ),
        NewsArticle(
            title="Crypto Market Shows Mixed Signals",
            content="The cryptocurrency market displayed mixed sentiment with some altcoins gaining while others declined.",
            url="https://example.com/3",
            source=NewsSource.CRYPTONEWS,
            published_at=datetime.now(),
            author="News Editor",
            tags=["crypto", "market", "mixed signals"],
            symbols_mentioned=["BTC", "ETH"],
            sentiment_score=None,
            importance_score=0.6,
            article_hash="hash3",
            scraped_at=datetime.now()
        )
    ]
      # Create sample social posts
    from nlp.social_monitor import SocialPost
    sample_social = [
        SocialPost(
            id="post1",
            platform=SocialPlatform.REDDIT,
            content="DOGE to the moon! 🚀 This is just the beginning",
            author="crypto_trader_123",
            published_at=datetime.now(),
            engagement_score=95,
            follower_count=1500,
            symbols_mentioned=["DOGE"],
            hashtags=["DOGE", "ToTheMoon"],
            mentions=[],
            url="https://reddit.com/r/dogecoin/post1",
            sentiment_score=None,
            influence_score=0.7,
            post_hash="social_hash1",
            scraped_at=datetime.now()
        ),
        SocialPost(
            id="post2",
            platform=SocialPlatform.REDDIT,
            content="Really worried about this crypto crash. Diamond hands though 💎",
            author="hodler_2021",
            published_at=datetime.now(),
            engagement_score=45,
            follower_count=800,
            symbols_mentioned=["CRYPTO"],
            hashtags=["DiamondHands", "HODL"],
            mentions=[],
            url="https://reddit.com/r/cryptocurrency/post2",
            sentiment_score=None,
            influence_score=0.4,
            post_hash="social_hash2",
            scraped_at=datetime.now()
        ),
        SocialPost(
            id="post3",
            platform=SocialPlatform.REDDIT,
            content="Market looking bullish for DOGE. Technical analysis shows strong support",
            author="technical_analyst",
            published_at=datetime.now(),
            engagement_score=78,
            follower_count=2000,
            symbols_mentioned=["DOGE"],
            hashtags=["TechnicalAnalysis", "Bullish"],
            mentions=[],
            url="https://reddit.com/r/dogecoin/post3",
            sentiment_score=None,
            influence_score=0.8,
            post_hash="social_hash3",
            scraped_at=datetime.now()
        )
    ]
    
    print("\n📰 Analyzing Sample News Articles:")
    for article in sample_articles:
        sentiment = analyzer.analyze_sentiment(f"{article.title} {article.content}")
        print(f"  • {article.title[:50]}...")
        print(f"    Sentiment: {sentiment.label.value} (confidence: {sentiment.confidence:.1%})")
    
    print("\n💬 Analyzing Sample Social Posts:")
    for post in sample_social:
        sentiment = analyzer.analyze_sentiment(post.content)
        print(f"  • {post.content[:50]}...")
        print(f"    Sentiment: {sentiment.label.value} (confidence: {sentiment.confidence:.1%})")
    
    # Create dummy OHLCV data for demonstration
    dummy_ohlcv = pd.DataFrame({
        'datetime': pd.date_range(start='2024-01-01', periods=100, freq='1H'),
        'open': [0.08] * 100,
        'high': [0.085] * 100,
        'low': [0.075] * 100,
        'close': [0.082] * 100,
        'volume': [1000000] * 100
    })
    
    print("\n🎯 Aggregating Market Sentiment:")
    
    # Aggregate sentiment
    sentiment_signal = await sentiment_aggregator.aggregate_market_sentiment(
        symbol="DOGE/USDT",
        ohlcv_data=dummy_ohlcv,
        news_articles=sample_articles,
        social_posts=sample_social
    )
    
    print(f"  Overall Sentiment: {sentiment_signal.overall_sentiment.value}")
    print(f"  Sentiment Score: {sentiment_signal.sentiment_score:.3f}")
    print(f"  Confidence: {sentiment_signal.confidence:.1%}")
    print(f"  News Sentiment: {sentiment_signal.news_sentiment:.3f}")
    print(f"  Social Sentiment: {sentiment_signal.social_sentiment:.3f}")
    print(f"  Volume Sentiment: {sentiment_signal.volume_sentiment:.3f}")
    print(f"  Technical Sentiment: {sentiment_signal.technical_sentiment:.3f}")
    
    sources_count = dict(sentiment_signal.sources_count)
    print(f"  Data Sources: {sources_count}")
    
    print(f"\n📝 Reasoning: {sentiment_signal.reasoning}")
    
    # Extract NLP features
    print("\n🔍 Extracting Advanced NLP Features:")
    
    nlp_extractor = NLPFeatureExtractor()
    
    # Collect all sentiment scores
    all_sentiment_scores = []
    for article in sample_articles:
        sentiment = analyzer.analyze_sentiment(f"{article.title} {article.content}")
        all_sentiment_scores.append(sentiment)
    
    for post in sample_social:
        sentiment = analyzer.analyze_sentiment(post.content)
        all_sentiment_scores.append(sentiment)
    
    nlp_features = nlp_extractor.extract_features(
        sentiment_scores=all_sentiment_scores,
        news_articles=sample_articles,
        social_posts=sample_social,
        symbol="DOGE"
    )
    
    print(f"  Sentiment Momentum: {nlp_features.sentiment_momentum:.3f}")
    print(f"  News Volume Score: {nlp_features.news_volume_score:.3f}")
    print(f"  Social Buzz Score: {nlp_features.social_buzz_score:.3f}")
    print(f"  Fear-Greed Index: {nlp_features.fear_greed_index:.3f}")
    print(f"  Viral Potential: {nlp_features.viral_potential:.3f}")
    print(f"  Controversy Score: {nlp_features.controversy_score:.3f}")
    print(f"  Urgency Score: {nlp_features.urgency_score:.3f}")
    print(f"  Confidence Score: {nlp_features.confidence_score:.3f}")
    
    print("\n" + "=" * 60)
    print("🎉 NLP SENTIMENT ANALYSIS DEMO COMPLETE!")
    print("✅ All components working perfectly")
    print("✅ Ready for integration with live trading system")
    print("✅ Can analyze real-time news and social sentiment")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(demo_nlp_sentiment())
