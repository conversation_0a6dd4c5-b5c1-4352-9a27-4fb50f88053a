"""
Test script for Epinnox v6 optimizations

This script tests all the optimization improvements:
1. Signal hierarchy resolution
2. Enhanced position sizing
3. Improved ML models
4. Fixed confidence handling
"""

import sys
import logging
from main import run_trading_system

# Configure logging to avoid Unicode issues
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

def test_optimizations():
    """Test all optimization improvements"""
    print("=" * 60)
    print("🚀 TESTING EPINNOX V6 OPTIMIZATIONS")
    print("=" * 60)
    
    try:
        # Test with DOGE/USDT
        print("\n📊 Running optimized trading system...")
        decision, explanation, parsed_response = run_trading_system(
            symbol='DOGE/USDT',
            use_live_data=False
        )
        
        print(f"\n✅ OPTIMIZATION TEST RESULTS:")
        print(f"Decision: {decision}")
        print(f"Explanation: {explanation[:200]}...")
        
        # Check for optimization features
        optimizations_tested = []
        
        # 1. Check ML predictions
        if 'ml_predictions' in parsed_response:
            ml_preds = parsed_response['ml_predictions']
            print(f"\n🤖 ML MODELS:")
            for model, pred in ml_preds.items():
                if isinstance(pred, dict):
                    print(f"  {model}: {pred.get('direction', 'N/A')} (conf: {pred.get('direction_confidence', 0):.3f})")
            optimizations_tested.append("✅ ML Models")
        else:
            optimizations_tested.append("❌ ML Models")
        
        # 2. Check signal hierarchy
        if 'signal_hierarchy' in parsed_response:
            hierarchy = parsed_response['signal_hierarchy']
            print(f"\n🎯 SIGNAL HIERARCHY:")
            print(f"  Final Decision: {hierarchy.get('decision', 'N/A')}")
            print(f"  Confidence: {hierarchy.get('confidence', 0):.1%}")
            print(f"  Risk Level: {hierarchy.get('risk_level', 'N/A')}")
            optimizations_tested.append("✅ Signal Hierarchy")
        else:
            optimizations_tested.append("❌ Signal Hierarchy")
        
        # 3. Check position sizing
        if 'position_sizing' in parsed_response:
            pos_sizing = parsed_response['position_sizing']
            print(f"\n💰 POSITION SIZING:")
            print(f"  Optimal Size: {pos_sizing.get('optimal_position_size', 0):.2f} units")
            print(f"  Liquidity Score: {pos_sizing.get('liquidity_score', 0):.3f}")
            print(f"  Market Impact: {pos_sizing.get('market_impact_estimate', 0):.3f}%")
            optimizations_tested.append("✅ Position Sizing")
        else:
            optimizations_tested.append("❌ Position Sizing")
        
        # 4. Check enhanced confidence
        if 'signal_scores' in parsed_response:
            scores = parsed_response['signal_scores']
            if 'enhanced_confidence' in scores:
                print(f"\n📈 CONFIDENCE ENHANCEMENT:")
                print(f"  Original: {scores.get('confidence', 0):.1f}%")
                print(f"  Enhanced: {scores.get('enhanced_confidence', 0):.1f}%")
                print(f"  Boost Factors: {scores.get('confidence_factors', [])}")
                optimizations_tested.append("✅ Confidence Enhancement")
            else:
                optimizations_tested.append("❌ Confidence Enhancement")
        else:
            optimizations_tested.append("❌ Confidence Enhancement")
        
        # 5. Check multi-timeframe analysis
        if 'multi_timeframe_analysis' in parsed_response:
            mtf = parsed_response['multi_timeframe_analysis']
            print(f"\n📊 MULTI-TIMEFRAME:")
            print(f"  Trend: {mtf.get('trend_direction', 'N/A')}")
            print(f"  Strength: {mtf.get('trend_strength', 0):.2f}")
            print(f"  Alignment: {mtf.get('trend_alignment', 0):.2f}")
            optimizations_tested.append("✅ Multi-Timeframe")
        else:
            optimizations_tested.append("❌ Multi-Timeframe")
        
        print(f"\n🔍 OPTIMIZATION STATUS:")
        for test in optimizations_tested:
            print(f"  {test}")
        
        # Calculate success rate
        success_count = sum(1 for test in optimizations_tested if test.startswith("✅"))
        total_tests = len(optimizations_tested)
        success_rate = (success_count / total_tests) * 100
        
        print(f"\n📊 OPTIMIZATION SUCCESS RATE: {success_rate:.1f}% ({success_count}/{total_tests})")
        
        if success_rate >= 80:
            print("🎉 EXCELLENT: Most optimizations are working!")
        elif success_rate >= 60:
            print("👍 GOOD: Most optimizations are working, some need attention")
        else:
            print("⚠️ NEEDS WORK: Several optimizations need fixing")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR during optimization testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_hierarchy_standalone():
    """Test signal hierarchy system standalone"""
    print("\n" + "=" * 60)
    print("🎯 TESTING SIGNAL HIERARCHY STANDALONE")
    print("=" * 60)
    
    try:
        from core.signal_hierarchy import IntelligentSignalHierarchy, SignalInput
        
        # Create test signals
        signals = [
            SignalInput(
                source='ml_ensemble',
                decision='WAIT',
                confidence=0.90,
                weight=0.35,
                reasoning='ML models predict WAIT with high confidence'
            ),
            SignalInput(
                source='technical_signals',
                decision='SHORT',
                confidence=0.83,
                weight=0.25,
                reasoning='Technical indicators suggest SHORT'
            ),
            SignalInput(
                source='multi_timeframe',
                decision='LONG',
                confidence=0.72,
                weight=0.20,
                reasoning='Multi-timeframe analysis suggests LONG'
            ),
            SignalInput(
                source='llm_analysis',
                decision='LONG',
                confidence=0.65,
                weight=0.05,
                reasoning='LLM analysis suggests LONG'
            )
        ]
        
        # Test hierarchy resolution
        hierarchy = IntelligentSignalHierarchy()
        result = hierarchy.resolve_signals(signals)
        
        print(f"📊 HIERARCHY RESOLUTION RESULT:")
        print(f"  Decision: {result['decision']}")
        print(f"  Confidence: {result['confidence']:.1%}")
        print(f"  Risk Level: {result['risk_level']}")
        print(f"  Reasoning: {result['reasoning']}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR in signal hierarchy test: {e}")
        return False

def test_position_sizing_standalone():
    """Test position sizing system standalone"""
    print("\n" + "=" * 60)
    print("💰 TESTING POSITION SIZING STANDALONE")
    print("=" * 60)
    
    try:
        from ml.position_sizing import SmartPositionSizer
        
        # Test with different modes
        for conservative in [True, False]:
            mode_name = "Conservative" if conservative else "Moderate"
            print(f"\n📊 Testing {mode_name} Mode:")
            
            sizer = SmartPositionSizer(conservative_mode=conservative)
            
            # Mock market data
            market_data = {
                'order_book': {
                    'bids': [[0.3245, 1000], [0.3244, 1500], [0.3243, 2000]],
                    'asks': [[0.3246, 1200], [0.3247, 1800], [0.3248, 2200]]
                },
                'volume_24h': 50000,
                'current_price': 0.3245,
                'recent_trades': [{'amount': 100} for _ in range(20)]
            }
            
            result = sizer.calculate_optimal_position_size(
                base_position_size=100.0,
                market_data=market_data,
                confidence=0.75,
                account_balance=500.0
            )
            
            print(f"  Optimal Size: {result['optimal_position_size']:.2f} units")
            print(f"  Position USD: ${result['position_usd']:.2f}")
            print(f"  Liquidity Score: {result['liquidity_score']:.3f}")
            print(f"  Market Impact: {result['market_impact_estimate']:.3f}%")
            
        return True
        
    except Exception as e:
        print(f"❌ ERROR in position sizing test: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Epinnox v6 Optimization Tests...")
    
    # Run all tests
    tests_passed = 0
    total_tests = 3
    
    if test_optimizations():
        tests_passed += 1
    
    if test_signal_hierarchy_standalone():
        tests_passed += 1
    
    if test_position_sizing_standalone():
        tests_passed += 1
    
    print(f"\n" + "=" * 60)
    print(f"🏁 TEST SUMMARY: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 ALL OPTIMIZATIONS WORKING PERFECTLY!")
    elif tests_passed >= 2:
        print("👍 MOST OPTIMIZATIONS WORKING!")
    else:
        print("⚠️ OPTIMIZATIONS NEED MORE WORK!")
    
    print("=" * 60)
