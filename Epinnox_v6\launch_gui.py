#!/usr/bin/env python
"""
Epinnox v6 Trading System - GUI Launcher
Professional entry point for the Epinnox trading interface
"""

import sys
import os
from pathlib import Path

def main():
    """Main entry point for Epinnox GUI application"""
     
    print("🚀 Starting Epinnox v6 Trading System...")
    print("=" * 60)
    
    # Ensure required directories exist
    os.makedirs('logs', exist_ok=True)
    os.makedirs('cache', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        sys.exit(1)
    
    # Check for required dependencies
    try:
        import PyQt5
        print("✓ PyQt5 available")
    except ImportError:
        print("❌ Error: PyQt5 is required")
        print("   Install with: pip install PyQt5")
        sys.exit(1)
    
    try:
        import ccxt
        print("✓ CCXT library available")
    except ImportError:
        print("❌ Error: CCXT is required for exchange connectivity")
        print("   Install with: pip install ccxt")
        sys.exit(1)
    
    # Check for configuration files
    config_dir = Path("config")
    if not (config_dir / "credentials.yaml").exists():
        print("⚠️  Warning: credentials.yaml not found")
        print("   Copy from credentials.yaml.example and configure your API keys")
    
    if not (config_dir / "production_config.yaml").exists():
        print("❌ Error: production_config.yaml not found")
        sys.exit(1)
    
    print("✓ All dependencies and configuration files verified")
    print("=" * 60)
    
    # Import and run the main application
    try:
        # Set up the environment
        os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "1"
        
        # Import the main trading interface
        from launch_epinnox import main as launch_main
        
        # Start the application
        print("🎯 Launching Epinnox Trading Interface...")
        launch_main()
        
    except ImportError as e:
        print(f"❌ Error importing main application: {e}")
        print("   Please ensure all dependencies are installed")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
