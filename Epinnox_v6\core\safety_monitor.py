"""
Safety Monitor
Comprehensive safety and monitoring system for autonomous trading
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class SafetyRule(Enum):
    MAX_DAILY_LOSS = "max_daily_loss"
    MAX_POSITION_SIZE = "max_position_size"
    MAX_LEVERAGE = "max_leverage"
    MAX_OPEN_POSITIONS = "max_open_positions"
    MIN_ACCOUNT_BALANCE = "min_account_balance"
    MAX_DRAWDOWN = "max_drawdown"
    STALE_DATA = "stale_data"
    EXCHANGE_CONNECTIVITY = "exchange_connectivity"
    SYSTEM_PERFORMANCE = "system_performance"

@dataclass
class SafetyAlert:
    rule: SafetyRule
    level: AlertLevel
    message: str
    value: Any
    threshold: Any
    timestamp: datetime
    resolved: bool = False

@dataclass
class SystemMetrics:
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_latency: float
    data_freshness: float  # seconds since last data update
    exchange_connectivity: bool
    active_positions: int
    daily_pnl: float
    account_balance: float
    max_drawdown: float
    timestamp: datetime

class SafetyMonitor:
    """
    Comprehensive safety monitoring system for autonomous trading
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.safety_rules = self._initialize_safety_rules()
        self.alerts = []
        self.metrics_history = []
        self.monitoring_enabled = True
        self.emergency_callbacks = []
        
        # Monitoring intervals
        self.health_check_interval = 30  # seconds
        self.metrics_collection_interval = 10  # seconds
        
        # Last check timestamps
        self.last_health_check = None
        self.last_metrics_collection = None
        
        logger.info("Safety Monitor initialized")
    
    def _initialize_safety_rules(self) -> Dict[SafetyRule, Dict]:
        """Initialize safety rules with thresholds"""
        return {
            SafetyRule.MAX_DAILY_LOSS: {
                'threshold': self.config.get('max_daily_loss', -500.0),
                'enabled': True,
                'level': AlertLevel.CRITICAL
            },
            SafetyRule.MAX_POSITION_SIZE: {
                'threshold': self.config.get('max_position_size', 1000.0),
                'enabled': True,
                'level': AlertLevel.WARNING
            },
            SafetyRule.MAX_LEVERAGE: {
                'threshold': self.config.get('max_leverage', 10.0),
                'enabled': True,
                'level': AlertLevel.WARNING
            },
            SafetyRule.MAX_OPEN_POSITIONS: {
                'threshold': self.config.get('max_open_positions', 5),
                'enabled': True,
                'level': AlertLevel.WARNING
            },
            SafetyRule.MIN_ACCOUNT_BALANCE: {
                'threshold': self.config.get('min_account_balance', 100.0),
                'enabled': True,
                'level': AlertLevel.CRITICAL
            },
            SafetyRule.MAX_DRAWDOWN: {
                'threshold': self.config.get('max_drawdown', -20.0),  # 20%
                'enabled': True,
                'level': AlertLevel.CRITICAL
            },
            SafetyRule.STALE_DATA: {
                'threshold': 300,  # 5 minutes
                'enabled': True,
                'level': AlertLevel.WARNING
            },
            SafetyRule.EXCHANGE_CONNECTIVITY: {
                'threshold': True,
                'enabled': True,
                'level': AlertLevel.CRITICAL
            },
            SafetyRule.SYSTEM_PERFORMANCE: {
                'threshold': {'cpu': 90.0, 'memory': 90.0, 'disk': 95.0},
                'enabled': True,
                'level': AlertLevel.WARNING
            }
        }
    
    async def start_monitoring(self):
        """Start the safety monitoring loop"""
        logger.info("Starting safety monitoring...")
        
        # Start monitoring tasks
        tasks = [
            asyncio.create_task(self._health_check_loop()),
            asyncio.create_task(self._metrics_collection_loop()),
            asyncio.create_task(self._alert_processing_loop())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Error in safety monitoring: {e}")
    
    async def _health_check_loop(self):
        """Continuous health check loop"""
        while self.monitoring_enabled:
            try:
                await self._perform_health_check()
                await asyncio.sleep(self.health_check_interval)
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(5)
    
    async def _metrics_collection_loop(self):
        """Continuous metrics collection loop"""
        while self.monitoring_enabled:
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(self.metrics_collection_interval)
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
                await asyncio.sleep(5)
    
    async def _alert_processing_loop(self):
        """Process and handle alerts"""
        while self.monitoring_enabled:
            try:
                await self._process_alerts()
                await asyncio.sleep(5)
            except Exception as e:
                logger.error(f"Error in alert processing loop: {e}")
                await asyncio.sleep(5)
    
    async def _perform_health_check(self):
        """Perform comprehensive health check"""
        try:
            # Collect current metrics
            metrics = await self._get_current_metrics()
            
            # Check each safety rule
            for rule, config in self.safety_rules.items():
                if not config['enabled']:
                    continue
                
                violation = await self._check_safety_rule(rule, metrics, config)
                if violation:
                    await self._create_alert(violation)
            
            self.last_health_check = datetime.now()
            
        except Exception as e:
            logger.error(f"Error in health check: {e}")
    
    async def _check_safety_rule(self, rule: SafetyRule, metrics: SystemMetrics, 
                                config: Dict) -> Optional[SafetyAlert]:
        """Check a specific safety rule"""
        try:
            threshold = config['threshold']
            level = config['level']
            
            if rule == SafetyRule.MAX_DAILY_LOSS:
                if metrics.daily_pnl < threshold:
                    return SafetyAlert(
                        rule=rule,
                        level=level,
                        message=f"Daily loss limit exceeded: ${metrics.daily_pnl:.2f} < ${threshold:.2f}",
                        value=metrics.daily_pnl,
                        threshold=threshold,
                        timestamp=datetime.now()
                    )
            
            elif rule == SafetyRule.MIN_ACCOUNT_BALANCE:
                if metrics.account_balance < threshold:
                    return SafetyAlert(
                        rule=rule,
                        level=level,
                        message=f"Account balance too low: ${metrics.account_balance:.2f} < ${threshold:.2f}",
                        value=metrics.account_balance,
                        threshold=threshold,
                        timestamp=datetime.now()
                    )
            
            elif rule == SafetyRule.MAX_DRAWDOWN:
                if metrics.max_drawdown < threshold:
                    return SafetyAlert(
                        rule=rule,
                        level=level,
                        message=f"Maximum drawdown exceeded: {metrics.max_drawdown:.2f}% < {threshold:.2f}%",
                        value=metrics.max_drawdown,
                        threshold=threshold,
                        timestamp=datetime.now()
                    )
            
            elif rule == SafetyRule.MAX_OPEN_POSITIONS:
                if metrics.active_positions > threshold:
                    return SafetyAlert(
                        rule=rule,
                        level=level,
                        message=f"Too many open positions: {metrics.active_positions} > {threshold}",
                        value=metrics.active_positions,
                        threshold=threshold,
                        timestamp=datetime.now()
                    )
            
            elif rule == SafetyRule.STALE_DATA:
                if metrics.data_freshness > threshold:
                    return SafetyAlert(
                        rule=rule,
                        level=level,
                        message=f"Stale data detected: {metrics.data_freshness:.0f}s > {threshold}s",
                        value=metrics.data_freshness,
                        threshold=threshold,
                        timestamp=datetime.now()
                    )
            
            elif rule == SafetyRule.EXCHANGE_CONNECTIVITY:
                if not metrics.exchange_connectivity:
                    return SafetyAlert(
                        rule=rule,
                        level=level,
                        message="Exchange connectivity lost",
                        value=metrics.exchange_connectivity,
                        threshold=threshold,
                        timestamp=datetime.now()
                    )
            
            elif rule == SafetyRule.SYSTEM_PERFORMANCE:
                if (metrics.cpu_usage > threshold['cpu'] or 
                    metrics.memory_usage > threshold['memory'] or 
                    metrics.disk_usage > threshold['disk']):
                    return SafetyAlert(
                        rule=rule,
                        level=level,
                        message=f"System performance degraded: CPU={metrics.cpu_usage:.1f}%, "
                               f"Memory={metrics.memory_usage:.1f}%, Disk={metrics.disk_usage:.1f}%",
                        value={'cpu': metrics.cpu_usage, 'memory': metrics.memory_usage, 'disk': metrics.disk_usage},
                        threshold=threshold,
                        timestamp=datetime.now()
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking safety rule {rule}: {e}")
            return None
    
    async def _get_current_metrics(self) -> SystemMetrics:
        """Get current system metrics"""
        try:
            # System performance metrics
            import psutil
            cpu_usage = psutil.cpu_percent(interval=1)
            memory_usage = psutil.virtual_memory().percent
            disk_usage = psutil.disk_usage('/').percent
            
            # Network latency (simplified)
            network_latency = 0.0  # Would implement actual ping test
            
            # Trading metrics (would get from portfolio manager)
            data_freshness = 0.0  # Seconds since last data update
            exchange_connectivity = True  # Would check actual connectivity
            active_positions = 0
            daily_pnl = 0.0
            account_balance = 10000.0  # Would get from account
            max_drawdown = 0.0
            
            return SystemMetrics(
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                disk_usage=disk_usage,
                network_latency=network_latency,
                data_freshness=data_freshness,
                exchange_connectivity=exchange_connectivity,
                active_positions=active_positions,
                daily_pnl=daily_pnl,
                account_balance=account_balance,
                max_drawdown=max_drawdown,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error getting system metrics: {e}")
            # Return default metrics
            return SystemMetrics(0, 0, 0, 0, 0, False, 0, 0, 0, 0, datetime.now())
    
    async def _collect_system_metrics(self):
        """Collect and store system metrics"""
        try:
            metrics = await self._get_current_metrics()
            self.metrics_history.append(metrics)
            
            # Keep only last 1000 metrics (about 3 hours at 10s intervals)
            if len(self.metrics_history) > 1000:
                self.metrics_history = self.metrics_history[-1000:]
            
            self.last_metrics_collection = datetime.now()
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
    
    async def _create_alert(self, alert: SafetyAlert):
        """Create and process a new alert"""
        # Check if similar alert already exists and is not resolved
        existing_alert = self._find_existing_alert(alert.rule)
        if existing_alert and not existing_alert.resolved:
            return  # Don't create duplicate alerts
        
        self.alerts.append(alert)
        logger.log(
            logging.CRITICAL if alert.level == AlertLevel.EMERGENCY else
            logging.ERROR if alert.level == AlertLevel.CRITICAL else
            logging.WARNING,
            f"SAFETY ALERT [{alert.level.value.upper()}]: {alert.message}"
        )
        
        # Trigger emergency callbacks for critical/emergency alerts
        if alert.level in [AlertLevel.CRITICAL, AlertLevel.EMERGENCY]:
            await self._trigger_emergency_callbacks(alert)
    
    def _find_existing_alert(self, rule: SafetyRule) -> Optional[SafetyAlert]:
        """Find existing unresolved alert for a rule"""
        for alert in reversed(self.alerts):
            if alert.rule == rule and not alert.resolved:
                return alert
        return None
    
    async def _trigger_emergency_callbacks(self, alert: SafetyAlert):
        """Trigger emergency callbacks"""
        for callback in self.emergency_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(alert)
                else:
                    callback(alert)
            except Exception as e:
                logger.error(f"Error in emergency callback: {e}")
    
    async def _process_alerts(self):
        """Process and manage alerts"""
        try:
            # Auto-resolve alerts that are no longer valid
            current_metrics = await self._get_current_metrics()
            
            for alert in self.alerts:
                if not alert.resolved:
                    # Check if alert condition is resolved
                    if await self._is_alert_resolved(alert, current_metrics):
                        alert.resolved = True
                        logger.info(f"Alert resolved: {alert.message}")
        
        except Exception as e:
            logger.error(f"Error processing alerts: {e}")
    
    async def _is_alert_resolved(self, alert: SafetyAlert, metrics: SystemMetrics) -> bool:
        """Check if an alert condition is resolved"""
        try:
            rule = alert.rule
            threshold = alert.threshold
            
            if rule == SafetyRule.MAX_DAILY_LOSS:
                return metrics.daily_pnl >= threshold
            elif rule == SafetyRule.MIN_ACCOUNT_BALANCE:
                return metrics.account_balance >= threshold
            elif rule == SafetyRule.MAX_DRAWDOWN:
                return metrics.max_drawdown >= threshold
            elif rule == SafetyRule.MAX_OPEN_POSITIONS:
                return metrics.active_positions <= threshold
            elif rule == SafetyRule.STALE_DATA:
                return metrics.data_freshness <= threshold
            elif rule == SafetyRule.EXCHANGE_CONNECTIVITY:
                return metrics.exchange_connectivity == threshold
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking if alert is resolved: {e}")
            return False
    
    def add_emergency_callback(self, callback: Callable):
        """Add emergency callback function"""
        self.emergency_callbacks.append(callback)
    
    def get_active_alerts(self) -> List[SafetyAlert]:
        """Get all active (unresolved) alerts"""
        return [alert for alert in self.alerts if not alert.resolved]
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        active_alerts = self.get_active_alerts()
        latest_metrics = self.metrics_history[-1] if self.metrics_history else None
        
        return {
            'monitoring_enabled': self.monitoring_enabled,
            'last_health_check': self.last_health_check,
            'last_metrics_collection': self.last_metrics_collection,
            'active_alerts_count': len(active_alerts),
            'critical_alerts_count': len([a for a in active_alerts if a.level in [AlertLevel.CRITICAL, AlertLevel.EMERGENCY]]),
            'latest_metrics': latest_metrics.__dict__ if latest_metrics else None,
            'safety_rules_enabled': sum(1 for rule in self.safety_rules.values() if rule['enabled'])
        }
    
    def stop_monitoring(self):
        """Stop safety monitoring"""
        logger.info("Stopping safety monitoring...")
        self.monitoring_enabled = False
