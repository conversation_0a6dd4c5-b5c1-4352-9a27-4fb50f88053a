{"deployment_id": "autonomous_20250630_225918", "timestamp": "2025-06-30T22:59:18.545026", "config_version": "6.0.0", "deployment_type": "autonomous_live", "initial_balance": 50.0, "risk_settings": {"max_portfolio_risk": 0.02, "portfolio_exposure_limit": 0.8, "max_daily_loss": 0.02, "max_drawdown": 0.05, "max_position_size": 0.01, "max_concurrent_positions": 1, "max_leverage": 2.0, "stop_loss_pct": 0.015, "take_profit_pct": 0.03, "max_trades_per_day": 3, "cooldown_minutes": 120, "circuit_breakers": {"consecutive_losses": 2, "daily_loss_threshold": 0.015, "api_error_limit": 3, "system_error_limit": 2}}, "safety_checks_passed": {"api_connectivity": true, "balance_sufficient": true, "risk_limits_configured": true, "safety_mechanisms_active": true, "monitoring_systems_ready": true, "configuration_valid": true}, "status": "ready_for_execution", "execution_instructions": ["1. Enable 'Auto-Select Best Symbol' checkbox", "2. Enable 'ScalperGPT Auto Trader' checkbox", "3. Verify autonomous trading loop starts", "4. Monitor autonomous_trading.log for activity"], "fixes_applied": "2025-07-06 23:02:04", "execution_steps": ["1. Run: python start_autonomous_loop.py", "2. Enable Auto-Select Best Symbol checkbox in GUI", "3. Enable ScalperGPT Auto Trader checkbox in GUI", "4. Monitor logs/autonomous_trading.log for activity", "5. Check main application logs for trading decisions"]}