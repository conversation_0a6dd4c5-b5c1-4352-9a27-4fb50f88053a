# EPINNOX v6 - Complete Requirements File
# Core dependencies for full functionality including RL, ML, GUI, and advanced features

# ===== CORE DEPENDENCIES =====
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0
python-dateutil>=2.8.0
pytz>=2022.1
requests>=2.28.0
aiohttp>=3.8.0
websockets>=10.0
sqlite3  # Built into Python

# ===== MACHINE LEARNING & RL =====
# PyTorch (CPU version - GPU version installed separately)
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0

# Reinforcement Learning
stable-baselines3[extra]>=1.6.0
gymnasium>=0.26.0  # Modern replacement for gym
gym>=0.21.0  # Legacy compatibility

# Deep Learning (Optional - for LSTM models)
tensorflow>=2.10.0
keras>=2.10.0

# Scikit-learn for traditional ML
scikit-learn>=1.1.0

# ===== TECHNICAL ANALYSIS =====
# TA-Lib (requires system library installation)
TA-Lib>=0.4.24

# Alternative technical analysis libraries
ta>=0.10.0
pandas-ta>=0.3.14b

# ===== GUI & VISUALIZATION =====
# PyQt5 for GUI dashboard
PyQt5>=5.15.0
pyqtgraph>=0.12.0

# Streamlit for web dashboard
streamlit>=1.25.0
plotly>=5.15.0

# Matplotlib for plotting
matplotlib>=3.5.0
seaborn>=0.11.0

# ===== TESTING FRAMEWORK =====
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
pytest-cov>=4.0.0
pytest-timeout>=2.1.0

# ===== DATA PROCESSING =====
# JSON and data handling
jsonschema>=4.0.0
pydantic>=1.10.0

# Time series analysis
statsmodels>=0.13.0

# ===== NETWORKING & APIs =====
# HTTP clients
httpx>=0.24.0
urllib3>=1.26.0

# WebSocket clients
websocket-client>=1.4.0

# ===== DEVELOPMENT TOOLS =====
# Code formatting and linting
black>=22.0.0
flake8>=5.0.0
isort>=5.10.0
mypy>=0.991

# Jupyter for analysis
jupyter>=1.0.0
ipykernel>=6.15.0

# ===== OPTIONAL PERFORMANCE =====
# Faster JSON processing
orjson>=3.8.0

# Faster data processing
numba>=0.56.0
cython>=0.29.0

# ===== MONITORING & LOGGING =====
# Advanced logging
loguru>=0.6.0

# System monitoring
psutil>=5.9.0

# ===== DOCUMENTATION =====
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0

# ===== DEPLOYMENT =====
# Configuration management
python-dotenv>=0.20.0
configparser>=5.3.0

# Process management
supervisor>=4.2.0

# ===== OPTIONAL ADVANCED FEATURES =====
# Natural Language Processing
transformers>=4.21.0
torch-audio>=0.12.0
sentencepiece>=0.1.97

# Advanced optimization
optuna>=3.0.0
ray[tune]>=2.0.0

# Database alternatives
sqlalchemy>=1.4.0
redis>=4.3.0

# ===== PLATFORM-SPECIFIC =====
# Windows-specific
pywin32>=304; sys_platform == "win32"

# Linux-specific  
python-systemd>=234; sys_platform == "linux"

# macOS-specific
pyobjc>=8.5; sys_platform == "darwin"
