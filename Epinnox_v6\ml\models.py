"""
Machine Learning Models for Trading System

This module implements SVM, Random Forest, and LSTM models for price prediction
and classification to enhance trading decisions.
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Any
from sklearn.svm import SVC, SVR
from sklearn.ensemble import RandomF<PERSON>tClassifier, RandomForestRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import joblib
import os
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger(__name__)

# Try to import deep learning libraries
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.optimizers import Adam
    TENSORFLOW_AVAILABLE = True
    logger.info("TensorFlow available for LSTM models")
except ImportError:
    TENSORFLOW_AVAILABLE = False
    logger.warning("TensorFlow not available. LSTM models will be disabled.")

class MLModelManager:
    """
    Manages multiple ML models for trading predictions
    """
    
    def __init__(self, model_dir='ml/saved_models'):
        """
        Initialize ML model manager
        
        Args:
            model_dir: Directory to save/load models
        """
        self.model_dir = model_dir
        self.models = {}
        self.scalers = {}
        self.label_encoders = {}
        self.feature_columns = []
        self.target_columns = []
        
        # Ensure model directory exists
        os.makedirs(model_dir, exist_ok=True)
        
        # Initialize models
        self._initialize_models()
        
        logger.info("ML Model Manager initialized")
    
    def _initialize_models(self):
        """Initialize all ML models"""
        
        # 1. ENHANCED SVM Models with optimized parameters
        self.models['svm_classifier'] = SVC(
            kernel='rbf',
            C=2.0,              # Increased regularization
            gamma='auto',       # Better for financial data
            probability=True,   # Enable probability estimates
            class_weight='balanced',  # Handle imbalanced classes
            random_state=42
        )

        self.models['svm_regressor'] = SVR(
            kernel='rbf',
            C=2.0,              # Increased regularization
            gamma='auto',       # Better for financial data
            epsilon=0.01        # Smaller epsilon for better precision
        )

        # 2. ENHANCED Random Forest Models with optimized parameters
        self.models['rf_classifier'] = RandomForestClassifier(
            n_estimators=200,        # More trees for better accuracy
            max_depth=15,            # Deeper trees for complex patterns
            min_samples_split=3,     # More sensitive to patterns
            min_samples_leaf=1,      # Allow more granular decisions
            max_features='sqrt',     # Optimal feature selection
            class_weight='balanced', # Handle imbalanced classes
            bootstrap=True,
            oob_score=True,         # Out-of-bag scoring
            random_state=42,
            n_jobs=-1
        )

        self.models['rf_regressor'] = RandomForestRegressor(
            n_estimators=200,        # More trees for better accuracy
            max_depth=15,            # Deeper trees for complex patterns
            min_samples_split=3,     # More sensitive to patterns
            min_samples_leaf=1,      # Allow more granular decisions
            max_features='sqrt',     # Optimal feature selection
            bootstrap=True,
            oob_score=True,         # Out-of-bag scoring
            random_state=42,
            n_jobs=-1
        )
        
        # 3. LSTM Model (if TensorFlow available)
        if TENSORFLOW_AVAILABLE:
            self.models['lstm_classifier'] = None  # Will be built dynamically
            self.models['lstm_regressor'] = None   # Will be built dynamically
        
        # Initialize scalers
        self.scalers['features'] = StandardScaler()
        self.scalers['targets'] = StandardScaler()
        
        # Initialize label encoder for classification
        self.label_encoders['direction'] = LabelEncoder()
    
    def prepare_features(self, data: pd.DataFrame) -> np.ndarray:
        """
        Prepare features for ML models
        
        Args:
            data: Market data DataFrame
            
        Returns:
            np.ndarray: Prepared features
        """
        try:
            # ENHANCED FEATURE ENGINEERING
            # Add technical indicators if not present
            data = self._add_technical_indicators(data)

            # Define comprehensive feature set
            base_features = [
                'open', 'high', 'low', 'close', 'volume',
                'rsi', 'macd', 'macd_signal', 'bb_upper', 'bb_lower',
                'sma_20', 'sma_50', 'ema_20', 'atr'
            ]

            # Add advanced features
            data = self._add_advanced_features(data)

            advanced_features = [
                'price_momentum', 'volume_momentum', 'volatility_ratio',
                'trend_strength', 'support_resistance', 'breakout_signal',
                'volume_profile', 'price_acceleration'
            ]

            # Combine all features
            feature_cols = base_features + advanced_features

            # Select available features
            available_cols = [col for col in feature_cols if col in data.columns]

            if not available_cols:
                logger.error("No valid feature columns found")
                return np.array([])

            # Extract features with improved preprocessing
            features = data[available_cols].copy()

            # Advanced preprocessing
            features = self._preprocess_features(features)

            # Store feature columns for consistency
            self.feature_columns = available_cols

            return features.values
            
        except Exception as e:
            logger.error(f"Error preparing features: {e}")
            return np.array([])
    
    def _add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators to data"""
        try:
            df = data.copy()
            
            # RSI
            if 'rsi' not in df.columns:
                delta = df['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                df['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD
            if 'macd' not in df.columns:
                exp1 = df['close'].ewm(span=12).mean()
                exp2 = df['close'].ewm(span=26).mean()
                df['macd'] = exp1 - exp2
                df['macd_signal'] = df['macd'].ewm(span=9).mean()
            
            # Bollinger Bands
            if 'bb_upper' not in df.columns:
                sma = df['close'].rolling(window=20).mean()
                std = df['close'].rolling(window=20).std()
                df['bb_upper'] = sma + (std * 2)
                df['bb_lower'] = sma - (std * 2)
            
            # Moving Averages
            if 'sma_20' not in df.columns:
                df['sma_20'] = df['close'].rolling(window=20).mean()
            if 'sma_50' not in df.columns:
                df['sma_50'] = df['close'].rolling(window=50).mean()
            if 'ema_20' not in df.columns:
                df['ema_20'] = df['close'].ewm(span=20).mean()
            
            # ATR
            if 'atr' not in df.columns:
                high_low = df['high'] - df['low']
                high_close = np.abs(df['high'] - df['close'].shift())
                low_close = np.abs(df['low'] - df['close'].shift())
                ranges = pd.concat([high_low, high_close, low_close], axis=1)
                true_range = ranges.max(axis=1)
                df['atr'] = true_range.rolling(window=14).mean()
            
            return df

        except Exception as e:
            logger.error(f"Error adding technical indicators: {e}")
            return data

    def _add_advanced_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add advanced features for better ML performance"""
        try:
            df = data.copy()

            # Price momentum features
            df['price_momentum'] = df['close'].pct_change(5)  # 5-period momentum
            df['volume_momentum'] = df['volume'].pct_change(5)

            # Volatility ratio
            short_vol = df['close'].rolling(5).std()
            long_vol = df['close'].rolling(20).std()
            df['volatility_ratio'] = short_vol / (long_vol + 1e-8)

            # Trend strength
            df['trend_strength'] = abs(df['close'] - df['sma_20']) / df['atr']

            # Support/Resistance levels
            df['support_resistance'] = (df['close'] - df['low'].rolling(20).min()) / (
                df['high'].rolling(20).max() - df['low'].rolling(20).min() + 1e-8)

            # Breakout signals
            df['breakout_signal'] = np.where(
                df['close'] > df['high'].rolling(10).max().shift(1), 1,
                np.where(df['close'] < df['low'].rolling(10).min().shift(1), -1, 0)
            )

            # Volume profile
            df['volume_profile'] = df['volume'] / df['volume'].rolling(20).mean()

            # Price acceleration
            df['price_acceleration'] = df['close'].diff().diff()

            return df

        except Exception as e:
            logger.error(f"Error adding advanced features: {e}")
            return data

    def _preprocess_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """Advanced feature preprocessing"""
        try:
            # Forward fill then backward fill
            features = features.ffill().bfill().fillna(0)

            # Remove infinite values
            features = features.replace([np.inf, -np.inf], 0)

            # Clip extreme outliers (beyond 5 standard deviations)
            for col in features.columns:
                if features[col].dtype in ['float64', 'int64']:
                    mean_val = features[col].mean()
                    std_val = features[col].std()
                    if std_val > 0:
                        features[col] = features[col].clip(
                            lower=mean_val - 5*std_val,
                            upper=mean_val + 5*std_val
                        )

            return features

        except Exception as e:
            logger.error(f"Error preprocessing features: {e}")
            return features
    
    def prepare_targets(self, data: pd.DataFrame, prediction_horizon: int = 5) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare target variables for training
        
        Args:
            data: Market data DataFrame
            prediction_horizon: Number of periods to predict ahead
            
        Returns:
            Tuple of (classification_targets, regression_targets)
        """
        try:
            # Calculate future returns
            future_returns = data['close'].pct_change(prediction_horizon).shift(-prediction_horizon)
            
            # Classification targets (direction)
            direction_targets = np.where(future_returns > 0.001, 'LONG',
                                       np.where(future_returns < -0.001, 'SHORT', 'WAIT'))
            
            # Regression targets (magnitude)
            magnitude_targets = future_returns.fillna(0).values
            
            return direction_targets, magnitude_targets
            
        except Exception as e:
            logger.error(f"Error preparing targets: {e}")
            return np.array([]), np.array([])
    
    def train_models(self, data: pd.DataFrame, retrain: bool = False) -> Dict[str, float]:
        """
        Train all ML models
        
        Args:
            data: Training data
            retrain: Whether to retrain existing models
            
        Returns:
            Dict with training scores
        """
        logger.info("Starting ML model training...")
        
        try:
            # Prepare features and targets
            features = self.prepare_features(data)
            direction_targets, magnitude_targets = self.prepare_targets(data)
            
            if len(features) == 0 or len(direction_targets) == 0:
                logger.error("No valid training data available")
                return {}
            
            # Remove NaN values
            valid_indices = ~(np.isnan(features).any(axis=1) | 
                            pd.isna(direction_targets) | 
                            np.isnan(magnitude_targets))
            
            features = features[valid_indices]
            direction_targets = direction_targets[valid_indices]
            magnitude_targets = magnitude_targets[valid_indices]
            
            if len(features) < 50:
                logger.warning("Insufficient training data (< 50 samples)")
                return {}
            
            # Scale features
            features_scaled = self.scalers['features'].fit_transform(features)
            magnitude_scaled = self.scalers['targets'].fit_transform(magnitude_targets.reshape(-1, 1)).flatten()
            
            # Encode direction targets
            direction_encoded = self.label_encoders['direction'].fit_transform(direction_targets)
            
            # Split data
            X_train, X_test, y_dir_train, y_dir_test, y_mag_train, y_mag_test = train_test_split(
                features_scaled, direction_encoded, magnitude_scaled, 
                test_size=0.2, random_state=42, stratify=direction_encoded
            )
            
            scores = {}
            
            # Train SVM models
            logger.info("Training SVM models...")
            self.models['svm_classifier'].fit(X_train, y_dir_train)
            self.models['svm_regressor'].fit(X_train, y_mag_train)
            
            scores['svm_classifier'] = self.models['svm_classifier'].score(X_test, y_dir_test)
            scores['svm_regressor'] = self.models['svm_regressor'].score(X_test, y_mag_test)
            
            # Train Random Forest models
            logger.info("Training Random Forest models...")
            self.models['rf_classifier'].fit(X_train, y_dir_train)
            self.models['rf_regressor'].fit(X_train, y_mag_train)
            
            scores['rf_classifier'] = self.models['rf_classifier'].score(X_test, y_dir_test)
            scores['rf_regressor'] = self.models['rf_regressor'].score(X_test, y_mag_test)
            
            # Train LSTM models if available
            if TENSORFLOW_AVAILABLE:
                logger.info("Training LSTM models...")
                lstm_scores = self._train_lstm_models(X_train, X_test, y_dir_train, y_dir_test, y_mag_train, y_mag_test)
                scores.update(lstm_scores)
            
            # Save models
            self._save_models()
            
            logger.info(f"ML model training completed. Scores: {scores}")
            return scores

        except Exception as e:
            logger.error(f"Error training ML models: {e}")
            return {}

    def _train_lstm_models(self, X_train, X_test, y_dir_train, y_dir_test, y_mag_train, y_mag_test) -> Dict[str, float]:
        """Train LSTM models"""
        try:
            # Reshape data for LSTM (samples, timesteps, features)
            sequence_length = 10
            X_train_lstm = self._create_sequences(X_train, sequence_length)
            X_test_lstm = self._create_sequences(X_test, sequence_length)

            if len(X_train_lstm) == 0:
                return {}

            # Adjust targets for sequence length (align with sequence creation)
            y_dir_train_lstm = y_dir_train[sequence_length:]
            y_dir_test_lstm = y_dir_test[sequence_length:]
            y_mag_train_lstm = y_mag_train[sequence_length:]
            y_mag_test_lstm = y_mag_test[sequence_length:]

            scores = {}

            # LSTM Classifier
            self.models['lstm_classifier'] = Sequential([
                LSTM(50, return_sequences=True, input_shape=(sequence_length, X_train.shape[1])),
                Dropout(0.2),
                LSTM(50, return_sequences=False),
                Dropout(0.2),
                Dense(25),
                Dense(len(np.unique(y_dir_train)), activation='softmax')
            ])

            self.models['lstm_classifier'].compile(
                optimizer=Adam(learning_rate=0.001),
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )

            # Train classifier
            history_clf = self.models['lstm_classifier'].fit(
                X_train_lstm, y_dir_train_lstm,
                epochs=50, batch_size=32, verbose=0,
                validation_data=(X_test_lstm, y_dir_test_lstm)
            )

            scores['lstm_classifier'] = max(history_clf.history['val_accuracy'])

            # LSTM Regressor
            self.models['lstm_regressor'] = Sequential([
                LSTM(50, return_sequences=True, input_shape=(sequence_length, X_train.shape[1])),
                Dropout(0.2),
                LSTM(50, return_sequences=False),
                Dropout(0.2),
                Dense(25),
                Dense(1)
            ])

            self.models['lstm_regressor'].compile(
                optimizer=Adam(learning_rate=0.001),
                loss='mse',
                metrics=['mae']
            )

            # Train regressor
            history_reg = self.models['lstm_regressor'].fit(
                X_train_lstm, y_mag_train_lstm,
                epochs=50, batch_size=32, verbose=0,
                validation_data=(X_test_lstm, y_mag_test_lstm)
            )

            scores['lstm_regressor'] = 1.0 - min(history_reg.history['val_loss'])  # Convert loss to score

            return scores

        except Exception as e:
            logger.error(f"Error training LSTM models: {e}")
            return {}

    def _create_sequences(self, data, sequence_length):
        """Create sequences for LSTM"""
        try:
            sequences = []
            for i in range(sequence_length, len(data)):
                sequences.append(data[i-sequence_length:i])
            return np.array(sequences)
        except Exception as e:
            logger.error(f"Error creating sequences: {e}")
            return np.array([])

    def predict(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Make predictions using all trained models

        Args:
            data: Recent market data for prediction

        Returns:
            Dict with predictions from all models
        """
        try:
            # Prepare features
            features = self.prepare_features(data)

            if len(features) == 0:
                logger.error("No features available for prediction")
                return {}

            # Use only the latest data point for prediction
            latest_features = features[-1:] if len(features.shape) > 1 else features.reshape(1, -1)

            # Scale features
            if hasattr(self.scalers['features'], 'mean_'):
                features_scaled = self.scalers['features'].transform(latest_features)
            else:
                logger.warning("Scaler not fitted, using raw features")
                features_scaled = latest_features

            predictions = {}

            # SVM Predictions
            if 'svm_classifier' in self.models and hasattr(self.models['svm_classifier'], 'predict'):
                try:
                    svm_dir_prob = self.models['svm_classifier'].predict_proba(features_scaled)[0]
                    svm_dir_pred = self.models['svm_classifier'].predict(features_scaled)[0]
                    svm_mag_pred = self.models['svm_regressor'].predict(features_scaled)[0]

                    # Decode direction prediction
                    direction_classes = self.label_encoders['direction'].classes_
                    svm_direction = direction_classes[svm_dir_pred]

                    predictions['svm'] = {
                        'direction': svm_direction,
                        'direction_confidence': max(svm_dir_prob),
                        'magnitude': svm_mag_pred,
                        'probabilities': dict(zip(direction_classes, svm_dir_prob))
                    }
                except Exception as e:
                    logger.error(f"SVM prediction error: {e}")

            # Random Forest Predictions
            if 'rf_classifier' in self.models and hasattr(self.models['rf_classifier'], 'predict'):
                try:
                    rf_dir_prob = self.models['rf_classifier'].predict_proba(features_scaled)[0]
                    rf_dir_pred = self.models['rf_classifier'].predict(features_scaled)[0]
                    rf_mag_pred = self.models['rf_regressor'].predict(features_scaled)[0]

                    # Decode direction prediction
                    direction_classes = self.label_encoders['direction'].classes_
                    rf_direction = direction_classes[rf_dir_pred]

                    predictions['random_forest'] = {
                        'direction': rf_direction,
                        'direction_confidence': max(rf_dir_prob),
                        'magnitude': rf_mag_pred,
                        'probabilities': dict(zip(direction_classes, rf_dir_prob))
                    }
                except Exception as e:
                    logger.error(f"Random Forest prediction error: {e}")

            # LSTM Predictions
            if TENSORFLOW_AVAILABLE and 'lstm_classifier' in self.models and self.models['lstm_classifier'] is not None:
                try:
                    # Prepare sequence data for LSTM
                    sequence_length = 10
                    if len(features) >= sequence_length:
                        lstm_features = self._create_sequences(
                            self.scalers['features'].transform(features[-sequence_length:]),
                            sequence_length
                        )

                        if len(lstm_features) > 0:
                            lstm_dir_prob = self.models['lstm_classifier'].predict(lstm_features, verbose=0)[0]
                            lstm_dir_pred = np.argmax(lstm_dir_prob)
                            lstm_mag_pred = self.models['lstm_regressor'].predict(lstm_features, verbose=0)[0][0]

                            # Decode direction prediction
                            direction_classes = self.label_encoders['direction'].classes_
                            lstm_direction = direction_classes[lstm_dir_pred]

                            predictions['lstm'] = {
                                'direction': lstm_direction,
                                'direction_confidence': max(lstm_dir_prob),
                                'magnitude': lstm_mag_pred,
                                'probabilities': dict(zip(direction_classes, lstm_dir_prob))
                            }
                except Exception as e:
                    logger.error(f"LSTM prediction error: {e}")

            # Calculate ensemble prediction
            if predictions:
                predictions['ensemble'] = self._calculate_ensemble_prediction(predictions)

            return predictions

        except Exception as e:
            logger.error(f"Error making predictions: {e}")
            return {}

    def _calculate_ensemble_prediction(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate ensemble prediction from all models"""
        try:
            directions = []
            confidences = []
            magnitudes = []

            for model_name, pred in predictions.items():
                if model_name != 'ensemble' and isinstance(pred, dict):
                    directions.append(pred.get('direction', 'WAIT'))
                    confidences.append(pred.get('direction_confidence', 0.33))
                    magnitudes.append(pred.get('magnitude', 0.0))

            if not directions:
                return {'direction': 'WAIT', 'confidence': 0.33, 'magnitude': 0.0}

            # Weighted voting for direction
            direction_votes = {}
            for direction, confidence in zip(directions, confidences):
                if direction not in direction_votes:
                    direction_votes[direction] = 0
                direction_votes[direction] += confidence

            # Get most confident direction
            ensemble_direction = max(direction_votes.items(), key=lambda x: x[1])[0]
            ensemble_confidence = direction_votes[ensemble_direction] / len(directions)
            ensemble_magnitude = np.mean(magnitudes)

            return {
                'direction': ensemble_direction,
                'confidence': ensemble_confidence,
                'magnitude': ensemble_magnitude,
                'model_votes': direction_votes,
                'individual_predictions': len(predictions)
            }

        except Exception as e:
            logger.error(f"Error calculating ensemble prediction: {e}")
            return {'direction': 'WAIT', 'confidence': 0.33, 'magnitude': 0.0}

    def _save_models(self):
        """Save trained models to disk"""
        try:
            # Save sklearn models
            for model_name, model in self.models.items():
                if model is not None and hasattr(model, 'fit') and model_name not in ['lstm_classifier', 'lstm_regressor']:
                    model_path = os.path.join(self.model_dir, f"{model_name}.joblib")
                    joblib.dump(model, model_path)

            # Save scalers and encoders
            joblib.dump(self.scalers, os.path.join(self.model_dir, "scalers.joblib"))
            joblib.dump(self.label_encoders, os.path.join(self.model_dir, "label_encoders.joblib"))
            joblib.dump(self.feature_columns, os.path.join(self.model_dir, "feature_columns.joblib"))

            # Save LSTM models
            if TENSORFLOW_AVAILABLE:
                for model_name in ['lstm_classifier', 'lstm_regressor']:
                    if model_name in self.models and self.models[model_name] is not None:
                        model_path = os.path.join(self.model_dir, f"{model_name}.h5")
                        self.models[model_name].save(model_path)

            logger.info("Models saved successfully")

        except Exception as e:
            logger.error(f"Error saving models: {e}")

    def load_models(self) -> bool:
        """Load trained models from disk"""
        try:
            # Load sklearn models
            sklearn_models = ['svm_classifier', 'svm_regressor', 'rf_classifier', 'rf_regressor']
            for model_name in sklearn_models:
                model_path = os.path.join(self.model_dir, f"{model_name}.joblib")
                if os.path.exists(model_path):
                    self.models[model_name] = joblib.load(model_path)

            # Load scalers and encoders
            scalers_path = os.path.join(self.model_dir, "scalers.joblib")
            if os.path.exists(scalers_path):
                self.scalers = joblib.load(scalers_path)

            encoders_path = os.path.join(self.model_dir, "label_encoders.joblib")
            if os.path.exists(encoders_path):
                self.label_encoders = joblib.load(encoders_path)

            features_path = os.path.join(self.model_dir, "feature_columns.joblib")
            if os.path.exists(features_path):
                self.feature_columns = joblib.load(features_path)

            # Load LSTM models
            if TENSORFLOW_AVAILABLE:
                for model_name in ['lstm_classifier', 'lstm_regressor']:
                    model_path = os.path.join(self.model_dir, f"{model_name}.h5")
                    if os.path.exists(model_path):
                        self.models[model_name] = tf.keras.models.load_model(model_path)

            logger.info("Models loaded successfully")
            return True

        except Exception as e:
            logger.error(f"Error loading models: {e}")
            return False
