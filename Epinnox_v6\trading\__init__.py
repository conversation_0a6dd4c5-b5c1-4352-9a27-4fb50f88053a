"""
Trading Module
This module handles live trading operations, including leverage settings and balance tracking.
"""
from .trading_manager import TradingManager

# Import new real trading components
try:
    from .real_trading_interface import RealTradingInterface
    from .ccxt_trading_engine import CCXTTradingEngine
    from .position_tracker import PositionTracker
    __all__ = ['TradingManager', 'RealTradingInterface', 'CCXTTradingEngine', 'PositionTracker']
except ImportError:
    # Fallback if new components not available
    __all__ = ['TradingManager']
