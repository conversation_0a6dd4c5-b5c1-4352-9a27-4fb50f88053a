# ScalperGPT - LLM-Driven Autonomous Trading

## Overview

ScalperGPT is Epinnox's advanced LLM-driven scalping system that provides full autonomous trading control. Unlike traditional trading bots, ScalperGPT makes intelligent decisions about quantity, leverage, stops, and takes based on comprehensive market analysis and account awareness.

## Key Features

### 🎯 Full Trade Execution Control
- **Autonomous Decision Making**: LLM decides quantity, leverage, stop loss, take profit, and order type
- **Intelligent Position Sizing**: Dynamic sizing based on account balance, risk tolerance, and market conditions
- **Smart Order Placement**: Chooses between MARKET and LIMIT orders based on market conditions

### 📊 Enriched Market Data Analysis
- **Top 5 Bid/Ask Levels**: Deep order book analysis with price and volume data
- **Last 100 Ticks Analysis**: Tick-level ATR, trade flow imbalance, and volume momentum
- **Real-time Spread Monitoring**: Spread calculation and percentage analysis
- **Data Latency Tracking**: Monitors data freshness for optimal execution timing

### 🤖 Adaptive Ensemble System
- **8 ML Models Integration**: SVM, Random Forest, LSTM, RSI, VWAP, Orderflow, Volatility, and Sentiment models
- **Dynamic Model Weighting**: Performance-based weighting using Sharpe ratios
- **Automatic Model Pruning**: Disables underperforming models (accuracy <52% or confidence <55%)
- **Consensus Analysis**: Majority vote, weighted ensemble scores, and confidence metrics

### 🧠 Advanced LLM Integration
- **ScalperGPT Persona**: Specialized for ultra-short trades (5-30 seconds)
- **Optimized Parameters**: Temperature 0.1-0.2, max tokens 120-200 for precise responses
- **Structured JSON Output**: Enforced format for reliable parsing and execution
- **Full Account Awareness**: Complete account state included in every decision

## Technical Implementation

### Market Data Enrichment
```python
market_data = {
    'best_bid': 0.17234,
    'best_ask': 0.17236,
    'spread': 0.00002,
    'spread_pct': 0.012,
    'top_5_bids': [(price, volume), ...],
    'top_5_asks': [(price, volume), ...],
    'tick_atr': 0.00015,
    'trade_flow_imbalance': 12.5,
    'volume_momentum': 8.3,
    'data_latency_ms': 45.0
}
```

### LLM Trade Instruction Format
```json
{
  "ACTION": "BUY",
  "QUANTITY": 150.0,
  "LEVERAGE": 20,
  "STOP_LOSS": 0.0235,
  "TAKE_PROFIT": 0.0250,
  "RISK_PCT": 2.0,
  "ORDER_TYPE": "MARKET"
}
```

### Adaptive Ensemble Analysis
- **Individual Model Tracking**: Each model's decision, confidence, and performance weight
- **Ensemble Metrics**: Majority vote, average confidence, weighted score, consensus strength
- **Performance Monitoring**: Rolling 100-trade window for Sharpe ratio calculation
- **Dynamic Reweighting**: Model weights adjusted based on recent performance

## Safety Features

### Pre-execution Safety Checks
- **Emergency Stop**: Immediate halt of all autonomous trading
- **Daily Trade Limits**: Configurable maximum trades per day
- **Risk Percentage Bounds**: 0.5-5.0% risk per trade enforcement
- **Minimum Balance**: $20 minimum for autonomous trades
- **Leverage Limits**: 1-200x leverage bounds enforcement

### Position Size Validation
- **Minimum Order Size**: Symbol-specific minimum order requirements
- **Balance Safety Cap**: Maximum 15% of balance per trade
- **Precision Handling**: Symbol-specific quantity precision rounding
- **Market Impact**: Position sizing to avoid excessive market impact

### Account State Monitoring
- **Real-time Balance**: Free balance, equity, margin usage tracking
- **Open Positions**: Current positions with unrealized PnL
- **Daily Statistics**: Realized PnL, max drawdown, trade count
- **Risk Metrics**: Margin level, concurrent trades monitoring

## Usage Instructions

### 1. Enable ScalperGPT
1. Ensure LMStudio is running with a compatible model
2. Enable "Auto Trader" checkbox in the interface
3. Configure risk parameters and leverage settings
4. Monitor the ScalperGPT analysis in real-time

### 2. Monitor Performance
- **Final Trading Verdict Panel**: Shows ScalperGPT decisions in real-time
- **Historical Verdicts**: Track past decisions and their outcomes
- **Model Performance**: Monitor individual model accuracy and weights
- **Account Metrics**: Real-time balance and position tracking

### 3. Safety Controls
- **Emergency Stop**: Click to immediately halt all trading
- **Daily Limits**: Set maximum trades per day
- **Risk Controls**: Configure maximum risk percentage per trade
- **Leverage Limits**: Set maximum leverage for autonomous trades

## Configuration

### LLM Settings
```python
# ScalperGPT optimized parameters
temperature = 0.2        # Low for consistent decisions
max_tokens = 250         # Sufficient for JSON response
system_prompt = "ScalperGPT specialized in ultra-short trades..."
```

### Risk Management
```python
# Default risk parameters
max_risk_per_trade = 2.0     # 2% of account per trade
max_leverage = 200           # Maximum leverage allowed
min_balance = 20.0           # Minimum balance for trading
max_daily_trades = 50        # Maximum trades per day
```

### Model Performance Thresholds
```python
# Automatic model pruning thresholds
min_accuracy = 52.0          # Minimum accuracy percentage
min_confidence = 55.0        # Minimum average confidence
performance_window = 100     # Trades for performance calculation
```

## Performance Metrics

### Model Tracking
- **Accuracy**: Percentage of correct predictions
- **Sharpe Ratio**: Risk-adjusted return metric
- **Confidence**: Average confidence level
- **Latency**: Response time for decisions

### Trading Statistics
- **Win Rate**: Percentage of profitable trades
- **Average Return**: Mean return per trade
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Daily PnL**: Realized profit/loss per day

## Troubleshooting

### Common Issues
1. **LLM Not Responding**: Check LMStudio connection and model loading
2. **Invalid JSON**: Review LLM temperature and prompt formatting
3. **Safety Check Failures**: Verify account balance and risk parameters
4. **Model Performance**: Monitor and adjust underperforming models

### Debug Information
- **Full LLM Prompts**: Logged for analysis and debugging
- **Trade Instructions**: Complete JSON parsing and validation logs
- **Safety Check Results**: Detailed pre-execution validation logs
- **Market Data**: Real-time enriched market data logging

## Future Enhancements

### Planned Features
- **Multi-symbol Trading**: Simultaneous trading across multiple pairs
- **Advanced Risk Models**: VaR and CVaR risk calculations
- **Machine Learning Pipeline**: Automated model training and deployment
- **Performance Analytics**: Advanced performance attribution analysis

### Research Areas
- **Reinforcement Learning**: RL-based position sizing optimization
- **Sentiment Analysis**: Social media and news sentiment integration
- **Market Microstructure**: Advanced order flow analysis
- **Cross-asset Correlation**: Multi-asset portfolio optimization

---

*ScalperGPT represents the cutting edge of AI-driven trading technology, combining the power of large language models with sophisticated market analysis and risk management.*
