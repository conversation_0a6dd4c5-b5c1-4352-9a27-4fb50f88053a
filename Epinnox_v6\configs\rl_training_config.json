{"total_timesteps": 100000, "model_type": "PPO", "save_path": "models/rl_trading_agent", "validation_episodes": 100, "environment": {"initial_balance": 10000.0, "max_steps": 1000, "transaction_cost": 0.001, "slippage": 0.0005}, "agent_config": {"learning_rate": 0.0003, "n_steps": 2048, "batch_size": 64, "n_epochs": 10, "gamma": 0.99, "gae_lambda": 0.95, "clip_range": 0.2, "ent_coef": 0.01}, "training_schedule": {"checkpoint_frequency": 10000, "evaluation_frequency": 5000, "early_stopping": {"enabled": true, "patience": 20000, "min_improvement": 0.01}}, "data_config": {"symbols": ["BTC/USDT:USDT", "ETH/USDT:USDT", "DOGE/USDT:USDT"], "timeframes": ["1m", "5m"], "lookback_periods": 100, "features": ["price", "volume", "volatility", "rsi", "macd", "bollinger_bands", "orderflow", "sentiment"]}, "reward_config": {"profit_weight": 1.0, "risk_penalty": 0.5, "transaction_penalty": 0.1, "holding_penalty": 0.01, "drawdown_penalty": 2.0}}