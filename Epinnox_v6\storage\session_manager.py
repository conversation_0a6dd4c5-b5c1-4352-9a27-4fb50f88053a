"""
Session Manager for Epinnox v6
Manages trading sessions with automatic persistence and replay functionality
"""

import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from PyQt5.QtCore import QObject, QTimer, pyqtSignal
import logging

from .database_manager import DatabaseManager

logger = logging.getLogger(__name__)


class SessionManager(QObject):
    """
    Manages trading sessions with automatic persistence and replay functionality
    """
    
    # Signals
    session_started = pyqtSignal(str)  # session_id
    session_ended = pyqtSignal(str, dict)  # session_id, summary
    trade_recorded = pyqtSignal(dict)  # trade_data
    signal_recorded = pyqtSignal(dict)  # signal_data
    performance_updated = pyqtSignal(dict)  # metrics
    
    def __init__(self, db_manager: DatabaseManager = None):
        super().__init__()
        
        self.db_manager = db_manager or DatabaseManager()
        self.current_session_id = None
        self.session_start_time = None
        self.auto_save_enabled = True
        self.session_config = {}
        
        # Performance tracking
        self.session_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'peak_balance': 0.0
        }
        
        # Auto-save timer
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save_performance)
        self.auto_save_timer.start(30000)  # Auto-save every 30 seconds
        
        logger.info("Session Manager initialized")
    
    def start_session(self, mode: str, symbol: str, initial_balance: float, 
                     configuration: Dict = None) -> str:
        """Start a new trading session"""
        try:
            # End current session if active
            if self.current_session_id:
                self.end_current_session()
            
            # Generate unique session ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            self.current_session_id = f"{mode}_{symbol.replace('/', '').replace(':', '')}_{timestamp}_{unique_id}"
            self.session_start_time = datetime.now()
            self.session_config = configuration or {}
            
            # Reset metrics
            self.session_metrics = {
                'total_trades': 0,
                'winning_trades': 0,
                'total_pnl': 0.0,
                'max_drawdown': 0.0,
                'peak_balance': initial_balance
            }
            
            # Create session in database
            success = self.db_manager.create_session(
                self.current_session_id,
                mode,
                symbol,
                initial_balance,
                configuration
            )
            
            if success:
                logger.info(f"Started session: {self.current_session_id}")
                self.session_started.emit(self.current_session_id)
                return self.current_session_id
            else:
                self.current_session_id = None
                return None
                
        except Exception as e:
            logger.error(f"Error starting session: {e}")
            return None
    
    def end_current_session(self) -> bool:
        """End the current trading session"""
        try:
            if not self.current_session_id:
                logger.warning("No active session to end")
                return False
            
            # Calculate final balance (this would come from trading interface)
            final_balance = self.session_metrics['peak_balance'] + self.session_metrics['total_pnl']
            
            success = self.db_manager.end_session(
                self.current_session_id,
                final_balance,
                self.session_metrics['total_trades'],
                self.session_metrics['winning_trades'],
                self.session_metrics['total_pnl'],
                self.session_metrics['max_drawdown']
            )
            
            if success:
                # Get session summary
                summary = self.db_manager.get_session_summary(self.current_session_id)
                
                logger.info(f"Ended session: {self.current_session_id}")
                self.session_ended.emit(self.current_session_id, summary)
                
                # Reset state
                session_id = self.current_session_id
                self.current_session_id = None
                self.session_start_time = None
                self.session_config = {}
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error ending session: {e}")
            return False
    
    def record_trade(self, trade_data: Dict) -> bool:
        """Record a trade in the current session"""
        try:
            if not self.current_session_id:
                logger.warning("No active session for trade recording")
                return False
            
            # Add session context
            trade_data['session_id'] = self.current_session_id
            
            # Update session metrics
            self.session_metrics['total_trades'] += 1
            pnl = trade_data.get('pnl', 0)
            self.session_metrics['total_pnl'] += pnl
            
            if pnl > 0:
                self.session_metrics['winning_trades'] += 1
            
            # Record in database
            success = self.db_manager.record_trade(self.current_session_id, trade_data)
            
            if success:
                self.trade_recorded.emit(trade_data)
                logger.debug(f"Recorded trade: {trade_data.get('trade_id', 'N/A')}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error recording trade: {e}")
            return False
    
    def record_signal(self, signal_data: Dict) -> bool:
        """Record a signal in the current session"""
        try:
            if not self.current_session_id:
                logger.warning("No active session for signal recording")
                return False
            
            # Add session context and timestamp
            signal_data['session_id'] = self.current_session_id
            if 'timestamp' not in signal_data:
                signal_data['timestamp'] = datetime.now()
            
            # Record in database
            success = self.db_manager.record_signal(self.current_session_id, signal_data)
            
            if success:
                self.signal_recorded.emit(signal_data)
                logger.debug(f"Recorded signal: {signal_data.get('source', 'N/A')} -> {signal_data.get('decision', 'N/A')}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error recording signal: {e}")
            return False
    
    def record_performance_snapshot(self, metrics: Dict) -> bool:
        """Record a performance snapshot in the current session"""
        try:
            if not self.current_session_id:
                return False
            
            # Add session context and timestamp
            metrics['session_id'] = self.current_session_id
            if 'timestamp' not in metrics:
                metrics['timestamp'] = datetime.now()
            
            # Update session metrics
            current_balance = metrics.get('balance', 0)
            if current_balance > self.session_metrics['peak_balance']:
                self.session_metrics['peak_balance'] = current_balance
            
            # Calculate drawdown
            drawdown = (self.session_metrics['peak_balance'] - current_balance) / self.session_metrics['peak_balance']
            if drawdown > self.session_metrics['max_drawdown']:
                self.session_metrics['max_drawdown'] = drawdown
            
            # Record in database
            success = self.db_manager.record_performance_snapshot(self.current_session_id, metrics)
            
            if success:
                self.performance_updated.emit(metrics)
            
            return success
            
        except Exception as e:
            logger.error(f"Error recording performance snapshot: {e}")
            return False
    
    def auto_save_performance(self):
        """Auto-save performance snapshot (called by timer)"""
        try:
            if not self.current_session_id:
                return
            
            # This would typically get real metrics from trading interface
            # For now, create a basic snapshot
            metrics = {
                'balance': self.session_metrics['peak_balance'] + self.session_metrics['total_pnl'],
                'equity': self.session_metrics['peak_balance'] + self.session_metrics['total_pnl'],
                'unrealized_pnl': 0,  # Would come from trading interface
                'realized_pnl': self.session_metrics['total_pnl'],
                'drawdown': self.session_metrics['max_drawdown'],
                'open_positions': 0,  # Would come from trading interface
                'daily_trades': self.session_metrics['total_trades'],
                'win_rate': (self.session_metrics['winning_trades'] / max(1, self.session_metrics['total_trades'])) * 100
            }
            
            self.record_performance_snapshot(metrics)
            
        except Exception as e:
            logger.error(f"Error in auto-save performance: {e}")
    
    def get_current_session_id(self) -> Optional[str]:
        """Get the current session ID"""
        return self.current_session_id
    
    def get_session_duration(self) -> Optional[timedelta]:
        """Get the duration of the current session"""
        if self.session_start_time:
            return datetime.now() - self.session_start_time
        return None
    
    def get_session_metrics(self) -> Dict:
        """Get current session metrics"""
        metrics = self.session_metrics.copy()
        if self.session_start_time:
            metrics['duration'] = self.get_session_duration()
            metrics['win_rate'] = (metrics['winning_trades'] / max(1, metrics['total_trades'])) * 100
        return metrics
    
    def export_current_session(self, export_path: str) -> bool:
        """Export the current session data"""
        if not self.current_session_id:
            logger.warning("No active session to export")
            return False
        
        return self.db_manager.export_session_data(self.current_session_id, export_path)
    
    def get_recent_sessions(self, limit: int = 10) -> List[Dict]:
        """Get recent trading sessions"""
        return self.db_manager.get_sessions(limit)
    
    def get_session_summary(self, session_id: str = None) -> Dict:
        """Get session summary"""
        target_session = session_id or self.current_session_id
        if not target_session:
            return {}
        
        return self.db_manager.get_session_summary(target_session)
    
    def replay_session(self, session_id: str) -> Dict:
        """Get session data for replay"""
        try:
            summary = self.db_manager.get_session_summary(session_id)
            trades = self.db_manager.get_trades(session_id)
            signals = self.db_manager.get_signals(session_id)
            performance = self.db_manager.get_performance_history(session_id)
            
            replay_data = {
                'session_summary': summary,
                'trades': trades,
                'signals': signals,
                'performance_history': performance,
                'replay_timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"Prepared replay data for session: {session_id}")
            return replay_data
            
        except Exception as e:
            logger.error(f"Error preparing replay data: {e}")
            return {}
    
    def set_auto_save_enabled(self, enabled: bool):
        """Enable or disable auto-save"""
        self.auto_save_enabled = enabled
        if enabled:
            self.auto_save_timer.start(30000)
        else:
            self.auto_save_timer.stop()
        
        logger.info(f"Auto-save {'enabled' if enabled else 'disabled'}")
    
    def cleanup_old_sessions(self, days_to_keep: int = 30):
        """Clean up old session data"""
        try:
            self.db_manager.cleanup_old_data(days_to_keep)
            logger.info(f"Cleaned up sessions older than {days_to_keep} days")
        except Exception as e:
            logger.error(f"Error cleaning up old sessions: {e}")


class TradeRecorder(QObject):
    """
    Specialized recorder for trade data with real-time updates
    """
    
    trade_recorded = pyqtSignal(dict)
    
    def __init__(self, session_manager: SessionManager):
        super().__init__()
        self.session_manager = session_manager
        self.pending_trades = []
        
        # Connect to session manager
        self.session_manager.trade_recorded.connect(self.on_trade_recorded)
    
    def record_trade_entry(self, trade_data: Dict) -> str:
        """Record trade entry"""
        try:
            trade_id = f"trade_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.pending_trades)}"
            
            entry_data = {
                'trade_id': trade_id,
                'symbol': trade_data.get('symbol', ''),
                'side': trade_data.get('side', ''),
                'type': trade_data.get('type', 'market'),
                'entry_time': datetime.now(),
                'entry_price': trade_data.get('entry_price', 0),
                'quantity': trade_data.get('quantity', 0),
                'leverage': trade_data.get('leverage', 1),
                'signal_source': trade_data.get('signal_source', ''),
                'signal_confidence': trade_data.get('signal_confidence', 0),
                'signal_reasoning': trade_data.get('signal_reasoning', ''),
                'metadata': trade_data.get('metadata', {})
            }
            
            # Store as pending trade
            self.pending_trades.append(entry_data)
            
            logger.debug(f"Recorded trade entry: {trade_id}")
            return trade_id
            
        except Exception as e:
            logger.error(f"Error recording trade entry: {e}")
            return ""
    
    def record_trade_exit(self, trade_id: str, exit_data: Dict) -> bool:
        """Record trade exit and complete the trade"""
        try:
            # Find pending trade
            pending_trade = None
            for trade in self.pending_trades:
                if trade['trade_id'] == trade_id:
                    pending_trade = trade
                    break
            
            if not pending_trade:
                logger.warning(f"No pending trade found for ID: {trade_id}")
                return False
            
            # Complete trade data
            exit_time = datetime.now()
            entry_time = pending_trade['entry_time']
            duration = (exit_time - entry_time).total_seconds() / 60
            
            complete_trade = pending_trade.copy()
            complete_trade.update({
                'exit_time': exit_time,
                'exit_price': exit_data.get('exit_price', 0),
                'pnl': exit_data.get('pnl', 0),
                'pnl_percent': exit_data.get('pnl_percent', 0),
                'fees': exit_data.get('fees', 0),
                'duration_minutes': int(duration)
            })
            
            # Record complete trade
            success = self.session_manager.record_trade(complete_trade)
            
            if success:
                # Remove from pending
                self.pending_trades.remove(pending_trade)
                logger.debug(f"Completed trade: {trade_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error recording trade exit: {e}")
            return False
    
    def on_trade_recorded(self, trade_data: Dict):
        """Handle trade recorded signal"""
        self.trade_recorded.emit(trade_data)
    
    def get_pending_trades(self) -> List[Dict]:
        """Get list of pending trades"""
        return self.pending_trades.copy()
    
    def cancel_pending_trade(self, trade_id: str) -> bool:
        """Cancel a pending trade"""
        try:
            for trade in self.pending_trades:
                if trade['trade_id'] == trade_id:
                    self.pending_trades.remove(trade)
                    logger.debug(f"Cancelled pending trade: {trade_id}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error cancelling pending trade: {e}")
            return False
