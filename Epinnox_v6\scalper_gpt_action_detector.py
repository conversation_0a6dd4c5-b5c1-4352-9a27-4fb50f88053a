
def detect_and_fix_action_field(response_data):
    """Detect and fix ACTION field in ScalperGPT response"""
    if not isinstance(response_data, dict):
        return response_data
    
    # Look for action in various forms
    action_value = None
    action_fields = ['action', 'ACTION', 'Action', 'decision', 'DECISION', 'Decision']
    
    for field in action_fields:
        if field in response_data:
            action_value = response_data[field]
            break
    
    # If no action found, try to infer from other fields
    if action_value is None:
        # Check for buy/sell indicators
        for key, value in response_data.items():
            if isinstance(value, str):
                value_upper = value.upper()
                if value_upper in ['BUY', 'LONG', 'BULL']:
                    action_value = 'BUY'
                    break
                elif value_upper in ['SELL', 'SHORT', 'BEAR']:
                    action_value = 'SELL'
                    break
                elif value_upper in ['WAIT', 'HOLD', 'NONE']:
                    action_value = 'WAIT'
                    break
    
    # Normalize action value
    if action_value:
        action_value = str(action_value).upper()
        
        # Map variations to standard actions
        action_mappings = {
            'BUY': 'BUY',
            'LONG': 'BUY',
            'BULL': 'BUY',
            'SELL': 'SELL',
            'SHORT': 'SELL',
            'BEAR': 'SELL',
            'WAIT': 'WAIT',
            'HOLD': 'WAIT',
            'NONE': 'WAIT'
        }
        
        action_value = action_mappings.get(action_value, 'WAIT')
    else:
        action_value = 'WAIT'
    
    # Set both action and ACTION fields
    response_data['action'] = action_value
    response_data['ACTION'] = action_value
    
    return response_data
