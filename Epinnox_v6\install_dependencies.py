#!/usr/bin/env python3
"""
EPINNOX v6 Dependency Installation Script
Automatically installs all required and optional dependencies
"""

import subprocess
import sys
import os
import platform
import importlib
from typing import List, Dict, Tuple

def run_command(command: str, description: str = "") -> Tuple[bool, str]:
    """Run a shell command and return success status and output"""
    try:
        print(f"🔄 {description or command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ Success: {description or command}")
            return True, result.stdout
        else:
            print(f"❌ Failed: {description or command}")
            print(f"Error: {result.stderr}")
            return False, result.stderr
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout: {description or command}")
        return False, "Command timed out"
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False, str(e)

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} is not supported. Please use Python 3.8+")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def detect_system():
    """Detect operating system and architecture"""
    system = platform.system().lower()
    arch = platform.machine().lower()
    
    print(f"🖥️ Detected system: {system} ({arch})")
    
    return system, arch

def check_gpu_support():
    """Check for GPU support"""
    try:
        # Try to detect NVIDIA GPU
        result = subprocess.run("nvidia-smi", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("🎮 NVIDIA GPU detected")
            return "nvidia"
    except:
        pass
    
    try:
        # Try to detect AMD GPU (basic check)
        if platform.system().lower() == "linux":
            result = subprocess.run("lspci | grep -i amd", shell=True, capture_output=True, text=True)
            if "VGA" in result.stdout or "Display" in result.stdout:
                print("🎮 AMD GPU detected")
                return "amd"
    except:
        pass
    
    print("💻 No GPU detected or GPU drivers not available")
    return "cpu"

def install_core_dependencies():
    """Install core Python dependencies"""
    print("\n📦 Installing Core Dependencies...")
    
    # Upgrade pip first
    success, _ = run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip")
    if not success:
        print("⚠️ Failed to upgrade pip, continuing anyway...")
    
    # Install core requirements
    core_packages = [
        "pandas>=1.5.0",
        "numpy>=1.21.0", 
        "scipy>=1.9.0",
        "python-dateutil>=2.8.0",
        "pytz>=2022.1",
        "requests>=2.28.0",
        "aiohttp>=3.8.0",
        "websockets>=10.0",
        "scikit-learn>=1.1.0"
    ]
    
    for package in core_packages:
        success, _ = run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}")
        if not success:
            print(f"⚠️ Failed to install {package}, continuing...")

def install_ml_dependencies(gpu_type: str = "cpu"):
    """Install ML and RL dependencies"""
    print("\n🧠 Installing ML/RL Dependencies...")
    
    # Install PyTorch based on GPU support
    if gpu_type == "nvidia":
        # CUDA 11.8 version
        torch_command = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
        success, _ = run_command(torch_command, "Installing PyTorch with CUDA support")
    else:
        # CPU version
        torch_command = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
        success, _ = run_command(torch_command, "Installing PyTorch (CPU version)")
    
    if not success:
        print("⚠️ PyTorch installation failed, trying basic version...")
        run_command(f"{sys.executable} -m pip install torch", "Installing basic PyTorch")
    
    # Install RL dependencies
    rl_packages = [
        "stable-baselines3[extra]",
        "gymnasium",
        "gym"
    ]
    
    for package in rl_packages:
        success, _ = run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}")
        if not success:
            print(f"⚠️ Failed to install {package}")
    
    # Install TensorFlow (optional)
    print("🔄 Installing TensorFlow...")
    tf_success, _ = run_command(f"{sys.executable} -m pip install tensorflow", "Installing TensorFlow")
    if not tf_success:
        print("⚠️ TensorFlow installation failed - LSTM models will be disabled")

def install_ta_lib(system: str):
    """Install TA-Lib with system-specific instructions"""
    print("\n📈 Installing TA-Lib...")
    
    if system == "windows":
        print("🪟 Windows detected - trying pip installation...")
        success, _ = run_command(f"{sys.executable} -m pip install TA-Lib", "Installing TA-Lib")
        
        if not success:
            print("❌ TA-Lib pip installation failed")
            print("📋 Manual installation required:")
            print("   1. Download TA-Lib from: https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib")
            print("   2. Install with: pip install downloaded_file.whl")
            print("   3. Or use conda: conda install -c conda-forge ta-lib")
    
    elif system == "darwin":  # macOS
        print("🍎 macOS detected - installing via Homebrew...")
        
        # Try to install via homebrew first
        brew_success, _ = run_command("brew install ta-lib", "Installing TA-Lib via Homebrew")
        
        if brew_success:
            success, _ = run_command(f"{sys.executable} -m pip install TA-Lib", "Installing TA-Lib Python wrapper")
        else:
            print("❌ Homebrew installation failed")
            print("📋 Manual installation:")
            print("   1. Install Homebrew: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
            print("   2. Run: brew install ta-lib")
            print("   3. Run: pip install TA-Lib")
    
    elif system == "linux":
        print("🐧 Linux detected - installing system library...")
        
        # Try different package managers
        managers = [
            ("apt-get update && apt-get install -y libta-lib-dev", "Installing via apt-get"),
            ("yum install -y ta-lib-devel", "Installing via yum"),
            ("dnf install -y ta-lib-devel", "Installing via dnf")
        ]
        
        lib_installed = False
        for command, description in managers:
            success, _ = run_command(f"sudo {command}", description)
            if success:
                lib_installed = True
                break
        
        if lib_installed:
            success, _ = run_command(f"{sys.executable} -m pip install TA-Lib", "Installing TA-Lib Python wrapper")
        else:
            print("❌ System library installation failed")
            print("📋 Manual installation:")
            print("   Ubuntu/Debian: sudo apt-get install libta-lib-dev")
            print("   CentOS/RHEL: sudo yum install ta-lib-devel")
            print("   Then: pip install TA-Lib")

def install_gui_dependencies():
    """Install GUI and visualization dependencies"""
    print("\n🖼️ Installing GUI Dependencies...")
    
    gui_packages = [
        "PyQt5",
        "pyqtgraph", 
        "streamlit",
        "plotly",
        "matplotlib",
        "seaborn"
    ]
    
    for package in gui_packages:
        success, _ = run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}")
        if not success:
            print(f"⚠️ Failed to install {package}")

def install_testing_dependencies():
    """Install testing framework dependencies"""
    print("\n🧪 Installing Testing Dependencies...")
    
    test_packages = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0", 
        "pytest-mock>=3.10.0",
        "pytest-cov>=4.0.0",
        "pytest-timeout>=2.1.0"
    ]
    
    for package in test_packages:
        success, _ = run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}")

def install_optional_dependencies():
    """Install optional performance and development dependencies"""
    print("\n⚡ Installing Optional Dependencies...")
    
    optional_packages = [
        "orjson",  # Faster JSON
        "numba",   # JIT compilation
        "loguru",  # Better logging
        "psutil",  # System monitoring
        "python-dotenv",  # Environment variables
        "black",   # Code formatting
        "isort"    # Import sorting
    ]
    
    for package in optional_packages:
        success, _ = run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}")
        if not success:
            print(f"⚠️ Optional package {package} failed to install")

def verify_installation():
    """Verify that key packages are installed correctly"""
    print("\n🔍 Verifying Installation...")
    
    critical_packages = {
        "pandas": "Data manipulation",
        "numpy": "Numerical computing", 
        "torch": "PyTorch ML framework",
        "stable_baselines3": "Reinforcement learning",
        "gymnasium": "RL environments"
    }
    
    optional_packages = {
        "talib": "Technical analysis",
        "PyQt5": "GUI framework",
        "streamlit": "Web dashboard",
        "tensorflow": "Deep learning"
    }
    
    print("📋 Critical Packages:")
    for package, description in critical_packages.items():
        try:
            importlib.import_module(package)
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"❌ {package} - {description} (MISSING)")
    
    print("\n📋 Optional Packages:")
    for package, description in optional_packages.items():
        try:
            importlib.import_module(package)
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"⚠️ {package} - {description} (optional, not installed)")

def create_missing_modules():
    """Create any missing module files"""
    print("\n🔧 Creating Missing Module Files...")
    
    # Create missing __init__.py files
    missing_inits = [
        "ui/__init__.py",
        "ui/dialogs/__init__.py", 
        "autotune/__init__.py"
    ]
    
    for init_file in missing_inits:
        if not os.path.exists(init_file):
            os.makedirs(os.path.dirname(init_file), exist_ok=True)
            with open(init_file, 'w') as f:
                f.write("# Auto-generated __init__.py\n")
            print(f"✅ Created {init_file}")

def main():
    """Main installation process"""
    print("🚀 EPINNOX v6 Dependency Installation")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Detect system
    system, arch = detect_system()
    
    # Check GPU support
    gpu_type = check_gpu_support()
    
    # Install dependencies in order
    try:
        install_core_dependencies()
        install_ml_dependencies(gpu_type)
        install_ta_lib(system)
        install_gui_dependencies()
        install_testing_dependencies()
        install_optional_dependencies()
        create_missing_modules()
        
        print("\n🎉 Installation Complete!")
        print("=" * 50)
        
        # Verify installation
        verify_installation()
        
        print("\n📋 Next Steps:")
        print("1. Run tests: python run_tests.py")
        print("2. Start dashboard: python start_dashboard.py")
        print("3. Run paper trading: python start_paper_trading.py --duration 60")
        print("4. Check main.py: python main.py --help")
        
    except KeyboardInterrupt:
        print("\n🛑 Installation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Installation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
