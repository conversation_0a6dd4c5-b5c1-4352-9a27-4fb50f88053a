#!/usr/bin/env python3
"""
Secure API Credentials Setup for Live Trading
"""

import os
import sys
import getpass
from pathlib import Path

def setup_credentials():
    """Setup API credentials securely"""
    print("🔐 HTX Exchange API Credentials Setup")
    print("="*50)
    print()
    print("⚠️  IMPORTANT SECURITY NOTES:")
    print("• Never share your API credentials")
    print("• Use API keys with minimal required permissions")
    print("• Enable IP whitelisting if possible")
    print("• Consider using read-only keys for monitoring")
    print()
    
    # Get API credentials
    print("Please enter your HTX exchange API credentials:")
    print("(You can find these in your HTX account settings)")
    print()
    
    api_key = getpass.getpass("HTX API Key: ").strip()
    if not api_key:
        print("❌ API Key is required")
        return False
    
    secret_key = getpass.getpass("HTX Secret Key: ").strip()
    if not secret_key:
        print("❌ Secret Key is required")
        return False
    
    passphrase = getpass.getpass("HTX Passphrase (optional, press Enter to skip): ").strip()
    
    # Validate credentials format
    if len(api_key) < 10:
        print("❌ API Key seems too short")
        return False
    
    if len(secret_key) < 10:
        print("❌ Secret Key seems too short")
        return False
    
    # Create environment file
    env_file = Path(".env")
    
    print(f"\n💾 Saving credentials to {env_file}...")
    
    with open(env_file, "w") as f:
        f.write(f"HTX_API_KEY={api_key}\n")
        f.write(f"HTX_SECRET_KEY={secret_key}\n")
        if passphrase:
            f.write(f"HTX_PASSPHRASE={passphrase}\n")
        f.write("\n# Live Trading Environment Variables\n")
        f.write("# Keep this file secure and never commit to version control\n")
    
    # Set permissions (Unix-like systems)
    try:
        os.chmod(env_file, 0o600)  # Read/write for owner only
    except:
        pass  # Windows doesn't support chmod
    
    print("✅ Credentials saved securely")
    print()
    print("🔧 Setting environment variables for current session...")
    
    # Set environment variables for current session
    os.environ['HTX_API_KEY'] = api_key
    os.environ['HTX_SECRET_KEY'] = secret_key
    if passphrase:
        os.environ['HTX_PASSPHRASE'] = passphrase
    
    print("✅ Environment variables set")
    print()
    print("📋 Next steps:")
    print("1. Run pre-deployment validation")
    print("2. Start live trading if validation passes")
    print()
    print("⚠️  Remember to:")
    print("• Monitor the system closely during initial operation")
    print("• Have emergency stop procedures ready")
    print("• Start with small position sizes")
    
    return True

def load_credentials():
    """Load credentials from .env file"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ No .env file found. Please run setup first.")
        return False
    
    print("📂 Loading credentials from .env file...")
    
    try:
        with open(env_file, "r") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    os.environ[key] = value
        
        # Verify required variables are set
        required_vars = ['HTX_API_KEY', 'HTX_SECRET_KEY']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            print(f"❌ Missing required variables: {missing_vars}")
            return False
        
        print("✅ Credentials loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error loading credentials: {e}")
        return False

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "load":
        # Load existing credentials
        success = load_credentials()
    else:
        # Setup new credentials
        success = setup_credentials()
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
