#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
EMA Bands Indicator
------------------
Indicator module for EMA with ATR-based bands.
"""

import pandas as pd
import numpy as np
import logging
from strategies.signal_generator import SignalGenerator

logger = logging.getLogger('strategy.indicators.ema_bands')

class EMABandsIndicator(SignalGenerator):
    """
    EMA Bands Indicator implementation.
    
    This indicator uses EMA with ATR-based bands to identify potential entry and exit points.
    """
    
    def __init__(self, config=None):
        """
        Initialize the EMA Bands Indicator.
        
        Args:
            config (dict): Configuration dictionary.
        """
        super().__init__(config, name="EMA Bands")
        self._load_parameters()
        
    def _load_parameters(self):
        """Load indicator parameters from config."""
        # Get indicator-specific parameters from config
        indicator_config = self.config.get('strategies', {}).get('atr_ema_bands', {})
        
        # ATR period
        self.atr_period = indicator_config.get('atr_period', 12)
        
        # EMA period
        self.ema_period = indicator_config.get('ema_period', 16)
        
        # Band multipliers
        self.band_multipliers = indicator_config.get('band_multipliers', [1, 2, 3])
        
        # Weight for this indicator
        self.weight = indicator_config.get('ema_bands_weight', 1.0)
        
    def calculate_ema_bands(self, df):
        """
        Calculate EMA and ATR-based bands.
        Uses multipliers from configuration.
        
        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            
        Returns:
            dict: Dictionary containing EMA and bands.
        """
        # Calculate EMA
        ema = df['close'].ewm(span=self.ema_period, adjust=False).mean()
        
        # Calculate ATR using RMA (as in TradingView)
        atr = self.calculate_atr(df, self.atr_period)
        
        # Calculate bands
        bands = {
            'ema': ema,
            'atr': atr
        }
        
        # Upper bands
        for mult in self.band_multipliers:
            bands[f'ema_plus_{mult}_atr'] = ema + (atr * mult)
        
        # Lower bands
        for mult in self.band_multipliers:
            bands[f'ema_minus_{mult}_atr'] = ema - (atr * mult)
        
        # Always include bands for 2 and 3 multipliers for backward compatibility
        if 2 not in self.band_multipliers:
            bands['ema_plus_2_atr'] = ema + (atr * 2)
            bands['ema_minus_2_atr'] = ema - (atr * 2)
        
        if 3 not in self.band_multipliers:
            bands['ema_plus_3_atr'] = ema + (atr * 3)
            bands['ema_minus_3_atr'] = ema - (atr * 3)
        
        return bands
    
    def generate_signal(self, df, **kwargs):
        """
        Generate trading signals based on EMA bands.
        
        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            **kwargs: Additional arguments.
            
        Returns:
            dict: Signal dictionary with score, direction, and bands.
        """
        try:
            if len(df) < max(self.atr_period, self.ema_period) + 10:
                return {'score': 0.5, 'direction': 'neutral'}
            
            # Calculate bands
            bands = self.calculate_ema_bands(df)
            
            # Get the latest values
            close = df['close'].iloc[-1]
            ema = bands['ema'].iloc[-1]
            
            # Upper bands - only using 2 and 3
            ema_plus_2_atr = bands['ema_plus_2_atr'].iloc[-1]
            ema_plus_3_atr = bands['ema_plus_3_atr'].iloc[-1]
            
            # Lower bands - only using 2 and 3
            ema_minus_2_atr = bands['ema_minus_2_atr'].iloc[-1]
            ema_minus_3_atr = bands['ema_minus_3_atr'].iloc[-1]
            
            # Initialize signal score (0.5 is neutral)
            signal_score = 0.5
            
            # Check price position relative to bands
            if close > ema_plus_3_atr:
                # Price is above the highest band - potential overbought
                signal_score = 0.3  # Bearish signal
            elif close > ema_plus_2_atr:
                signal_score = 0.4  # Slightly bearish
            elif close > ema:
                # Price is between EMA and EMA+2ATR - neutral to slightly bullish
                signal_score = 0.55  # Neutral to slightly bullish
            elif close > ema_minus_2_atr:
                # Price is between EMA-2ATR and EMA - neutral to slightly bearish
                signal_score = 0.45  # Neutral to slightly bearish
            elif close > ema_minus_3_atr:
                # Price is between EMA-3ATR and EMA-2ATR - slightly more bearish
                signal_score = 0.35  # More bearish
            else:
                # Price is below the lowest band - potential oversold
                signal_score = 0.7  # Bullish
            
            # Check for band crossovers (last few candles)
            lookback = min(3, len(df) - 1)
            
            # Bullish crossovers
            if (df['close'].iloc[-lookback-1] < bands['ema_minus_3_atr'].iloc[-lookback-1] and
                df['close'].iloc[-1] > bands['ema_minus_3_atr'].iloc[-1]):
                # Crossed above the lowest band
                signal_score += 0.15
            
            if (df['close'].iloc[-lookback-1] < bands['ema'].iloc[-lookback-1] and
                df['close'].iloc[-1] > bands['ema'].iloc[-1]):
                # Crossed above the EMA
                signal_score += 0.2
            
            # Bearish crossovers
            if (df['close'].iloc[-lookback-1] > bands['ema_plus_3_atr'].iloc[-lookback-1] and
                df['close'].iloc[-1] < bands['ema_plus_3_atr'].iloc[-1]):
                # Crossed below the highest band
                signal_score -= 0.15
            
            if (df['close'].iloc[-lookback-1] > bands['ema'].iloc[-lookback-1] and
                df['close'].iloc[-1] < bands['ema'].iloc[-1]):
                # Crossed below the EMA
                signal_score -= 0.2
            
            # Ensure score is between 0 and 1
            signal_score = self.normalize_score(signal_score)
            
            # Determine direction based on score
            direction = 'neutral'
            if signal_score >= 0.6:
                direction = 'buy'
            elif signal_score <= 0.4:
                direction = 'sell'
            
            return {
                'score': signal_score,
                'direction': direction,
                'bands': bands,
                'weight': self.weight
            }
            
        except Exception as e:
            logger.error(f"Error generating EMA Bands signal: {e}", exc_info=True)
            return {'score': 0.5, 'direction': 'neutral', 'weight': self.weight}
