
def validate_scalper_gpt_fields(response_data):
    """Validate and fix ScalperGPT response fields"""
    if not isinstance(response_data, dict):
        return {'action': 'WAIT', 'ACTION': 'WAIT', 'quantity': 0.001, 'QUANTITY': 0.001, 
                'leverage': 1, 'LEVERAGE': 1, 'risk_pct': 1.0, 'RISK_PCT': 1.0, 
                'order_type': 'MARKET', 'ORDER_TYPE': 'MARKET'}
    
    # Required fields with defaults
    required_fields = {
        'action': 'WAIT',
        'ACTION': 'WAIT',
        'quantity': 0.001,
        'QUANTITY': 0.001,
        'leverage': 1,
        'LEVERAGE': 1,
        'risk_pct': 1.0,
        'RISK_PCT': 1.0,
        'order_type': 'MARKET',
        'ORDER_TYPE': 'MARKET',
        'stop_loss': 1.0,
        'take_profit': 2.0
    }
    
    # Validate and fix each field
    for field, default_value in required_fields.items():
        if field not in response_data or response_data[field] is None:
            response_data[field] = default_value
        else:
            # Type validation and conversion
            try:
                if field in ['quantity', 'QUANTITY', 'risk_pct', 'RISK_PCT', 'stop_loss', 'take_profit']:
                    response_data[field] = float(response_data[field])
                elif field in ['leverage', 'LEVERAGE']:
                    response_data[field] = int(response_data[field])
                elif field in ['action', 'ACTION', 'order_type', 'ORDER_TYPE']:
                    response_data[field] = str(response_data[field]).upper()
            except (ValueError, TypeError):
                response_data[field] = default_value
    
    # Validate ranges
    if response_data['quantity'] <= 0:
        response_data['quantity'] = response_data['QUANTITY'] = 0.001
    
    if response_data['leverage'] < 1:
        response_data['leverage'] = response_data['LEVERAGE'] = 1
    elif response_data['leverage'] > 200:
        response_data['leverage'] = response_data['LEVERAGE'] = 200
    
    if response_data['risk_pct'] < 0.1:
        response_data['risk_pct'] = response_data['RISK_PCT'] = 0.1
    elif response_data['risk_pct'] > 10.0:
        response_data['risk_pct'] = response_data['RISK_PCT'] = 10.0
    
    # Validate action values
    valid_actions = ['BUY', 'SELL', 'WAIT']
    if response_data['action'] not in valid_actions:
        response_data['action'] = response_data['ACTION'] = 'WAIT'
    
    # Validate order type
    valid_order_types = ['MARKET', 'LIMIT']
    if response_data['order_type'] not in valid_order_types:
        response_data['order_type'] = response_data['ORDER_TYPE'] = 'MARKET'
    
    return response_data
