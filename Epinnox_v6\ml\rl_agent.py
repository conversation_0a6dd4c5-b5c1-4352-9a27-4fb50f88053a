"""
Reinforcement Learning Trading Agent
Uses PPO or SAC for autonomous trading decisions
"""

import numpy as np
import pickle
import os
import logging
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

# Try to import RL libraries, fallback to mock if not available
try:
    import torch
    import torch.nn as nn
    from stable_baselines3 import PPO, SAC
    from stable_baselines3.common.vec_env import DummyVecEnv
    from stable_baselines3.common.callbacks import BaseCallback
    RL_AVAILABLE = True
except ImportError:
    logger.warning("Stable-baselines3 not available. RL agent will use mock implementation.")
    RL_AVAILABLE = False

class TradingCallback:
    """Callback for tracking training progress"""
    
    def __init__(self):
        self.training_history = []
    
    def _on_step(self) -> bool:
        # Log training progress
        if hasattr(self, 'n_calls') and self.n_calls % 1000 == 0:
            self.training_history.append({
                'step': self.n_calls,
                'reward': getattr(self, 'locals', {}).get('rewards', [0])[-1] if hasattr(self, 'locals') else 0
            })
        return True

class MockRLModel:
    """Mock RL model for when stable-baselines3 is not available"""
    
    def __init__(self, *args, **kwargs):
        self.trained = False
    
    def learn(self, total_timesteps, callback=None):
        logger.info(f"Mock training for {total_timesteps} timesteps")
        self.trained = True
        return self
    
    def predict(self, observation, deterministic=True):
        # Return random action
        action = np.array([
            np.random.randint(0, 3),  # direction
            np.random.random(),       # position_size
            np.random.uniform(1.0, 5.0)  # leverage
        ])
        return action, None
    
    def save(self, path):
        with open(f"{path}.pkl", 'wb') as f:
            pickle.dump({'trained': self.trained}, f)
    
    @classmethod
    def load(cls, path):
        try:
            with open(f"{path}.pkl", 'rb') as f:
                data = pickle.load(f)
            model = cls()
            model.trained = data.get('trained', False)
            return model
        except:
            return None

class TradingRLAgent:
    """
    Reinforcement Learning agent for trading decisions
    """
    
    def __init__(self, env, model_type='PPO'):
        self.env = env
        self.model_type = model_type
        self.model = None
        self.training_history = []
        
        # Initialize model
        if RL_AVAILABLE:
            if model_type == 'PPO':
                self.model = PPO(
                    "MlpPolicy",
                    env,
                    verbose=1,
                    learning_rate=3e-4,
                    n_steps=2048,
                    batch_size=64,
                    n_epochs=10,
                    gamma=0.99,
                    gae_lambda=0.95,
                    clip_range=0.2,
                    ent_coef=0.01,
                    device='cuda' if torch.cuda.is_available() else 'cpu'
                )
            elif model_type == 'SAC':
                self.model = SAC(
                    "MlpPolicy",
                    env,
                    verbose=1,
                    learning_rate=3e-4,
                    buffer_size=100000,
                    batch_size=256,
                    gamma=0.99,
                    tau=0.005,
                    device='cuda' if torch.cuda.is_available() else 'cpu'
                )
        else:
            # Use mock model
            self.model = MockRLModel()
            logger.info(f"Using mock {model_type} model (stable-baselines3 not available)")
    
    def train(self, total_timesteps=100000, save_path="models/rl_trading_agent"):
        """Train the RL agent"""
        logger.info(f"Training {self.model_type} agent for {total_timesteps} timesteps...")
        
        # Create callback to track training progress
        callback = TradingCallback()
        
        # Train the model
        if RL_AVAILABLE and hasattr(self.model, 'learn'):
            self.model.learn(
                total_timesteps=total_timesteps,
                callback=callback
            )
        else:
            # Mock training
            self.model.learn(total_timesteps, callback)
        
        # Save the trained model
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        self.model.save(save_path)
        
        logger.info(f"Training completed. Model saved to {save_path}")
        return callback.training_history
    
    def predict(self, observation, deterministic=True):
        """Get action from trained model"""
        if self.model is None:
            raise ValueError("Model not trained or loaded")
        
        action, _ = self.model.predict(observation, deterministic=deterministic)
        return action
    
    def load_model(self, path="models/rl_trading_agent"):
        """Load a trained model"""
        if RL_AVAILABLE:
            if os.path.exists(f"{path}.zip"):
                if self.model_type == 'PPO':
                    self.model = PPO.load(path)
                elif self.model_type == 'SAC':
                    self.model = SAC.load(path)
                logger.info(f"RL model loaded from {path}")
                return True
        else:
            # Try to load mock model
            mock_model = MockRLModel.load(path)
            if mock_model:
                self.model = mock_model
                logger.info(f"Mock model loaded from {path}")
                return True
        
        return False
    
    def backtest(self, test_env, episodes=10):
        """Backtest the trained agent"""
        if self.model is None:
            raise ValueError("Model not trained or loaded")
        
        results = []
        
        for episode in range(episodes):
            obs = test_env.reset()
            total_reward = 0
            done = False
            step_count = 0
            
            while not done:
                action = self.predict(obs, deterministic=True)
                obs, reward, done, info = test_env.step(action)
                total_reward += reward
                step_count += 1
            
            results.append({
                'episode': episode,
                'total_reward': total_reward,
                'final_balance': info['balance'],
                'steps': step_count,
                'trades': info['trade_count']
            })
        
        return results
    
    def evaluate_performance(self, test_env, episodes=5) -> Dict:
        """Evaluate agent performance"""
        results = self.backtest(test_env, episodes)
        
        if not results:
            return {}
        
        total_rewards = [r['total_reward'] for r in results]
        final_balances = [r['final_balance'] for r in results]
        
        return {
            'avg_reward': np.mean(total_rewards),
            'std_reward': np.std(total_rewards),
            'avg_final_balance': np.mean(final_balances),
            'win_rate': len([b for b in final_balances if b > 1000]) / len(final_balances),
            'best_balance': max(final_balances),
            'worst_balance': min(final_balances),
            'episodes': episodes
        }
    
    def get_model_info(self) -> Dict:
        """Get information about the current model"""
        return {
            'model_type': self.model_type,
            'rl_available': RL_AVAILABLE,
            'model_loaded': self.model is not None,
            'device': 'cuda' if RL_AVAILABLE and torch.cuda.is_available() else 'cpu'
        }
