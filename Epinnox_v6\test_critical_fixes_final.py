#!/usr/bin/env python3
"""
Final comprehensive test to verify all critical fixes are working
Tests autonomous trading stats, method signatures, timer setup, and error handling
"""

import sys
import os
import time
sys.path.append('.')

def test_autonomous_trading_stats_initialization():
    """Test that autonomous trading stats are properly initialized"""
    print("🧪 Testing autonomous trading stats initialization...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        
        # Create interface instance
        interface = EpinnoxTradingInterface()
        
        # Check that autonomous_trading_stats is initialized
        if hasattr(interface, 'autonomous_trading_stats'):
            stats = interface.autonomous_trading_stats
            
            # Check required fields
            required_fields = [
                'enabled', 'daily_trades', 'total_trades', 'successful_trades',
                'winning_trades', 'losing_trades', 'max_daily_trades',
                'emergency_stop_triggered', 'last_trade_time', 'win_rate',
                'total_pnl', 'daily_pnl', 'max_drawdown', 'max_drawdown_limit',
                'avg_trade_duration', 'trades_today', 'daily_limit_reached',
                'last_update'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in stats:
                    missing_fields.append(field)
            
            if not missing_fields:
                print("   ✅ All autonomous trading stats fields present")
                print(f"   ✅ Stats initialized: {len(stats)} fields")
                return True
            else:
                print(f"   ❌ Missing stats fields: {missing_fields}")
                return False
        else:
            print("   ❌ autonomous_trading_stats attribute not found")
            return False
        
    except Exception as e:
        print(f"   ❌ Error testing autonomous trading stats: {e}")
        return False

def test_update_system_health_display_signature():
    """Test that update_system_health_display method has correct signature"""
    print("\n🧪 Testing update_system_health_display method signature...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        import inspect
        
        # Get method signature
        method = getattr(EpinnoxTradingInterface, 'update_system_health_display')
        signature = inspect.signature(method)
        
        # Check parameters
        params = list(signature.parameters.keys())
        
        # Should have 'self' and 'overall_health' with default value
        if len(params) >= 2 and params[0] == 'self' and params[1] == 'overall_health':
            # Check if overall_health has default value
            overall_health_param = signature.parameters['overall_health']
            if overall_health_param.default is not inspect.Parameter.empty:
                print("   ✅ Method signature correct with default parameter")
                return True
            else:
                print("   ❌ overall_health parameter missing default value")
                return False
        else:
            print(f"   ❌ Incorrect method signature: {params}")
            return False
        
    except Exception as e:
        print(f"   ❌ Error testing method signature: {e}")
        return False

def test_timer_setup_thread_safety():
    """Test that timer setup includes thread safety checks"""
    print("\n🧪 Testing timer setup thread safety...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        import inspect
        
        # Get setup_timers method source
        method = getattr(EpinnoxTradingInterface, 'setup_timers')
        source = inspect.getsource(method)
        
        # Check for thread safety measures
        thread_safety_checks = [
            'self.thread()',
            'QApplication.instance().thread()',
            'QTimer.singleShot',
            'QTimer(self)'  # Parent to main window
        ]
        
        found_checks = []
        for check in thread_safety_checks:
            if check in source:
                found_checks.append(check)
        
        if len(found_checks) >= 3:  # Should have most thread safety measures
            print("   ✅ Timer setup includes thread safety measures")
            print(f"   ✅ Found checks: {found_checks}")
            return True
        else:
            print(f"   ❌ Missing thread safety measures: {found_checks}")
            return False
        
    except Exception as e:
        print(f"   ❌ Error testing timer setup: {e}")
        return False

def test_scalper_gpt_widget_methods():
    """Test that ScalperGPT widget methods are available"""
    print("\n🧪 Testing ScalperGPT widget methods...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        
        # Check for ScalperGPT methods
        scalper_methods = [
            'setup_scalper_gpt_widgets',
            'setup_scalper_gpt_layout',
            'apply_scalper_theme',
            'setup_memory_management',
            'setup_unified_update_scheduler',
            'cleanup_stale_analysis_data',
            'monitor_scalper_performance'
        ]
        
        missing_methods = []
        for method_name in scalper_methods:
            if not hasattr(EpinnoxTradingInterface, method_name):
                missing_methods.append(method_name)
        
        if not missing_methods:
            print("   ✅ All ScalperGPT methods available")
            print(f"   ✅ Methods found: {len(scalper_methods)}")
            return True
        else:
            print(f"   ❌ Missing ScalperGPT methods: {missing_methods}")
            return False
        
    except Exception as e:
        print(f"   ❌ Error testing ScalperGPT methods: {e}")
        return False

def test_enhanced_error_handling_methods():
    """Test that enhanced error handling methods are available"""
    print("\n🧪 Testing enhanced error handling methods...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        
        # Check for error handling methods
        error_handling_methods = [
            'validate_market_data_safely',
            'safe_division',
            'enhanced_data_validation',
            'robust_fetch_enriched_market_data',
            'get_emergency_fallback_data',
            'flush_database_writes'
        ]
        
        missing_methods = []
        for method_name in error_handling_methods:
            if not hasattr(EpinnoxTradingInterface, method_name):
                missing_methods.append(method_name)
        
        if not missing_methods:
            print("   ✅ All error handling methods available")
            print(f"   ✅ Methods found: {len(error_handling_methods)}")
            return True
        else:
            print(f"   ❌ Missing error handling methods: {missing_methods}")
            return False
        
    except Exception as e:
        print(f"   ❌ Error testing error handling methods: {e}")
        return False

def test_enhanced_analysis_methods():
    """Test that enhanced analysis methods are available"""
    print("\n🧪 Testing enhanced analysis methods...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        
        # Check for enhanced analysis methods
        analysis_methods = [
            'detect_volatility_clustering',
            'get_dynamic_atr_threshold',
            'get_dynamic_volume_threshold',
            'calculate_order_flow_strength',
            'calculate_orderbook_flow_strength',
            'calculate_weighted_order_book_levels'
        ]
        
        missing_methods = []
        for method_name in analysis_methods:
            if not hasattr(EpinnoxTradingInterface, method_name):
                missing_methods.append(method_name)
        
        if not missing_methods:
            print("   ✅ All enhanced analysis methods available")
            print(f"   ✅ Methods found: {len(analysis_methods)}")
            return True
        else:
            print(f"   ❌ Missing analysis methods: {missing_methods}")
            return False
        
    except Exception as e:
        print(f"   ❌ Error testing analysis methods: {e}")
        return False

def test_system_integration():
    """Test overall system integration"""
    print("\n🧪 Testing system integration...")
    
    try:
        # Test that the main interface can be imported without errors
        from launch_epinnox import EpinnoxTradingInterface
        
        # Test that critical attributes are available
        critical_attributes = [
            'autonomous_trading_stats',
            'emergency_stop_active',
            'emergency_risk_mode',
            'autonomous_trading_enabled',
            'scanner_enabled'
        ]
        
        # Create a minimal test instance
        interface = EpinnoxTradingInterface()
        
        missing_attributes = []
        for attr in critical_attributes:
            if not hasattr(interface, attr):
                missing_attributes.append(attr)
        
        if not missing_attributes:
            print("   ✅ All critical attributes available")
            print("   ✅ System integration successful")
            return True
        else:
            print(f"   ❌ Missing critical attributes: {missing_attributes}")
            return False
        
    except Exception as e:
        print(f"   ❌ Error testing system integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all critical fixes verification tests"""
    print("🚀 Running Critical Fixes Final Verification")
    print("=" * 60)
    
    test1_result = test_autonomous_trading_stats_initialization()
    test2_result = test_update_system_health_display_signature()
    test3_result = test_timer_setup_thread_safety()
    test4_result = test_scalper_gpt_widget_methods()
    test5_result = test_enhanced_error_handling_methods()
    test6_result = test_enhanced_analysis_methods()
    test7_result = test_system_integration()
    
    print("\n" + "=" * 60)
    print("📊 Final Test Results Summary:")
    print(f"   Autonomous trading stats: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Method signature fix: {'✅ PASS' if test2_result else '❌ FAIL'}")
    print(f"   Timer thread safety: {'✅ PASS' if test3_result else '❌ FAIL'}")
    print(f"   ScalperGPT widgets: {'✅ PASS' if test4_result else '❌ FAIL'}")
    print(f"   Error handling methods: {'✅ PASS' if test5_result else '❌ FAIL'}")
    print(f"   Enhanced analysis: {'✅ PASS' if test6_result else '❌ FAIL'}")
    print(f"   System integration: {'✅ PASS' if test7_result else '❌ FAIL'}")
    
    all_passed = all([test1_result, test2_result, test3_result, test4_result, 
                     test5_result, test6_result, test7_result])
    
    if all_passed:
        print("\n🎉 ALL CRITICAL FIXES VERIFIED SUCCESSFULLY!")
        print("Expected system improvements:")
        print("  • No more missing autonomous_trading_stats errors")
        print("  • Method signature mismatches resolved")
        print("  • Timer threading warnings eliminated")
        print("  • ScalperGPT widgets properly initialized")
        print("  • Enhanced error handling and data validation")
        print("  • Improved analysis engine with dynamic thresholds")
        print("  • System should achieve 100% health score")
        return True
    else:
        print("\n⚠️ Some critical fixes verification failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
