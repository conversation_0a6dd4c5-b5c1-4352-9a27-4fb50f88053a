#!/usr/bin/env python3
"""
Test script for Epinnox v6 Integrated GUI

This script demonstrates the GUI functionality and provides
usage instructions for the comprehensive trading interface.
"""

import sys
import os

def print_gui_info():
    """Print information about the Epinnox v6 GUI"""
    print("=" * 80)
    print("🚀 EPINNOX v6 INTEGRATED TRADING SYSTEM GUI")
    print("=" * 80)
    print()
    
    print("📊 FEATURES IMPLEMENTED:")
    print("✅ Matrix Theme Design (Green-on-Black Terminal Style)")
    print("✅ Real-Time Trading System Integration")
    print("✅ Dynamic Leverage Management")
    print("✅ ML Model Predictions Display")
    print("✅ Signal Hierarchy Resolution")
    print("✅ Smart Position Sizing")
    print("✅ Multi-Timeframe Analysis")
    print("✅ Market Regime Detection")
    print("✅ Risk Management Metrics")
    print("✅ Configurable Settings")
    print("✅ Auto-Refresh Capabilities")
    print()
    
    print("🎛️ GUI TABS AVAILABLE:")
    print("1. 📈 Live Trading - Real-time analysis and trading decisions")
    print("2. 🔍 Scalping Scanner - Multi-symbol opportunity detection")
    print("3. 🎯 Manual Trader - Direct order placement interface")
    print("4. 🤖 Auto Trader - Automated strategy queue management")
    print("5. 📊 Microstructure - Order flow and market depth analysis")
    print("6. 📈 Performance Dashboard - Equity curves and statistics")
    print("7. ⚙️  Settings - Dynamic configuration of all parameters")
    print()
    
    print("🎨 MATRIX THEME FEATURES:")
    print("• Courier New monospace font")
    print("• Bright green (#00FF00) text on black background")
    print("• Professional trading terminal appearance")
    print("• Color-coded decisions (Green=LONG, Red=SHORT, Yellow=WAIT)")
    print("• Responsive design with proper scaling")
    print()
    
    print("🔧 INTEGRATION CAPABILITIES:")
    print("• Real-time execution of run_trading_system() function")
    print("• Dynamic leverage fetching from HTX exchange")
    print("• ML model training and prediction display")
    print("• Signal hierarchy conflict resolution")
    print("• Smart position sizing with risk management")
    print("• Configurable timeframe weights and parameters")
    print("• Settings persistence (saves to epinnox_settings.json)")
    print()
    
    print("🚀 HOW TO USE:")
    print("1. Run: python epinnox_int.py")
    print("2. Select a trading symbol (DOGE/USDT, BTC/USDT, etc.)")
    print("3. Click 'ANALYZE SYMBOL' to run the trading system")
    print("4. View results in real-time across all panels")
    print("5. Configure settings in the Settings tab")
    print("6. Enable auto-refresh for continuous monitoring")
    print()
    
    print("📋 LIVE TRADING TAB FEATURES:")
    print("• Symbol selection with popular crypto pairs")
    print("• Live/Historical data toggle")
    print("• Auto-refresh every 30 seconds")
    print("• Real-time decision display with confidence levels")
    print("• ML model status table")
    print("• Leverage analysis (Max/Recommended/Effective)")
    print("• Signal hierarchy breakdown")
    print("• Market analysis (regime, trend, volatility)")
    print("• Risk warnings and position sizing")
    print("• Analysis log with timestamps")
    print()
    
    print("⚙️ SETTINGS TAB CONFIGURATION:")
    print("• Timeframe Weights (1m, 5m, 15m)")
    print("• Leverage Management (Base balance, Max risk, Conservative mode)")
    print("• ML Model Settings (Confidence threshold, Retrain interval)")
    print("• Signal Hierarchy Weights (ML, Technical, Multi-timeframe)")
    print("• Save/Load/Reset settings functionality")
    print("• System information display")
    print("• Configuration log")
    print()
    
    print("🎯 REAL-TIME FEATURES:")
    print("• Threading for non-blocking analysis")
    print("• Status updates and progress indication")
    print("• Error handling with user-friendly messages")
    print("• Automatic UI state management")
    print("• Connection status monitoring")
    print("• Time display with continuous updates")
    print()
    
    print("💡 TIPS FOR OPTIMAL USE:")
    print("• Start with DOGE/USDT for testing (good liquidity)")
    print("• Enable auto-refresh for continuous monitoring")
    print("• Check the Settings tab to configure parameters")
    print("• Monitor the Analysis Log for detailed information")
    print("• Watch Risk Warnings for important alerts")
    print("• Use the Signal Hierarchy table to understand decisions")
    print()
    
    print("🔧 TECHNICAL REQUIREMENTS:")
    print("• PyQt5 for GUI framework")
    print("• All Epinnox v6 modules (main.py, core/, ml/)")
    print("• Exchange connectivity (HTX/Huobi)")
    print("• Optional: TensorFlow for LSTM models")
    print("• Optional: TA-Lib for enhanced technical indicators")
    print()
    
    print("=" * 80)
    print("🎉 EPINNOX v6 GUI - PROFESSIONAL TRADING INTERFACE READY!")
    print("=" * 80)

def check_requirements():
    """Check if all requirements are met"""
    print("\n🔍 CHECKING REQUIREMENTS:")
    
    # Check PyQt5
    try:
        import PyQt5
        print("✅ PyQt5: Available")
    except ImportError:
        print("❌ PyQt5: Not installed (pip install PyQt5)")
        return False
    
    # Check Epinnox modules
    required_modules = [
        'main',
        'core.leverage_manager',
        'ml.models',
        'core.signal_hierarchy',
        'ml.position_sizing',
        'data.exchange'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}: Available")
        except ImportError:
            print(f"❌ {module}: Not found")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ Missing modules: {missing_modules}")
        print("Make sure you're running from the Epinnox_v6 directory")
        return False
    
    # Check optional modules
    optional_modules = ['tensorflow', 'talib']
    for module in optional_modules:
        try:
            __import__(module)
            print(f"✅ {module}: Available (optional)")
        except ImportError:
            print(f"⚠️ {module}: Not available (optional)")
    
    print("\n✅ All required components are available!")
    return True

def main():
    """Main function"""
    print_gui_info()
    
    if check_requirements():
        print("\n🚀 Ready to launch Epinnox v6 GUI!")
        print("Run: python epinnox_int.py")
    else:
        print("\n❌ Please install missing requirements first")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
