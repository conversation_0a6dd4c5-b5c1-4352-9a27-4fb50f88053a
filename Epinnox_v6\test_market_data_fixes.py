#!/usr/bin/env python3
"""
Test script for market data feed fixes
Tests the enhanced market data fetching without GUI dependencies
"""

import sys
import os
sys.path.append('.')

def test_direct_market_data_fetching():
    """Test direct market data fetching using CCXT"""
    print("🧪 Testing direct market data fetching...")
    
    try:
        # Test direct CCXT calls
        from me2 import fetch_order_book, fetch_ticker, fetch_ohlcv
        
        symbol = 'DOGE/USDT:USDT'
        
        # Test order book fetching
        print(f"\n📊 Testing order book for {symbol}...")
        order_book = fetch_order_book(symbol)
        
        if order_book and 'bids' in order_book and 'asks' in order_book:
            bids = order_book['bids'][:5]
            asks = order_book['asks'][:5]
            
            if bids and asks:
                best_bid = float(bids[0][0])
                best_ask = float(asks[0][0])
                spread = best_ask - best_bid
                spread_pct = (spread / best_bid) * 100
                
                print(f"✅ Order book data:")
                print(f"   Best bid: ${best_bid:.6f}")
                print(f"   Best ask: ${best_ask:.6f}")
                print(f"   Spread: ${spread:.6f} ({spread_pct:.3f}%)")
                print(f"   Bid levels: {len(bids)}")
                print(f"   Ask levels: {len(asks)}")
            else:
                print("❌ Empty order book levels")
        else:
            print("❌ No order book data available")
        
        # Test ticker fetching
        print(f"\n📈 Testing ticker for {symbol}...")
        ticker = fetch_ticker(symbol)
        
        if ticker and 'last' in ticker:
            print(f"✅ Ticker data:")
            print(f"   Last price: ${ticker['last']:.6f}")
            print(f"   High: ${ticker.get('high', 0):.6f}")
            print(f"   Low: ${ticker.get('low', 0):.6f}")
            print(f"   Volume: {ticker.get('volume', 0):,.0f}")
        else:
            print("❌ No ticker data available")
        
        # Test OHLCV fetching
        print(f"\n📊 Testing OHLCV for {symbol}...")
        candles = fetch_ohlcv(symbol, '1m', limit=10)
        
        if candles and len(candles) >= 5:
            print(f"✅ OHLCV data: {len(candles)} candles")
            
            # Calculate basic metrics
            highs = [float(candle[2]) for candle in candles[-5:]]
            lows = [float(candle[3]) for candle in candles[-5:]]
            closes = [float(candle[4]) for candle in candles[-5:]]
            volumes = [float(candle[5]) for candle in candles[-5:]]
            
            # Calculate tick ATR
            ranges = [high - low for high, low in zip(highs, lows)]
            tick_atr = sum(ranges) / len(ranges) if ranges else 0.0
            
            # Calculate flow imbalance
            price_changes = [closes[i] - closes[i-1] for i in range(1, len(closes))]
            positive_changes = sum(1 for change in price_changes if change > 0)
            flow_imbalance = ((positive_changes / len(price_changes)) - 0.5) * 200 if price_changes else 0
            
            # Calculate volume momentum
            recent_vol = sum(volumes[-2:]) / 2
            older_vol = sum(volumes[-5:-2]) / 3 if len(volumes) >= 5 else recent_vol
            volume_momentum = ((recent_vol - older_vol) / older_vol) * 100 if older_vol > 0 else 0
            
            print(f"   Tick ATR: ${tick_atr:.6f}")
            print(f"   Flow imbalance: {flow_imbalance:+.1f}%")
            print(f"   Volume momentum: {volume_momentum:+.1f}%")
            print(f"   Latest close: ${closes[-1]:.6f}")
        else:
            print("❌ No OHLCV data available")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing direct market data: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_market_data_logic():
    """Test the enhanced market data logic without GUI"""
    print("\n🧪 Testing enhanced market data logic...")
    
    try:
        # Simulate the enhanced fetch_enriched_market_data logic
        def fetch_enriched_market_data_test(symbol):
            """Test version of enhanced market data fetching"""
            market_data = {
                'best_bid': 0.0,
                'best_ask': 0.0,
                'spread': 0.0,
                'spread_pct': 0.0,
                'top_5_bids': [],
                'top_5_asks': [],
                'tick_atr': 0.0,
                'trade_flow_imbalance': 0.0,
                'volume_momentum': 0.0,
                'data_latency_ms': 0.0
            }
            
            # Method 1: Try direct CCXT call
            try:
                from me2 import fetch_order_book
                order_book = fetch_order_book(symbol)
                if order_book and 'bids' in order_book and 'asks' in order_book:
                    bids = order_book['bids'][:5]
                    asks = order_book['asks'][:5]

                    if bids and asks:
                        market_data['best_bid'] = float(bids[0][0])
                        market_data['best_ask'] = float(asks[0][0])
                        market_data['spread'] = market_data['best_ask'] - market_data['best_bid']
                        market_data['spread_pct'] = (market_data['spread'] / market_data['best_bid']) * 100
                        market_data['top_5_bids'] = [(float(bid[0]), float(bid[1])) for bid in bids]
                        market_data['top_5_asks'] = [(float(ask[0]), float(ask[1])) for ask in asks]
                        print(f"✅ Order book data fetched successfully")
            except Exception as e:
                print(f"⚠️ Order book fetch failed: {e}")

            # Get historical data for tick analysis
            try:
                from me2 import fetch_ohlcv
                candles = fetch_ohlcv(symbol, '1m', limit=10)
                if candles and len(candles) >= 5:
                    # Calculate tick ATR from recent price movements
                    highs = [float(candle[2]) for candle in candles[-5:]]
                    lows = [float(candle[3]) for candle in candles[-5:]]
                    closes = [float(candle[4]) for candle in candles[-5:]]
                    volumes = [float(candle[5]) for candle in candles[-5:]]

                    # Enhanced tick ATR calculation
                    ranges = [high - low for high, low in zip(highs, lows)]
                    market_data['tick_atr'] = sum(ranges) / len(ranges) if ranges else 0.0

                    # Improved trade flow imbalance
                    if len(closes) >= 2:
                        price_changes = [closes[i] - closes[i-1] for i in range(1, len(closes))]
                        positive_changes = sum(1 for change in price_changes if change > 0)
                        market_data['trade_flow_imbalance'] = ((positive_changes / len(price_changes)) - 0.5) * 200

                    # Enhanced volume momentum
                    if len(volumes) >= 2:
                        recent_vol = sum(volumes[-2:]) / 2
                        older_vol = sum(volumes[-5:-2]) / 3 if len(volumes) >= 5 else recent_vol
                        market_data['volume_momentum'] = ((recent_vol - older_vol) / older_vol) * 100 if older_vol > 0 else 0

                    print(f"✅ Tick analysis completed with {len(candles)} candles")
            except Exception as e:
                print(f"⚠️ Tick analysis failed: {e}")

            # Calculate data latency
            market_data['data_latency_ms'] = 50.0 if market_data['spread'] > 0 else 100.0

            return market_data
        
        # Test the enhanced logic
        symbol = 'DOGE/USDT:USDT'
        market_data = fetch_enriched_market_data_test(symbol)
        
        print(f"\n📊 Enhanced market data results for {symbol}:")
        for key, value in market_data.items():
            if isinstance(value, float):
                if 'pct' in key or 'momentum' in key or 'imbalance' in key:
                    print(f"   {key}: {value:+.3f}%")
                elif 'ms' in key:
                    print(f"   {key}: {value:.0f}ms")
                else:
                    print(f"   {key}: ${value:.6f}")
            else:
                print(f"   {key}: {len(value) if isinstance(value, list) else value}")
        
        # Validate results
        has_real_data = any([
            market_data.get('spread', 0) > 0,
            market_data.get('tick_atr', 0) > 0,
            market_data.get('volume_momentum', 0) != 0
        ])
        
        if has_real_data:
            print("\n✅ SUCCESS: Real market data was fetched and processed!")
            return True
        else:
            print("\n⚠️ INFO: No real market data available (may be expected in test environment)")
            return False
        
    except Exception as e:
        print(f"❌ Error testing enhanced market data logic: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all market data tests"""
    print("🚀 Running Market Data Feed Fixes Tests")
    print("=" * 60)
    
    test1_result = test_direct_market_data_fetching()
    test2_result = test_enhanced_market_data_logic()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   Direct market data fetching: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Enhanced market data logic: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 All market data feed tests PASSED!")
        print("The enhanced market data fetching should now work in the live interface.")
    else:
        print("\n⚠️ Some tests failed - market data may not be available in current environment")
        print("This is expected if exchange connectivity is limited.")

if __name__ == "__main__":
    main()
