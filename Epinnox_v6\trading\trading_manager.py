"""
Trading Manager Module
This module handles live trading operations, including leverage settings and balance tracking.
"""
import logging
import yaml
import os
import time
import json
from datetime import datetime
import ccxt
from typing import Dict, Any, Tuple, List, Optional

# Configure logging
logger = logging.getLogger(__name__)

class TradingManager:
    """
    Trading Manager class for handling live trading operations.
    """
    def __init__(self, config_path: str = 'trading_config.yaml'):
        """
        Initialize the Trading Manager.

        Args:
            config_path: Path to the trading configuration YAML file
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.exchange = self._initialize_exchange()
        self.balance_history = []
        self.positions = {}
        self.last_balance_check = datetime.now()
        self.balance_check_interval = 300  # 5 minutes

        # Load initial balance
        self.update_balance()

        logger.info(f"Trading Manager initialized with exchange: {self.config['credentials']['exchange']}")

    def _load_config(self) -> Dict[str, Any]:
        """
        Load trading configuration from YAML file.

        Returns:
            Dict: Trading configuration
        """
        try:
            with open(self.config_path, 'r') as file:
                config = yaml.safe_load(file)
            logger.info(f"Loaded trading configuration from {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"Error loading trading configuration: {e}")
            raise

    def _initialize_exchange(self) -> ccxt.Exchange:
        """
        Initialize exchange connection.

        Returns:
            ccxt.Exchange: Exchange instance
        """
        try:
            exchange_id = self.config['credentials']['exchange']
            api_key = self.config['credentials']['apiKey']
            secret = self.config['credentials']['secret']

            # Map exchange ID to ccxt class
            exchange_map = {
                'htx': ccxt.htx,
                'huobi': ccxt.huobi,
                'binance': ccxt.binance,
                'bybit': ccxt.bybit,
                'okx': ccxt.okx
            }

            if exchange_id.lower() not in exchange_map:
                raise ValueError(f"Unsupported exchange: {exchange_id}")

            exchange_class = exchange_map[exchange_id.lower()]
            exchange = exchange_class({
                'apiKey': api_key,
                'secret': secret,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',  # Set default type to futures
                }
            })

            logger.info(f"Initialized exchange: {exchange_id}")
            return exchange
        except Exception as e:
            logger.error(f"Error initializing exchange: {e}")
            raise

    def update_balance(self) -> Dict[str, Any]:
        """
        Update account balance information.

        Returns:
            Dict: Account balance information
        """
        try:
            # Fetch balance
            balance = self.exchange.fetch_balance()

            # Record balance history
            timestamp = datetime.now().isoformat()
            balance_record = {
                'timestamp': timestamp,
                'total': balance['total'],
                'free': balance['free'],
                'used': balance['used']
            }
            self.balance_history.append(balance_record)

            # Save balance history to file
            self._save_balance_history()

            logger.info(f"Updated balance: Total USDT = {balance['total'].get('USDT', 0)}")
            self.last_balance_check = datetime.now()

            return balance
        except Exception as e:
            logger.error(f"Error updating balance: {e}")
            return {}

    def _save_balance_history(self):
        """
        Save balance history to file.
        """
        try:
            with open('balance_history.json', 'w') as file:
                json.dump(self.balance_history, file, indent=2)
        except Exception as e:
            logger.error(f"Error saving balance history: {e}")

    def update_positions(self) -> Dict[str, Any]:
        """
        Update open positions information.

        Returns:
            Dict: Open positions information
        """
        try:
            # Fetch positions
            positions = self.exchange.fetch_positions()

            # Filter out positions with zero size
            active_positions = [p for p in positions if float(p['contracts']) > 0]

            # Update positions dictionary
            self.positions = {p['symbol']: p for p in active_positions}

            logger.info(f"Updated positions: {len(active_positions)} active positions")
            return self.positions
        except Exception as e:
            logger.error(f"Error updating positions: {e}")
            return {}

    def get_asset_config(self, symbol: str) -> Dict[str, Any]:
        """
        Get asset-specific configuration.

        Args:
            symbol: Trading symbol (e.g., 'DOGE/USDT:USDT')

        Returns:
            Dict: Asset configuration
        """
        # Get default configuration
        default_config = self.config['trading']['default']

        # Get asset-specific configuration if available
        asset_config = self.config['trading']['assets'].get(symbol, {})

        # Merge configurations, with asset-specific taking precedence
        config = {**default_config, **asset_config}

        return config

    def set_leverage(self, symbol: str) -> bool:
        """
        Set leverage for a specific symbol.

        Args:
            symbol: Trading symbol (e.g., 'DOGE/USDT:USDT')

        Returns:
            bool: Success status
        """
        try:
            # Get asset configuration
            asset_config = self.get_asset_config(symbol)
            leverage = asset_config.get('leverage', 1)

            # Set leverage
            self.exchange.set_leverage(leverage, symbol)

            logger.info(f"Set leverage for {symbol} to {leverage}x")
            return True
        except Exception as e:
            logger.error(f"Error setting leverage for {symbol}: {e}")
            return False

    def calculate_position_size(self, symbol: str, price: float) -> float:
        """
        Calculate position size based on risk parameters.

        Args:
            symbol: Trading symbol (e.g., 'DOGE/USDT:USDT')
            price: Current price

        Returns:
            float: Position size in base currency
        """
        try:
            # Get asset configuration
            asset_config = self.get_asset_config(symbol)
            risk_percentage = asset_config.get('risk_percentage', 2.0)
            max_position_size = asset_config.get('max_position_size', 1000.0)

            # Get available balance
            balance = self.exchange.fetch_balance()
            available_usdt = balance['free'].get('USDT', 0)

            # Calculate position size based on risk percentage
            position_size_usdt = available_usdt * (risk_percentage / 100)

            # Limit to max position size
            position_size_usdt = min(position_size_usdt, max_position_size)

            # Convert to base currency
            position_size = position_size_usdt / price

            logger.info(f"Calculated position size for {symbol}: {position_size} units (${position_size_usdt:.2f})")
            return position_size
        except Exception as e:
            logger.error(f"Error calculating position size for {symbol}: {e}")
            return 0.0

    def execute_trade(self, symbol: str, side: str, price: float = None, decision_id: str = None) -> Dict[str, Any]:
        """
        Execute a trade.

        Args:
            symbol: Trading symbol (e.g., 'DOGE/USDT:USDT')
            side: Trade side ('buy' or 'sell')
            price: Current price (optional, for market orders)
            decision_id: Associated decision ID (optional)

        Returns:
            Dict: Order information
        """
        try:
            # Set leverage
            self.set_leverage(symbol)

            # Calculate position size
            if price is None:
                # Fetch current price
                ticker = self.exchange.fetch_ticker(symbol)
                price = ticker['last']

            position_size = self.calculate_position_size(symbol, price)

            if position_size <= 0:
                logger.warning(f"Position size for {symbol} is zero or negative")
                return {}

            # Get asset configuration
            asset_config = self.get_asset_config(symbol)
            stop_loss_percentage = asset_config.get('stop_loss_percentage', 2.0)
            take_profit_percentage = asset_config.get('take_profit_percentage', 4.0)

            # Calculate stop loss and take profit prices
            if side.lower() == 'buy':
                stop_loss_price = price * (1 - stop_loss_percentage / 100)
                take_profit_price = price * (1 + take_profit_percentage / 100)
            else:
                stop_loss_price = price * (1 + stop_loss_percentage / 100)
                take_profit_price = price * (1 - take_profit_percentage / 100)

            # Execute order
            order = self.exchange.create_order(
                symbol=symbol,
                type='market',
                side=side,
                amount=position_size,
                params={
                    'stopLoss': {
                        'type': 'market',
                        'price': stop_loss_price
                    },
                    'takeProfit': {
                        'type': 'market',
                        'price': take_profit_price
                    }
                }
            )

            logger.info(f"Executed {side} order for {position_size} {symbol} at {price}")

            # Update balance and positions
            self.update_balance()
            self.update_positions()

            # Add decision ID to order info
            if decision_id:
                order['decision_id'] = decision_id

            return order
        except Exception as e:
            logger.error(f"Error executing trade for {symbol}: {e}")
            return {}

    def close_position(self, symbol: str) -> Dict[str, Any]:
        """
        Close an open position.

        Args:
            symbol: Trading symbol (e.g., 'DOGE/USDT:USDT')

        Returns:
            Dict: Order information
        """
        try:
            # Check if position exists
            positions = self.update_positions()
            if symbol not in positions:
                logger.warning(f"No open position found for {symbol}")
                return {}

            position = positions[symbol]

            # Determine close side (opposite of position side)
            side = 'sell' if position['side'] == 'long' else 'buy'

            # Close position
            # For Huobi/HTX, use 'offset': 'close' instead of 'reduceOnly'
            exchange_id = self.config['credentials']['exchange'].lower()

            # Set parameters based on exchange
            if exchange_id in ['htx', 'huobi']:
                params = {'offset': 'close'}
            else:
                params = {'reduceOnly': True}

            order = self.exchange.create_order(
                symbol=symbol,
                type='market',
                side=side,
                amount=abs(float(position['contracts'])),
                params=params
            )

            logger.info(f"Closed position for {symbol}")

            # Update balance and positions
            self.update_balance()
            self.update_positions()

            return order
        except Exception as e:
            logger.error(f"Error closing position for {symbol}: {e}")
            return {}

    def evaluate_decision(self, symbol: str, decision: str, decision_id: str) -> Dict[str, Any]:
        """
        Evaluate a trading decision based on balance changes.

        Args:
            symbol: Trading symbol (e.g., 'DOGE/USDT:USDT')
            decision: Trading decision ('LONG', 'SHORT', 'WAIT')
            decision_id: Decision ID

        Returns:
            Dict: Evaluation results
        """
        try:
            # Get initial balance
            initial_balance = self.balance_history[-2]['total'].get('USDT', 0) if len(self.balance_history) >= 2 else 0

            # Get current balance
            current_balance = self.balance_history[-1]['total'].get('USDT', 0)

            # Calculate balance change
            balance_change = current_balance - initial_balance
            balance_change_percentage = (balance_change / initial_balance * 100) if initial_balance > 0 else 0

            # Determine if decision was correct
            correct = False
            if decision == 'LONG' and balance_change > 0:
                correct = True
            elif decision == 'SHORT' and balance_change > 0:
                correct = True
            elif decision == 'WAIT' and abs(balance_change) < 0.1:  # Less than 0.1% change for WAIT decisions
                correct = True

            # Create evaluation result
            result = {
                'decision_id': decision_id,
                'symbol': symbol,
                'decision': decision,
                'initial_balance': initial_balance,
                'current_balance': current_balance,
                'balance_change': balance_change,
                'balance_change_percentage': balance_change_percentage,
                'correct': correct,
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"Evaluated decision {decision_id} for {symbol}: Balance change = {balance_change_percentage:.2f}%, Correct = {correct}")

            return result
        except Exception as e:
            logger.error(f"Error evaluating decision for {symbol}: {e}")
            return {}

    def check_balance_change(self) -> bool:
        """
        Check if balance needs to be updated based on time interval.

        Returns:
            bool: True if balance was updated, False otherwise
        """
        now = datetime.now()
        time_diff = (now - self.last_balance_check).total_seconds()

        if time_diff >= self.balance_check_interval:
            self.update_balance()
            return True

        return False

    def get_max_leverage(self, symbol: str) -> int:
        """
        Get maximum allowed leverage for a symbol.

        Args:
            symbol: Trading symbol (e.g., 'DOGE/USDT:USDT')

        Returns:
            int: Maximum leverage
        """
        try:
            # Get market information
            markets = self.exchange.fetch_markets()
            market = next((m for m in markets if m['symbol'] == symbol), None)

            if market and 'limits' in market and 'leverage' in market['limits']:
                max_leverage = market['limits']['leverage']['max']
                logger.info(f"Maximum leverage for {symbol}: {max_leverage}x")
                return max_leverage

            # If not found in market info, use configuration
            asset_config = self.get_asset_config(symbol)
            leverage = asset_config.get('leverage', 1)

            logger.info(f"Using configured leverage for {symbol}: {leverage}x")
            return leverage
        except Exception as e:
            logger.error(f"Error getting max leverage for {symbol}: {e}")
            return 1  # Default to 1x leverage on error

    def get_balance_history(self) -> List[Dict[str, Any]]:
        """
        Get balance history.

        Returns:
            List: Balance history
        """
        return self.balance_history

    def get_active_positions(self) -> Dict[str, Any]:
        """
        Get active positions.

        Returns:
            Dict: Active positions
        """
        return self.positions
