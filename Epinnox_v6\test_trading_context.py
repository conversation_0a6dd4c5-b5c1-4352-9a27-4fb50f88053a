#!/usr/bin/env python3
"""
Test script to verify TradingContext initialization works correctly
"""

import sys
from datetime import datetime

# Test if LLM components can be imported
try:
    from core.llm_orchestrator import TradingContext
    print("✅ Successfully imported TradingContext")
except ImportError as e:
    print(f"❌ Failed to import TradingContext: {e}")
    sys.exit(1)

# Test TradingContext initialization with all required parameters
def test_trading_context_creation():
    """Test creating a TradingContext with all required parameters"""
    try:
        # Create test data
        symbol = "BTC/USDT:USDT"
        current_price = 45000.0
        timestamp = datetime.now()
        account_balance = 1000.0
        open_positions = []
        market_data = {
            'symbol': symbol,
            'current_price': current_price,
            'best_bid': 44990.0,
            'best_ask': 45010.0,
            'spread': 20.0,
            'spread_pct': 0.04
        }
        emergency_flags = []
        performance_metrics = {
            'total_trades': 0,
            'successful_trades': 0,
            'win_rate': 0.0
        }

        # Create TradingContext instance
        trading_context = TradingContext(
            symbol=symbol,
            current_price=current_price,
            timestamp=timestamp,
            account_balance=account_balance,
            open_positions=open_positions,
            market_data=market_data,
            emergency_flags=emergency_flags,
            performance_metrics=performance_metrics
        )

        print("✅ TradingContext created successfully!")
        print(f"   Symbol: {trading_context.symbol}")
        print(f"   Current Price: {trading_context.current_price}")
        print(f"   Timestamp: {trading_context.timestamp}")
        print(f"   Account Balance: {trading_context.account_balance}")
        print(f"   Market Data: {trading_context.market_data}")
        
        return True

    except Exception as e:
        print(f"❌ Failed to create TradingContext: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing TradingContext initialization...")
    success = test_trading_context_creation()
    
    if success:
        print("\n🎉 All tests passed! TradingContext is working correctly.")
    else:
        print("\n💥 Tests failed! Check the errors above.")
        sys.exit(1)
