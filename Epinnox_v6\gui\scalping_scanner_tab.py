"""
Scalping Scanner Tab for Epinnox v6 Trading System
Multi-symbol scanning and analysis
"""

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
except ImportError:
    print("PyQt5 not installed. Install with: pip install PyQt5")
    raise

from .base_tab import BaseTab
from .matrix_theme import MatrixTheme
import logging

logger = logging.getLogger(__name__)

class ScalpingScannerTab(BaseTab):
    """Scalping scanner for multi-symbol analysis"""
    
    # Signals
    scan_started = pyqtSignal(list)  # symbols list
    scan_stopped = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.scanning = False
        
    def setup_ui(self):
        """Setup the Scalping Scanner tab UI"""
        layout = QVBoxLayout(self)
        
        # Scanner controls
        controls_layout = self.create_scanner_controls()
        
        # Scanner results table
        self.scanner_table = QTableWidget(0, 8)
        self.scanner_table.setHorizontalHeaderLabels([
            "Symbol", "Decision", "Confidence", "ML Consensus",
            "Leverage", "Position Size", "Risk", "Last Update"
        ])
        self.apply_matrix_table_styling(self.scanner_table)
        
        layout.addLayout(controls_layout)
        layout.addWidget(self.scanner_table)
    
    def create_scanner_controls(self):
        """Create scanner control panel"""
        layout = QHBoxLayout()
        
        # Symbols input
        self.scanner_symbols = QLineEdit("DOGE/USDT,BTC/USDT,ETH/USDT,ADA/USDT")
        self.scanner_symbols.setPlaceholderText("Enter symbols separated by commas")
        
        # Control buttons
        self.scan_button = self.create_matrix_button("START SCAN", self.start_scanner)
        self.stop_scan_button = self.create_matrix_button("STOP SCAN", self.stop_scanner, False)
        
        # Scan interval
        self.scan_interval = QSpinBox()
        self.scan_interval.setRange(10, 300)
        self.scan_interval.setValue(30)
        self.scan_interval.setSuffix(" seconds")
        
        layout.addWidget(QLabel("Symbols:"))
        layout.addWidget(self.scanner_symbols)
        layout.addWidget(QLabel("Interval:"))
        layout.addWidget(self.scan_interval)
        layout.addWidget(self.scan_button)
        layout.addWidget(self.stop_scan_button)
        
        return layout
    
    def start_scanner(self):
        """Start the multi-symbol scanner"""
        symbols_text = self.scanner_symbols.text().strip()
        if not symbols_text:
            self.show_error_message("Scanner Error", "Please enter symbols to scan")
            return
        
        symbols = [s.strip() for s in symbols_text.split(',') if s.strip()]
        
        self.scanning = True
        self.scan_button.setEnabled(False)
        self.stop_scan_button.setEnabled(True)
        
        self.scan_started.emit(symbols)
        self.log_message(f"Scanner started for symbols: {symbols}")
    
    def stop_scanner(self):
        """Stop the multi-symbol scanner"""
        self.scanning = False
        self.scan_button.setEnabled(True)
        self.stop_scan_button.setEnabled(False)
        
        self.scan_stopped.emit()
        self.log_message("Scanner stopped")
    
    def update_scanner_results(self, results):
        """Update scanner results table"""
        try:
            self.scanner_table.setRowCount(len(results))
            
            for row, result in enumerate(results):
                self.scanner_table.setItem(row, 0, QTableWidgetItem(result.get('symbol', '')))
                
                # Decision with color coding
                decision = result.get('decision', 'UNKNOWN')
                decision_item = QTableWidgetItem(decision)
                if decision == 'LONG':
                    decision_item.setForeground(QColor(MatrixTheme.GREEN))
                elif decision == 'SHORT':
                    decision_item.setForeground(QColor(MatrixTheme.RED))
                else:
                    decision_item.setForeground(QColor(MatrixTheme.YELLOW))
                self.scanner_table.setItem(row, 1, decision_item)
                
                self.scanner_table.setItem(row, 2, QTableWidgetItem(f"{result.get('confidence', 0):.1%}"))
                self.scanner_table.setItem(row, 3, QTableWidgetItem(result.get('ml_consensus', '--')))
                self.scanner_table.setItem(row, 4, QTableWidgetItem(f"{result.get('leverage', 0):.1f}x"))
                self.scanner_table.setItem(row, 5, QTableWidgetItem(f"${result.get('position_size', 0):.2f}"))
                self.scanner_table.setItem(row, 6, QTableWidgetItem(f"{result.get('risk', 0):.1%}"))
                self.scanner_table.setItem(row, 7, QTableWidgetItem(result.get('last_update', '--')))
                
        except Exception as e:
            self.log_message(f"Error updating scanner results: {e}", logging.ERROR)
