#!/usr/bin/env python3
"""
Autonomous Trading System Integration Test
Tests all components working together seamlessly
"""

import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_component_integration():
    """Test integration of all autonomous components"""
    logger.info("🧪 Testing Autonomous Component Integration...")
    
    results = {}
    
    # Test 1: AutonomousTradeExecutor with MockExchange
    try:
        from execution.autonomous_executor import AutonomousTradeExecutor
        from tests.mocks.mock_exchange import MockExchange
        
        mock_exchange = MockExchange(initial_balance=1000.0)
        executor = AutonomousTradeExecutor(mock_exchange)
        
        results['AutonomousTradeExecutor_MockExchange'] = "✅ PASS"
        logger.info("✅ AutonomousTradeExecutor + MockExchange integration successful")
        
    except Exception as e:
        results['AutonomousTradeExecutor_MockExchange'] = f"❌ FAIL: {e}"
        logger.error(f"❌ AutonomousTradeExecutor + MockExchange integration failed: {e}")
    
    # Test 2: PortfolioManager with PerformanceTracker
    try:
        from portfolio.portfolio_manager import PortfolioManager
        from monitoring.performance_tracker import PerformanceTracker
        
        portfolio = PortfolioManager(initial_balance=1000.0)
        tracker = PerformanceTracker()
        
        # Test recording a trade
        trade_data = {
            'timestamp': datetime.now(),
            'symbol': 'BTC/USDT:USDT',
            'decision': 'LONG',
            'confidence': 0.75,
            'entry_price': 50000.0,
            'exit_price': 51000.0,
            'position_size': 0.1,
            'leverage': 2.0,
            'pnl_usd': 100.0,
            'pnl_pct': 2.0,
            'duration_minutes': 30
        }
        
        tracker.record_trade(trade_data)
        
        results['PortfolioManager_PerformanceTracker'] = "✅ PASS"
        logger.info("✅ PortfolioManager + PerformanceTracker integration successful")
        
    except Exception as e:
        results['PortfolioManager_PerformanceTracker'] = f"❌ FAIL: {e}"
        logger.error(f"❌ PortfolioManager + PerformanceTracker integration failed: {e}")
    
    # Test 3: OnlineLearningManager with ML components
    try:
        from ml.adaptive_updater import OnlineLearningManager
        from ml.models import MLModelManager
        
        ml_manager = MLModelManager()
        online_manager = OnlineLearningManager(ml_manager, tracker)
        
        # Test adaptive prediction
        mock_predictions = {
            'svm': {'prediction': 0.7, 'confidence': 0.8},
            'random_forest': {'prediction': 0.6, 'confidence': 0.7},
            'lstm': {'prediction': 0.8, 'confidence': 0.9}
        }
        
        adaptive_pred = online_manager.get_adaptive_prediction(mock_predictions)
        
        if 'prediction' in adaptive_pred and 'confidence' in adaptive_pred:
            results['OnlineLearningManager_ML'] = "✅ PASS"
            logger.info("✅ OnlineLearningManager + ML integration successful")
        else:
            results['OnlineLearningManager_ML'] = "❌ FAIL: Invalid prediction format"
            
    except Exception as e:
        results['OnlineLearningManager_ML'] = f"❌ FAIL: {e}"
        logger.error(f"❌ OnlineLearningManager + ML integration failed: {e}")
    
    # Test 4: TradingEnvironment with TradingRLAgent
    try:
        from ml.trading_env import TradingEnvironment
        from ml.rl_agent import TradingRLAgent
        from data.exchange import ExchangeDataFetcher
        
        data_fetcher = ExchangeDataFetcher()
        env = TradingEnvironment(data_fetcher, initial_balance=1000.0, max_steps=100)
        agent = TradingRLAgent(env, model_type='PPO')
        
        # Test environment-agent interaction
        obs = env.reset()
        action, _ = agent.predict(obs, deterministic=True)
        obs, reward, done, info = env.step(action)
        
        results['TradingEnvironment_RLAgent'] = "✅ PASS"
        logger.info("✅ TradingEnvironment + RLAgent integration successful")
        
    except Exception as e:
        results['TradingEnvironment_RLAgent'] = f"❌ FAIL: {e}"
        logger.error(f"❌ TradingEnvironment + RLAgent integration failed: {e}")
    
    # Test 5: AutonomousController with all components
    try:
        from core.autonomous_controller import AutonomousController
        
        config = {
            'initial_balance': 1000.0,
            'max_positions': 3,
            'min_confidence': 0.65,
            'use_rl': False
        }
        
        controller = AutonomousController(mock_exchange, config)
        
        results['AutonomousController_Integration'] = "✅ PASS"
        logger.info("✅ AutonomousController integration successful")
        
    except Exception as e:
        results['AutonomousController_Integration'] = f"❌ FAIL: {e}"
        logger.error(f"❌ AutonomousController integration failed: {e}")
    
    return results

def test_end_to_end_workflow():
    """Test end-to-end autonomous trading workflow"""
    logger.info("🧪 Testing End-to-End Workflow...")
    
    try:
        from tests.mocks.mock_exchange import MockExchange
        from execution.autonomous_executor import AutonomousTradeExecutor
        from portfolio.portfolio_manager import PortfolioManager
        from monitoring.performance_tracker import PerformanceTracker
        from ml.adaptive_updater import OnlineLearningManager
        from ml.models import MLModelManager
        
        # Setup components
        mock_exchange = MockExchange(initial_balance=10000.0)
        portfolio = PortfolioManager(initial_balance=10000.0)
        tracker = PerformanceTracker()
        ml_manager = MLModelManager()
        online_manager = OnlineLearningManager(ml_manager, tracker)
        executor = AutonomousTradeExecutor(mock_exchange, portfolio_manager=portfolio)
        
        # Simulate trading decision workflow
        decision_data = {
            'decision': 'LONG',
            'confidence': 0.75,
            'selected_symbol': 'BTC/USDT:USDT',
            'leverage_position_sizing': {
                'leverage': 2.0,
                'position_size': 0.1
            },
            'explanation': 'Test trading decision'
        }
        
        # Test decision execution (this would normally be async)
        logger.info("Testing decision execution workflow...")
        
        # Test portfolio position opening
        portfolio_check = portfolio.can_open_position(
            'BTC/USDT:USDT', 
            1000.0,  # position size in USD
            2.0      # leverage
        )
        
        if portfolio_check['allowed']:
            logger.info("✅ Portfolio position check passed")
        else:
            logger.warning(f"Portfolio position check failed: {portfolio_check['reason']}")
        
        # Test adaptive learning update
        trade_result = {
            'symbol': 'BTC/USDT:USDT',
            'pnl_usd': 100.0,
            'ml_predictions': {
                'svm': {'prediction': 0.7, 'confidence': 0.8},
                'random_forest': {'prediction': 0.6, 'confidence': 0.7}
            }
        }
        
        # This would normally be async
        logger.info("Testing adaptive learning update...")
        
        return "✅ PASS"
        
    except Exception as e:
        logger.error(f"❌ End-to-end workflow test failed: {e}")
        return f"❌ FAIL: {e}"

def main():
    """Run all integration tests"""
    logger.info("🚀 Starting Autonomous Trading System Integration Tests")
    logger.info("=" * 70)
    
    all_results = {}
    
    # Test component integration
    integration_results = test_component_integration()
    all_results.update(integration_results)
    
    # Test end-to-end workflow
    workflow_result = test_end_to_end_workflow()
    all_results['EndToEnd_Workflow'] = workflow_result
    
    # Summary
    logger.info("=" * 70)
    logger.info("📊 INTEGRATION TEST SUMMARY")
    logger.info("=" * 70)
    
    passed = 0
    failed = 0
    
    for test_name, result in all_results.items():
        logger.info(f"{test_name}: {result}")
        if "✅ PASS" in result:
            passed += 1
        else:
            failed += 1
    
    logger.info("=" * 70)
    logger.info(f"✅ PASSED: {passed}")
    logger.info(f"❌ FAILED: {failed}")
    logger.info(f"📈 SUCCESS RATE: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        logger.info("🎉 ALL INTEGRATION TESTS PASSED!")
        return 0
    else:
        logger.info("⚠️  SOME INTEGRATION TESTS FAILED")
        return 1

if __name__ == "__main__":
    sys.exit(main())
