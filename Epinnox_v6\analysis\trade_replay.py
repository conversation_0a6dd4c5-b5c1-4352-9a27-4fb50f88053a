"""
Trade Replay Engine
Re-run previous trading sessions from logs for analysis and debugging
"""

import pandas as pd
import numpy as np
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class ReplayConfig:
    """Configuration for trade replay"""
    session_id: str = None
    start_time: datetime = None
    end_time: datetime = None
    symbols: List[str] = None
    replay_speed: float = 1.0  # 1.0 = real-time, 10.0 = 10x speed
    include_market_data: bool = True
    include_signals: bool = True
    include_decisions: bool = True

class TradeReplayEngine:
    """
    Comprehensive trade replay system for analysis and debugging
    """
    
    def __init__(self, performance_db_path: str = "data/trading_performance.db"):
        self.db_path = performance_db_path
        self.replay_data = {}
        self.current_replay = None
        
    def load_session_data(self, session_id: str = None, 
                         start_time: datetime = None, 
                         end_time: datetime = None) -> Dict:
        """Load trading session data for replay"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Build query conditions
            conditions = []
            params = []
            
            if session_id:
                conditions.append("session_id = ?")
                params.append(session_id)
            
            if start_time:
                conditions.append("timestamp >= ?")
                params.append(start_time)
            
            if end_time:
                conditions.append("timestamp <= ?")
                params.append(end_time)
            
            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
            
            # Load trades
            trades_query = f"SELECT * FROM trades{where_clause} ORDER BY timestamp"
            trades_df = pd.read_sql_query(trades_query, conn, params=params)
            
            # Load decisions (if table exists)
            decisions_df = pd.DataFrame()
            try:
                decisions_query = f"SELECT * FROM decisions{where_clause} ORDER BY timestamp"
                decisions_df = pd.read_sql_query(decisions_query, conn, params=params)
            except Exception:
                logger.warning("Decisions table not found, skipping decision data")
            
            # Load market data snapshots (if available)
            market_data_df = pd.DataFrame()
            try:
                market_query = f"SELECT * FROM market_snapshots{where_clause} ORDER BY timestamp"
                market_data_df = pd.read_sql_query(market_query, conn, params=params)
            except Exception:
                logger.warning("Market snapshots table not found, skipping market data")
            
            conn.close()
            
            # Convert timestamps
            if not trades_df.empty:
                trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'])
            if not decisions_df.empty:
                decisions_df['timestamp'] = pd.to_datetime(decisions_df['timestamp'])
            if not market_data_df.empty:
                market_data_df['timestamp'] = pd.to_datetime(market_data_df['timestamp'])
            
            session_data = {
                'trades': trades_df,
                'decisions': decisions_df,
                'market_data': market_data_df,
                'session_info': self._extract_session_info(trades_df, decisions_df)
            }
            
            logger.info(f"Loaded session data: {len(trades_df)} trades, "
                       f"{len(decisions_df)} decisions, "
                       f"{len(market_data_df)} market snapshots")
            
            return session_data
            
        except Exception as e:
            logger.error(f"Error loading session data: {e}")
            return {}
    
    def _extract_session_info(self, trades_df: pd.DataFrame, 
                            decisions_df: pd.DataFrame) -> Dict:
        """Extract session information from data"""
        
        info = {
            'total_trades': len(trades_df),
            'total_decisions': len(decisions_df),
            'start_time': None,
            'end_time': None,
            'symbols': [],
            'duration_minutes': 0
        }
        
        if not trades_df.empty:
            info['start_time'] = trades_df['timestamp'].min()
            info['end_time'] = trades_df['timestamp'].max()
            info['symbols'] = trades_df['symbol'].unique().tolist()
            info['duration_minutes'] = (info['end_time'] - info['start_time']).total_seconds() / 60
        
        return info
    
    def replay_session(self, config: ReplayConfig) -> Dict:
        """Replay a trading session step by step"""
        
        # Load session data
        session_data = self.load_session_data(
            session_id=config.session_id,
            start_time=config.start_time,
            end_time=config.end_time
        )
        
        if not session_data or session_data['trades'].empty:
            logger.error("No session data found for replay")
            return {}
        
        logger.info(f"Starting replay of session with {len(session_data['trades'])} trades")
        
        # Initialize replay state
        replay_state = {
            'current_time': session_data['session_info']['start_time'],
            'end_time': session_data['session_info']['end_time'],
            'portfolio_value': 10000.0,  # Starting value
            'positions': {},
            'trade_count': 0,
            'decision_count': 0,
            'events': []
        }
        
        # Combine and sort all events by timestamp
        all_events = self._create_event_timeline(session_data)
        
        # Replay events
        for event in all_events:
            self._process_replay_event(event, replay_state, config)
        
        # Generate replay summary
        summary = self._generate_replay_summary(replay_state, session_data)
        
        self.current_replay = {
            'config': config,
            'session_data': session_data,
            'replay_state': replay_state,
            'summary': summary
        }
        
        return summary
    
    def _create_event_timeline(self, session_data: Dict) -> List[Dict]:
        """Create chronological timeline of all events"""
        
        events = []
        
        # Add trade events
        for _, trade in session_data['trades'].iterrows():
            events.append({
                'timestamp': trade['timestamp'],
                'type': 'trade',
                'data': trade.to_dict()
            })
        
        # Add decision events
        for _, decision in session_data['decisions'].iterrows():
            events.append({
                'timestamp': decision['timestamp'],
                'type': 'decision',
                'data': decision.to_dict()
            })
        
        # Add market data events
        for _, market in session_data['market_data'].iterrows():
            events.append({
                'timestamp': market['timestamp'],
                'type': 'market_data',
                'data': market.to_dict()
            })
        
        # Sort by timestamp
        events.sort(key=lambda x: x['timestamp'])
        
        return events
    
    def _process_replay_event(self, event: Dict, replay_state: Dict, config: ReplayConfig):
        """Process a single replay event"""
        
        replay_state['current_time'] = event['timestamp']
        
        if event['type'] == 'trade':
            self._process_trade_event(event['data'], replay_state)
        elif event['type'] == 'decision':
            self._process_decision_event(event['data'], replay_state)
        elif event['type'] == 'market_data':
            self._process_market_data_event(event['data'], replay_state)
        
        # Add to events log
        replay_state['events'].append({
            'timestamp': event['timestamp'],
            'type': event['type'],
            'portfolio_value': replay_state['portfolio_value'],
            'positions_count': len(replay_state['positions'])
        })
    
    def _process_trade_event(self, trade_data: Dict, replay_state: Dict):
        """Process a trade event during replay"""
        
        symbol = trade_data.get('symbol')
        side = trade_data.get('decision', trade_data.get('side'))
        pnl = trade_data.get('pnl_usd', 0)
        
        # Update portfolio value
        replay_state['portfolio_value'] += pnl
        
        # Update positions
        if side in ['BUY', 'LONG']:
            replay_state['positions'][symbol] = replay_state['positions'].get(symbol, 0) + 1
        elif side in ['SELL', 'SHORT']:
            replay_state['positions'][symbol] = replay_state['positions'].get(symbol, 0) - 1
        
        # Clean up zero positions
        if symbol in replay_state['positions'] and replay_state['positions'][symbol] == 0:
            del replay_state['positions'][symbol]
        
        replay_state['trade_count'] += 1
        
        logger.debug(f"Trade: {side} {symbol}, P&L: ${pnl:.2f}, "
                    f"Portfolio: ${replay_state['portfolio_value']:.2f}")
    
    def _process_decision_event(self, decision_data: Dict, replay_state: Dict):
        """Process a decision event during replay"""
        
        replay_state['decision_count'] += 1
        
        # Log decision details
        symbol = decision_data.get('symbol')
        decision = decision_data.get('decision')
        confidence = decision_data.get('confidence')
        
        logger.debug(f"Decision: {decision} {symbol} (confidence: {confidence})")
    
    def _process_market_data_event(self, market_data: Dict, replay_state: Dict):
        """Process a market data event during replay"""
        
        # Update current market prices (for position valuation)
        symbol = market_data.get('symbol')
        price = market_data.get('price', market_data.get('close'))
        
        if symbol and price:
            # Could update unrealized P&L here
            pass
    
    def _generate_replay_summary(self, replay_state: Dict, session_data: Dict) -> Dict:
        """Generate summary of replay results"""
        
        original_trades = len(session_data['trades'])
        replayed_trades = replay_state['trade_count']
        
        summary = {
            'replay_completed': True,
            'original_session': {
                'total_trades': original_trades,
                'start_time': session_data['session_info']['start_time'].isoformat(),
                'end_time': session_data['session_info']['end_time'].isoformat(),
                'duration_minutes': session_data['session_info']['duration_minutes'],
                'symbols': session_data['session_info']['symbols']
            },
            'replay_results': {
                'trades_processed': replayed_trades,
                'decisions_processed': replay_state['decision_count'],
                'final_portfolio_value': replay_state['portfolio_value'],
                'final_positions': replay_state['positions'],
                'total_return': (replay_state['portfolio_value'] - 10000) / 10000,
                'events_timeline': replay_state['events']
            },
            'validation': {
                'trades_match': replayed_trades == original_trades,
                'data_integrity': True  # Could add more checks
            }
        }
        
        return summary
    
    def analyze_replay_differences(self, original_results: Dict, 
                                 replay_results: Dict) -> Dict:
        """Analyze differences between original and replay results"""
        
        differences = {
            'trade_count_diff': 0,
            'portfolio_value_diff': 0,
            'timing_differences': [],
            'missing_trades': [],
            'extra_trades': []
        }
        
        # Compare trade counts
        orig_trades = original_results.get('total_trades', 0)
        replay_trades = replay_results.get('trades_processed', 0)
        differences['trade_count_diff'] = replay_trades - orig_trades
        
        # Compare final values
        orig_value = original_results.get('final_portfolio_value', 10000)
        replay_value = replay_results.get('final_portfolio_value', 10000)
        differences['portfolio_value_diff'] = replay_value - orig_value
        
        return differences
    
    def export_replay_report(self, filename: str = None) -> str:
        """Export detailed replay report"""
        
        if not self.current_replay:
            raise ValueError("No replay data available. Run replay_session first.")
        
        if filename is None:
            filename = f"replay_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # Prepare report data
        report = {
            'replay_info': {
                'timestamp': datetime.now().isoformat(),
                'config': {
                    'session_id': self.current_replay['config'].session_id,
                    'start_time': self.current_replay['config'].start_time.isoformat() if self.current_replay['config'].start_time else None,
                    'end_time': self.current_replay['config'].end_time.isoformat() if self.current_replay['config'].end_time else None,
                    'symbols': self.current_replay['config'].symbols,
                    'replay_speed': self.current_replay['config'].replay_speed
                }
            },
            'summary': self.current_replay['summary'],
            'detailed_events': self.current_replay['replay_state']['events']
        }
        
        # Save report
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Replay report exported to {filename}")
        
        return filename
    
    def get_available_sessions(self, days_back: int = 30) -> List[Dict]:
        """Get list of available sessions for replay"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            cutoff_date = datetime.now() - timedelta(days=days_back)
            
            # Get session summaries
            query = """
                SELECT 
                    session_id,
                    MIN(timestamp) as start_time,
                    MAX(timestamp) as end_time,
                    COUNT(*) as trade_count,
                    GROUP_CONCAT(DISTINCT symbol) as symbols
                FROM trades 
                WHERE timestamp > ?
                GROUP BY session_id
                ORDER BY start_time DESC
            """
            
            df = pd.read_sql_query(query, conn, params=(cutoff_date,))
            conn.close()
            
            sessions = []
            for _, row in df.iterrows():
                sessions.append({
                    'session_id': row['session_id'],
                    'start_time': row['start_time'],
                    'end_time': row['end_time'],
                    'trade_count': row['trade_count'],
                    'symbols': row['symbols'].split(',') if row['symbols'] else [],
                    'duration_hours': (pd.to_datetime(row['end_time']) - pd.to_datetime(row['start_time'])).total_seconds() / 3600
                })
            
            return sessions
            
        except Exception as e:
            logger.error(f"Error getting available sessions: {e}")
            return []
    
    def compare_sessions(self, session_id1: str, session_id2: str) -> Dict:
        """Compare two trading sessions"""
        
        session1_data = self.load_session_data(session_id=session_id1)
        session2_data = self.load_session_data(session_id=session_id2)
        
        if not session1_data or not session2_data:
            return {}
        
        comparison = {
            'session1': {
                'id': session_id1,
                'trades': len(session1_data['trades']),
                'duration': session1_data['session_info']['duration_minutes'],
                'symbols': session1_data['session_info']['symbols']
            },
            'session2': {
                'id': session_id2,
                'trades': len(session2_data['trades']),
                'duration': session2_data['session_info']['duration_minutes'],
                'symbols': session2_data['session_info']['symbols']
            },
            'differences': {
                'trade_count_diff': len(session2_data['trades']) - len(session1_data['trades']),
                'duration_diff': session2_data['session_info']['duration_minutes'] - session1_data['session_info']['duration_minutes'],
                'common_symbols': list(set(session1_data['session_info']['symbols']) & set(session2_data['session_info']['symbols'])),
                'unique_symbols_1': list(set(session1_data['session_info']['symbols']) - set(session2_data['session_info']['symbols'])),
                'unique_symbols_2': list(set(session2_data['session_info']['symbols']) - set(session1_data['session_info']['symbols']))
            }
        }
        
        return comparison
