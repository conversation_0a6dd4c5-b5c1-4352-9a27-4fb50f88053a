# EPINNOX v6 - ML/RL Requirements
# Machine Learning and Reinforcement Learning dependencies

# Include minimal requirements
-r requirements-minimal.txt

# PyTorch (CPU version - for GPU use setup_gpu.py)
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0

# Reinforcement Learning
stable-baselines3[extra]>=1.6.0
gymnasium>=0.26.0
gym>=0.21.0

# Deep Learning (Optional)
tensorflow>=2.10.0

# Advanced ML
transformers>=4.21.0
optuna>=3.0.0
