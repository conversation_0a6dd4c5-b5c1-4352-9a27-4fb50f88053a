#!/usr/bin/env python3
"""
Quick RL test script to verify the training pipeline works
"""

import sys
import os
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_rl_components():
    """Test RL components individually"""
    logger.info("🧪 Testing RL Components...")
    
    results = {}
    
    # Test TradingEnvironment
    try:
        from ml.trading_env import TradingEnvironment
        from data.exchange import ExchangeDataFetcher
        
        data_fetcher = ExchangeDataFetcher()
        env = TradingEnvironment(data_fetcher, initial_balance=1000.0, max_steps=100)
        
        # Test environment reset and step
        obs = env.reset()
        action = env.action_space.sample()
        obs, reward, done, info = env.step(action)
        
        results['TradingEnvironment'] = "✅ PASS"
        logger.info("✅ TradingEnvironment test passed")
        
    except Exception as e:
        results['TradingEnvironment'] = f"❌ FAIL: {e}"
        logger.error(f"❌ TradingEnvironment test failed: {e}")
    
    # Test TradingRLAgent
    try:
        from ml.rl_agent import TradingRLAgent
        
        agent = TradingRLAgent(env, model_type='PPO')
        results['TradingRLAgent'] = "✅ PASS"
        logger.info("✅ TradingRLAgent test passed")
        
    except Exception as e:
        results['TradingRLAgent'] = f"❌ FAIL: {e}"
        logger.error(f"❌ TradingRLAgent test failed: {e}")
    
    # Test training pipeline import
    try:
        from train_rl_agent import RLTrainingPipeline
        
        config = {
            'total_timesteps': 1000,  # Very short for testing
            'model_type': 'PPO',
            'save_path': 'models/test_rl_agent',
            'validation_episodes': 5
        }
        
        pipeline = RLTrainingPipeline(config)
        results['RLTrainingPipeline'] = "✅ PASS"
        logger.info("✅ RLTrainingPipeline test passed")
        
    except Exception as e:
        results['RLTrainingPipeline'] = f"❌ FAIL: {e}"
        logger.error(f"❌ RLTrainingPipeline test failed: {e}")
    
    return results

def test_quick_training():
    """Test a very quick training run"""
    logger.info("🧪 Testing Quick Training Run...")
    
    try:
        from train_rl_agent import RLTrainingPipeline
        
        # Very minimal config for quick test
        config = {
            'total_timesteps': 1000,
            'model_type': 'PPO',
            'save_path': 'models/test_rl_agent',
            'validation_episodes': 5,
            'environment': {
                'initial_balance': 1000.0,
                'max_steps': 50
            }
        }
        
        pipeline = RLTrainingPipeline(config)
        
        # Setup components
        pipeline.setup_environment()
        pipeline.setup_agent()
        
        logger.info("✅ Quick training setup successful")
        return "✅ PASS"
        
    except Exception as e:
        logger.error(f"❌ Quick training test failed: {e}")
        return f"❌ FAIL: {e}"

def main():
    """Run all RL tests"""
    logger.info("🚀 Starting RL Component Tests")
    logger.info("=" * 50)
    
    all_results = {}
    
    # Test individual components
    component_results = test_rl_components()
    all_results.update(component_results)
    
    # Test quick training
    training_result = test_quick_training()
    all_results['QuickTraining'] = training_result
    
    # Summary
    logger.info("=" * 50)
    logger.info("📊 RL TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in all_results.items():
        logger.info(f"{test_name}: {result}")
        if "✅ PASS" in result:
            passed += 1
        else:
            failed += 1
    
    logger.info("=" * 50)
    logger.info(f"✅ PASSED: {passed}")
    logger.info(f"❌ FAILED: {failed}")
    logger.info(f"📈 SUCCESS RATE: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        logger.info("🎉 ALL RL TESTS PASSED!")
        return 0
    else:
        logger.info("⚠️  SOME RL TESTS FAILED")
        return 1

if __name__ == "__main__":
    sys.exit(main())
