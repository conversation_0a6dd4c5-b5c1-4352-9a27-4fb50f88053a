#!/usr/bin/env python3
"""
Real Interface Components Unit Tests
Tests actual interface components from the running Epinnox v6 system
"""

import sys
import os
import time
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestRealInterfaceComponents:
    """
    Test suite for actual interface components in the running system
    """
    
    def __init__(self):
        self.test_results = []
        self.passed_tests = []
        self.failed_tests = []
        self.interface_components = {}
        
    def discover_interface_components(self):
        """Discover all interface components from the actual system"""
        logger.info("🔍 Discovering Interface Components...")
        
        try:
            # Import the main interface
            from launch_epinnox import EpinnoxTradingInterface
            
            # Create a test instance (without full initialization)
            self.test_interface = EpinnoxTradingInterface(demo_mode=True)
            
            # Discover components
            self.discover_buttons()
            self.discover_labels()
            self.discover_input_controls()
            self.discover_panels()
            
            logger.info(f"✅ Discovered {len(self.interface_components)} interface components")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to discover interface components: {e}")
            return False
    
    def discover_buttons(self):
        """Discover all buttons in the interface"""
        button_components = [
            # Orchestrator Controls
            "orchestrator_enable_btn",
            "orchestrator_emergency_btn",
            
            # Trading Controls
            "limit_buy_btn",
            "market_buy_btn", 
            "limit_sell_btn",
            "market_sell_btn",
            "limit_close_btn",
            "market_close_btn",
            "cancel_all_btn",
            
            # Analysis Controls
            "analyze_button",
            "stop_button",
            
            # Refresh Controls
            "refresh_positions_btn",
            "refresh_orders_btn",
            "refresh_balance_btn",
            
            # Price Fill Buttons
            "fill_bid_btn",
            "fill_ask_btn",
        ]
        
        for button_name in button_components:
            if hasattr(self.test_interface, button_name):
                self.interface_components[button_name] = {
                    'type': 'button',
                    'component': getattr(self.test_interface, button_name),
                    'category': 'buttons'
                }
    
    def discover_labels(self):
        """Discover all labels and status displays"""
        label_components = [
            # Status Labels
            "system_status_label",
            "balance_label",
            "mode_label",
            "orchestrator_status_label",
            "time_label",
            
            # Trading Status
            "current_bid_label",
            "current_ask_label",
            "spread_label",
            "position_pnl_label",
            
            # Portfolio Status
            "portfolio_value_label",
            "portfolio_risk_label",
            "exposure_label",
        ]
        
        for label_name in label_components:
            if hasattr(self.test_interface, label_name):
                self.interface_components[label_name] = {
                    'type': 'label',
                    'component': getattr(self.test_interface, label_name),
                    'category': 'labels'
                }
    
    def discover_input_controls(self):
        """Discover all input controls"""
        input_components = [
            # Symbol Selection
            "symbol_combo",
            
            # Order Parameters
            "order_quantity",
            "order_price",
            "leverage_spin",
            
            # Checkboxes
            "live_data_checkbox",
            "auto_refresh_checkbox",
            "auto_trading_enabled",
            
            # Text Inputs
            "auto_trading_symbols",
            "auto_trading_interval",
        ]
        
        for input_name in input_components:
            if hasattr(self.test_interface, input_name):
                self.interface_components[input_name] = {
                    'type': 'input',
                    'component': getattr(self.test_interface, input_name),
                    'category': 'inputs'
                }
    
    def discover_panels(self):
        """Discover all panels and tables"""
        panel_components = [
            # Tables
            "positions_table",
            "orders_table",
            "trades_table",
            "scanner_table",
            "strategy_queue_table",
            
            # Log Areas
            "log_display",
            "trading_log",
            "orchestrator_log",
        ]
        
        for panel_name in panel_components:
            if hasattr(self.test_interface, panel_name):
                self.interface_components[panel_name] = {
                    'type': 'panel',
                    'component': getattr(self.test_interface, panel_name),
                    'category': 'panels'
                }
    
    def test_button_functionality(self):
        """Test all button functionality"""
        logger.info("🧪 Testing Button Functionality...")
        
        button_tests = [
            # Test button properties and click handlers
            ("orchestrator_enable_btn", "LLM Orchestrator Toggle"),
            ("orchestrator_emergency_btn", "Emergency Stop"),
            ("limit_buy_btn", "Limit Buy Order"),
            ("market_buy_btn", "Market Buy Order"),
            ("limit_sell_btn", "Limit Sell Order"),
            ("market_sell_btn", "Market Sell Order"),
            ("cancel_all_btn", "Cancel All Orders"),
            ("analyze_button", "Analyze Symbol"),
            ("refresh_positions_btn", "Refresh Positions"),
        ]
        
        for button_name, test_description in button_tests:
            try:
                if button_name in self.interface_components:
                    button = self.interface_components[button_name]['component']
                    
                    # Test button properties
                    assert hasattr(button, 'text'), f"Button {button_name} missing text property"
                    assert hasattr(button, 'isEnabled'), f"Button {button_name} missing isEnabled property"
                    assert hasattr(button, 'clicked'), f"Button {button_name} missing clicked signal"
                    
                    # Test button text is not empty
                    button_text = button.text()
                    assert button_text and button_text.strip(), f"Button {button_name} has empty text"
                    
                    self.passed_tests.append(f"Button: {test_description}")
                    logger.info(f"   ✅ {test_description}: PASSED")
                else:
                    self.failed_tests.append(f"Button: {test_description} - Component not found")
                    logger.warning(f"   ⚠️ {test_description}: Component not found")
                    
            except Exception as e:
                self.failed_tests.append(f"Button: {test_description} - {str(e)}")
                logger.error(f"   ❌ {test_description}: FAILED - {e}")
    
    def test_label_updates(self):
        """Test all label update functionality"""
        logger.info("🧪 Testing Label Updates...")
        
        label_tests = [
            ("system_status_label", "System Status Display", "ACTIVE"),
            ("balance_label", "Balance Display", "Equity: $100.00 Free: $100.00"),
            ("orchestrator_status_label", "Orchestrator Status", "Status: ACTIVE"),
            ("current_bid_label", "Current Bid Display", "Bid: 0.17000"),
            ("current_ask_label", "Current Ask Display", "Ask: 0.17001"),
        ]
        
        for label_name, test_description, test_value in label_tests:
            try:
                if label_name in self.interface_components:
                    label = self.interface_components[label_name]['component']
                    
                    # Test label properties
                    assert hasattr(label, 'setText'), f"Label {label_name} missing setText method"
                    assert hasattr(label, 'text'), f"Label {label_name} missing text method"
                    
                    # Test setting and getting text
                    original_text = label.text()
                    label.setText(test_value)
                    assert label.text() == test_value, f"Label {label_name} text update failed"
                    
                    # Restore original text
                    label.setText(original_text)
                    
                    self.passed_tests.append(f"Label: {test_description}")
                    logger.info(f"   ✅ {test_description}: PASSED")
                else:
                    self.failed_tests.append(f"Label: {test_description} - Component not found")
                    logger.warning(f"   ⚠️ {test_description}: Component not found")
                    
            except Exception as e:
                self.failed_tests.append(f"Label: {test_description} - {str(e)}")
                logger.error(f"   ❌ {test_description}: FAILED - {e}")
    
    def test_input_controls(self):
        """Test all input control functionality"""
        logger.info("🧪 Testing Input Controls...")
        
        input_tests = [
            ("symbol_combo", "Symbol Selection", "DOGE/USDT"),
            ("order_quantity", "Order Quantity", 100),
            ("order_price", "Order Price", 0.17),
            ("leverage_spin", "Leverage Setting", 20),
            ("live_data_checkbox", "Live Data Toggle", True),
            ("auto_refresh_checkbox", "Auto Refresh Toggle", True),
        ]
        
        for input_name, test_description, test_value in input_tests:
            try:
                if input_name in self.interface_components:
                    control = self.interface_components[input_name]['component']
                    
                    # Test based on control type
                    if hasattr(control, 'setCurrentText'):  # QComboBox
                        control.addItem(str(test_value))
                        control.setCurrentText(str(test_value))
                        assert control.currentText() == str(test_value)
                        
                    elif hasattr(control, 'setValue'):  # QSpinBox/QDoubleSpinBox
                        control.setValue(test_value)
                        assert control.value() == test_value
                        
                    elif hasattr(control, 'setChecked'):  # QCheckBox
                        control.setChecked(test_value)
                        assert control.isChecked() == test_value
                        
                    elif hasattr(control, 'setText'):  # QLineEdit
                        control.setText(str(test_value))
                        assert control.text() == str(test_value)
                    
                    self.passed_tests.append(f"Input: {test_description}")
                    logger.info(f"   ✅ {test_description}: PASSED")
                else:
                    self.failed_tests.append(f"Input: {test_description} - Component not found")
                    logger.warning(f"   ⚠️ {test_description}: Component not found")
                    
            except Exception as e:
                self.failed_tests.append(f"Input: {test_description} - {str(e)}")
                logger.error(f"   ❌ {test_description}: FAILED - {e}")
    
    def test_method_availability(self):
        """Test that all expected methods are available"""
        logger.info("🧪 Testing Method Availability...")
        
        expected_methods = [
            ("toggle_orchestrator", "Toggle LLM Orchestrator"),
            ("emergency_stop_orchestrator", "Emergency Stop Orchestrator"),
            ("place_limit_buy", "Place Limit Buy Order"),
            ("place_market_buy", "Place Market Buy Order"),
            ("place_limit_sell", "Place Limit Sell Order"),
            ("place_market_sell", "Place Market Sell Order"),
            ("cancel_all_orders", "Cancel All Orders"),
            ("close_all_positions", "Close All Positions"),
            ("refresh_positions", "Refresh Positions"),
            ("refresh_orders", "Refresh Orders"),
            ("refresh_balance", "Refresh Balance"),
            ("update_balance_display", "Update Balance Display"),
            ("log_message", "Log Message"),
        ]
        
        for method_name, test_description in expected_methods:
            try:
                if hasattr(self.test_interface, method_name):
                    method = getattr(self.test_interface, method_name)
                    assert callable(method), f"Method {method_name} is not callable"
                    
                    self.passed_tests.append(f"Method: {test_description}")
                    logger.info(f"   ✅ {test_description}: AVAILABLE")
                else:
                    self.failed_tests.append(f"Method: {test_description} - Method not found")
                    logger.warning(f"   ⚠️ {test_description}: Method not found")
                    
            except Exception as e:
                self.failed_tests.append(f"Method: {test_description} - {str(e)}")
                logger.error(f"   ❌ {test_description}: FAILED - {e}")
    
    def generate_comprehensive_report(self):
        """Generate comprehensive test report"""
        total_tests = len(self.passed_tests) + len(self.failed_tests)
        pass_rate = (len(self.passed_tests) / total_tests * 100) if total_tests > 0 else 0
        
        # Component breakdown
        component_breakdown = {}
        for comp_name, comp_info in self.interface_components.items():
            category = comp_info['category']
            if category not in component_breakdown:
                component_breakdown[category] = 0
            component_breakdown[category] += 1
        
        report = f"""
🧪 REAL INTERFACE COMPONENTS TEST REPORT
{'='*60}
Test Date: {time.strftime('%Y-%m-%d %H:%M:%S')}
Total Tests: {total_tests}
Passed: {len(self.passed_tests)}
Failed: {len(self.failed_tests)}
Pass Rate: {pass_rate:.1f}%

📊 DISCOVERED COMPONENTS:
{chr(10).join([f"   {category.title()}: {count}" for category, count in component_breakdown.items()])}
Total Components: {len(self.interface_components)}

✅ PASSED TESTS ({len(self.passed_tests)}):
{chr(10).join([f"   ✅ {test}" for test in self.passed_tests[:20]])}
{'   ... and more' if len(self.passed_tests) > 20 else ''}

❌ FAILED TESTS ({len(self.failed_tests)}):
{chr(10).join([f"   ❌ {test}" for test in self.failed_tests])}

🎯 TEST CATEGORIES COMPLETED:
   🔘 Button Functionality: Click handlers and properties
   🔘 Label Updates: Text setting and display
   🔘 Input Controls: Value setting and retrieval
   🔘 Method Availability: Function accessibility
   🔘 Component Discovery: Interface structure mapping

{'🎉 ALL INTERFACE COMPONENTS OPERATIONAL' if len(self.failed_tests) == 0 else '⚠️ SOME COMPONENTS NEED ATTENTION'}
"""
        return report
    
    def run_all_tests(self):
        """Run all interface component tests"""
        logger.info("🚀 Starting Real Interface Components Testing...")
        
        # Discover components first
        if not self.discover_interface_components():
            return False
        
        # Run all test categories
        self.test_button_functionality()
        self.test_label_updates()
        self.test_input_controls()
        self.test_method_availability()
        
        # Generate report
        report = self.generate_comprehensive_report()
        print(report)
        
        return len(self.failed_tests) == 0

def main():
    """Main test execution"""
    test_suite = TestRealInterfaceComponents()
    success = test_suite.run_all_tests()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
