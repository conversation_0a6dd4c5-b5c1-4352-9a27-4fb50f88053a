#!/usr/bin/env python3
"""
Comprehensive System Validation for Autonomous Trading System
Tests all components together to achieve 100% test pass rate
"""

import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveSystemValidator:
    """Comprehensive validator for the autonomous trading system"""
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
    async def run_all_tests(self):
        """Run all comprehensive system tests"""
        logger.info("🚀 Starting Comprehensive System Validation")
        logger.info("=" * 80)

        # Test 1: Core Component Imports
        self.test_core_imports()

        # Test 2: Configuration System
        self.test_configuration_system()

        # Test 3: Mock Exchange Integration
        self.test_mock_exchange_integration()

        # Test 4: Portfolio Management (FIXED: Added await)
        await self.test_portfolio_management()

        # Test 5: Performance Tracking
        self.test_performance_tracking()

        # Test 6: ML Components
        self.test_ml_components()

        # Test 7: RL Components
        self.test_rl_components()

        # Test 8: Autonomous Controller
        self.test_autonomous_controller()

        # Test 9: End-to-End Integration
        self.test_end_to_end_integration()
        
        # Test 10: Symbol Scanner
        self.test_symbol_scanner()
        
        # Generate final report
        self.generate_final_report()
        
        return self.passed_tests == self.total_tests
    
    def test_core_imports(self):
        """Test that all core components can be imported"""
        logger.info("🧪 Testing Core Component Imports...")
        
        components = [
            ('execution.autonomous_executor', 'AutonomousTradeExecutor'),
            ('portfolio.portfolio_manager', 'PortfolioManager'),
            ('monitoring.performance_tracker', 'PerformanceTracker'),
            ('ml.rl_agent', 'TradingRLAgent'),
            ('ml.trading_env', 'TradingEnvironment'),
            ('ml.adaptive_updater', 'OnlineLearningManager'),
            ('core.autonomous_controller', 'AutonomousController'),
            ('tests.mocks.mock_exchange', 'MockExchange'),
            ('config.autonomous_config', 'AutonomousConfigManager'),
            ('symbol_scanner', 'SymbolScanner')
        ]
        
        for module_name, class_name in components:
            self.total_tests += 1
            try:
                module = __import__(module_name, fromlist=[class_name])
                cls = getattr(module, class_name)
                self.test_results[f"Import_{class_name}"] = "✅ PASS"
                self.passed_tests += 1
                logger.info(f"✅ {class_name} imported successfully")
            except Exception as e:
                self.test_results[f"Import_{class_name}"] = f"❌ FAIL: {e}"
                self.failed_tests += 1
                logger.error(f"❌ {class_name} import failed: {e}")
    
    def test_configuration_system(self):
        """Test configuration management system"""
        logger.info("🧪 Testing Configuration System...")
        
        self.total_tests += 1
        try:
            from config.autonomous_config import AutonomousConfigManager
            from config.config import get_autonomous_config

            # Test configuration creation (FIXED: Removed autonomous_mode parameter)
            config_manager = AutonomousConfigManager()

            # Test configuration access
            autonomous_config = get_autonomous_config()

            # Test configuration validation
            validation = config_manager.validate_config()

            if validation['valid'] and autonomous_config is not None:
                self.test_results["Configuration_System"] = "✅ PASS"
                self.passed_tests += 1
                logger.info("✅ Configuration system working correctly")
            else:
                self.test_results["Configuration_System"] = f"❌ FAIL: Validation issues: {validation.get('issues', [])}"
                self.failed_tests += 1
                
        except Exception as e:
            self.test_results["Configuration_System"] = f"❌ FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ Configuration system test failed: {e}")
    
    def test_mock_exchange_integration(self):
        """Test mock exchange integration"""
        logger.info("🧪 Testing Mock Exchange Integration...")
        
        self.total_tests += 1
        try:
            from tests.mocks.mock_exchange import MockExchange
            from execution.autonomous_executor import AutonomousTradeExecutor
            
            # Create mock exchange
            mock_exchange = MockExchange(initial_balance=10000.0)
            
            # Test basic operations
            balance = mock_exchange.fetch_balance()
            
            # Create autonomous executor
            executor = AutonomousTradeExecutor(mock_exchange)
            
            if balance and 'total' in balance:
                self.test_results["MockExchange_Integration"] = "✅ PASS"
                self.passed_tests += 1
                logger.info("✅ Mock exchange integration successful")
            else:
                self.test_results["MockExchange_Integration"] = "❌ FAIL: Invalid balance format"
                self.failed_tests += 1
                
        except Exception as e:
            self.test_results["MockExchange_Integration"] = f"❌ FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ Mock exchange integration failed: {e}")
    
    async def test_portfolio_management(self):
        """Test portfolio management functionality"""
        logger.info("🧪 Testing Portfolio Management...")

        self.total_tests += 1
        try:
            from portfolio.portfolio_manager import PortfolioManager

            # Create portfolio manager
            portfolio = PortfolioManager(initial_balance=10000.0)

            # Test position checking (FIXED: Added await)
            check_result = await portfolio.can_open_position('BTC/USDT:USDT', 1000.0, 2.0)

            # Test portfolio metrics
            total_value = portfolio.get_total_value()
            risk = portfolio.calculate_total_portfolio_risk()

            if check_result['allowed'] and total_value > 0:
                self.test_results["Portfolio_Management"] = "✅ PASS"
                self.passed_tests += 1
                logger.info("✅ Portfolio management working correctly")
            else:
                self.test_results["Portfolio_Management"] = f"❌ FAIL: {check_result.get('reason', 'Unknown error')}"
                self.failed_tests += 1
                
        except Exception as e:
            self.test_results["Portfolio_Management"] = f"❌ FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ Portfolio management test failed: {e}")
    
    def test_performance_tracking(self):
        """Test performance tracking functionality"""
        logger.info("🧪 Testing Performance Tracking...")
        
        self.total_tests += 1
        try:
            from monitoring.performance_tracker import PerformanceTracker
            
            # Create performance tracker
            tracker = PerformanceTracker()
            
            # Test trade recording
            trade_data = {
                'timestamp': datetime.now(),
                'symbol': 'BTC/USDT:USDT',
                'decision': 'LONG',
                'confidence': 0.75,
                'entry_price': 50000.0,
                'exit_price': 51000.0,
                'position_size': 0.1,
                'leverage': 2.0,
                'pnl_usd': 100.0,
                'pnl_pct': 2.0,
                'duration_minutes': 30
            }
            
            tracker.record_trade(trade_data)
            
            # Test metrics calculation
            daily_metrics = tracker.calculate_daily_metrics()
            
            self.test_results["Performance_Tracking"] = "✅ PASS"
            self.passed_tests += 1
            logger.info("✅ Performance tracking working correctly")
            
        except Exception as e:
            self.test_results["Performance_Tracking"] = f"❌ FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ Performance tracking test failed: {e}")
    
    def test_ml_components(self):
        """Test ML components functionality"""
        logger.info("🧪 Testing ML Components...")
        
        self.total_tests += 1
        try:
            from ml.adaptive_updater import OnlineLearningManager
            from ml.models import MLModelManager
            from monitoring.performance_tracker import PerformanceTracker
            
            # Create components
            tracker = PerformanceTracker()
            ml_manager = MLModelManager()
            online_manager = OnlineLearningManager(ml_manager, tracker)
            
            # Test adaptive prediction
            mock_predictions = {
                'svm': {'prediction': 0.7, 'confidence': 0.8},
                'random_forest': {'prediction': 0.6, 'confidence': 0.7},
                'lstm': {'prediction': 0.8, 'confidence': 0.9}
            }
            
            adaptive_pred = online_manager.get_adaptive_prediction(mock_predictions)
            
            if 'prediction' in adaptive_pred and 'confidence' in adaptive_pred:
                self.test_results["ML_Components"] = "✅ PASS"
                self.passed_tests += 1
                logger.info("✅ ML components working correctly")
            else:
                self.test_results["ML_Components"] = "❌ FAIL: Invalid prediction format"
                self.failed_tests += 1
                
        except Exception as e:
            self.test_results["ML_Components"] = f"❌ FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ ML components test failed: {e}")
    
    def test_rl_components(self):
        """Test RL components functionality"""
        logger.info("🧪 Testing RL Components...")
        
        self.total_tests += 1
        try:
            from ml.trading_env import TradingEnvironment
            from ml.rl_agent import TradingRLAgent
            from data.exchange import ExchangeDataFetcher
            
            # Create components
            data_fetcher = ExchangeDataFetcher()
            env = TradingEnvironment(data_fetcher, initial_balance=1000.0, max_steps=100)
            agent = TradingRLAgent(env, model_type='PPO')
            
            # Test environment-agent interaction
            obs = env.reset()
            # FIXED: Handle different return formats from agent.predict()
            predict_result = agent.predict(obs, deterministic=True)
            if isinstance(predict_result, tuple) and len(predict_result) >= 2:
                action = predict_result[0]
            else:
                action = predict_result

            # FIXED: Handle both 4 and 5 return values from env.step()
            step_result = env.step(action)
            if len(step_result) == 5:
                obs, reward, done, truncated, info = step_result
            else:
                obs, reward, done, info = step_result
            
            self.test_results["RL_Components"] = "✅ PASS"
            self.passed_tests += 1
            logger.info("✅ RL components working correctly")
            
        except Exception as e:
            self.test_results["RL_Components"] = f"❌ FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ RL components test failed: {e}")
    
    def test_autonomous_controller(self):
        """Test autonomous controller functionality"""
        logger.info("🧪 Testing Autonomous Controller...")
        
        self.total_tests += 1
        try:
            from core.autonomous_controller import AutonomousController
            from tests.mocks.mock_exchange import MockExchange
            
            # Create mock exchange and controller
            mock_exchange = MockExchange(initial_balance=10000.0)
            config = {
                'initial_balance': 10000.0,
                'max_positions': 3,
                'min_confidence': 0.65,
                'use_rl': False
            }
            
            controller = AutonomousController(mock_exchange, config)
            
            self.test_results["Autonomous_Controller"] = "✅ PASS"
            self.passed_tests += 1
            logger.info("✅ Autonomous controller working correctly")
            
        except Exception as e:
            self.test_results["Autonomous_Controller"] = f"❌ FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ Autonomous controller test failed: {e}")
    
    def test_symbol_scanner(self):
        """Test symbol scanner functionality"""
        logger.info("🧪 Testing Symbol Scanner...")
        
        self.total_tests += 1
        try:
            from symbol_scanner import SymbolScannerConfig
            from tests.mocks.mock_exchange import MockExchange
            
            # Create mock exchange and scanner
            mock_exchange = MockExchange(initial_balance=10000.0)
            scanner = SymbolScannerConfig.create_scanner(
                market_api=mock_exchange,
                mode='scalping',
                update_interval=5.0
            )
            
            # Test scanner interval was set correctly
            if scanner.scan_interval == 5.0:
                self.test_results["Symbol_Scanner"] = "✅ PASS"
                self.passed_tests += 1
                logger.info("✅ Symbol scanner working correctly")
            else:
                self.test_results["Symbol_Scanner"] = "❌ FAIL: Update interval not applied"
                self.failed_tests += 1
                
        except Exception as e:
            self.test_results["Symbol_Scanner"] = f"❌ FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ Symbol scanner test failed: {e}")
    
    def test_end_to_end_integration(self):
        """Test end-to-end system integration"""
        logger.info("🧪 Testing End-to-End Integration...")
        
        self.total_tests += 1
        try:
            # Import all components
            from tests.mocks.mock_exchange import MockExchange
            from execution.autonomous_executor import AutonomousTradeExecutor
            from portfolio.portfolio_manager import PortfolioManager
            from monitoring.performance_tracker import PerformanceTracker
            from ml.adaptive_updater import OnlineLearningManager
            from ml.models import MLModelManager
            from config.autonomous_config import AutonomousConfigManager
            
            # Create integrated system
            mock_exchange = MockExchange(initial_balance=10000.0)
            portfolio = PortfolioManager(initial_balance=10000.0)
            tracker = PerformanceTracker()
            ml_manager = MLModelManager()
            online_manager = OnlineLearningManager(ml_manager, tracker)
            executor = AutonomousTradeExecutor(mock_exchange, portfolio_manager=portfolio)
            config_manager = AutonomousConfigManager()
            
            # Test system integration
            config = config_manager.get_all_config()
            validation = config_manager.validate_config()
            
            if validation['valid'] and config is not None:
                self.test_results["EndToEnd_Integration"] = "✅ PASS"
                self.passed_tests += 1
                logger.info("✅ End-to-end integration successful")
            else:
                self.test_results["EndToEnd_Integration"] = f"❌ FAIL: Integration issues: {validation.get('issues', [])}"
                self.failed_tests += 1
                
        except Exception as e:
            self.test_results["EndToEnd_Integration"] = f"❌ FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ End-to-end integration test failed: {e}")
    
    def generate_final_report(self):
        """Generate final validation report"""
        logger.info("=" * 80)
        logger.info("📊 COMPREHENSIVE SYSTEM VALIDATION REPORT")
        logger.info("=" * 80)
        
        # Display all test results
        for test_name, result in self.test_results.items():
            logger.info(f"{test_name}: {result}")
        
        # Calculate success rate
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        logger.info("=" * 80)
        logger.info(f"📈 TOTAL TESTS: {self.total_tests}")
        logger.info(f"✅ PASSED: {self.passed_tests}")
        logger.info(f"❌ FAILED: {self.failed_tests}")
        logger.info(f"📊 SUCCESS RATE: {success_rate:.1f}%")
        
        # Deployment readiness assessment
        if success_rate == 100.0:
            logger.info("🎉 SYSTEM VALIDATION: 100% PASS RATE ACHIEVED!")
            logger.info("🚀 AUTONOMOUS TRADING SYSTEM READY FOR DEPLOYMENT")
        elif success_rate >= 90.0:
            logger.info("✅ SYSTEM VALIDATION: HIGH SUCCESS RATE")
            logger.info("⚠️  MINOR ISSUES DETECTED - REVIEW BEFORE DEPLOYMENT")
        else:
            logger.info("❌ SYSTEM VALIDATION: CRITICAL ISSUES DETECTED")
            logger.info("🛑 SYSTEM NOT READY FOR DEPLOYMENT")
        
        logger.info("=" * 80)

async def main():
    """Run comprehensive system validation"""
    validator = ComprehensiveSystemValidator()
    success = await validator.run_all_tests()

    return 0 if success else 1

if __name__ == "__main__":
    import asyncio
    sys.exit(asyncio.run(main()))
