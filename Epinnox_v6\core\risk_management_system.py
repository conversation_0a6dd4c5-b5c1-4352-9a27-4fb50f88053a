#!/usr/bin/env python3
"""
Risk Management System for Epinnox v6
Comprehensive risk management with daily loss limits, position size limits, 
correlation checks, and emergency stop mechanisms
"""

import logging
import time
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class RiskLimits:
    """Risk limit configuration"""
    max_daily_loss_pct: float = 5.0      # Maximum daily loss percentage
    max_position_size_pct: float = 10.0   # Maximum position size as % of balance
    max_leverage: int = 20                # Maximum leverage allowed
    max_daily_trades: int = 10            # Maximum trades per day
    max_concurrent_positions: int = 3     # Maximum concurrent positions
    max_correlation_exposure: float = 0.7 # Maximum correlation exposure
    emergency_stop_loss_pct: float = 10.0 # Emergency stop at 10% account loss

@dataclass
class RiskMetrics:
    """Current risk metrics"""
    daily_pnl_pct: float = 0.0
    current_positions: int = 0
    daily_trades: int = 0
    max_position_risk_pct: float = 0.0
    correlation_risk: float = 0.0
    account_drawdown_pct: float = 0.0

class RiskManagementSystem:
    """
    Comprehensive risk management system for autonomous trading
    """
    
    def __init__(self, risk_limits: Optional[RiskLimits] = None):
        """Initialize risk management system"""
        self.risk_limits = risk_limits or RiskLimits()
        self.risk_metrics = RiskMetrics()
        
        # Daily tracking
        self.daily_stats = {
            'date': date.today(),
            'trades': 0,
            'pnl': 0.0,
            'max_drawdown': 0.0,
            'starting_balance': 0.0
        }
        
        # Emergency state
        self.emergency_stop_active = False
        self.emergency_reasons = []
        
        # Position tracking
        self.positions = {}
        self.correlation_matrix = {}
        
        logger.info("🛡️ Risk Management System initialized")
    
    def validate_trade(self, symbol: str, side: str, quantity: float, 
                      price: float, leverage: int, balance: float) -> Tuple[bool, str]:
        """
        Validate if a trade meets risk management criteria
        Returns (is_valid, reason)
        """
        try:
            # Check emergency stop
            if self.emergency_stop_active:
                return False, "Emergency stop active"
            
            # Check daily loss limit
            if not self._check_daily_loss_limit(balance):
                return False, f"Daily loss limit exceeded ({self.risk_limits.max_daily_loss_pct}%)"
            
            # Check daily trade limit
            if not self._check_daily_trade_limit():
                return False, f"Daily trade limit exceeded ({self.risk_limits.max_daily_trades})"
            
            # Check position size limit
            position_value = quantity * price
            position_pct = (position_value / balance) * 100
            if position_pct > self.risk_limits.max_position_size_pct:
                return False, f"Position size too large ({position_pct:.1f}% > {self.risk_limits.max_position_size_pct}%)"
            
            # Check leverage limit
            if leverage > self.risk_limits.max_leverage:
                return False, f"Leverage too high ({leverage}x > {self.risk_limits.max_leverage}x)"
            
            # Check concurrent positions limit
            if len(self.positions) >= self.risk_limits.max_concurrent_positions:
                return False, f"Too many concurrent positions ({len(self.positions)} >= {self.risk_limits.max_concurrent_positions})"
            
            # Check correlation risk
            if not self._check_correlation_risk(symbol, side, position_value):
                return False, f"Correlation risk too high (>{self.risk_limits.max_correlation_exposure})"
            
            return True, "Trade validated"
            
        except Exception as e:
            logger.error(f"Error validating trade: {e}")
            return False, f"Validation error: {e}"
    
    def update_position(self, symbol: str, side: str, quantity: float, 
                       price: float, leverage: int):
        """Update position tracking"""
        try:
            position_id = f"{symbol}_{side}"
            self.positions[position_id] = {
                'symbol': symbol,
                'side': side,
                'quantity': quantity,
                'price': price,
                'leverage': leverage,
                'value': quantity * price,
                'timestamp': datetime.now()
            }
            
            logger.info(f"📊 Position updated: {position_id}")
            
        except Exception as e:
            logger.error(f"Error updating position: {e}")
    
    def remove_position(self, symbol: str, side: str):
        """Remove position from tracking"""
        try:
            position_id = f"{symbol}_{side}"
            if position_id in self.positions:
                del self.positions[position_id]
                logger.info(f"📊 Position removed: {position_id}")
                
        except Exception as e:
            logger.error(f"Error removing position: {e}")
    
    def update_daily_pnl(self, pnl_change: float, current_balance: float):
        """Update daily P&L tracking"""
        try:
            # Reset daily stats if new day
            today = date.today()
            if self.daily_stats['date'] != today:
                self.daily_stats = {
                    'date': today,
                    'trades': 0,
                    'pnl': 0.0,
                    'max_drawdown': 0.0,
                    'starting_balance': current_balance
                }
            
            # Update P&L
            self.daily_stats['pnl'] += pnl_change
            
            # Calculate percentage
            if self.daily_stats['starting_balance'] > 0:
                pnl_pct = (self.daily_stats['pnl'] / self.daily_stats['starting_balance']) * 100
                self.risk_metrics.daily_pnl_pct = pnl_pct
                
                # Update max drawdown
                if pnl_pct < self.daily_stats['max_drawdown']:
                    self.daily_stats['max_drawdown'] = pnl_pct
                    self.risk_metrics.account_drawdown_pct = abs(pnl_pct)
            
            # Check emergency stop conditions
            self._check_emergency_conditions(current_balance)
            
        except Exception as e:
            logger.error(f"Error updating daily P&L: {e}")
    
    def increment_daily_trades(self):
        """Increment daily trade counter"""
        try:
            today = date.today()
            if self.daily_stats['date'] != today:
                self.daily_stats['trades'] = 0
                self.daily_stats['date'] = today
            
            self.daily_stats['trades'] += 1
            self.risk_metrics.daily_trades = self.daily_stats['trades']
            
        except Exception as e:
            logger.error(f"Error incrementing daily trades: {e}")
    
    def activate_emergency_stop(self, reason: str):
        """Activate emergency stop"""
        try:
            self.emergency_stop_active = True
            self.emergency_reasons.append({
                'reason': reason,
                'timestamp': datetime.now()
            })
            
            logger.critical(f"🚨 EMERGENCY STOP ACTIVATED: {reason}")
            
        except Exception as e:
            logger.error(f"Error activating emergency stop: {e}")
    
    def deactivate_emergency_stop(self):
        """Deactivate emergency stop (manual override)"""
        try:
            self.emergency_stop_active = False
            logger.warning("⚠️ Emergency stop deactivated (manual override)")
            
        except Exception as e:
            logger.error(f"Error deactivating emergency stop: {e}")
    
    def get_risk_status(self) -> Dict:
        """Get current risk status"""
        try:
            return {
                'emergency_stop_active': self.emergency_stop_active,
                'emergency_reasons': self.emergency_reasons,
                'risk_metrics': self.risk_metrics,
                'daily_stats': self.daily_stats,
                'positions_count': len(self.positions),
                'risk_limits': self.risk_limits
            }
            
        except Exception as e:
            logger.error(f"Error getting risk status: {e}")
            return {}
    
    def _check_daily_loss_limit(self, current_balance: float) -> bool:
        """Check if daily loss limit is exceeded"""
        try:
            if self.daily_stats['starting_balance'] > 0:
                loss_pct = abs(self.risk_metrics.daily_pnl_pct)
                return loss_pct < self.risk_limits.max_daily_loss_pct
            return True
            
        except Exception as e:
            logger.error(f"Error checking daily loss limit: {e}")
            return True
    
    def _check_daily_trade_limit(self) -> bool:
        """Check if daily trade limit is exceeded"""
        try:
            return self.daily_stats['trades'] < self.risk_limits.max_daily_trades
            
        except Exception as e:
            logger.error(f"Error checking daily trade limit: {e}")
            return True
    
    def _check_correlation_risk(self, symbol: str, side: str, position_value: float) -> bool:
        """Check correlation risk with existing positions"""
        try:
            # Simplified correlation check
            # In a real implementation, you'd use actual correlation data
            
            # Check if we already have a position in the same direction
            same_direction_value = 0
            for pos in self.positions.values():
                if pos['side'] == side:
                    same_direction_value += pos['value']
            
            # Calculate correlation exposure
            total_exposure = same_direction_value + position_value
            if total_exposure > 0:
                correlation_ratio = same_direction_value / total_exposure
                return correlation_ratio < self.risk_limits.max_correlation_exposure
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking correlation risk: {e}")
            return True
    
    def _check_emergency_conditions(self, current_balance: float):
        """Check for emergency stop conditions"""
        try:
            # Check account drawdown
            if self.risk_metrics.account_drawdown_pct >= self.risk_limits.emergency_stop_loss_pct:
                self.activate_emergency_stop(f"Account drawdown {self.risk_metrics.account_drawdown_pct:.1f}% >= {self.risk_limits.emergency_stop_loss_pct}%")
            
            # Check daily loss
            if abs(self.risk_metrics.daily_pnl_pct) >= self.risk_limits.emergency_stop_loss_pct:
                self.activate_emergency_stop(f"Daily loss {abs(self.risk_metrics.daily_pnl_pct):.1f}% >= {self.risk_limits.emergency_stop_loss_pct}%")
            
        except Exception as e:
            logger.error(f"Error checking emergency conditions: {e}")
