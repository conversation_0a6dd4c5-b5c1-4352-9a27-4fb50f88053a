#!/usr/bin/env python3
"""
Test Unicode Encoding and Portfolio Data Fixes
Validates that both critical issues have been resolved
"""

import sys
import os
import logging
import json
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_unicode_encoding_fix():
    """Test Unicode encoding fixes"""
    print("🧪 TESTING UNICODE ENCODING FIXES")
    print("=" * 50)
    
    try:
        # Test 1: Import the fixed modules
        print("\n📋 Test 1: Importing modules with Unicode fixes...")
        
        try:
            from start_live_trading import setup_unicode_console, SafeUnicodeFormatter
            print("   ✅ start_live_trading Unicode fixes imported successfully")
        except ImportError as e:
            print(f"   ❌ Failed to import start_live_trading fixes: {e}")
            return False
        
        try:
            from launch_epinnox import setup_unicode_console as setup_unicode_console_2
            print("   ✅ launch_epinnox Unicode fixes imported successfully")
        except ImportError as e:
            print(f"   ❌ Failed to import launch_epinnox fixes: {e}")
            return False
        
        # Test 2: Setup Unicode console
        print("\n🖥️ Test 2: Setting up Unicode console...")
        unicode_ok = setup_unicode_console()
        print(f"   Unicode console setup: {'✅ SUCCESS' if unicode_ok else '⚠️ PARTIAL'}")
        
        # Test 3: Test SafeUnicodeFormatter
        print("\n🔤 Test 3: Testing SafeUnicodeFormatter...")
        formatter = SafeUnicodeFormatter('%(levelname)s - %(message)s')
        
        # Create a test log record
        import logging
        record = logging.LogRecord(
            name='test',
            level=logging.INFO,
            pathname='',
            lineno=0,
            msg='🚀 Test message with emojis ✅ 📊 💰',
            args=(),
            exc_info=None
        )
        
        formatted_message = formatter.format(record)
        print(f"   Original: 🚀 Test message with emojis ✅ 📊 💰")
        print(f"   Formatted: {formatted_message}")
        
        # Test 4: Test emoji replacement
        print("\n🎭 Test 4: Testing emoji replacement...")
        test_emojis = ['🚀', '✅', '❌', '📊', '💰', '🎯', '⚠️']
        
        for emoji in test_emojis:
            if emoji in formatter.EMOJI_MAP:
                replacement = formatter.EMOJI_MAP[emoji]
                print(f"   {emoji} → {replacement}")
            else:
                print(f"   ❌ {emoji} not in emoji map")
        
        # Test 5: Test actual logging with emojis
        print("\n📝 Test 5: Testing actual logging with emojis...")
        
        # Setup test logger
        logger = logging.getLogger('unicode_test')
        logger.setLevel(logging.INFO)
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Add console handler with safe formatter
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # Test logging various emojis
        test_messages = [
            "🚀 System startup successful",
            "✅ Configuration loaded",
            "📊 Market data received",
            "💰 Balance updated",
            "⚠️ Warning message",
            "❌ Error occurred"
        ]
        
        for message in test_messages:
            try:
                logger.info(message)
                print(f"   ✅ Logged: {message}")
            except UnicodeEncodeError as e:
                print(f"   ❌ Unicode error: {e}")
                return False
        
        print("\n✅ UNICODE ENCODING TESTS PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Unicode encoding test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_portfolio_data_fix():
    """Test portfolio data access fixes"""
    print("\n🧪 TESTING PORTFOLIO DATA FIXES")
    print("=" * 50)
    
    try:
        # Test 1: Import portfolio manager
        print("\n📋 Test 1: Importing portfolio manager...")
        
        try:
            from portfolio.portfolio_manager import PortfolioManager
            print("   ✅ PortfolioManager imported successfully")
        except ImportError as e:
            print(f"   ❌ Failed to import PortfolioManager: {e}")
            return False
        
        # Test 2: Create portfolio manager instance
        print("\n💼 Test 2: Creating portfolio manager instance...")
        
        try:
            portfolio = PortfolioManager(initial_balance=100.0)
            print("   ✅ PortfolioManager instance created")
        except Exception as e:
            print(f"   ❌ Failed to create PortfolioManager: {e}")
            return False
        
        # Test 3: Get portfolio summary
        print("\n📊 Test 3: Getting portfolio summary...")
        
        try:
            summary = portfolio.get_portfolio_summary()
            print("   ✅ Portfolio summary retrieved")
            print(f"   📋 Summary keys: {list(summary.keys())}")
        except Exception as e:
            print(f"   ❌ Failed to get portfolio summary: {e}")
            return False
        
        # Test 4: Verify correct key names
        print("\n🔑 Test 4: Verifying correct key names...")
        
        expected_keys = [
            'balance',  # This is the correct key (not 'current_balance')
            'total_value',
            'unrealized_pnl',
            'daily_pnl',
            'max_drawdown',
            'total_return',
            'open_positions',
            'portfolio_risk',
            'positions'
        ]
        
        missing_keys = []
        for key in expected_keys:
            if key in summary:
                print(f"   ✅ {key}: {summary[key]}")
            else:
                print(f"   ❌ Missing key: {key}")
                missing_keys.append(key)
        
        if missing_keys:
            print(f"   ❌ Missing keys: {missing_keys}")
            return False
        
        # Test 5: Verify 'current_balance' is NOT in the summary
        print("\n🚫 Test 5: Verifying 'current_balance' is not used...")
        
        if 'current_balance' in summary:
            print("   ❌ 'current_balance' key found (should not exist)")
            return False
        else:
            print("   ✅ 'current_balance' key correctly not present")
        
        # Test 6: Test the fixed code path
        print("\n🔧 Test 6: Testing fixed code path...")
        
        try:
            # Simulate the fixed code from start_live_trading.py
            balance_value = summary['balance']  # This should work now
            total_value = summary['total_value']
            
            print(f"   ✅ Balance access works: ${balance_value:.2f}")
            print(f"   ✅ Total value access works: ${total_value:.2f}")
            
            # This would have failed before the fix:
            # balance_value = summary['current_balance']  # KeyError!
            
        except KeyError as e:
            print(f"   ❌ Key error accessing portfolio data: {e}")
            return False
        
        print("\n✅ PORTFOLIO DATA TESTS PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Portfolio data test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_comprehensive_fix_validation():
    """Run comprehensive validation of both fixes"""
    print("🔧 COMPREHENSIVE FIX VALIDATION")
    print("=" * 60)
    
    # Test results
    unicode_test_passed = test_unicode_encoding_fix()
    portfolio_test_passed = test_portfolio_data_fix()
    
    # Generate report
    print("\n📊 FINAL TEST RESULTS")
    print("=" * 60)
    
    print(f"🔤 Unicode Encoding Fix: {'✅ PASSED' if unicode_test_passed else '❌ FAILED'}")
    print(f"📊 Portfolio Data Fix: {'✅ PASSED' if portfolio_test_passed else '❌ FAILED'}")
    
    overall_success = unicode_test_passed and portfolio_test_passed
    print(f"\n🎯 OVERALL RESULT: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 Both critical issues have been successfully resolved!")
        print("   • Unicode encoding errors are fixed")
        print("   • Portfolio data access is corrected")
        print("   • System should run without encoding crashes")
        print("   • Balance tracking will work properly")
    else:
        print("\n⚠️ Some issues remain - please review the test output above")
    
    return overall_success

def create_comprehensive_gui_test_suite():
    """Create comprehensive GUI test suite"""
    print("\n🧪 CREATING COMPREHENSIVE GUI TEST SUITE")
    print("=" * 60)

    try:
        # Test 1: Check PyQt5 availability
        print("\n📋 Test 1: Checking PyQt5 availability...")

        try:
            from PyQt5.QtWidgets import QApplication, QWidget, QPushButton, QLabel, QTableWidget
            from PyQt5.QtCore import QTimer, Qt
            print("   ✅ PyQt5 imported successfully")
            pyqt_available = True
        except ImportError as e:
            print(f"   ❌ PyQt5 not available: {e}")
            pyqt_available = False
            return False

        # Test 2: Create QApplication
        print("\n🖥️ Test 2: Creating QApplication...")

        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            print("   ✅ QApplication created successfully")
        except Exception as e:
            print(f"   ❌ Failed to create QApplication: {e}")
            return False

        # Test 3: Test main GUI components
        print("\n🏠 Test 3: Testing main GUI components...")

        try:
            from gui.main_window import TradingSystemGUI
            main_window = TradingSystemGUI()
            print("   ✅ Main window created successfully")

            # Test window properties
            if hasattr(main_window, 'windowTitle'):
                title = main_window.windowTitle()
                print(f"   ✅ Window title: {title}")

            # Test key components
            components_to_test = [
                ('tab_widget', 'Tab Widget'),
                ('start_button', 'Start Button'),
                ('stop_button', 'Stop Button'),
                ('status_label', 'Status Label')
            ]

            for attr_name, display_name in components_to_test:
                if hasattr(main_window, attr_name):
                    print(f"   ✅ {display_name} available")
                else:
                    print(f"   ❌ {display_name} missing")

            main_window.close()

        except Exception as e:
            print(f"   ❌ Main GUI component test failed: {e}")
            return False

        # Test 4: Test individual tab components
        print("\n📑 Test 4: Testing individual tab components...")

        tab_tests = [
            ('gui.live_trading_tab', 'LiveTradingTab', 'Live Trading Tab'),
            ('gui.auto_trader_tab', 'AutoTraderTab', 'Auto Trader Tab'),
            ('gui.manual_trader_tab', 'ManualTraderTab', 'Manual Trader Tab'),
            ('gui.performance_dashboard_tab', 'PerformanceDashboardTab', 'Performance Dashboard Tab'),
            ('gui.scalping_scanner_tab', 'ScalpingScannerTab', 'Scalping Scanner Tab'),
            ('gui.settings_tab', 'SettingsTab', 'Settings Tab')
        ]

        for module_name, class_name, display_name in tab_tests:
            try:
                module = __import__(module_name, fromlist=[class_name])
                tab_class = getattr(module, class_name)
                tab_instance = tab_class()
                print(f"   ✅ {display_name} created successfully")
                tab_instance.close() if hasattr(tab_instance, 'close') else None
            except Exception as e:
                print(f"   ❌ {display_name} failed: {e}")

        # Test 5: Test widget interactions
        print("\n🔄 Test 5: Testing widget interactions...")

        try:
            # Create test widgets
            test_button = QPushButton("Test Button")
            test_label = QLabel("Test Label")
            test_table = QTableWidget(5, 3)

            # Test button click
            clicked = False
            def on_click():
                nonlocal clicked
                clicked = True

            test_button.clicked.connect(on_click)
            test_button.click()

            if clicked:
                print("   ✅ Button click handling works")
            else:
                print("   ❌ Button click handling failed")

            # Test label text setting
            test_label.setText("Updated Text")
            if test_label.text() == "Updated Text":
                print("   ✅ Label text setting works")
            else:
                print("   ❌ Label text setting failed")

            # Test table operations
            test_table.setItem(0, 0, QTableWidget().item(0, 0) or test_table.item(0, 0))
            print("   ✅ Table operations work")

        except Exception as e:
            print(f"   ❌ Widget interaction test failed: {e}")
            return False

        print("\n✅ COMPREHENSIVE GUI TEST SUITE COMPLETED")
        return True

    except Exception as e:
        print(f"❌ GUI test suite creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    # Run both test suites
    fix_success = run_comprehensive_fix_validation()
    gui_success = create_comprehensive_gui_test_suite()

    overall_success = fix_success and gui_success
    print(f"\n🎯 OVERALL TEST RESULT: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")

    sys.exit(0 if overall_success else 1)
