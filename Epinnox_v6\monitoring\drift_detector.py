"""
Model & Signal Drift Detection System
Monitors ML/NLP models and trading signals for performance degradation
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
import json

logger = logging.getLogger(__name__)

class DriftSeverity(Enum):
    """Drift severity levels"""
    NORMAL = "normal"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class DriftAlert:
    """Drift detection alert"""
    timestamp: datetime
    component: str
    metric: str
    current_value: float
    threshold: float
    severity: DriftSeverity
    message: str
    recommended_action: str

class ModelDriftDetector:
    """
    Comprehensive drift detection for ML models and trading signals
    """
    
    def __init__(self, performance_tracker, config: Dict = None):
        self.performance_tracker = performance_tracker
        self.config = config or self._default_config()
        self.drift_history = []
        self.baseline_metrics = {}
        self.alert_callbacks = []
        
        # Initialize baseline if available
        self._initialize_baseline()
    
    def _default_config(self) -> Dict:
        """Default drift detection configuration"""
        return {
            'sharpe_thresholds': {
                'warning': 0.5,
                'critical': 0.0,
                'emergency': -0.5
            },
            'win_rate_thresholds': {
                'warning': 0.40,
                'critical': 0.30,
                'emergency': 0.20
            },
            'drawdown_thresholds': {
                'warning': 0.15,
                'critical': 0.25,
                'emergency': 0.40
            },
            'profit_factor_thresholds': {
                'warning': 1.0,
                'critical': 0.8,
                'emergency': 0.5
            },
            'confidence_drift_threshold': 0.15,  # 15% change in avg confidence
            'lookback_periods': {
                'short': 7,   # days
                'medium': 30, # days
                'long': 90    # days
            },
            'min_trades_for_analysis': 10,
            'alert_cooldown_hours': 6  # Prevent spam alerts
        }
    
    def _initialize_baseline(self):
        """Initialize baseline metrics from historical data"""
        try:
            # Get historical performance for baseline
            historical_performance = self.performance_tracker.get_model_performance(days=90)
            
            if historical_performance and 'overall' in historical_performance:
                overall = historical_performance['overall']
                self.baseline_metrics = {
                    'win_rate': overall.get('win_rate', 0.5),
                    'avg_pnl': overall.get('avg_pnl', 0.0),
                    'total_pnl': overall.get('total_pnl', 0.0),
                    'baseline_date': datetime.now()
                }
                logger.info(f"[DRIFT] Baseline metrics initialized: {self.baseline_metrics}")
            
        except Exception as e:
            logger.warning(f"[DRIFT] Could not initialize baseline: {e}")
            self.baseline_metrics = {}
    
    def detect_performance_drift(self, period_days: int = 7) -> List[DriftAlert]:
        """Detect drift in overall trading performance"""
        alerts = []
        
        try:
            # Get recent performance metrics
            recent_performance = self.performance_tracker.get_recent_performance_summary(
                hours=period_days * 24
            )
            
            if recent_performance['total_trades'] < self.config['min_trades_for_analysis']:
                return alerts
            
            # Calculate current metrics
            current_metrics = self._calculate_current_metrics(recent_performance)
            
            # Check each metric for drift
            for metric_name, current_value in current_metrics.items():
                if metric_name in self.config:
                    thresholds = self.config[f"{metric_name}_thresholds"]
                    severity = self._assess_drift_severity(current_value, thresholds, metric_name)
                    
                    if severity != DriftSeverity.NORMAL:
                        alert = DriftAlert(
                            timestamp=datetime.now(),
                            component="performance",
                            metric=metric_name,
                            current_value=current_value,
                            threshold=thresholds[severity.value],
                            severity=severity,
                            message=f"{metric_name.replace('_', ' ').title()} has degraded to {current_value:.3f}",
                            recommended_action=self._get_recommended_action(metric_name, severity)
                        )
                        alerts.append(alert)
            
        except Exception as e:
            logger.error(f"[DRIFT] Error detecting performance drift: {e}")
        
        return alerts
    
    def detect_confidence_drift(self, period_days: int = 7) -> List[DriftAlert]:
        """Detect drift in model confidence levels"""
        alerts = []
        
        try:
            # Get recent trades with confidence data
            import sqlite3
            conn = sqlite3.connect(self.performance_tracker.db_path)
            
            cutoff_date = datetime.now() - timedelta(days=period_days)
            df = pd.read_sql_query('''
                SELECT confidence, timestamp FROM trades 
                WHERE timestamp > ? AND confidence IS NOT NULL
                ORDER BY timestamp DESC
            ''', conn, params=(cutoff_date,))
            
            conn.close()
            
            if len(df) < self.config['min_trades_for_analysis']:
                return alerts
            
            # Calculate confidence drift
            recent_confidence = df['confidence'].mean()
            
            if self.baseline_metrics and 'avg_confidence' in self.baseline_metrics:
                baseline_confidence = self.baseline_metrics['avg_confidence']
                confidence_change = abs(recent_confidence - baseline_confidence) / baseline_confidence
                
                if confidence_change > self.config['confidence_drift_threshold']:
                    severity = DriftSeverity.WARNING if confidence_change < 0.25 else DriftSeverity.CRITICAL
                    
                    alert = DriftAlert(
                        timestamp=datetime.now(),
                        component="model_confidence",
                        metric="confidence_drift",
                        current_value=confidence_change,
                        threshold=self.config['confidence_drift_threshold'],
                        severity=severity,
                        message=f"Model confidence has drifted {confidence_change:.1%} from baseline",
                        recommended_action="Review model inputs and consider retraining"
                    )
                    alerts.append(alert)
            
        except Exception as e:
            logger.error(f"[DRIFT] Error detecting confidence drift: {e}")
        
        return alerts
    
    def detect_signal_drift(self, signal_data: Dict) -> List[DriftAlert]:
        """Detect drift in individual trading signals"""
        alerts = []
        
        try:
            # Analyze signal strength distribution
            for signal_name, signal_values in signal_data.items():
                if not isinstance(signal_values, (list, np.ndarray)) or len(signal_values) < 10:
                    continue
                
                # Calculate signal statistics
                signal_mean = np.mean(signal_values)
                signal_std = np.std(signal_values)
                signal_range = np.max(signal_values) - np.min(signal_values)
                
                # Check for anomalies
                if signal_std > signal_mean * 2:  # High volatility
                    alert = DriftAlert(
                        timestamp=datetime.now(),
                        component="signal",
                        metric=f"{signal_name}_volatility",
                        current_value=signal_std,
                        threshold=signal_mean * 2,
                        severity=DriftSeverity.WARNING,
                        message=f"Signal {signal_name} showing high volatility (std: {signal_std:.3f})",
                        recommended_action="Check signal calculation and data quality"
                    )
                    alerts.append(alert)
                
                # Check for signal saturation
                if signal_range < signal_mean * 0.1:  # Low range
                    alert = DriftAlert(
                        timestamp=datetime.now(),
                        component="signal",
                        metric=f"{signal_name}_saturation",
                        current_value=signal_range,
                        threshold=signal_mean * 0.1,
                        severity=DriftSeverity.WARNING,
                        message=f"Signal {signal_name} may be saturated (range: {signal_range:.3f})",
                        recommended_action="Review signal parameters and scaling"
                    )
                    alerts.append(alert)
            
        except Exception as e:
            logger.error(f"[DRIFT] Error detecting signal drift: {e}")
        
        return alerts
    
    def detect_market_regime_change(self) -> List[DriftAlert]:
        """Detect changes in market regime that might affect model performance"""
        alerts = []
        
        try:
            # Get recent market data and performance
            recent_performance = self.performance_tracker.get_recent_performance_summary(hours=168)  # 1 week
            
            if recent_performance['total_trades'] < 5:
                return alerts
            
            # Simple regime change detection based on performance patterns
            win_rate = recent_performance['win_rate']
            avg_pnl = recent_performance['avg_pnl_per_trade']
            
            # Check for sudden performance degradation
            if win_rate < 0.3 and avg_pnl < 0:
                alert = DriftAlert(
                    timestamp=datetime.now(),
                    component="market_regime",
                    metric="performance_degradation",
                    current_value=win_rate,
                    threshold=0.3,
                    severity=DriftSeverity.CRITICAL,
                    message=f"Possible market regime change detected (win rate: {win_rate:.1%}, avg PnL: ${avg_pnl:.2f})",
                    recommended_action="Consider model retraining or strategy adjustment"
                )
                alerts.append(alert)
            
        except Exception as e:
            logger.error(f"[DRIFT] Error detecting market regime change: {e}")
        
        return alerts
    
    def _calculate_current_metrics(self, performance_data: Dict) -> Dict:
        """Calculate current performance metrics"""
        metrics = {}
        
        # Calculate Sharpe ratio (simplified)
        if performance_data['total_trades'] > 1:
            # Estimate Sharpe from available data
            avg_return = performance_data['avg_pnl_per_trade'] / 1000  # Normalize
            # Simple Sharpe approximation
            metrics['sharpe'] = avg_return * np.sqrt(252) if avg_return != 0 else 0
        
        # Win rate
        metrics['win_rate'] = performance_data['win_rate']
        
        # Drawdown (simplified - using worst trade as proxy)
        if performance_data['worst_trade'] < 0:
            metrics['drawdown'] = abs(performance_data['worst_trade']) / 1000  # Normalize
        
        # Profit factor approximation
        if performance_data['worst_trade'] < 0:
            gross_profit = max(0, performance_data['total_pnl'])
            gross_loss = abs(min(0, performance_data['worst_trade']))
            metrics['profit_factor'] = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        return metrics
    
    def _assess_drift_severity(self, current_value: float, thresholds: Dict, metric_name: str) -> DriftSeverity:
        """Assess the severity of drift for a given metric"""
        # For metrics where higher is better (win_rate, sharpe, profit_factor)
        if metric_name in ['win_rate', 'sharpe', 'profit_factor']:
            if current_value <= thresholds['emergency']:
                return DriftSeverity.EMERGENCY
            elif current_value <= thresholds['critical']:
                return DriftSeverity.CRITICAL
            elif current_value <= thresholds['warning']:
                return DriftSeverity.WARNING
        
        # For metrics where lower is better (drawdown)
        elif metric_name in ['drawdown']:
            if current_value >= thresholds['emergency']:
                return DriftSeverity.EMERGENCY
            elif current_value >= thresholds['critical']:
                return DriftSeverity.CRITICAL
            elif current_value >= thresholds['warning']:
                return DriftSeverity.WARNING
        
        return DriftSeverity.NORMAL
    
    def _get_recommended_action(self, metric_name: str, severity: DriftSeverity) -> str:
        """Get recommended action based on metric and severity"""
        actions = {
            'sharpe': {
                DriftSeverity.WARNING: "Review strategy parameters and risk management",
                DriftSeverity.CRITICAL: "Consider reducing position sizes and retraining models",
                DriftSeverity.EMERGENCY: "Stop trading and perform comprehensive system review"
            },
            'win_rate': {
                DriftSeverity.WARNING: "Analyze recent losing trades and adjust confidence thresholds",
                DriftSeverity.CRITICAL: "Retrain models with recent data and review signal quality",
                DriftSeverity.EMERGENCY: "Halt autonomous trading and manual review required"
            },
            'drawdown': {
                DriftSeverity.WARNING: "Tighten risk management and reduce leverage",
                DriftSeverity.CRITICAL: "Implement emergency risk controls and reduce exposure",
                DriftSeverity.EMERGENCY: "Emergency stop - close all positions and review system"
            },
            'profit_factor': {
                DriftSeverity.WARNING: "Review trade sizing and exit strategies",
                DriftSeverity.CRITICAL: "Adjust risk-reward ratios and model parameters",
                DriftSeverity.EMERGENCY: "Stop trading and comprehensive strategy overhaul needed"
            }
        }
        
        return actions.get(metric_name, {}).get(severity, "Review system performance and consider adjustments")
    
    def run_comprehensive_drift_check(self) -> List[DriftAlert]:
        """Run all drift detection checks"""
        all_alerts = []
        
        logger.info("[DRIFT] Running comprehensive drift detection...")
        
        # Performance drift
        all_alerts.extend(self.detect_performance_drift())
        
        # Confidence drift
        all_alerts.extend(self.detect_confidence_drift())
        
        # Market regime change
        all_alerts.extend(self.detect_market_regime_change())
        
        # Filter out recent duplicate alerts
        filtered_alerts = self._filter_duplicate_alerts(all_alerts)
        
        # Process alerts
        for alert in filtered_alerts:
            self._process_alert(alert)
        
        logger.info(f"[DRIFT] Drift check completed. {len(filtered_alerts)} alerts generated.")
        
        return filtered_alerts
    
    def _filter_duplicate_alerts(self, alerts: List[DriftAlert]) -> List[DriftAlert]:
        """Filter out duplicate alerts within cooldown period"""
        filtered = []
        cooldown_hours = self.config['alert_cooldown_hours']
        
        for alert in alerts:
            # Check if similar alert was recently generated
            is_duplicate = False
            cutoff_time = datetime.now() - timedelta(hours=cooldown_hours)
            
            for recent_alert in self.drift_history:
                if (recent_alert.timestamp > cutoff_time and
                    recent_alert.component == alert.component and
                    recent_alert.metric == alert.metric and
                    recent_alert.severity == alert.severity):
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                filtered.append(alert)
        
        return filtered
    
    def _process_alert(self, alert: DriftAlert):
        """Process a drift alert"""
        # Log the alert
        logger.warning(f"[DRIFT ALERT] {alert.severity.value.upper()}: {alert.message}")
        logger.info(f"[DRIFT ACTION] Recommended: {alert.recommended_action}")
        
        # Add to history
        self.drift_history.append(alert)
        
        # Call registered callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"[DRIFT] Error in alert callback: {e}")
        
        # Auto-adjustments based on severity
        if alert.severity == DriftSeverity.EMERGENCY:
            self._trigger_emergency_response(alert)
        elif alert.severity == DriftSeverity.CRITICAL:
            self._trigger_critical_response(alert)
    
    def _trigger_emergency_response(self, alert: DriftAlert):
        """Trigger emergency response for critical drift"""
        logger.critical(f"[DRIFT EMERGENCY] {alert.message}")
        
        # Could trigger:
        # - Immediate trading halt
        # - Position closure
        # - Alert notifications
        # - Model rollback
        
        # For now, just log the emergency
        logger.critical("[DRIFT EMERGENCY] Manual intervention required!")
    
    def _trigger_critical_response(self, alert: DriftAlert):
        """Trigger critical response for significant drift"""
        logger.error(f"[DRIFT CRITICAL] {alert.message}")
        
        # Could trigger:
        # - Reduced position sizing
        # - Increased confidence thresholds
        # - Model retraining
        # - Risk parameter adjustment
    
    def register_alert_callback(self, callback):
        """Register callback function for drift alerts"""
        self.alert_callbacks.append(callback)
    
    def get_drift_summary(self, days: int = 30) -> Dict:
        """Get summary of drift alerts over specified period"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_alerts = [alert for alert in self.drift_history if alert.timestamp > cutoff_date]
        
        summary = {
            'total_alerts': len(recent_alerts),
            'by_severity': {},
            'by_component': {},
            'by_metric': {},
            'most_recent': recent_alerts[-1] if recent_alerts else None
        }
        
        for alert in recent_alerts:
            # By severity
            severity_key = alert.severity.value
            summary['by_severity'][severity_key] = summary['by_severity'].get(severity_key, 0) + 1
            
            # By component
            component_key = alert.component
            summary['by_component'][component_key] = summary['by_component'].get(component_key, 0) + 1
            
            # By metric
            metric_key = alert.metric
            summary['by_metric'][metric_key] = summary['by_metric'].get(metric_key, 0) + 1
        
        return summary
    
    def save_drift_report(self, filename: str = None):
        """Save drift detection report to file"""
        if filename is None:
            filename = f"drift_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'config': self.config,
            'baseline_metrics': self.baseline_metrics,
            'drift_summary': self.get_drift_summary(),
            'recent_alerts': [
                {
                    'timestamp': alert.timestamp.isoformat(),
                    'component': alert.component,
                    'metric': alert.metric,
                    'current_value': alert.current_value,
                    'threshold': alert.threshold,
                    'severity': alert.severity.value,
                    'message': alert.message,
                    'recommended_action': alert.recommended_action
                }
                for alert in self.drift_history[-50:]  # Last 50 alerts
            ]
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"[DRIFT] Drift report saved to {filename}")
