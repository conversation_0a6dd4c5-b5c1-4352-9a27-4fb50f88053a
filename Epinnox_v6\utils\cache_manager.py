"""
Cache Management Utility for Epinnox v6
Provides automatic cache cleanup and management functionality
"""

import os
import json
import time
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from core.error_handling import safe_execute, ErrorContext
from collections import deque

logger = logging.getLogger(__name__)

class CacheManager:
    """
    Manages cache files with automatic cleanup and size limits
    """
    
    def __init__(self, cache_dir: str = "cache", max_age_hours: int = 24, max_size_mb: int = 100):
        self.cache_dir = Path(cache_dir)
        self.max_age_hours = max_age_hours
        self.max_size_mb = max_size_mb
        self.max_size_bytes = max_size_mb * 1024 * 1024
        
        # Ensure cache directory exists
        self.cache_dir.mkdir(exist_ok=True)
        
        logger.info(f"Cache manager initialized: {self.cache_dir}, max_age={max_age_hours}h, max_size={max_size_mb}MB")
        
        self.price_history = deque(maxlen=60)
        self.signal_history = deque(maxlen=20)
    
    def cleanup_expired_files(self) -> Dict[str, int]:
        """
        Remove expired cache files based on age
        
        Returns:
            dict: Statistics about cleanup operation
        """
        stats = {
            'files_removed': 0,
            'bytes_freed': 0,
            'errors': 0
        }
        
        with ErrorContext("cache cleanup", log_level="warning"):
            cutoff_time = time.time() - (self.max_age_hours * 3600)
            
            for cache_file in self.cache_dir.glob("*.json"):
                try:
                    file_stat = cache_file.stat()
                    if file_stat.st_mtime < cutoff_time:
                        file_size = file_stat.st_size
                        cache_file.unlink()
                        stats['files_removed'] += 1
                        stats['bytes_freed'] += file_size
                        logger.debug(f"Removed expired cache file: {cache_file.name}")
                except Exception as e:
                    stats['errors'] += 1
                    logger.warning(f"Error removing cache file {cache_file}: {e}")
        
        if stats['files_removed'] > 0:
            logger.info(f"Cache cleanup: removed {stats['files_removed']} files, "
                       f"freed {stats['bytes_freed'] / 1024 / 1024:.2f} MB")
        
        return stats
    
    def cleanup_by_size(self) -> Dict[str, int]:
        """
        Remove oldest cache files if total size exceeds limit
        
        Returns:
            dict: Statistics about cleanup operation
        """
        stats = {
            'files_removed': 0,
            'bytes_freed': 0,
            'total_size_before': 0,
            'total_size_after': 0
        }
        
        with ErrorContext("cache size cleanup", log_level="warning"):
            # Get all cache files with their sizes and modification times
            cache_files = []
            total_size = 0
            
            for cache_file in self.cache_dir.glob("*.json"):
                try:
                    file_stat = cache_file.stat()
                    cache_files.append({
                        'path': cache_file,
                        'size': file_stat.st_size,
                        'mtime': file_stat.st_mtime
                    })
                    total_size += file_stat.st_size
                except Exception as e:
                    logger.warning(f"Error reading cache file {cache_file}: {e}")
            
            stats['total_size_before'] = total_size
            
            # If total size exceeds limit, remove oldest files
            if total_size > self.max_size_bytes:
                # Sort by modification time (oldest first)
                cache_files.sort(key=lambda x: x['mtime'])
                
                bytes_to_remove = total_size - self.max_size_bytes
                bytes_removed = 0
                
                for file_info in cache_files:
                    if bytes_removed >= bytes_to_remove:
                        break
                    
                    try:
                        file_info['path'].unlink()
                        stats['files_removed'] += 1
                        stats['bytes_freed'] += file_info['size']
                        bytes_removed += file_info['size']
                        logger.debug(f"Removed cache file for size limit: {file_info['path'].name}")
                    except Exception as e:
                        logger.warning(f"Error removing cache file {file_info['path']}: {e}")
                
                stats['total_size_after'] = total_size - bytes_removed
                
                if stats['files_removed'] > 0:
                    logger.info(f"Cache size cleanup: removed {stats['files_removed']} files, "
                               f"freed {stats['bytes_freed'] / 1024 / 1024:.2f} MB")
            else:
                stats['total_size_after'] = total_size
        
        return stats
    
    def get_cache_stats(self) -> Dict:
        """
        Get current cache statistics
        
        Returns:
            dict: Cache statistics
        """
        stats = {
            'total_files': 0,
            'total_size_bytes': 0,
            'total_size_mb': 0,
            'oldest_file_age_hours': 0,
            'newest_file_age_hours': 0,
            'file_types': {}
        }
        
        with ErrorContext("cache stats", log_level="debug"):
            current_time = time.time()
            oldest_time = current_time
            newest_time = 0
            
            for cache_file in self.cache_dir.glob("*.json"):
                try:
                    file_stat = cache_file.stat()
                    stats['total_files'] += 1
                    stats['total_size_bytes'] += file_stat.st_size
                    
                    # Track file age
                    oldest_time = min(oldest_time, file_stat.st_mtime)
                    newest_time = max(newest_time, file_stat.st_mtime)
                    
                    # Track file types
                    file_prefix = cache_file.name.split('_')[0]
                    stats['file_types'][file_prefix] = stats['file_types'].get(file_prefix, 0) + 1
                    
                except Exception as e:
                    logger.warning(f"Error reading cache file {cache_file}: {e}")
            
            stats['total_size_mb'] = stats['total_size_bytes'] / 1024 / 1024
            
            if stats['total_files'] > 0:
                stats['oldest_file_age_hours'] = (current_time - oldest_time) / 3600
                stats['newest_file_age_hours'] = (current_time - newest_time) / 3600
        
        return stats
    
    def clear_all_cache(self) -> Dict[str, int]:
        """
        Remove all cache files
        
        Returns:
            dict: Statistics about cleanup operation
        """
        stats = {
            'files_removed': 0,
            'bytes_freed': 0,
            'errors': 0
        }
        
        with ErrorContext("cache clear all", log_level="info"):
            for cache_file in self.cache_dir.glob("*.json"):
                try:
                    file_size = cache_file.stat().st_size
                    cache_file.unlink()
                    stats['files_removed'] += 1
                    stats['bytes_freed'] += file_size
                except Exception as e:
                    stats['errors'] += 1
                    logger.warning(f"Error removing cache file {cache_file}: {e}")
        
        logger.info(f"Cache cleared: removed {stats['files_removed']} files, "
                   f"freed {stats['bytes_freed'] / 1024 / 1024:.2f} MB")
        
        return stats
    
    def clear_cache_by_type(self, cache_type: str) -> Dict[str, int]:
        """
        Remove cache files of a specific type
        
        Args:
            cache_type: Type prefix (e.g., 'ohlcv', 'orderbook', 'trades')
        
        Returns:
            dict: Statistics about cleanup operation
        """
        stats = {
            'files_removed': 0,
            'bytes_freed': 0,
            'errors': 0
        }
        
        with ErrorContext(f"cache clear {cache_type}", log_level="info"):
            pattern = f"{cache_type}_*.json"
            for cache_file in self.cache_dir.glob(pattern):
                try:
                    file_size = cache_file.stat().st_size
                    cache_file.unlink()
                    stats['files_removed'] += 1
                    stats['bytes_freed'] += file_size
                except Exception as e:
                    stats['errors'] += 1
                    logger.warning(f"Error removing cache file {cache_file}: {e}")
        
        if stats['files_removed'] > 0:
            logger.info(f"Cache cleared for {cache_type}: removed {stats['files_removed']} files, "
                       f"freed {stats['bytes_freed'] / 1024 / 1024:.2f} MB")
        
        return stats
    
    def auto_cleanup(self) -> Dict:
        """
        Perform automatic cleanup based on age and size limits
        
        Returns:
            dict: Combined statistics from all cleanup operations
        """
        logger.info("Starting automatic cache cleanup")
        
        # Get initial stats
        initial_stats = self.get_cache_stats()
        
        # Cleanup expired files first
        age_cleanup = self.cleanup_expired_files()
        
        # Then cleanup by size if needed
        size_cleanup = self.cleanup_by_size()
        
        # Get final stats
        final_stats = self.get_cache_stats()
        
        combined_stats = {
            'initial_files': initial_stats['total_files'],
            'final_files': final_stats['total_files'],
            'initial_size_mb': initial_stats['total_size_mb'],
            'final_size_mb': final_stats['total_size_mb'],
            'files_removed': age_cleanup['files_removed'] + size_cleanup['files_removed'],
            'bytes_freed': age_cleanup['bytes_freed'] + size_cleanup['bytes_freed'],
            'age_cleanup': age_cleanup,
            'size_cleanup': size_cleanup
        }
        
        logger.info(f"Auto cleanup complete: {combined_stats['files_removed']} files removed, "
                   f"{combined_stats['bytes_freed'] / 1024 / 1024:.2f} MB freed")
        
        return combined_stats
    
    def add_price(self, price: float):
        """Append (datetime.utcnow(), price) to price_history."""
        self.price_history.append((datetime.utcnow(), price))

    def add_signal(self, decision: str, confidence: float):
        """Append {'decision', 'confidence', 'time'} to signal_history."""
        self.signal_history.append({'decision': decision, 'confidence': confidence, 'time': datetime.utcnow()})

    def get_context(self) -> dict:
        """Return {'recent_prices': list(self.price_history), 'recent_signals': list(self.signal_history)}."""
        return {
            'recent_prices': list(self.price_history),
            'recent_signals': list(self.signal_history)
        }

# Global cache manager instance
cache_manager = CacheManager()

def cleanup_cache():
    """Convenience function for cache cleanup"""
    return cache_manager.auto_cleanup()

def get_cache_stats():
    """Convenience function to get cache stats"""
    return cache_manager.get_cache_stats()

def clear_cache():
    """Convenience function to clear all cache"""
    return cache_manager.clear_all_cache()
