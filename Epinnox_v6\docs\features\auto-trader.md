# 🤖 Epinnox Auto Trader

## Overview

The Epinnox Auto Trader is an intelligent autonomous trading system that combines machine learning predictions, LLM analysis, and sophisticated risk management to execute trades automatically. It uses limit orders with best bid/ask pricing and intelligent position sizing based on your account state, leverage, and market conditions.

## Key Features

### ✅ **Intelligent Decision Making**
- **ML Model Integration**: Combines predictions from 8 different ML models (SVM, Random Forest, LSTM, RSI, VWAP, Orderflow, Volatility, Sentiment)
- **LLM Analysis**: Uses local LLM models (LLaMA/Phi) for comprehensive market analysis
- **Multi-Factor Scoring**: Weighs confidence levels, market conditions, and account state

### ✅ **Professional Order Execution**
- **Limit Orders Only**: Uses limit orders with best bid/ask prices for optimal execution
- **No Market Impact**: Prevents slippage and ensures predictable execution prices
- **Real-Time Pricing**: Gets live bid/ask data from WebSocket feeds

### ✅ **Smart Position Sizing**
- **Leverage-Aware**: Adjusts position size based on leverage to maintain consistent risk
- **Account-Aware**: Considers your actual balance, open positions, and trading history
- **Market-Aware**: Reduces size during high volatility periods
- **Performance-Based**: Scales based on win rate and drawdown

## How It Works

### 1. **Analysis Phase**
```
🔍 Data Collection → 🤖 ML Predictions → 🧠 LLM Analysis → 🎯 Final Verdict
```

1. **Market Data**: Collects OHLCV data, order book, and technical indicators
2. **ML Predictions**: 8 ML models generate BUY/SELL/WAIT predictions with confidence scores
3. **LLM Analysis**: Local LLM analyzes all data and provides reasoning
4. **Final Verdict**: Structured decision with confidence, position size, and risk level

### 2. **Safety Checks**
```
✅ Confidence ≥ 70% → ✅ Balance ≥ $20 → ✅ Daily Limit → ✅ Position Conflicts → ✅ Market Conditions
```

- **Minimum Confidence**: Requires 70% confidence for autonomous trades
- **Balance Requirements**: Minimum $20 free balance for trading
- **Daily Trade Limits**: Configurable maximum trades per day (default: 10)
- **Position Concentration**: Prevents conflicting positions and over-exposure
- **Market Conditions**: Checks liquidity, spread, and volatility

### 3. **Position Sizing Algorithm**
```python
# Multi-factor position sizing
base_risk = 3.0% / max(1, leverage / 10)  # Leverage-adjusted base risk
confidence_multiplier = 0.7x to 2.0x      # Based on AI confidence (70-100%)
leverage_multiplier = 1.0x to 1.5x        # Leverage efficiency bonus
drawdown_multiplier = 0.2x to 1.0x        # Reduces during losses
position_count_multiplier = 0.3x to 1.2x  # First position bonus
volatility_multiplier = 0.5x to 1.0x      # High volatility protection

final_size = base_risk × all_multipliers
```

### 4. **Order Execution**
```
📊 Get Best Bid/Ask → 📦 Calculate Quantity → 🎯 Place Limit Order → ✅ Confirm Execution
```

1. **Price Discovery**: Gets real-time best bid/ask from WebSocket or API
2. **Quantity Calculation**: `quantity = position_value_usd / entry_price`
3. **Order Placement**: Places limit order at best bid (SHORT) or best ask (LONG)
4. **Validation**: Ensures minimum order size and precision requirements

## Configuration

### **Auto Trader Settings**
- **Enable/Disable**: Checkbox to activate autonomous trading
- **Leverage**: 1x to 200x leverage (affects position sizing)
- **Daily Trade Limit**: Maximum trades per day (default: 10)
- **Emergency Stop**: Automatic shutdown on 20% drawdown

### **Risk Management**
- **Maximum Drawdown**: 20% (triggers emergency stop)
- **Position Concentration**: Maximum 80% of balance exposure
- **Minimum Confidence**: 70% required for trades
- **Volatility Threshold**: Reduces size when volatility > 5%

## Usage Instructions

### **1. Enable Auto Trader**
1. Ensure you have sufficient balance (minimum $20 USDT)
2. Set your desired leverage (recommended: 10x-50x)
3. Check the "Auto Trader" checkbox
4. Monitor the logs for autonomous trading activity

### **2. Monitor Performance**
- **Real-Time Logs**: All decisions logged to terminal and daily log files
- **Historical Verdicts**: Track all trading decisions and outcomes
- **Position Tracking**: Real-time position and PnL monitoring
- **Risk Metrics**: Win rate, drawdown, and performance statistics

### **3. Safety Controls**
- **Emergency Stop**: Automatically triggers on excessive losses
- **Daily Limits**: Prevents over-trading
- **Manual Override**: Can disable at any time
- **Position Conflicts**: Prevents opposing positions

## Example Execution Log

```
[21:48:34] 🤖 AUTO TRADER: Starting autonomous trade execution
[21:48:34] 🔍 AUTO TRADER: Running pre-execution safety checks
[21:48:34] ✅ AUTO TRADER: Safety checks passed

[21:48:34] 🧮 ENHANCED POSITION SIZE CALCULATION:
[21:48:34]    💰 Free Balance: $41.60
[21:48:34]    ⚖️ Leverage: 20x
[21:48:34]    📊 Base Risk: 1.50% ($0.62)
[21:48:34]    🎯 Confidence: 85% (×1.25)
[21:48:34]    ⚖️ Leverage Efficiency: ×1.95
[21:48:34]    💵 Final Position Size: $20.00

[21:48:34] 🤖 EXECUTING AUTONOMOUS SHORT:
[21:48:34]    📊 Symbol: DOGE/USDT:USDT
[21:48:34]    💰 Position Value: $20.00
[21:48:34]    📉 Entry Price: $0.169612 (Best Bid)
[21:48:34]    📦 Quantity: 117.9167
[21:48:34]    ⚖️ Leverage: 20x
[21:48:34]    💵 Margin Required: $1.00

[21:48:34] ✅ Autonomous LIMIT SHORT executed: 117.9167 DOGE/USDT:USDT @ $0.169612
[21:48:34] ✅ Autonomous trade executed successfully
```

## Risk Warnings

### ⚠️ **Important Disclaimers**
- **High Risk**: Cryptocurrency trading involves substantial risk of loss
- **Autonomous Trading**: System trades automatically based on AI decisions
- **No Guarantees**: Past performance does not guarantee future results
- **Monitor Closely**: Always supervise autonomous trading activity
- **Emergency Stop**: System will stop trading on excessive losses

### 🛡️ **Safety Recommendations**
- **Start Small**: Begin with small position sizes to test the system
- **Monitor Actively**: Check logs and performance regularly
- **Set Limits**: Use appropriate daily trade limits
- **Emergency Plan**: Know how to disable auto trading quickly
- **Backup Funds**: Never risk more than you can afford to lose

## Technical Details

### **Supported Exchanges**
- HTX (Huobi) Linear Swaps
- Real-time WebSocket data feeds
- CCXT integration for order execution

### **Supported Symbols**
- DOGE/USDT:USDT (minimum: 1.0 DOGE)
- BTC/USDT:USDT (minimum: 0.0001 BTC)
- ETH/USDT:USDT (minimum: 0.001 ETH)
- And other major cryptocurrency pairs

### **Order Types**
- **Limit Orders Only**: No market orders for better execution
- **Best Bid/Ask Pricing**: Optimal entry prices
- **Automatic Quantity Calculation**: Based on USD position size
- **Exchange Compliance**: Respects minimum sizes and precision

## Troubleshooting

### **Common Issues**
1. **"Trading is disabled"**: Check if real trading interface is connected
2. **"Insufficient balance"**: Ensure minimum $20 USDT available
3. **"Confidence too low"**: AI confidence below 70% threshold
4. **"Daily limit reached"**: Maximum trades per day exceeded
5. **"Position conflicts"**: Trying to open opposite position

### **Log Files**
- **Location**: `logs/epinnox_YYYYMMDD.log`
- **Content**: Complete audit trail of all decisions and executions
- **Format**: Timestamped entries with detailed context

## Support

For technical support or questions about the Auto Trader:
1. Check the log files for detailed error messages
2. Review the safety checks and requirements
3. Ensure proper exchange connectivity
4. Verify account balance and trading permissions

## Advanced Features

### **Emergency Stop System**
The Auto Trader includes multiple emergency stop mechanisms:

```python
# Automatic triggers
max_drawdown > 20%           # Stop on excessive losses
daily_trades >= limit        # Stop when daily limit reached
balance < minimum            # Stop when insufficient funds
confidence < threshold       # Skip low-confidence trades
```

### **Position Concentration Management**
- **Same Direction**: Allows adding to existing positions
- **Opposite Direction**: Prevents conflicting long/short positions
- **Total Exposure**: Limits to 80% of account balance
- **Symbol Limits**: Prevents over-concentration in single assets

### **Market Condition Adaptation**
```python
# Volatility-based adjustments
volatility > 8%: size × 0.5   # Very high volatility
volatility > 5%: size × 0.7   # High volatility
volatility > 3%: size × 0.9   # Moderate volatility
volatility ≤ 3%: size × 1.0   # Normal volatility

# Liquidity checks
liquidity < 0.3: cancel_trade  # Insufficient liquidity
spread > 0.5%: warn_execution  # Wide spread warning
```

### **Performance Tracking**
The system tracks comprehensive performance metrics:
- **Win Rate**: Percentage of profitable trades
- **Average PnL**: Mean profit/loss per trade
- **Maximum Drawdown**: Worst peak-to-trough decline
- **Sharpe Ratio**: Risk-adjusted returns
- **Trade Frequency**: Trades per day/week/month

## Integration with Epinnox Components

### **ML Model Pipeline**
```
Market Data → Feature Engineering → 8 ML Models → Confidence Scoring → Decision Matrix
```

1. **SVM Model**: Support Vector Machine for trend classification
2. **Random Forest**: Ensemble method for robust predictions
3. **LSTM**: Long Short-Term Memory for sequence analysis
4. **RSI Model**: Relative Strength Index momentum analysis
5. **VWAP Model**: Volume Weighted Average Price analysis
6. **Orderflow Model**: Order book flow analysis
7. **Volatility Model**: Market volatility predictions
8. **Sentiment Model**: Market sentiment analysis

### **LLM Analysis Chain**
```
ML Predictions → Market Context → Account State → Risk Assessment → Final Verdict
```

The LLM receives:
- All ML model predictions and confidence scores
- Current market conditions and technical indicators
- Complete account state (balance, positions, history)
- Risk metrics and performance statistics
- Market volatility and liquidity assessments

### **Real-Time Data Integration**
- **WebSocket Feeds**: Live price and order book data
- **CCXT Integration**: Exchange connectivity and order execution
- **Position Tracking**: Real-time position and PnL monitoring
- **Balance Updates**: Live account balance tracking

## Configuration Files

### **Auto Trader Settings** (`config/auto_trader.json`)
```json
{
  "enabled": false,
  "max_daily_trades": 10,
  "min_confidence": 70,
  "max_drawdown": 20,
  "min_balance": 20,
  "max_position_exposure": 0.8,
  "emergency_stop": {
    "enabled": true,
    "drawdown_limit": 20,
    "consecutive_losses": 5
  }
}
```

### **Risk Management** (`config/risk_management.json`)
```json
{
  "position_sizing": {
    "base_risk_percent": 3.0,
    "leverage_adjustment": true,
    "confidence_scaling": true,
    "volatility_adjustment": true
  },
  "safety_checks": {
    "min_confidence": 70,
    "max_volatility": 10,
    "min_liquidity": 0.3,
    "max_spread": 0.5
  }
}
```

## API Reference

### **Auto Trader Methods**
```python
# Enable/disable auto trading
auto_trader.enable()
auto_trader.disable()

# Get current status
status = auto_trader.get_status()
stats = auto_trader.get_statistics()

# Emergency controls
auto_trader.emergency_stop(reason)
auto_trader.reset_daily_limits()

# Position management
positions = auto_trader.get_active_positions()
auto_trader.close_all_positions()
```

### **Configuration Methods**
```python
# Update settings
auto_trader.set_max_daily_trades(15)
auto_trader.set_min_confidence(75)
auto_trader.set_leverage(25)

# Risk management
auto_trader.set_max_drawdown(15)
auto_trader.set_position_limit(0.7)
```

## Best Practices

### **Getting Started**
1. **Paper Trading**: Test with small amounts first
2. **Monitor Closely**: Watch the first few trades carefully
3. **Gradual Scaling**: Increase position sizes gradually
4. **Regular Review**: Check performance weekly

### **Risk Management**
1. **Diversification**: Don't put all funds in auto trading
2. **Stop Losses**: Set appropriate emergency stop levels
3. **Position Limits**: Keep individual positions reasonable
4. **Regular Monitoring**: Check logs and performance daily

### **Optimization**
1. **Confidence Tuning**: Adjust minimum confidence based on performance
2. **Leverage Optimization**: Find optimal leverage for your risk tolerance
3. **Time-based Rules**: Consider market hours and volatility patterns
4. **Model Performance**: Monitor which ML models perform best

---

**Remember**: The Auto Trader is a powerful tool that requires careful monitoring and risk management. Always trade responsibly and within your risk tolerance.
