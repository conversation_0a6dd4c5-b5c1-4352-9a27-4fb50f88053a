"""
Dynamic Target Calculator for EPINNOX v6
Calculates dynamic stop-loss and take-profit targets based on market conditions
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class TargetLevels:
    """Container for target levels"""
    stop_loss: float
    take_profit: float
    confidence: float
    reasoning: str

class DynamicTargetCalculator:
    """
    Calculate dynamic stop-loss and take-profit targets
    based on volatility, support/resistance, and market conditions
    """
    
    def __init__(self, 
                 base_stop_loss: float = 0.02,
                 base_take_profit: float = 0.04,
                 volatility_multiplier: float = 1.5,
                 min_risk_reward: float = 1.5):
        """
        Initialize dynamic target calculator
        
        Args:
            base_stop_loss: Base stop loss percentage (default 2%)
            base_take_profit: Base take profit percentage (default 4%)
            volatility_multiplier: Multiplier for volatility-based adjustments
            min_risk_reward: Minimum risk-reward ratio
        """
        self.base_stop_loss = base_stop_loss
        self.base_take_profit = base_take_profit
        self.volatility_multiplier = volatility_multiplier
        self.min_risk_reward = min_risk_reward
        
        logger.info(f"Dynamic Target Calculator initialized with base SL: {base_stop_loss:.1%}, TP: {base_take_profit:.1%}")
    
    def calculate_targets(self, 
                         ohlcv_data: pd.DataFrame,
                         entry_price: float,
                         direction: str,
                         confidence: float = 0.7) -> TargetLevels:
        """
        Calculate dynamic stop-loss and take-profit targets
        
        Args:
            ohlcv_data: OHLCV DataFrame
            entry_price: Entry price for the trade
            direction: Trade direction ('LONG' or 'SHORT')
            confidence: Signal confidence (0-1)
            
        Returns:
            TargetLevels object with calculated targets
        """
        try:
            if ohlcv_data is None or ohlcv_data.empty:
                return self._get_default_targets(entry_price, direction)
            
            # Calculate volatility-based adjustments
            volatility_factor = self._calculate_volatility_factor(ohlcv_data)
            
            # Calculate support/resistance levels
            support_resistance = self._find_support_resistance(ohlcv_data)
            
            # Calculate ATR-based targets
            atr_targets = self._calculate_atr_targets(ohlcv_data, entry_price, direction)
            
            # Combine all factors
            targets = self._combine_target_factors(
                entry_price, direction, confidence,
                volatility_factor, support_resistance, atr_targets
            )
            
            # Validate and adjust targets
            validated_targets = self._validate_targets(targets, entry_price, direction)
            
            logger.info(f"Calculated dynamic targets for {direction} at {entry_price}: "
                       f"SL: {validated_targets.stop_loss:.4f}, TP: {validated_targets.take_profit:.4f}")
            
            return validated_targets
            
        except Exception as e:
            logger.error(f"Error calculating dynamic targets: {e}")
            return self._get_default_targets(entry_price, direction)
    
    def _calculate_volatility_factor(self, ohlcv_data: pd.DataFrame) -> float:
        """Calculate volatility factor for target adjustment"""
        try:
            # Calculate recent volatility
            close = ohlcv_data['close']
            returns = close.pct_change().dropna()
            
            if len(returns) < 10:
                return 1.0
            
            # Use 20-period rolling volatility
            volatility = returns.rolling(window=min(20, len(returns))).std().iloc[-1]
            
            # Normalize volatility (typical crypto volatility is 0.02-0.05)
            normalized_vol = volatility / 0.03  # 3% as baseline
            
            # Apply multiplier and cap
            factor = min(max(normalized_vol * self.volatility_multiplier, 0.5), 3.0)
            
            logger.debug(f"Volatility factor: {factor:.2f} (volatility: {volatility:.4f})")
            return factor
            
        except Exception as e:
            logger.warning(f"Error calculating volatility factor: {e}")
            return 1.0
    
    def _find_support_resistance(self, ohlcv_data: pd.DataFrame) -> Dict[str, float]:
        """Find recent support and resistance levels"""
        try:
            high = ohlcv_data['high']
            low = ohlcv_data['low']
            close = ohlcv_data['close']
            
            # Look at recent data (last 50 candles or available data)
            recent_data = min(50, len(ohlcv_data))
            recent_high = high.tail(recent_data)
            recent_low = low.tail(recent_data)
            
            # Find local maxima and minima
            resistance_levels = []
            support_levels = []
            
            # Simple peak/trough detection
            for i in range(2, len(recent_high) - 2):
                # Resistance (local maxima)
                if (recent_high.iloc[i] > recent_high.iloc[i-1] and 
                    recent_high.iloc[i] > recent_high.iloc[i+1] and
                    recent_high.iloc[i] > recent_high.iloc[i-2] and 
                    recent_high.iloc[i] > recent_high.iloc[i+2]):
                    resistance_levels.append(recent_high.iloc[i])
                
                # Support (local minima)
                if (recent_low.iloc[i] < recent_low.iloc[i-1] and 
                    recent_low.iloc[i] < recent_low.iloc[i+1] and
                    recent_low.iloc[i] < recent_low.iloc[i-2] and 
                    recent_low.iloc[i] < recent_low.iloc[i+2]):
                    support_levels.append(recent_low.iloc[i])
            
            current_price = close.iloc[-1]
            
            # Find nearest levels
            nearest_resistance = None
            nearest_support = None
            
            if resistance_levels:
                above_current = [r for r in resistance_levels if r > current_price]
                if above_current:
                    nearest_resistance = min(above_current)
            
            if support_levels:
                below_current = [s for s in support_levels if s < current_price]
                if below_current:
                    nearest_support = max(below_current)
            
            return {
                'resistance': nearest_resistance,
                'support': nearest_support,
                'current_price': current_price
            }
            
        except Exception as e:
            logger.warning(f"Error finding support/resistance: {e}")
            return {'resistance': None, 'support': None, 'current_price': None}
    
    def _calculate_atr_targets(self, ohlcv_data: pd.DataFrame, entry_price: float, direction: str) -> Dict[str, float]:
        """Calculate ATR-based targets"""
        try:
            # Calculate True Range
            high = ohlcv_data['high']
            low = ohlcv_data['low']
            close = ohlcv_data['close']
            
            # True Range calculation
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # Average True Range (14 periods)
            atr_period = min(14, len(true_range))
            atr = true_range.rolling(window=atr_period).mean().iloc[-1]
            
            if pd.isna(atr) or atr == 0:
                atr = entry_price * 0.02  # Fallback to 2% of price
            
            # ATR-based targets
            if direction == 'LONG':
                atr_stop_loss = entry_price - (atr * 1.5)
                atr_take_profit = entry_price + (atr * 2.5)
            else:  # SHORT
                atr_stop_loss = entry_price + (atr * 1.5)
                atr_take_profit = entry_price - (atr * 2.5)
            
            return {
                'atr': atr,
                'atr_stop_loss': atr_stop_loss,
                'atr_take_profit': atr_take_profit
            }
            
        except Exception as e:
            logger.warning(f"Error calculating ATR targets: {e}")
            return {'atr': entry_price * 0.02, 'atr_stop_loss': None, 'atr_take_profit': None}
    
    def _combine_target_factors(self, 
                               entry_price: float,
                               direction: str,
                               confidence: float,
                               volatility_factor: float,
                               support_resistance: Dict[str, float],
                               atr_targets: Dict[str, float]) -> TargetLevels:
        """Combine all factors to determine final targets"""
        try:
            # Base targets adjusted for volatility
            base_sl = self.base_stop_loss * volatility_factor
            base_tp = self.base_take_profit * volatility_factor
            
            # Confidence adjustment (higher confidence = wider targets)
            confidence_multiplier = 0.8 + (confidence * 0.4)  # 0.8 to 1.2
            
            if direction == 'LONG':
                # Stop loss calculation
                stop_loss_candidates = [entry_price * (1 - base_sl * confidence_multiplier)]
                
                # Consider support level
                if support_resistance.get('support'):
                    support_sl = support_resistance['support'] * 0.995  # Slightly below support
                    stop_loss_candidates.append(support_sl)
                
                # Consider ATR
                if atr_targets.get('atr_stop_loss'):
                    stop_loss_candidates.append(atr_targets['atr_stop_loss'])
                
                # Take most conservative (highest) stop loss
                stop_loss = max(stop_loss_candidates)
                
                # Take profit calculation
                take_profit_candidates = [entry_price * (1 + base_tp * confidence_multiplier)]
                
                # Consider resistance level
                if support_resistance.get('resistance'):
                    resistance_tp = support_resistance['resistance'] * 0.995  # Slightly below resistance
                    take_profit_candidates.append(resistance_tp)
                
                # Consider ATR
                if atr_targets.get('atr_take_profit'):
                    take_profit_candidates.append(atr_targets['atr_take_profit'])
                
                # Take most conservative (lowest) take profit
                take_profit = min([tp for tp in take_profit_candidates if tp > entry_price])
                
            else:  # SHORT
                # Stop loss calculation
                stop_loss_candidates = [entry_price * (1 + base_sl * confidence_multiplier)]
                
                # Consider resistance level
                if support_resistance.get('resistance'):
                    resistance_sl = support_resistance['resistance'] * 1.005  # Slightly above resistance
                    stop_loss_candidates.append(resistance_sl)
                
                # Consider ATR
                if atr_targets.get('atr_stop_loss'):
                    stop_loss_candidates.append(atr_targets['atr_stop_loss'])
                
                # Take most conservative (lowest) stop loss
                stop_loss = min(stop_loss_candidates)
                
                # Take profit calculation
                take_profit_candidates = [entry_price * (1 - base_tp * confidence_multiplier)]
                
                # Consider support level
                if support_resistance.get('support'):
                    support_tp = support_resistance['support'] * 1.005  # Slightly above support
                    take_profit_candidates.append(support_tp)
                
                # Consider ATR
                if atr_targets.get('atr_take_profit'):
                    take_profit_candidates.append(atr_targets['atr_take_profit'])
                
                # Take most conservative (highest) take profit
                take_profit = max([tp for tp in take_profit_candidates if tp < entry_price])
            
            # Generate reasoning
            reasoning = f"Volatility factor: {volatility_factor:.2f}, Confidence: {confidence:.2f}"
            
            return TargetLevels(
                stop_loss=stop_loss,
                take_profit=take_profit,
                confidence=confidence,
                reasoning=reasoning
            )
            
        except Exception as e:
            logger.error(f"Error combining target factors: {e}")
            return self._get_default_targets(entry_price, direction)
    
    def _validate_targets(self, targets: TargetLevels, entry_price: float, direction: str) -> TargetLevels:
        """Validate and adjust targets if necessary"""
        try:
            # Calculate risk-reward ratio
            if direction == 'LONG':
                risk = entry_price - targets.stop_loss
                reward = targets.take_profit - entry_price
            else:  # SHORT
                risk = targets.stop_loss - entry_price
                reward = entry_price - targets.take_profit
            
            if risk <= 0 or reward <= 0:
                logger.warning("Invalid risk/reward calculation, using defaults")
                return self._get_default_targets(entry_price, direction)
            
            risk_reward_ratio = reward / risk
            
            # Ensure minimum risk-reward ratio
            if risk_reward_ratio < self.min_risk_reward:
                logger.info(f"Adjusting targets for minimum R:R ratio {self.min_risk_reward}")
                
                if direction == 'LONG':
                    targets.take_profit = entry_price + (risk * self.min_risk_reward)
                else:  # SHORT
                    targets.take_profit = entry_price - (risk * self.min_risk_reward)
                
                targets.reasoning += f", Adjusted for R:R {self.min_risk_reward}"
            
            return targets
            
        except Exception as e:
            logger.error(f"Error validating targets: {e}")
            return self._get_default_targets(entry_price, direction)
    
    def _get_default_targets(self, entry_price: float, direction: str) -> TargetLevels:
        """Get default targets when calculation fails"""
        if direction == 'LONG':
            stop_loss = entry_price * (1 - self.base_stop_loss)
            take_profit = entry_price * (1 + self.base_take_profit)
        else:  # SHORT
            stop_loss = entry_price * (1 + self.base_stop_loss)
            take_profit = entry_price * (1 - self.base_take_profit)
        
        return TargetLevels(
            stop_loss=stop_loss,
            take_profit=take_profit,
            confidence=0.5,
            reasoning="Default targets (calculation failed)"
        )
