"""
News Scraping and Analysis Module for Financial Markets
This module scrapes financial news from various sources and analyzes their market impact.
"""

import logging
import asyncio
import aiohttp
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import re
import json
import hashlib
from urllib.parse import urljoin, urlparse
import feedparser
import requests
from bs4 import BeautifulSoup
import time

logger = logging.getLogger(__name__)

class NewsSource(Enum):
    """Supported news sources"""
    COINDESK = "coindesk"
    COINTELEGRAPH = "cointelegraph"
    CRYPTONEWS = "cryptonews"
    YAHOO_FINANCE = "yahoo_finance"
    REUTERS = "reuters"
    BLOOMBERG = "bloomberg"
    BENZINGA = "benzinga"
    MARKETWATCH = "marketwatch"

@dataclass
class NewsArticle:
    """News article data structure"""
    title: str
    content: str
    url: str
    source: NewsSource
    published_at: datetime
    author: Optional[str]
    tags: List[str]
    symbols_mentioned: List[str]
    sentiment_score: Optional[float]
    importance_score: float
    article_hash: str
    scraped_at: datetime

class NewsSourceConfig:
    """Configuration for news sources"""
    
    SOURCES = {
        NewsSource.COINDESK: {
            'rss_url': 'https://www.coindesk.com/arc/outboundfeeds/rss/',
            'base_url': 'https://www.coindesk.com',
            'selectors': {
                'content': 'div.at-text, div.articleBody',
                'author': 'span.author-name, div.author-info',
                'published': 'time, span.timestamp'
            }
        },
        NewsSource.COINTELEGRAPH: {
            'rss_url': 'https://cointelegraph.com/rss',
            'base_url': 'https://cointelegraph.com',
            'selectors': {
                'content': 'div.post-content, div.post__content',
                'author': 'a.post-meta__author-name',
                'published': 'time.post-meta__publish-date'
            }
        },
        NewsSource.CRYPTONEWS: {
            'rss_url': 'https://cryptonews.com/news/feed/',
            'base_url': 'https://cryptonews.com',
            'selectors': {
                'content': 'div.cn-content, div.article-content',
                'author': 'div.author-info',
                'published': 'time.published-date'
            }
        },
        NewsSource.YAHOO_FINANCE: {
            'rss_url': 'https://feeds.finance.yahoo.com/rss/2.0/headline',
            'base_url': 'https://finance.yahoo.com',
            'selectors': {
                'content': 'div.caas-body, div.ArticleBody',
                'author': 'div.byline-attr',
                'published': 'time'
            }
        }
    }

class NewsScraperManager:
    """Manages news scraping from multiple sources"""
    
    def __init__(self, sources: List[NewsSource] = None, max_articles_per_source: int = 50):
        """
        Initialize news scraper
        
        Args:
            sources: List of news sources to scrape
            max_articles_per_source: Maximum articles to fetch per source
        """
        self.sources = sources or [NewsSource.COINDESK, NewsSource.COINTELEGRAPH, NewsSource.CRYPTONEWS]
        self.max_articles_per_source = max_articles_per_source
        self.session = None
        self.scraped_articles = {}  # Cache to avoid duplicates
        
        # Crypto symbol patterns
        self.crypto_patterns = {
            'btc': r'\b(?:bitcoin|btc)\b',
            'eth': r'\b(?:ethereum|eth)\b',
            'doge': r'\b(?:dogecoin|doge)\b',
            'ada': r'\b(?:cardano|ada)\b',
            'sol': r'\b(?:solana|sol)\b',
            'matic': r'\b(?:polygon|matic)\b',
            'dot': r'\b(?:polkadot|dot)\b',
            'link': r'\b(?:chainlink|link)\b',
            'uni': r'\b(?:uniswap|uni)\b',
            'ltc': r'\b(?:litecoin|ltc)\b'
        }
        
        # Financial keywords for importance scoring
        self.importance_keywords = {
            'high': ['breaking', 'urgent', 'alert', 'exclusive', 'major', 'significant', 'massive', 'huge'],
            'medium': ['announces', 'launches', 'partnership', 'integration', 'adoption', 'price', 'surge', 'drop'],
            'low': ['opinion', 'analysis', 'prediction', 'forecast', 'could', 'might', 'potentially']
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def get_article_hash(self, title: str, url: str) -> str:
        """Generate unique hash for article to avoid duplicates"""
        content = f"{title}{url}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def extract_symbols(self, text: str) -> List[str]:
        """Extract cryptocurrency symbols from text"""
        text_lower = text.lower()
        found_symbols = []
        
        for symbol, pattern in self.crypto_patterns.items():
            if re.search(pattern, text_lower, re.IGNORECASE):
                found_symbols.append(symbol.upper())
        
        return list(set(found_symbols))  # Remove duplicates
    
    def calculate_importance_score(self, title: str, content: str) -> float:
        """
        Calculate importance score for an article
        
        Args:
            title: Article title
            content: Article content
            
        Returns:
            Importance score (0.0 to 1.0)
        """
        text = f"{title} {content}".lower()
        score = 0.0
        
        # High importance keywords
        for keyword in self.importance_keywords['high']:
            if keyword in text:
                score += 0.3
        
        # Medium importance keywords
        for keyword in self.importance_keywords['medium']:
            if keyword in text:
                score += 0.2
        
        # Low importance keywords (negative impact)
        for keyword in self.importance_keywords['low']:
            if keyword in text:
                score -= 0.1
        
        # Boost score for cryptocurrency mentions
        crypto_mentions = len(self.extract_symbols(text))
        score += crypto_mentions * 0.1
        
        # Boost score for title keywords
        title_lower = title.lower()
        for keyword in self.importance_keywords['high']:
            if keyword in title_lower:
                score += 0.2
        
        # Normalize score
        return max(0.0, min(1.0, score))
    
    async def fetch_rss_feed(self, source: NewsSource) -> List[Dict[str, Any]]:
        """
        Fetch RSS feed for a news source
        
        Args:
            source: News source to fetch
            
        Returns:
            List of article data from RSS feed
        """
        try:
            config = NewsSourceConfig.SOURCES[source]
            rss_url = config['rss_url']
            
            # Use requests for RSS parsing (feedparser doesn't work well with aiohttp)
            response = requests.get(rss_url, timeout=30)
            response.raise_for_status()
            
            # Parse RSS feed
            feed = feedparser.parse(response.content)
            articles = []
            
            for entry in feed.entries[:self.max_articles_per_source]:
                # Extract published date
                published_at = datetime.now()
                if hasattr(entry, 'published_parsed') and entry.published_parsed:
                    published_at = datetime(*entry.published_parsed[:6])
                elif hasattr(entry, 'updated_parsed') and entry.updated_parsed:
                    published_at = datetime(*entry.updated_parsed[:6])
                
                article_data = {
                    'title': entry.get('title', ''),
                    'url': entry.get('link', ''),
                    'summary': entry.get('summary', ''),
                    'published_at': published_at,
                    'author': entry.get('author', ''),
                    'source': source
                }
                
                articles.append(article_data)
            
            logger.info(f"Fetched {len(articles)} articles from {source.value} RSS feed")
            return articles
            
        except Exception as e:
            logger.error(f"Error fetching RSS feed for {source.value}: {e}")
            return []
    
    async def scrape_article_content(self, url: str, source: NewsSource) -> Tuple[str, str]:
        """
        Scrape full article content from URL
        
        Args:
            url: Article URL
            source: News source
            
        Returns:
            Tuple of (content, author)
        """
        try:
            if not self.session:
                return "", ""
            
            config = NewsSourceConfig.SOURCES.get(source, {})
            selectors = config.get('selectors', {})
            
            async with self.session.get(url) as response:
                if response.status != 200:
                    logger.warning(f"Failed to fetch article: {url} (status: {response.status})")
                    return "", ""
                
                html = await response.text()
                soup = BeautifulSoup(html, 'html.parser')
                
                # Extract content
                content = ""
                content_selectors = selectors.get('content', 'div.content, div.article-body, div.post-content')
                content_elements = soup.select(content_selectors)
                
                if content_elements:
                    content = ' '.join([elem.get_text().strip() for elem in content_elements])
                else:
                    # Fallback: try to get text from common content containers
                    fallback_selectors = ['article', 'div.content', 'div.article', 'div.post', 'main']
                    for selector in fallback_selectors:
                        elements = soup.select(selector)
                        if elements:
                            content = elements[0].get_text().strip()
                            break
                
                # Extract author
                author = ""
                author_selectors = selectors.get('author', 'span.author, div.author, a.author')
                author_elements = soup.select(author_selectors)
                
                if author_elements:
                    author = author_elements[0].get_text().strip()
                
                # Clean content
                content = re.sub(r'\s+', ' ', content).strip()
                content = content[:5000]  # Limit content length
                
                return content, author
                
        except Exception as e:
            logger.error(f"Error scraping article content from {url}: {e}")
            return "", ""
    
    async def scrape_source(self, source: NewsSource) -> List[NewsArticle]:
        """
        Scrape articles from a single news source
        
        Args:
            source: News source to scrape
            
        Returns:
            List of NewsArticle objects
        """
        try:
            # Fetch RSS feed
            rss_articles = await self.fetch_rss_feed(source)
            
            if not rss_articles:
                return []
            
            articles = []
            
            # Process each article
            for article_data in rss_articles:
                # Generate article hash
                article_hash = self.get_article_hash(article_data['title'], article_data['url'])
                
                # Skip if already scraped
                if article_hash in self.scraped_articles:
                    continue
                
                # Scrape full content
                content, author = await self.scrape_article_content(article_data['url'], source)
                
                # Use summary if content scraping failed
                if not content:
                    content = article_data.get('summary', '')
                
                if not content:
                    continue
                
                # Extract symbols and calculate importance
                symbols_mentioned = self.extract_symbols(f"{article_data['title']} {content}")
                importance_score = self.calculate_importance_score(article_data['title'], content)
                
                # Create NewsArticle object
                article = NewsArticle(
                    title=article_data['title'],
                    content=content,
                    url=article_data['url'],
                    source=source,
                    published_at=article_data['published_at'],
                    author=author or article_data['author'],
                    tags=[],  # Can be enhanced with tag extraction
                    symbols_mentioned=symbols_mentioned,
                    sentiment_score=None,  # Will be filled by sentiment analyzer
                    importance_score=importance_score,
                    article_hash=article_hash,
                    scraped_at=datetime.now()
                )
                
                articles.append(article)
                self.scraped_articles[article_hash] = article
                
                # Small delay to be respectful
                await asyncio.sleep(0.5)
            
            logger.info(f"Successfully scraped {len(articles)} articles from {source.value}")
            return articles
            
        except Exception as e:
            logger.error(f"Error scraping source {source.value}: {e}")
            return []
    
    async def scrape_all_sources(self) -> List[NewsArticle]:
        """
        Scrape articles from all configured sources
        
        Returns:
            List of all scraped NewsArticle objects
        """
        all_articles = []
        
        # Create scraping tasks for all sources
        tasks = [self.scrape_source(source) for source in self.sources]
        
        # Execute tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Collect results
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Error scraping {self.sources[i].value}: {result}")
            else:
                all_articles.extend(result)
        
        # Sort by importance and published date
        all_articles.sort(key=lambda x: (x.importance_score, x.published_at), reverse=True)
        
        logger.info(f"Total articles scraped: {len(all_articles)}")
        return all_articles
    
    def filter_articles_by_symbols(self, articles: List[NewsArticle], symbols: List[str]) -> List[NewsArticle]:
        """
        Filter articles by cryptocurrency symbols
        
        Args:
            articles: List of articles to filter
            symbols: List of symbols to filter by (e.g., ['BTC', 'ETH', 'DOGE'])
            
        Returns:
            Filtered list of articles
        """
        filtered_articles = []
        symbols_upper = [s.upper() for s in symbols]
        
        for article in articles:
            if any(symbol in article.symbols_mentioned for symbol in symbols_upper):
                filtered_articles.append(article)
        
        return filtered_articles
    
    def filter_articles_by_time(self, articles: List[NewsArticle], hours: int = 24) -> List[NewsArticle]:
        """
        Filter articles by publication time
        
        Args:
            articles: List of articles to filter
            hours: Number of hours to look back
            
        Returns:
            Filtered list of articles
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [article for article in articles if article.published_at >= cutoff_time]
    
    def get_news_summary(self, articles: List[NewsArticle]) -> Dict[str, Any]:
        """
        Generate summary statistics for news articles
        
        Args:
            articles: List of articles to summarize
            
        Returns:
            Dictionary with summary statistics
        """
        if not articles:
            return {
                'total_articles': 0,
                'sources': {},
                'symbols': {},
                'avg_importance': 0.0,
                'time_range': None
            }
        
        # Source distribution
        source_counts = {}
        for article in articles:
            source = article.source.value
            source_counts[source] = source_counts.get(source, 0) + 1
        
        # Symbol distribution
        symbol_counts = {}
        for article in articles:
            for symbol in article.symbols_mentioned:
                symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
        
        # Time range
        published_times = [article.published_at for article in articles]
        time_range = {
            'earliest': min(published_times),
            'latest': max(published_times)
        } if published_times else None
        
        # Average importance
        avg_importance = np.mean([article.importance_score for article in articles])
        
        return {
            'total_articles': len(articles),
            'sources': source_counts,
            'symbols': dict(sorted(symbol_counts.items(), key=lambda x: x[1], reverse=True)),
            'avg_importance': avg_importance,
            'time_range': time_range,
            'high_importance_count': sum(1 for a in articles if a.importance_score > 0.7),
            'recent_count': sum(1 for a in articles if (datetime.now() - a.published_at).total_seconds() < 3600)
        }

# Example usage
async def main():
    """Example usage of the news scraper"""
    
    # Initialize scraper
    sources = [NewsSource.COINDESK, NewsSource.COINTELEGRAPH]
    
    async with NewsScraperManager(sources=sources, max_articles_per_source=10) as scraper:
        # Scrape all sources
        articles = await scraper.scrape_all_sources()
        
        print(f"Scraped {len(articles)} articles total")
        
        # Filter for specific symbols
        doge_articles = scraper.filter_articles_by_symbols(articles, ['DOGE', 'BTC'])
        print(f"Found {len(doge_articles)} articles mentioning DOGE or BTC")
        
        # Filter by time (last 24 hours)
        recent_articles = scraper.filter_articles_by_time(articles, hours=24)
        print(f"Found {len(recent_articles)} articles from last 24 hours")
        
        # Get summary
        summary = scraper.get_news_summary(articles)
        print(f"News summary: {summary}")
        
        # Print top articles
        print("\nTop Articles:")
        for i, article in enumerate(articles[:5]):
            print(f"{i+1}. {article.title}")
            print(f"   Source: {article.source.value}")
            print(f"   Importance: {article.importance_score:.2f}")
            print(f"   Symbols: {article.symbols_mentioned}")
            print(f"   URL: {article.url}")
            print()

if __name__ == "__main__":
    asyncio.run(main())
