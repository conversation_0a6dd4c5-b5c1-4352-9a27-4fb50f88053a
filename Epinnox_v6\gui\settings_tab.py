"""
Settings Tab for Epinnox v6 Trading System
Configuration and system settings management
"""

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
except ImportError:
    print("PyQt5 not installed. Install with: pip install PyQt5")
    raise

from .base_tab import BaseTab
from .matrix_theme import MatrixTheme
import json
import logging

logger = logging.getLogger(__name__)

class SettingsTab(BaseTab):
    """Settings tab for system configuration"""
    
    # Signals
    settings_saved = pyqtSignal(dict)
    settings_loaded = pyqtSignal(dict)
    settings_reset = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def setup_ui(self):
        """Setup the Settings tab UI"""
        layout = QHBoxLayout(self)
        
        # Left panel - Trading Settings
        left_panel = self.create_left_panel()
        left_panel.setMaximumWidth(400)
        
        # Right panel - System Information
        right_panel = self.create_right_panel()
        
        layout.addWidget(left_panel)
        layout.addWidget(right_panel)
    
    def create_left_panel(self):
        """Create left settings panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Timeframe weights
        timeframe_group = self.create_timeframe_weights()
        
        # Leverage settings
        leverage_group = self.create_leverage_settings()
        
        # ML Model settings
        ml_group = self.create_ml_settings()
        
        # Signal hierarchy weights
        hierarchy_group = self.create_hierarchy_weights()
        
        # Buttons
        button_layout = self.create_settings_buttons()
        
        layout.addWidget(timeframe_group)
        layout.addWidget(leverage_group)
        layout.addWidget(ml_group)
        layout.addWidget(hierarchy_group)
        layout.addLayout(button_layout)
        layout.addStretch()
        
        return panel
    
    def create_timeframe_weights(self):
        """Create timeframe weights settings"""
        group = self.create_matrix_group_box("Timeframe Weights")
        layout = QVBoxLayout(group)
        
        self.timeframe_1m_weight = QDoubleSpinBox()
        self.timeframe_1m_weight.setRange(0.0, 1.0)
        self.timeframe_1m_weight.setSingleStep(0.1)
        self.timeframe_1m_weight.setValue(0.2)
        self.timeframe_1m_weight.setDecimals(1)
        
        self.timeframe_5m_weight = QDoubleSpinBox()
        self.timeframe_5m_weight.setRange(0.0, 1.0)
        self.timeframe_5m_weight.setSingleStep(0.1)
        self.timeframe_5m_weight.setValue(0.3)
        self.timeframe_5m_weight.setDecimals(1)
        
        self.timeframe_15m_weight = QDoubleSpinBox()
        self.timeframe_15m_weight.setRange(0.0, 1.0)
        self.timeframe_15m_weight.setSingleStep(0.1)
        self.timeframe_15m_weight.setValue(0.5)
        self.timeframe_15m_weight.setDecimals(1)
        
        layout.addWidget(QLabel("1m Weight:"))
        layout.addWidget(self.timeframe_1m_weight)
        layout.addWidget(QLabel("5m Weight:"))
        layout.addWidget(self.timeframe_5m_weight)
        layout.addWidget(QLabel("15m Weight:"))
        layout.addWidget(self.timeframe_15m_weight)
        
        return group
    
    def create_leverage_settings(self):
        """Create leverage management settings"""
        group = self.create_matrix_group_box("Leverage Management")
        layout = QVBoxLayout(group)
        
        self.base_balance = QDoubleSpinBox()
        self.base_balance.setRange(1.0, 100000.0)
        self.base_balance.setValue(50.0)
        self.base_balance.setPrefix("$")
        
        self.max_risk_per_trade = QDoubleSpinBox()
        self.max_risk_per_trade.setRange(0.1, 20.0)
        self.max_risk_per_trade.setValue(5.0)
        self.max_risk_per_trade.setSuffix("%")
        
        self.conservative_mode = QCheckBox("Conservative Position Sizing")
        
        layout.addWidget(QLabel("Base Balance:"))
        layout.addWidget(self.base_balance)
        layout.addWidget(QLabel("Max Risk per Trade:"))
        layout.addWidget(self.max_risk_per_trade)
        layout.addWidget(self.conservative_mode)
        
        return group
    
    def create_ml_settings(self):
        """Create ML model settings"""
        group = self.create_matrix_group_box("ML Model Settings")
        layout = QVBoxLayout(group)
        
        self.confidence_threshold = QDoubleSpinBox()
        self.confidence_threshold.setRange(0.1, 0.9)
        self.confidence_threshold.setValue(0.6)
        self.confidence_threshold.setSingleStep(0.1)
        self.confidence_threshold.setDecimals(1)
        
        self.retrain_interval = QSpinBox()
        self.retrain_interval.setRange(1, 168)
        self.retrain_interval.setValue(24)
        self.retrain_interval.setSuffix(" hours")
        
        layout.addWidget(QLabel("Confidence Threshold:"))
        layout.addWidget(self.confidence_threshold)
        layout.addWidget(QLabel("Retrain Interval:"))
        layout.addWidget(self.retrain_interval)
        
        return group
    
    def create_hierarchy_weights(self):
        """Create signal hierarchy weights"""
        group = self.create_matrix_group_box("Signal Hierarchy Weights")
        layout = QVBoxLayout(group)
        
        self.ml_weight = QDoubleSpinBox()
        self.ml_weight.setRange(0.0, 1.0)
        self.ml_weight.setValue(0.35)
        self.ml_weight.setSingleStep(0.05)
        self.ml_weight.setDecimals(2)
        
        self.technical_weight = QDoubleSpinBox()
        self.technical_weight.setRange(0.0, 1.0)
        self.technical_weight.setValue(0.25)
        self.technical_weight.setSingleStep(0.05)
        self.technical_weight.setDecimals(2)
        
        self.mtf_weight = QDoubleSpinBox()
        self.mtf_weight.setRange(0.0, 1.0)
        self.mtf_weight.setValue(0.20)
        self.mtf_weight.setSingleStep(0.05)
        self.mtf_weight.setDecimals(2)
        
        layout.addWidget(QLabel("ML Ensemble Weight:"))
        layout.addWidget(self.ml_weight)
        layout.addWidget(QLabel("Technical Signals Weight:"))
        layout.addWidget(self.technical_weight)
        layout.addWidget(QLabel("Multi-Timeframe Weight:"))
        layout.addWidget(self.mtf_weight)
        
        return group
    
    def create_settings_buttons(self):
        """Create settings control buttons"""
        layout = QHBoxLayout()
        
        self.save_settings_button = self.create_matrix_button("SAVE SETTINGS", self.save_settings)
        self.load_settings_button = self.create_matrix_button("LOAD SETTINGS", self.load_settings)
        self.reset_settings_button = self.create_matrix_button("RESET TO DEFAULTS", self.reset_settings)
        
        layout.addWidget(self.save_settings_button)
        layout.addWidget(self.load_settings_button)
        layout.addWidget(self.reset_settings_button)
        
        return layout
    
    def create_right_panel(self):
        """Create right information panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # System status
        system_group = self.create_matrix_group_box("System Information")
        system_layout = QVBoxLayout(system_group)
        
        self.system_info_text = QTextEdit()
        self.system_info_text.setReadOnly(True)
        self.system_info_text.setMaximumHeight(200)
        
        system_layout.addWidget(self.system_info_text)
        
        # Configuration log
        config_group = self.create_matrix_group_box("Configuration Log")
        config_layout = QVBoxLayout(config_group)
        
        self.config_log = QTextEdit()
        self.config_log.setReadOnly(True)
        
        config_layout.addWidget(self.config_log)
        
        layout.addWidget(system_group)
        layout.addWidget(config_group)
        
        return panel
    
    def save_settings(self):
        """Save current settings"""
        try:
            settings = self.get_current_settings()
            self.settings_saved.emit(settings)
            self.log_message("Settings saved successfully")
            self.show_info_message("Settings", "Settings saved successfully")
        except Exception as e:
            self.log_message(f"Error saving settings: {e}", logging.ERROR)
            self.show_error_message("Settings Error", f"Failed to save settings: {str(e)}")
    
    def load_settings(self):
        """Load settings from file"""
        try:
            self.settings_loaded.emit({})
            self.log_message("Settings loaded successfully")
        except Exception as e:
            self.log_message(f"Error loading settings: {e}", logging.ERROR)
            self.show_error_message("Settings Error", f"Failed to load settings: {str(e)}")
    
    def reset_settings(self):
        """Reset settings to defaults"""
        try:
            self.reset_to_defaults()
            self.settings_reset.emit()
            self.log_message("Settings reset to defaults")
            self.show_info_message("Settings", "Settings reset to defaults")
        except Exception as e:
            self.log_message(f"Error resetting settings: {e}", logging.ERROR)
    
    def get_current_settings(self):
        """Get current settings values"""
        return {
            'timeframe_weights': {
                '1m': self.timeframe_1m_weight.value(),
                '5m': self.timeframe_5m_weight.value(),
                '15m': self.timeframe_15m_weight.value()
            },
            'leverage_settings': {
                'base_balance': self.base_balance.value(),
                'max_risk_per_trade': self.max_risk_per_trade.value(),
                'conservative_mode': self.conservative_mode.isChecked()
            },
            'ml_settings': {
                'confidence_threshold': self.confidence_threshold.value(),
                'retrain_interval': self.retrain_interval.value()
            },
            'hierarchy_weights': {
                'ml_weight': self.ml_weight.value(),
                'technical_weight': self.technical_weight.value(),
                'mtf_weight': self.mtf_weight.value()
            }
        }
    
    def apply_settings(self, settings):
        """Apply settings to UI controls"""
        try:
            # Timeframe weights
            tf_weights = settings.get('timeframe_weights', {})
            self.timeframe_1m_weight.setValue(tf_weights.get('1m', 0.2))
            self.timeframe_5m_weight.setValue(tf_weights.get('5m', 0.3))
            self.timeframe_15m_weight.setValue(tf_weights.get('15m', 0.5))
            
            # Leverage settings
            lev_settings = settings.get('leverage_settings', {})
            self.base_balance.setValue(lev_settings.get('base_balance', 50.0))
            self.max_risk_per_trade.setValue(lev_settings.get('max_risk_per_trade', 5.0))
            self.conservative_mode.setChecked(lev_settings.get('conservative_mode', False))
            
            # ML settings
            ml_settings = settings.get('ml_settings', {})
            self.confidence_threshold.setValue(ml_settings.get('confidence_threshold', 0.6))
            self.retrain_interval.setValue(ml_settings.get('retrain_interval', 24))
            
            # Hierarchy weights
            hier_weights = settings.get('hierarchy_weights', {})
            self.ml_weight.setValue(hier_weights.get('ml_weight', 0.35))
            self.technical_weight.setValue(hier_weights.get('technical_weight', 0.25))
            self.mtf_weight.setValue(hier_weights.get('mtf_weight', 0.20))
            
        except Exception as e:
            self.log_message(f"Error applying settings: {e}", logging.ERROR)
    
    def reset_to_defaults(self):
        """Reset all settings to default values"""
        default_settings = {
            'timeframe_weights': {'1m': 0.2, '5m': 0.3, '15m': 0.5},
            'leverage_settings': {'base_balance': 50.0, 'max_risk_per_trade': 5.0, 'conservative_mode': False},
            'ml_settings': {'confidence_threshold': 0.6, 'retrain_interval': 24},
            'hierarchy_weights': {'ml_weight': 0.35, 'technical_weight': 0.25, 'mtf_weight': 0.20}
        }
        self.apply_settings(default_settings)
