"""
Setup script for Epinnox v6 Trading System
Enterprise-grade AI-powered trading platform
"""
from setuptools import setup, find_packages
import os

# Read requirements from requirements.txt
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    with open(requirements_path, 'r', encoding='utf-8') as f:
        requirements = []
        for line in f:
            line = line.strip()
            # Skip comments, empty lines, and optional dependencies
            if line and not line.startswith('#') and not line.startswith('torch-'):
                # Remove version constraints for setup.py compatibility
                if '>=' in line:
                    package = line.split('>=')[0]
                    requirements.append(package)
                elif '==' in line:
                    package = line.split('==')[0]
                    requirements.append(package)
                else:
                    requirements.append(line)
        return requirements

# Read long description from README
def read_long_description():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Epinnox v6 - Enterprise AI-Powered Trading System"

setup(
    name="epinnox",
    version="6.0.0",
    packages=find_packages(),
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "epinnox=launch_epinnox:main",
            "epinnox-gui=launch_gui:main",
        ],
    },
    author="Epinnox Development Team",
    author_email="<EMAIL>",
    description="Enterprise AI-Powered Trading System with LLM Integration",
    long_description=read_long_description(),
    long_description_content_type="text/markdown",
    keywords="trading, cryptocurrency, futures, ai, llm, machine-learning, algorithmic-trading",
    url="https://github.com/Geo222222/potential-octo-parakeet",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.8",
    include_package_data=True,
    zip_safe=False,
)
