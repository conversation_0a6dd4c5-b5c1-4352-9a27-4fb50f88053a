"""
Simulation Trading Script
This script runs the Epinnox Trading System with a simulated account.
"""
import logging
import argparse
import time
import yaml
import os
import random
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import threading

from main import run_trading_system
from data.exchange import ExchangeDataFetcher
from dynamic_targets import DynamicTargetCalculator
from core.signal_scoring import SignalScorer
from core.trade_lifecycle import TradeManager
from utils import CliDisplay
from core.multi_timeframe import MultiTimeframeAnalyzer
from core.market_regime import MarketRegimeDetector
from core.adaptive_risk import AdaptiveRiskManager

# Try to import dashboard
try:
    from dashboard import run_dashboard, PYQT_AVAILABLE, DISPLAY_AVAILABLE
    DASHBOARD_AVAILABLE = PYQT_AVAILABLE and DISPLAY_AVAILABLE
except ImportError:
    DASHBOARD_AVAILABLE = False
    print("Dashboard not available. Install PyQt5 with: pip install PyQt5")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("simulation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimulatedTrader:
    def __init__(self, initial_balance=1000.0, leverage=75):
        self.balance = initial_balance
        self.positions = {}
        self.trade_history = []
        self.leverage = leverage
        self.balance_history = []
        self.record_balance()

    def open_position(self, symbol, side, amount, price, leverage=None):
        """
        Open a new simulated position
        """
        leverage = leverage or self.leverage
        position_value = amount * price
        margin = position_value / leverage

        if margin > self.balance:
            logger.warning(f"Insufficient balance for position. Required: {margin}, Available: {self.balance}")
            return None

        position_id = len(self.positions) + 1
        position = {
            'id': position_id,
            'symbol': symbol,
            'side': side,
            'amount': amount,
            'entry_price': price,
            'leverage': leverage,
            'margin': margin,
            'position_value': position_value,
            'timestamp': datetime.now(),
            'status': 'open',
            'unrealized_pnl': 0.0,
            'exit_price': None,
            'exit_timestamp': None,
            'realized_pnl': None
        }

        self.positions[position_id] = position
        self.balance -= margin
        self.record_balance()
        self.record_trade('open', position)
        
        return position_id

    def update_position(self, position_id, current_price):
        """
        Update position's unrealized PnL based on current price
        """
        if position_id not in self.positions:
            return False

        position = self.positions[position_id]
        if position['status'] != 'open':
            return False

        entry_price = position['entry_price']
        amount = position['amount']

        if position['side'] == 'buy':
            pnl = (current_price - entry_price) * amount * position['leverage']
        else:
            pnl = (entry_price - current_price) * amount * position['leverage']

        position['unrealized_pnl'] = pnl
        return True

    def close_position(self, position_id, exit_price):
        """
        Close a position and realize PnL
        """
        if position_id not in self.positions:
            return False

        position = self.positions[position_id]
        if position['status'] != 'open':
            return False

        # Calculate realized PnL
        if position['side'] == 'buy':
            pnl = (exit_price - position['entry_price']) * position['amount'] * position['leverage']
        else:
            pnl = (position['entry_price'] - exit_price) * position['amount'] * position['leverage']

        # Update position
        position['status'] = 'closed'
        position['exit_price'] = exit_price
        position['exit_timestamp'] = datetime.now()
        position['realized_pnl'] = pnl

        # Return margin and add PnL to balance
        self.balance += position['margin'] + pnl
        self.record_balance()
        self.record_trade('close', position)

        return True

    def record_balance(self):
        """Record current balance"""
        self.balance_history.append({
            'timestamp': datetime.now(),
            'balance': self.balance
        })

    def record_trade(self, trade_type, position):
        """Record trade in history"""
        self.trade_history.append({
            'timestamp': datetime.now(),
            'type': trade_type,
            'symbol': position['symbol'],
            'side': position['side'],
            'amount': position['amount'],
            'price': position['entry_price'] if trade_type == 'open' else position['exit_price'],
            'leverage': position['leverage'],
            'margin': position['margin'],
            'balance_after': self.balance
        })

    def get_open_positions(self):
        """Get all open positions"""
        return {k: v for k, v in self.positions.items() if v['status'] == 'open'}

    def get_balance(self):
        """Get current balance"""
        return self.balance

    def get_trade_history(self):
        """Get trade history"""
        return self.trade_history

    def get_balance_history(self):
        """Get balance history"""
        return self.balance_history

class SimulatedMarket:
    """
    Simulated market for testing.
    """
    def __init__(self, symbol, initial_price, volatility=0.001):
        """
        Initialize the simulated market.

        Args:
            symbol: Trading symbol
            initial_price: Initial price
            volatility: Price volatility
        """
        self.symbol = symbol
        self.price = initial_price
        self.volatility = volatility
        self.price_history = []
        self.record_price()

        logger.info(f"Initialized simulated market for {symbol} at {initial_price}")

    def record_price(self):
        """
        Record current price.
        """
        self.price_history.append({
            'timestamp': datetime.now(),
            'price': self.price
        })

    def update_price(self):
        """
        Update price with random movement.

        Returns:
            float: New price
        """
        # Generate random price movement
        change_pct = np.random.normal(0, self.volatility)

        # Update price
        self.price *= (1 + change_pct)
        self.record_price()

        return self.price

    def get_price(self):
        """
        Get current price.

        Returns:
            float: Current price
        """
        return self.price

    def get_price_history(self):
        """
        Get price history.

        Returns:
            list: Price history
        """
        return self.price_history

    def plot_price_history(self, save_path=None):
        """
        Plot price history.

        Args:
            save_path: Path to save the plot (optional)
        """
        df = pd.DataFrame(self.price_history)

        plt.figure(figsize=(12, 6))
        plt.plot(df['timestamp'], df['price'], label='Price', color='green', linewidth=2)

        plt.title(f'{self.symbol} Price History', fontsize=16)
        plt.xlabel('Time', fontsize=12)
        plt.ylabel('Price', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.legend()

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path)
            logger.info(f"Saved price history plot to {save_path}")
        else:
            plt.show()

        plt.close()

def load_config(config_path='simulation_config.yaml'):
    """
    Load simulation configuration from YAML file.

    Args:
        config_path: Path to the simulation configuration YAML file

    Returns:
        dict: Simulation configuration
    """
    try:
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
        logger.info(f"Loaded simulation configuration from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Error loading simulation configuration: {e}")
        raise

def run_simulation(args):
    """
    Run the trading simulation.

    Args:
        args: Command line arguments
    """
    logger.info(f"Starting trading simulation for {args.symbol}")

    # Load configuration
    config = load_config(args.config)

    # Initialize components
    account = SimulatedAccount(
        initial_balance=config['simulation']['initial_balance'],
        currency=config['simulation']['currency']
    )

    # Initialize components
    signal_scorer = SignalScorer()
    trade_manager = TradeManager(save_dir=os.path.join(config['performance']['save_dir'], 'trades'))
    mt_analyzer = MultiTimeframeAnalyzer(timeframes=['1m', '5m', '15m'])
    regime_detector = MarketRegimeDetector()
    risk_manager = AdaptiveRiskManager()

    # Initialize CLI display
    cli_display = CliDisplay()

    # Initialize dashboard if available
    dashboard_app = None
    dashboard = None
    if DASHBOARD_AVAILABLE and args.dashboard:
        try:
            dashboard_app, dashboard = run_dashboard(account, trade_manager, args.symbol)
            logger.info("Started PyQt dashboard")
        except Exception as e:
            logger.error(f"Error starting dashboard: {e}")
            dashboard_app = None
            dashboard = None

    # Get initial price from exchange
    data_fetcher = ExchangeDataFetcher(exchange_id='htx')
    try:
        # Try to get the latest price from OHLCV data
        ohlcv = data_fetcher.fetch_ohlcv(args.symbol, limit=1)
        initial_price = ohlcv['close'].iloc[0] if not ohlcv.empty else 0.15
    except Exception as e:
        logger.warning(f"Error fetching initial price: {e}")
        initial_price = 0.15  # Default to 0.15 if fetch fails

    logger.info(f"Initial price for {args.symbol}: {initial_price}")

    market = SimulatedMarket(
        symbol=args.symbol,
        initial_price=initial_price,
        volatility=0.001  # 0.1% volatility per update
    )

    # Get asset configuration
    asset_config = config['trading']['assets'].get(args.symbol, config['trading']['default'])
    leverage = asset_config.get('leverage', 1)
    risk_percentage = asset_config.get('risk_percentage', 2.0)
    max_position_size = asset_config.get('max_position_size', 100.0)

    # Initialize dynamic target calculator
    target_calculator = DynamicTargetCalculator(exchange_id='htx')

    # Calculate initial dynamic targets based on historical data
    # We'll recalculate with signal confidence later
    dynamic_targets = target_calculator.calculate_targets(args.symbol, leverage=leverage)

    # Use dynamic targets or fall back to config values if calculation fails
    stop_loss_percentage = dynamic_targets.get('stop_loss', asset_config.get('stop_loss_percentage', 2.0))
    take_profit_percentage = dynamic_targets.get('take_profit', asset_config.get('take_profit_percentage', 4.0))

    # We'll store the base leverage for later adjustment
    base_leverage = leverage

    logger.info(f"Using dynamic targets: Take profit: {take_profit_percentage:.2f}%, Stop loss: {stop_loss_percentage:.2f}%")

    logger.info(f"Using {leverage}x leverage for {args.symbol}")

    # Create save directory
    os.makedirs(config['performance']['save_dir'], exist_ok=True)

    # Calculate simulation end time
    start_time = datetime.now()
    end_time = start_time + timedelta(minutes=config['simulation']['duration_minutes'])

    logger.info(f"Simulation will run until {end_time}")

    # Run simulation
    try:
        run_count = 0
        active_positions = {}

        while datetime.now() < end_time:
            run_count += 1
            current_time = datetime.now()
            logger.info(f"Run #{run_count} at {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

            # Update market price
            current_price = market.update_price()

            # Update open positions
            open_positions = account.get_open_positions()
            for pos_id, position in open_positions.items():
                account.update_position(pos_id, current_price)

                # Check for stop loss or take profit
                if position['side'] == 'buy':
                    stop_loss_price = position['entry_price'] * (1 - stop_loss_percentage / 100)
                    take_profit_price = position['entry_price'] * (1 + take_profit_percentage / 100)

                    if current_price <= stop_loss_price:
                        logger.info(f"Stop loss triggered for position {pos_id}")
                        account.close_position(pos_id, current_price)
                    elif current_price >= take_profit_price:
                        logger.info(f"Take profit triggered for position {pos_id}")
                        account.close_position(pos_id, current_price)
                else:
                    stop_loss_price = position['entry_price'] * (1 + stop_loss_percentage / 100)
                    take_profit_price = position['entry_price'] * (1 - take_profit_percentage / 100)

                    if current_price >= stop_loss_price:
                        logger.info(f"Stop loss triggered for position {pos_id}")
                        account.close_position(pos_id, current_price)
                    elif current_price <= take_profit_price:
                        logger.info(f"Take profit triggered for position {pos_id}")
                        account.close_position(pos_id, current_price)

            # Run trading system
            # Make sure we're using the correct symbol format
            symbol = args.symbol
            if symbol.endswith(':USDT:USDT'):
                # Fix incorrect symbol format
                symbol = symbol.replace(':USDT:USDT', ':USDT')
                logger.info(f"Corrected symbol format from {args.symbol} to {symbol}")

            # Get market data and trading decision
            decision, explanation, parsed_response = run_trading_system(
                symbol,
                interval=args.interval,
                period=args.period,
                use_exchange=True,
                use_live_data=args.live
            )

            # Extract model confidence and TP/SL if available
            model_confidence = parsed_response.get('confidence')
            model_take_profit = parsed_response.get('take_profit')
            model_stop_loss = parsed_response.get('stop_loss')

            # Get signal scores and alignment
            signal_scores = parsed_response.get('signal_scores', {})
            signal_confidence = signal_scores.get('confidence', 50.0)
            signal_alignment = signal_scores.get('alignment', 50.0)

            # Calculate signal scores
            # Get the latest market data
            market_data = data_fetcher.fetch_combined_data(
                spot_symbol=symbol.split(':')[0] if ':' in symbol else symbol,
                futures_symbol=symbol,
                timeframe='5m',
                limit=100,
                include_trades=True
            )

            # Convert to DataFrame for signal scoring
            market_df = pd.DataFrame()
            if 'spot_ohlcv' in market_data and market_data['spot_ohlcv']:
                spot_df = pd.DataFrame(market_data['spot_ohlcv'])
                market_df = spot_df.copy()

            # Add order book data
            if 'spot_orderbook' in market_data:
                market_df['spot_orderbook'] = [market_data['spot_orderbook']] * len(market_df)
            if 'futures_orderbook' in market_data:
                market_df['futures_orderbook'] = [market_data['futures_orderbook']] * len(market_df)

            # Calculate signal scores
            scores = signal_scorer.calculate_scores(market_df)
            model_confidence = scores['confidence']
            signal_score = scores['total_score']

            # Update open positions with current price
            trade_manager.update_positions(current_price, symbol)

            # Check for positions that should be closed based on TP/SL
            positions_to_close = trade_manager.check_exit_conditions(current_price, symbol)
            for position in positions_to_close:
                closed_position = trade_manager.close_position(position['id'], current_price, position['exit_reason'])

                if closed_position:
                    # Print trade closure activity log
                    pnl = closed_position['realized_pnl']
                    pnl_pct = closed_position['pnl_percentage']
                    emoji = '🟢' if pnl > 0 else '🔴'
                    reason = closed_position['exit_reason'].upper()

                    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] {emoji} CLOSED TRADE: {closed_position['side']} position at {closed_position['exit_price']:.6f}")
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] 💰 PnL: {pnl:.2f} ({pnl_pct:.2f}%), Reason: {reason}")
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] ⏱️ Duration: {closed_position['duration_seconds']:.1f} seconds")

            # Print results using CLI display
            cli_display.clear_screen()
            cli_display.print_header(f"EPINNOX TRADING SYSTEM - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
            cli_display.print_separator('=')

            # Print market info
            cli_display.print_market_info(args.symbol, current_price, current_time)
            cli_display.print_separator('-')

            # Print account info
            cli_display.print_account_info(account.get_balance(), account.initial_balance, config['simulation']['currency'])

            # Print position info
            open_positions = trade_manager.get_open_positions(symbol)
            position = open_positions[0] if open_positions else None
            cli_display.print_position_info(position)
            cli_display.print_separator('-')

            # Print decision info
            confidence_to_use = model_confidence if model_confidence is not None else scores['confidence']
            cli_display.print_decision_info(decision, explanation, confidence_to_use)
            cli_display.print_signal_info(scores)
            cli_display.print_separator('-')

            # Print performance summary
            cli_display.print_performance_summary(trade_manager)
            cli_display.print_separator('=')

            # Print activity log
            print(f"\n[{current_time.strftime('%H:%M:%S')}] Activity: Decision={decision}, Price={current_price:.6f}, Score={signal_score:.4f}")
            if position:
                unrealized_pnl = position['unrealized_pnl']
                print(f"[{current_time.strftime('%H:%M:%S')}] Position: {position['side']} at {position['entry_price']:.6f}, PnL: {unrealized_pnl:.2f}")

            # Update dashboard if available
            if dashboard is not None:
                dashboard.update_decision(decision, explanation, confidence_to_use, scores)

            # Execute trade if requested
            if args.execute and decision != "WAIT" and decision != "ERROR":
                # Calculate position size using dynamic calculator
                available_balance = account.get_balance()

                # Use dynamic position sizing if possible
                try:
                    position_size = target_calculator.calculate_position_size(
                        symbol=args.symbol,
                        available_balance=available_balance,
                        current_price=current_price,
                        leverage=leverage,
                        risk_percentage=risk_percentage
                    )

                    # Ensure position size doesn't exceed max position size
                    position_size_usd = position_size * current_price
                    if position_size_usd > max_position_size:
                        position_size = max_position_size / current_price
                        logger.info(f"Position size reduced to respect max position size: {max_position_size} USD")
                except Exception as e:
                    # Fall back to simple calculation if dynamic sizing fails
                    logger.warning(f"Dynamic position sizing failed: {e}. Using simple calculation.")
                    position_size_usd = min(available_balance * risk_percentage / 100, max_position_size)
                    position_size = position_size_usd / current_price

                # Determine trade side
                side = 'LONG' if decision == 'LONG' else 'SHORT'

                # Get multi-timeframe data for better decision making
                try:
                    # Fetch multi-timeframe data
                    multi_timeframe_data = mt_analyzer.fetch_multi_timeframe_data(
                        data_fetcher,
                        args.symbol.split(':')[0] if ':' in args.symbol else args.symbol
                    )

                    # Calculate indicators and analyze trends
                    if multi_timeframe_data:
                        indicators = mt_analyzer.calculate_indicators(multi_timeframe_data)
                        trend_analysis = mt_analyzer.analyze_trend(indicators)
                        trend_metrics = mt_analyzer.calculate_trend_strength(trend_analysis)

                        # Log multi-timeframe analysis
                        logger.info(f"Multi-timeframe analysis: {trend_metrics['trend_direction']} (strength: {trend_metrics['trend_strength']:.2f})")

                        # Detect market regime using market data from multi-timeframe analysis
                        # Create a simple DataFrame from the primary timeframe data
                        market_df = pd.DataFrame(multi_timeframe_data.get('1m', []))

                        # Detect market regime
                        regime_result = regime_detector.detect_regime(
                            market_df,
                            trend_strength=trend_metrics['trend_strength'],
                            volatility=trend_metrics['volatility'],
                            trend_alignment=trend_metrics['trend_alignment']
                        )

                        # Get regime adjustments
                        regime_adjustments = regime_result['adjustments']
                        logger.info(f"Market regime: {regime_result['primary_regime']}")

                        # Adjust leverage based on market regime
                        adjusted_leverage = leverage * regime_adjustments['leverage_factor']
                        logger.info(f"Adjusted leverage from {leverage}x to {adjusted_leverage:.2f}x based on market regime")
                        leverage = adjusted_leverage

                        # Calculate volatility-based stops using market data
                        adaptive_stops = risk_manager.calculate_volatility_based_stops(
                            market_df,
                            current_price,
                            side,
                            confidence=model_confidence/100 if model_confidence else 0.7
                        )

                        # Use adaptive stops or model's stops
                        if model_take_profit:
                            tp_percentage = model_take_profit
                            logger.info(f"Using model's take profit: {tp_percentage:.2f}%")
                        else:
                            tp_percentage = adaptive_stops['take_profit_pct']
                            logger.info(f"Using adaptive take profit: {tp_percentage:.2f}%")

                        if model_stop_loss:
                            sl_percentage = model_stop_loss
                            logger.info(f"Using model's stop loss: {sl_percentage:.2f}%")
                        else:
                            sl_percentage = adaptive_stops['stop_loss_pct']
                            logger.info(f"Using adaptive stop loss: {sl_percentage:.2f}%")

                        # Calculate position scaling strategy if confidence is high
                        if model_confidence and model_confidence > 70:
                            position_scaling = risk_manager.calculate_position_scaling(
                                position_size,
                                available_balance,
                                model_confidence/100,
                                max_position_value=max_position_size
                            )

                            if position_scaling['scaling_stages'] > 0:
                                logger.info(f"Using position scaling with {position_scaling['scaling_stages']} stages")
                                # Use the total size from scaling strategy
                                position_size = position_scaling['total_size']
                except Exception as e:
                    logger.warning(f"Error in advanced risk management: {e}. Using standard parameters.")
                    # Fallback to original dynamic targets
                    updated_targets = target_calculator.calculate_targets(
                        symbol=args.symbol,
                        leverage=leverage,
                        lookback_days=7,
                        confidence=0.7,
                        signal_confidence=signal_confidence,
                        signal_alignment=signal_alignment
                    )

                    # Get the dynamic targets and adjusted leverage
                    tp_percentage = model_take_profit if model_take_profit else updated_targets['take_profit']
                    sl_percentage = model_stop_loss if model_stop_loss else updated_targets['stop_loss']
                    adjusted_leverage = updated_targets.get('adjusted_leverage', leverage)

                    # Update leverage
                    if adjusted_leverage != leverage:
                        logger.info(f"Adjusted leverage from {leverage}x to {adjusted_leverage:.2f}x based on signal confidence")
                        leverage = adjusted_leverage

                # Calculate take profit and stop loss prices
                if side == 'LONG':
                    take_profit_price = current_price * (1 + tp_percentage / 100)
                    stop_loss_price = current_price * (1 - sl_percentage / 100)
                else:  # SHORT
                    take_profit_price = current_price * (1 - tp_percentage / 100)
                    stop_loss_price = current_price * (1 + sl_percentage / 100)

                # Open position in trade manager
                trade_position = trade_manager.open_position(
                    symbol=args.symbol,
                    side=side,
                    entry_price=current_price,
                    position_size=position_size,
                    leverage=leverage,
                    take_profit_price=take_profit_price,
                    stop_loss_price=stop_loss_price,
                    model_confidence=model_confidence,
                    signal_score=signal_score
                )

                # Also open position in account for balance tracking
                account_side = 'buy' if side == 'LONG' else 'sell'
                position = account.open_position(args.symbol, account_side, position_size, current_price, leverage)

                if position and trade_position:
                    logger.info(f"Opened {side} position for {position_size} {args.symbol} at {current_price} with {leverage}x leverage")
                    logger.info(f"Take profit: {take_profit_price:.6f}, Stop loss: {stop_loss_price:.6f}")

                    # Print trade activity log
                    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 🔴 NEW TRADE: Opened {side} position at {current_price:.6f}")
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] 📊 Size: {position_size:.6f}, Leverage: {leverage}x")
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎯 TP: {take_profit_price:.6f}, SL: {stop_loss_price:.6f}")

            # Create visualizations periodically
            if run_count % config['performance']['visualization_interval'] == 0:
                account.plot_balance_history(os.path.join(config['performance']['save_dir'], 'balance_history.png'))
                market.plot_price_history(os.path.join(config['performance']['save_dir'], 'price_history.png'))

                # Create combined plot
                create_combined_plot(
                    account.get_balance_history(),
                    market.get_price_history(),
                    account.get_trade_history(),
                    os.path.join(config['performance']['save_dir'], 'combined_plot.png')
                )

            # Calculate dynamic delay if enabled
            delay_to_use = args.delay
            if args.dynamic_delay and len(market.price_history) > 10:
                # Calculate recent price volatility
                recent_prices = [p['price'] for p in market.price_history[-10:]]
                price_changes = [abs(recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1] * 100 for i in range(1, len(recent_prices))]
                avg_change = sum(price_changes) / len(price_changes)

                # Adjust delay based on volatility
                if avg_change > 0.1:  # High volatility
                    delay_to_use = max(1, args.delay // 3)
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] ⚡ High volatility detected ({avg_change:.4f}%). Using shorter delay: {delay_to_use}s")
                elif avg_change > 0.05:  # Medium volatility
                    delay_to_use = max(2, args.delay // 2)
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🔄 Medium volatility detected ({avg_change:.4f}%). Using medium delay: {delay_to_use}s")
                else:  # Low volatility
                    delay_to_use = args.delay
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🐢 Low volatility detected ({avg_change:.4f}%). Using standard delay: {delay_to_use}s")

            # Wait for next run
            time.sleep(delay_to_use)

        logger.info("Simulation completed")

        # Close any open positions
        open_positions = account.get_open_positions()
        for pos_id, position in open_positions.items():
            account.close_position(pos_id, current_price)

        # Create final visualizations
        account.plot_balance_history(os.path.join(config['performance']['save_dir'], 'final_balance_history.png'))
        market.plot_price_history(os.path.join(config['performance']['save_dir'], 'final_price_history.png'))

        # Create combined plot
        create_combined_plot(
            account.get_balance_history(),
            market.get_price_history(),
            account.get_trade_history(),
            os.path.join(config['performance']['save_dir'], 'final_combined_plot.png')
        )

        # Print final results
        final_balance = account.get_balance()
        profit_loss = final_balance - config['simulation']['initial_balance']
        profit_loss_pct = (profit_loss / config['simulation']['initial_balance']) * 100

        # Generate trade performance report
        trade_report = trade_manager.generate_performance_report()

        print(f"\n{'='*50}")
        print(f"SIMULATION RESULTS")
        print(f"{'='*50}")
        print(f"Initial Balance: {config['simulation']['initial_balance']} {config['simulation']['currency']}")
        print(f"Final Balance: {final_balance} {config['simulation']['currency']}")
        print(f"Profit/Loss: {profit_loss} {config['simulation']['currency']} ({profit_loss_pct:.2f}%)")
        print(f"Total Trades: {len(account.get_trade_history())}")
        print(f"{'='*50}\n")

        # Print trade performance report
        print(trade_report)

        # Save trade performance report to file
        with open(os.path.join(config['performance']['save_dir'], 'trade_report.txt'), 'w') as f:
            f.write(trade_report)

    except KeyboardInterrupt:
        logger.info("Simulation stopped by user")

        # Close any open positions
        open_positions = account.get_open_positions()
        current_price = market.get_price()
        for pos_id, position in open_positions.items():
            account.close_position(pos_id, current_price)

        # Create final visualizations
        account.plot_balance_history(os.path.join(config['performance']['save_dir'], 'final_balance_history.png'))
        market.plot_price_history(os.path.join(config['performance']['save_dir'], 'final_price_history.png'))

        # Create combined plot
        create_combined_plot(
            account.get_balance_history(),
            market.get_price_history(),
            account.get_trade_history(),
            os.path.join(config['performance']['save_dir'], 'final_combined_plot.png')
        )

        # Print final results
        final_balance = account.get_balance()
        profit_loss = final_balance - config['simulation']['initial_balance']
        profit_loss_pct = (profit_loss / config['simulation']['initial_balance']) * 100

        # Generate trade performance report
        trade_report = trade_manager.generate_performance_report()

        print(f"\n{'='*50}")
        print(f"SIMULATION RESULTS (INTERRUPTED)")
        print(f"{'='*50}")
        print(f"Initial Balance: {config['simulation']['initial_balance']} {config['simulation']['currency']}")
        print(f"Final Balance: {final_balance} {config['simulation']['currency']}")
        print(f"Profit/Loss: {profit_loss} {config['simulation']['currency']} ({profit_loss_pct:.2f}%)")
        print(f"Total Trades: {len(account.get_trade_history())}")
        print(f"{'='*50}\n")

        # Print trade performance report
        print(trade_report)

        # Save trade performance report to file
        with open(os.path.join(config['performance']['save_dir'], 'trade_report.txt'), 'w') as f:
            f.write(trade_report)

def create_combined_plot(balance_history, price_history, trade_history, save_path=None):
    """
    Create a combined plot of balance and price history with trade markers.

    Args:
        balance_history: Balance history
        price_history: Price history
        trade_history: Trade history
        save_path: Path to save the plot (optional)
    """
    # Convert to DataFrames
    balance_df = pd.DataFrame(balance_history)
    price_df = pd.DataFrame(price_history)
    trade_df = pd.DataFrame(trade_history)

    # Create figure with two y-axes
    fig, ax1 = plt.subplots(figsize=(14, 8))
    ax2 = ax1.twinx()

    # Plot balance on left y-axis
    ax1.plot(balance_df['timestamp'], balance_df['balance'], label='Balance', color='blue', linewidth=2)
    ax1.set_xlabel('Time', fontsize=12)
    ax1.set_ylabel('Balance (USDT)', color='blue', fontsize=12)
    ax1.tick_params(axis='y', labelcolor='blue')

    # Plot price on right y-axis
    ax2.plot(price_df['timestamp'], price_df['price'], label='Price', color='green', linewidth=2)
    ax2.set_ylabel('Price', color='green', fontsize=12)
    ax2.tick_params(axis='y', labelcolor='green')

    # Add trade markers
    buy_trades = trade_df[trade_df['side'] == 'buy']
    sell_trades = trade_df[trade_df['side'] == 'sell']

    if not buy_trades.empty:
        ax2.scatter(buy_trades['timestamp'], buy_trades['price'], color='green', marker='^', s=100, label='Buy')

    if not sell_trades.empty:
        ax2.scatter(sell_trades['timestamp'], sell_trades['price'], color='red', marker='v', s=100, label='Sell')

    # Add title and legend
    plt.title('Balance and Price History with Trades', fontsize=16)

    # Create combined legend
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

    plt.grid(True, alpha=0.3)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        logger.info(f"Saved combined plot to {save_path}")
    else:
        plt.show()

    plt.close()

def main():
    """
    Main function to run the simulation.
    """
    parser = argparse.ArgumentParser(description='Epinnox Trading Simulation')

    # Basic parameters
    parser.add_argument('--symbol', type=str, default='DOGE/USDT:USDT', help='Trading symbol')
    parser.add_argument('--interval', type=str, default='1m', help='Data interval')
    parser.add_argument('--period', type=str, default='1d', help='Data period')
    parser.add_argument('--delay', type=int, default=10, help='Delay between runs in seconds')
    parser.add_argument('--dynamic-delay', action='store_true', help='Use dynamic delay based on market volatility')
    parser.add_argument('--live', action='store_true', help='Use live trades data instead of historical OHLCV')

    # Simulation parameters
    parser.add_argument('--execute', action='store_true', help='Execute trades')
    parser.add_argument('--config', type=str, default='simulation_config.yaml', help='Path to simulation configuration file')

    # Display parameters
    parser.add_argument('--dashboard', action='store_true', help='Show PyQt dashboard')

    args = parser.parse_args()

    # Run simulation
    run_simulation(args)

    # If dashboard is enabled, start the Qt event loop
    if DASHBOARD_AVAILABLE and args.dashboard:
        import sys
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            sys.exit(app.exec_())

if __name__ == '__main__':
    main()

