"""
Startup Script for Epinnox Trading System
This script initializes the GPU environment and loads the necessary components
"""
import os
import sys
import logging
from pathlib import Path

# Get logger (don't configure here - let main.py handle it)
logger = logging.getLogger(__name__)

def setup_environment():
    """
    Set up the environment for the Epinnox Trading System
    """
    logger.info("Setting up environment for Epinnox Trading System")
    
    # Add the current directory to the Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # Set up GPU environment (simplified)
    try:
        import torch
        if torch.cuda.is_available():
            device = torch.cuda.get_device_name()
            cuda_version = torch.version.cuda
            logger.info(f"GPU acceleration available: {device} (CUDA {cuda_version})")
            gpu_config = {
                'gpu_enabled': True,
                'device': 'cuda',
                'dtype': torch.float16,
                'device_info': {
                    'device_name': device,
                    'cuda_version': cuda_version
                }
            }
        else:
            logger.info("GPU acceleration not available. Using CPU only.")
            print("GPU acceleration not available. Using CPU only.")
            gpu_config = {
                'gpu_enabled': False,
                'device': 'cpu',
                'dtype': None,
                'device_info': {}
            }
        return gpu_config
    except ImportError:
        logger.info("PyTorch not available. GPU acceleration disabled.")
        print("GPU acceleration not available. Using CPU only.")
        return {
            'gpu_enabled': False,
            'device': 'cpu',
            'dtype': None,
            'device_info': {}
        }
    except Exception as e:
        logger.error(f"Error setting up GPU environment: {e}")
        print(f"Error setting up GPU environment: {e}")
        print("GPU acceleration not available. Using CPU only.")
        return {
            'gpu_enabled': False,
            'device': 'cpu',
            'dtype': None,
            'device_info': {}
        }

def main():
    """
    Main function to run the startup script
    """
    logger.info("Starting Epinnox Trading System")
    
    # Set up environment
    gpu_config = setup_environment()
    
    # Return GPU configuration
    return gpu_config

if __name__ == "__main__":
    main()
