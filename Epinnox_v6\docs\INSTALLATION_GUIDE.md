# 🚀 EPINNOX v6 Installation Guide

## 📋 Quick Start (Recommended)

### One-Click Setup
```bash
# Complete setup with all features
python setup_epinnox.py

# Minimal setup (core features only)
python setup_epinnox.py --minimal

# Setup with GPU acceleration
python setup_epinnox.py --gpu
```

### Verify Installation
```bash
# Check all dependencies
python check_dependencies.py

# Run tests
python run_tests.py --quick
```

## 🔧 Manual Installation

### 1. Core Dependencies
```bash
# Install minimal requirements
pip install -r requirements-minimal.txt

# Or install manually
pip install pandas numpy scipy requests aiohttp websockets
```

### 2. ML/RL Dependencies
```bash
# Install ML requirements
pip install -r requirements-ml.txt

# Or install manually
pip install torch stable-baselines3[extra] gymnasium tensorflow
```

### 3. GUI Dependencies
```bash
# Install GUI requirements
pip install -r requirements-gui.txt

# Or install manually
pip install PyQt5 streamlit plotly matplotlib
```

### 4. Technical Analysis
```bash
# Try TA-Lib first
pip install TA-Lib

# If TA-Li<PERSON> fails, use alternatives
pip install ta pandas-ta
```

## 🎮 GPU Acceleration Setup

### Prerequisites
- NVIDIA GPU with CUDA support
- NVIDIA drivers installed
- CUDA Toolkit installed

### Automatic GPU Setup
```bash
python setup_gpu.py
```

### Manual GPU Setup

#### Install CUDA Toolkit

**Windows:**
1. Download from: https://developer.nvidia.com/cuda-downloads
2. Run installer and restart

**Linux (Ubuntu/Debian):**
```bash
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-keyring_1.0-1_all.deb
sudo dpkg -i cuda-keyring_1.0-1_all.deb
sudo apt-get update
sudo apt-get -y install cuda
```

**macOS:**
- CUDA not supported on Apple Silicon
- Use Metal Performance Shaders (MPS) instead

#### Install GPU-Accelerated PyTorch
```bash
# CUDA 11.8
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# CUDA 12.1
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

#### Install GPU-Accelerated TensorFlow
```bash
pip install tensorflow[and-cuda]
```

## 🔍 Troubleshooting

### Common Issues

#### TA-Lib Installation Fails
```bash
# Windows: Download wheel from
# https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
pip install downloaded_file.whl

# macOS: Install via Homebrew
brew install ta-lib
pip install TA-Lib

# Linux: Install system library
sudo apt-get install libta-lib-dev  # Ubuntu/Debian
sudo yum install ta-lib-devel       # CentOS/RHEL
pip install TA-Lib
```

#### PyQt5 Installation Issues
```bash
# Try alternative installation
conda install pyqt

# Or use PySide2 as alternative
pip install PySide2
```

#### GPU Not Detected
```bash
# Check NVIDIA driver
nvidia-smi

# Check CUDA installation
nvcc --version

# Verify PyTorch CUDA
python -c "import torch; print(torch.cuda.is_available())"
```

#### Import Errors
```bash
# Create missing directories
python -c "
import os
dirs = ['ui', 'ui/dialogs', 'autotune', 'data', 'logs', 'models']
for d in dirs: os.makedirs(d, exist_ok=True)
"

# Create missing __init__.py files
touch ui/__init__.py ui/dialogs/__init__.py autotune/__init__.py
```

### Dependency Check
```bash
# Run comprehensive dependency check
python check_dependencies.py

# Check specific package
python -c "import package_name; print(package_name.__version__)"
```

## 📦 Installation Options

### Option 1: Full Installation
```bash
pip install -r requirements-full.txt
```
**Includes:** All features, ML/RL, GUI, testing, optional packages

### Option 2: Minimal Installation
```bash
pip install -r requirements-minimal.txt
```
**Includes:** Core trading functionality only

### Option 3: Custom Installation
```bash
# Core + ML
pip install -r requirements-minimal.txt -r requirements-ml.txt

# Core + GUI
pip install -r requirements-minimal.txt -r requirements-gui.txt

# Everything except GUI
pip install -r requirements-minimal.txt -r requirements-ml.txt
```

## 🧪 Testing Installation

### Quick Tests
```bash
# Basic functionality
python run_tests.py --quick

# Import tests only
python -c "
import pandas, numpy, torch
print('✅ Core packages working')
"
```

### Comprehensive Tests
```bash
# All tests
python run_tests.py --verbose

# Specific test categories
python run_tests.py --unit-only
python run_tests.py --integration-only
```

### Feature Tests
```bash
# Test dashboard
python start_dashboard.py &
# Visit http://localhost:8501

# Test paper trading
python start_paper_trading.py --duration 5

# Test parameter optimization
python run_param_search.py --preset quick

# Test stress testing
python run_stress_test.py flash_crash --intensity 1.0 --duration 10
```

## 🔧 Environment Setup

### Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv epinnox_env

# Activate (Windows)
epinnox_env\Scripts\activate

# Activate (Linux/macOS)
source epinnox_env/bin/activate

# Install dependencies
python setup_epinnox.py
```

### Conda Environment
```bash
# Create conda environment
conda create -n epinnox python=3.9

# Activate environment
conda activate epinnox

# Install dependencies
python setup_epinnox.py
```

## 📋 System Requirements

### Minimum Requirements
- **Python:** 3.8+
- **RAM:** 4GB
- **Storage:** 2GB free space
- **OS:** Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)

### Recommended Requirements
- **Python:** 3.9+
- **RAM:** 8GB+
- **Storage:** 5GB free space
- **GPU:** NVIDIA GPU with 4GB+ VRAM (for ML acceleration)
- **CPU:** Multi-core processor for parallel processing

### Optional Requirements
- **NVIDIA GPU:** For ML/RL acceleration
- **CUDA Toolkit:** For GPU computing
- **TA-Lib:** For advanced technical analysis
- **PyQt5:** For desktop GUI

## 🚀 Next Steps

After successful installation:

1. **Configure Settings:**
   ```bash
   python main.py --help
   ```

2. **Start Dashboard:**
   ```bash
   python start_dashboard.py
   ```

3. **Run Paper Trading:**
   ```bash
   python start_paper_trading.py --duration 60
   ```

4. **Optimize Parameters:**
   ```bash
   python run_param_search.py --preset quick
   ```

5. **Read Documentation:**
   - `README.md` - Main documentation
   - `ADVANCED_TESTING_GUIDE.md` - Testing features
   - `INSTALLATION_GUIDE.md` - This guide

## 📞 Support

If you encounter issues:

1. **Check Dependencies:** `python check_dependencies.py`
2. **Run Tests:** `python run_tests.py --quick`
3. **Check Logs:** Look in `logs/` directory
4. **Verify Installation:** `python setup_epinnox.py --no-tests`

---

**🎉 Welcome to EPINNOX v6 - Advanced Autonomous Trading System!**
