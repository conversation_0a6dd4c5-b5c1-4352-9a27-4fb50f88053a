# NLP and Sentiment Analysis Dependencies for Epinnox Trading System

# Core NLP libraries
nltk>=3.8.1
textblob>=0.17.1
transformers>=4.35.0
torch>=2.0.0
tokenizers>=0.15.0

# Web scraping and HTTP
aiohttp>=3.9.0
requests>=2.31.0
beautifulsoup4>=4.12.0
feedparser>=6.0.10
lxml>=4.9.0

# Data processing
pandas>=2.0.0
numpy>=1.24.0

# Optional: For advanced sentiment models
# huggingface-hub>=0.19.0
# datasets>=2.14.0

# Optional: For Twitter API (if you get credentials)
# tweepy>=4.14.0

# Optional: For Reddit API enhancement
# praw>=7.7.0
