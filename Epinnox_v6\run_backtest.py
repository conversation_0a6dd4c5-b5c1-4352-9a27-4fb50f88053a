#!/usr/bin/env python3
"""
Backtesting CLI for EPINNOX v6
Run comprehensive backtests with historical data
"""

import argparse
import sys
import os
from datetime import datetime, timedelta
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tests.backtesting.backtest_runner import BacktestRunner, BacktestConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main CLI entry point for backtesting"""
    parser = argparse.ArgumentParser(description='EPINNOX v6 Backtesting Engine')
    
    # Basic parameters
    parser.add_argument('--symbol', type=str, default='BTC/USDT', 
                       help='Trading symbol (default: BTC/USDT)')
    parser.add_argument('--symbols', type=str, nargs='+', 
                       help='Multiple symbols to test')
    parser.add_argument('--days', type=int, default=30, 
                       help='Number of days to backtest (default: 30)')
    parser.add_argument('--start-date', type=str, 
                       help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, 
                       help='End date (YYYY-MM-DD)')
    
    # Trading parameters
    parser.add_argument('--initial-balance', type=float, default=10000.0,
                       help='Initial balance (default: 10000)')
    parser.add_argument('--max-positions', type=int, default=5,
                       help='Maximum concurrent positions (default: 5)')
    parser.add_argument('--min-confidence', type=float, default=0.65,
                       help='Minimum confidence threshold (default: 0.65)')
    
    # Risk parameters
    parser.add_argument('--slippage', type=float, default=0.001,
                       help='Slippage percentage (default: 0.001)')
    parser.add_argument('--commission', type=float, default=0.001,
                       help='Commission percentage (default: 0.001)')
    
    # Output options
    parser.add_argument('--output', type=str, 
                       help='Output file for results (JSON)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Quiet mode (minimal output)')
    
    # Preset configurations
    parser.add_argument('--preset', type=str, choices=['conservative', 'aggressive', 'balanced'],
                       help='Use preset configuration')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    elif args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Determine date range
    if args.start_date and args.end_date:
        start_date = args.start_date
        end_date = args.end_date
    else:
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=args.days)).strftime('%Y-%m-%d')
    
    # Determine symbols
    if args.symbols:
        symbols = args.symbols
    else:
        symbols = [args.symbol]
    
    # Apply presets
    if args.preset == 'conservative':
        min_confidence = 0.75
        max_positions = 3
        slippage = 0.0015
        commission = 0.001
    elif args.preset == 'aggressive':
        min_confidence = 0.55
        max_positions = 8
        slippage = 0.0005
        commission = 0.0008
    elif args.preset == 'balanced':
        min_confidence = 0.65
        max_positions = 5
        slippage = 0.001
        commission = 0.001
    else:
        min_confidence = args.min_confidence
        max_positions = args.max_positions
        slippage = args.slippage
        commission = args.commission
    
    # Create backtest configuration
    config = BacktestConfig(
        initial_balance=args.initial_balance,
        start_date=start_date,
        end_date=end_date,
        symbols=symbols,
        slippage=slippage,
        commission=commission,
        max_positions=max_positions,
        min_confidence=min_confidence
    )
    
    # Display configuration
    if not args.quiet:
        print(f"\n{'='*60}")
        print(f"EPINNOX v6 BACKTESTING ENGINE")
        print(f"{'='*60}")
        print(f"Symbols:          {', '.join(symbols)}")
        print(f"Date Range:       {start_date} to {end_date}")
        print(f"Initial Balance:  ${config.initial_balance:,.2f}")
        print(f"Max Positions:    {config.max_positions}")
        print(f"Min Confidence:   {config.min_confidence:.1%}")
        print(f"Slippage:         {config.slippage:.3%}")
        print(f"Commission:       {config.commission:.3%}")
        if args.preset:
            print(f"Preset:           {args.preset}")
        print(f"{'='*60}\n")
    
    try:
        # Run backtest
        runner = BacktestRunner(config)
        result = runner.run_backtest()
        
        # Display results
        if not args.quiet:
            runner.print_summary(result)
        
        # Save results if requested
        if args.output:
            runner.save_results(result, args.output)
            logger.info(f"Results saved to {args.output}")
        else:
            # Save with default filename
            default_filename = f"backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            runner.save_results(result, default_filename)
            logger.info(f"Results saved to {default_filename}")
        
        # Return appropriate exit code
        if result.total_return > 0:
            logger.info("✅ Backtest completed successfully with positive returns")
            return 0
        else:
            logger.warning("⚠️ Backtest completed with negative returns")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Backtest failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

def run_quick_backtest():
    """Run a quick backtest with default parameters"""
    print("🚀 Running Quick Backtest...")
    
    config = BacktestConfig(
        initial_balance=10000.0,
        start_date=(datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d'),
        end_date=datetime.now().strftime('%Y-%m-%d'),
        symbols=['BTC/USDT'],
        min_confidence=0.65
    )
    
    runner = BacktestRunner(config)
    result = runner.run_backtest()
    runner.print_summary(result)
    
    return result

def run_multi_symbol_backtest():
    """Run backtest across multiple symbols"""
    print("🔄 Running Multi-Symbol Backtest...")
    
    config = BacktestConfig(
        initial_balance=10000.0,
        start_date=(datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
        end_date=datetime.now().strftime('%Y-%m-%d'),
        symbols=['BTC/USDT', 'ETH/USDT', 'ADA/USDT'],
        min_confidence=0.65
    )
    
    runner = BacktestRunner(config)
    result = runner.run_backtest()
    runner.print_summary(result)
    
    return result

if __name__ == "__main__":
    sys.exit(main())
