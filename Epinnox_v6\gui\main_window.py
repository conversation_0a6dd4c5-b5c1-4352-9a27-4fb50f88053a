"""
Main Window for EPINNOX v6 GUI
Primary GUI interface for the trading system
"""

try:
    from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                                QMenuBar, QStatusBar, QTabWidget, QLabel, 
                                QPushButton, QTextEdit, QSplitter)
    from PyQt5.QtCore import Qt, QTimer, pyqtSignal
    from PyQt5.QtGui import QFont, QIcon
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

if PYQT_AVAILABLE:
    class TradingSystemGUI(QMainWindow):
        """Main GUI window for EPINNOX v6 trading system"""
        
        # Signals
        start_trading = pyqtSignal()
        stop_trading = pyqtSignal()
        settings_changed = pyqtSignal(dict)
        
        def __init__(self, parent=None):
            super().__init__(parent)
            self.trading_system = None
            self.gui_integration = None
            self.dock_widgets = {}  # Add dock_widgets attribute for testing
            self.setup_ui()
            self.setup_connections()

            logger.info("Trading System GUI initialized")
        
        def setup_ui(self):
            """Setup the user interface"""
            self.setWindowTitle("EPINNOX v6 - Autonomous Trading System")
            self.setGeometry(100, 100, 1200, 800)
            
            # Create central widget
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # Main layout
            main_layout = QVBoxLayout(central_widget)
            
            # Create menu bar
            self.create_menu_bar()
            
            # Create toolbar
            self.create_toolbar()
            
            # Create main content area
            self.create_main_content(main_layout)
            
            # Create status bar
            self.create_status_bar()
        
        def create_menu_bar(self):
            """Create menu bar"""
            menubar = self.menuBar()
            
            # File menu
            file_menu = menubar.addMenu('File')
            file_menu.addAction('New Strategy', self.new_strategy)
            file_menu.addAction('Load Strategy', self.load_strategy)
            file_menu.addAction('Save Strategy', self.save_strategy)
            file_menu.addSeparator()
            file_menu.addAction('Exit', self.close)
            
            # Trading menu
            trading_menu = menubar.addMenu('Trading')
            trading_menu.addAction('Start Trading', self.start_trading_action)
            trading_menu.addAction('Stop Trading', self.stop_trading_action)
            trading_menu.addAction('Paper Trading', self.toggle_paper_trading)
            
            # Tools menu
            tools_menu = menubar.addMenu('Tools')
            tools_menu.addAction('Backtest', self.run_backtest)
            tools_menu.addAction('Optimize Parameters', self.optimize_parameters)
            tools_menu.addAction('Settings', self.show_settings)
            
            # Help menu
            help_menu = menubar.addMenu('Help')
            help_menu.addAction('Documentation', self.show_documentation)
            help_menu.addAction('About', self.show_about)
        
        def create_toolbar(self):
            """Create toolbar"""
            toolbar = self.addToolBar('Main')
            
            # Start/Stop buttons
            self.start_button = QPushButton('Start Trading')
            self.start_button.clicked.connect(self.start_trading_action)
            toolbar.addWidget(self.start_button)
            
            self.stop_button = QPushButton('Stop Trading')
            self.stop_button.clicked.connect(self.stop_trading_action)
            self.stop_button.setEnabled(False)
            toolbar.addWidget(self.stop_button)
            
            toolbar.addSeparator()
            
            # Status indicator
            self.status_label = QLabel('Status: Stopped')
            toolbar.addWidget(self.status_label)
        
        def create_main_content(self, layout):
            """Create main content area"""
            # Create tab widget
            self.tab_widget = QTabWidget()
            layout.addWidget(self.tab_widget)
            
            # Dashboard tab
            self.create_dashboard_tab()
            
            # Trading tab
            self.create_trading_tab()
            
            # Portfolio tab
            self.create_portfolio_tab()
            
            # Logs tab
            self.create_logs_tab()
        
        def create_dashboard_tab(self):
            """Create dashboard tab"""
            dashboard_widget = QWidget()
            layout = QVBoxLayout(dashboard_widget)
            
            # Dashboard content
            dashboard_label = QLabel("Trading Dashboard")
            dashboard_label.setFont(QFont("Arial", 16, QFont.Bold))
            layout.addWidget(dashboard_label)
            
            # Metrics area
            metrics_widget = QWidget()
            metrics_layout = QHBoxLayout(metrics_widget)
            
            # Balance
            balance_label = QLabel("Balance: $0.00")
            balance_label.setFont(QFont("Arial", 12))
            metrics_layout.addWidget(balance_label)
            
            # P&L
            pnl_label = QLabel("P&L: $0.00")
            pnl_label.setFont(QFont("Arial", 12))
            metrics_layout.addWidget(pnl_label)
            
            # Win Rate
            winrate_label = QLabel("Win Rate: 0%")
            winrate_label.setFont(QFont("Arial", 12))
            metrics_layout.addWidget(winrate_label)
            
            layout.addWidget(metrics_widget)
            
            # Chart area placeholder
            chart_label = QLabel("Chart Area (Coming Soon)")
            chart_label.setAlignment(Qt.AlignCenter)
            chart_label.setStyleSheet("border: 1px solid gray; min-height: 300px;")
            layout.addWidget(chart_label)
            
            self.tab_widget.addTab(dashboard_widget, "Dashboard")
        
        def create_trading_tab(self):
            """Create trading tab"""
            trading_widget = QWidget()
            layout = QVBoxLayout(trading_widget)
            
            trading_label = QLabel("Trading Controls")
            trading_label.setFont(QFont("Arial", 16, QFont.Bold))
            layout.addWidget(trading_label)
            
            # Trading controls
            controls_widget = QWidget()
            controls_layout = QHBoxLayout(controls_widget)
            
            # Symbol selection
            symbol_label = QLabel("Symbol:")
            controls_layout.addWidget(symbol_label)
            
            # Manual trade buttons
            buy_button = QPushButton("Manual Buy")
            buy_button.clicked.connect(self.manual_buy)
            controls_layout.addWidget(buy_button)
            
            sell_button = QPushButton("Manual Sell")
            sell_button.clicked.connect(self.manual_sell)
            controls_layout.addWidget(sell_button)
            
            layout.addWidget(controls_widget)
            
            # Trading log
            log_label = QLabel("Trading Activity:")
            layout.addWidget(log_label)
            
            self.trading_log = QTextEdit()
            self.trading_log.setReadOnly(True)
            self.trading_log.setMaximumHeight(200)
            layout.addWidget(self.trading_log)
            
            self.tab_widget.addTab(trading_widget, "Trading")
        
        def create_portfolio_tab(self):
            """Create portfolio tab"""
            portfolio_widget = QWidget()
            layout = QVBoxLayout(portfolio_widget)
            
            portfolio_label = QLabel("Portfolio Overview")
            portfolio_label.setFont(QFont("Arial", 16, QFont.Bold))
            layout.addWidget(portfolio_label)
            
            # Portfolio content placeholder
            content_label = QLabel("Portfolio details will be displayed here")
            content_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(content_label)
            
            self.tab_widget.addTab(portfolio_widget, "Portfolio")
        
        def create_logs_tab(self):
            """Create logs tab"""
            logs_widget = QWidget()
            layout = QVBoxLayout(logs_widget)
            
            logs_label = QLabel("System Logs")
            logs_label.setFont(QFont("Arial", 16, QFont.Bold))
            layout.addWidget(logs_label)
            
            # Log display
            self.log_display = QTextEdit()
            self.log_display.setReadOnly(True)
            layout.addWidget(self.log_display)
            
            # Log controls
            log_controls = QHBoxLayout()
            
            clear_button = QPushButton("Clear Logs")
            clear_button.clicked.connect(self.clear_logs)
            log_controls.addWidget(clear_button)
            
            save_logs_button = QPushButton("Save Logs")
            save_logs_button.clicked.connect(self.save_logs)
            log_controls.addWidget(save_logs_button)
            
            layout.addLayout(log_controls)
            
            self.tab_widget.addTab(logs_widget, "Logs")
        
        def create_status_bar(self):
            """Create status bar"""
            self.status_bar = self.statusBar()
            self.status_bar.showMessage("Ready")
        
        def setup_connections(self):
            """Setup signal connections"""
            # Timer for periodic updates
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_display)
            self.update_timer.start(5000)  # Update every 5 seconds
        
        def set_trading_system(self, trading_system):
            """Set the trading system reference"""
            self.trading_system = trading_system
        
        def set_gui_integration(self, gui_integration):
            """Set the GUI integration reference"""
            self.gui_integration = gui_integration
            if gui_integration:
                gui_integration.register_gui_component("main_window", self)
        
        def update_display(self):
            """Update display with current data"""
            if self.gui_integration:
                data = self.gui_integration.get_cached_data()
                self.update_data(data)
        
        def update_data(self, data: Dict[str, Any]):
            """Update GUI with new data"""
            try:
                # Update status
                status = data.get("status", "Unknown")
                self.status_label.setText(f"Status: {status}")
                
                # Update status bar
                timestamp = data.get("timestamp", "")
                self.status_bar.showMessage(f"Last update: {timestamp}")
                
                # Log update
                if "message" in data:
                    self.add_log_message(data["message"])
            
            except Exception as e:
                logger.error(f"Error updating GUI data: {e}")
        
        def add_log_message(self, message: str):
            """Add message to log display"""
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.log_display.append(f"[{timestamp}] {message}")
        
        # Action handlers
        def start_trading_action(self):
            """Start trading"""
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("Status: Running")
            self.start_trading.emit()
            self.add_log_message("Trading started")
        
        def stop_trading_action(self):
            """Stop trading"""
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText("Status: Stopped")
            self.stop_trading.emit()
            self.add_log_message("Trading stopped")
        
        def manual_buy(self):
            """Manual buy order"""
            self.add_log_message("Manual buy order placed")
        
        def manual_sell(self):
            """Manual sell order"""
            self.add_log_message("Manual sell order placed")
        
        def clear_logs(self):
            """Clear log display"""
            self.log_display.clear()
        
        def save_logs(self):
            """Save logs to file"""
            self.add_log_message("Logs saved")
        
        # Menu actions
        def new_strategy(self):
            self.add_log_message("New strategy created")
        
        def load_strategy(self):
            self.add_log_message("Strategy loaded")
        
        def save_strategy(self):
            self.add_log_message("Strategy saved")
        
        def toggle_paper_trading(self):
            self.add_log_message("Paper trading toggled")
        
        def run_backtest(self):
            self.add_log_message("Backtest started")
        
        def optimize_parameters(self):
            self.add_log_message("Parameter optimization started")
        
        def show_settings(self):
            self.add_log_message("Settings dialog opened")
        
        def show_documentation(self):
            self.add_log_message("Documentation opened")
        
        def show_about(self):
            self.add_log_message("About dialog opened")

else:
    # Mock class when PyQt5 is not available
    class TradingSystemGUI:
        """Mock GUI class for testing without PyQt5"""
        
        def __init__(self, parent=None):
            self.trading_system = None
            self.gui_integration = None
            self.dock_widgets = {}  # Add dock_widgets attribute for testing
            logger.warning("PyQt5 not available. Using mock GUI.")
        
        def set_trading_system(self, trading_system):
            self.trading_system = trading_system
        
        def set_gui_integration(self, gui_integration):
            self.gui_integration = gui_integration
        
        def update_data(self, data):
            logger.info(f"Mock GUI update: {data}")
        
        def show(self):
            logger.info("Mock GUI shown")
        
        def close(self):
            logger.info("Mock GUI closed")
