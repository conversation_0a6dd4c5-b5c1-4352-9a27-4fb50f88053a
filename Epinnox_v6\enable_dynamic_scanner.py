"""
Enable Dynamic Scanner for Epinnox v6
Automatically enables the dynamic symbol scanner to find cheaper alternatives to BTC
"""

import sys
import time
import logging
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtWidgets import QApplication

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def enable_dynamic_scanner():
    """Enable the dynamic scanner programmatically"""
    try:
        # Get the QApplication instance
        app = QApplication.instance()
        if not app:
            logger.error("No QApplication instance found. Make sure Epinnox is running.")
            return False
        
        # Find the main window
        main_window = None
        for widget in app.topLevelWidgets():
            if hasattr(widget, 'dynamic_scan_cb'):
                main_window = widget
                break
        
        if not main_window:
            logger.error("Main window with dynamic scanner not found.")
            return False
        
        # Check if scanner checkbox exists
        if not hasattr(main_window, 'dynamic_scan_cb'):
            logger.error("Dynamic scanner checkbox not found in main window.")
            return False
        
        # Enable the dynamic scanner
        scanner_checkbox = main_window.dynamic_scan_cb
        
        if scanner_checkbox.isChecked():
            logger.info("✅ Dynamic scanner is already enabled")
            return True
        
        logger.info("🚀 Enabling dynamic scanner...")
        
        # Programmatically check the checkbox
        scanner_checkbox.setChecked(True)
        
        # Trigger the toggle event manually if needed
        if hasattr(main_window, 'on_dynamic_scan_toggled'):
            main_window.on_dynamic_scan_toggled(Qt.Checked)
        
        # Wait a moment for initialization
        time.sleep(1)
        
        # Verify it's enabled
        if hasattr(main_window, 'scanner_enabled') and main_window.scanner_enabled:
            logger.info("✅ Dynamic scanner successfully enabled!")
            logger.info("🔍 Scanner will now automatically find cheaper alternatives to BTC")
            logger.info("📊 Looking for symbols with better affordability scores...")
            return True
        else:
            logger.warning("⚠️ Scanner checkbox checked but scanner_enabled flag not set")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error enabling dynamic scanner: {e}")
        return False

def check_scanner_status():
    """Check the current status of the dynamic scanner"""
    try:
        app = QApplication.instance()
        if not app:
            return "No QApplication found"
        
        # Find main window
        main_window = None
        for widget in app.topLevelWidgets():
            if hasattr(widget, 'dynamic_scan_cb'):
                main_window = widget
                break
        
        if not main_window:
            return "Main window not found"
        
        # Check scanner status
        checkbox_checked = main_window.dynamic_scan_cb.isChecked() if hasattr(main_window, 'dynamic_scan_cb') else False
        scanner_enabled = getattr(main_window, 'scanner_enabled', False)
        
        status_text = "Unknown"
        if hasattr(main_window, 'scanner_status_label'):
            status_text = main_window.scanner_status_label.text()
        
        return {
            'checkbox_checked': checkbox_checked,
            'scanner_enabled': scanner_enabled,
            'status_text': status_text,
            'has_scanner_timer': hasattr(main_window, 'scanner_timer'),
            'timer_active': main_window.scanner_timer.isActive() if hasattr(main_window, 'scanner_timer') and main_window.scanner_timer else False
        }
        
    except Exception as e:
        return f"Error checking status: {e}"

def force_scanner_activation():
    """Force immediate scanner activation and symbol search"""
    try:
        app = QApplication.instance()
        if not app:
            logger.error("No QApplication instance found")
            return False
        
        # Find main window
        main_window = None
        for widget in app.topLevelWidgets():
            if hasattr(widget, 'dynamic_scan_cb'):
                main_window = widget
                break
        
        if not main_window:
            logger.error("Main window not found")
            return False
        
        # Enable scanner first
        if not enable_dynamic_scanner():
            return False
        
        # Force immediate scan
        if hasattr(main_window, 'on_scan_tick'):
            logger.info("🔍 Forcing immediate symbol scan...")
            main_window.on_scan_tick()
            logger.info("✅ Immediate scan triggered")
            return True
        else:
            logger.warning("on_scan_tick method not found")
            return False
            
    except Exception as e:
        logger.error(f"Error forcing scanner activation: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Epinnox Dynamic Scanner Enabler")
    print("=" * 50)
    
    # Check if we're running within the Epinnox application
    app = QApplication.instance()
    if not app:
        print("❌ This script must be run while Epinnox is running")
        print("💡 Start Epinnox first, then run this script")
        sys.exit(1)
    
    # Check current status
    print("📊 Checking current scanner status...")
    status = check_scanner_status()
    print(f"Status: {status}")
    
    # Enable scanner
    print("\n🔧 Enabling dynamic scanner...")
    success = enable_dynamic_scanner()
    
    if success:
        print("\n✅ SUCCESS!")
        print("🎯 The system will now automatically:")
        print("   • Scan all available symbols every 5 seconds")
        print("   • Find cheaper alternatives to BTC (DOGE, ADA, MATIC, etc.)")
        print("   • Switch to symbols with better affordability scores")
        print("   • Prioritize symbols suitable for small accounts")
        print("\n📈 Watch the GUI for automatic symbol changes!")
        
        # Force immediate scan
        print("\n🚀 Triggering immediate scan...")
        force_scanner_activation()
        
    else:
        print("\n❌ FAILED to enable dynamic scanner")
        print("💡 Make sure Epinnox is fully loaded and try again")
