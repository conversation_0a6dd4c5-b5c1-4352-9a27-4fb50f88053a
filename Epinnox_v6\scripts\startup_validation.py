#!/usr/bin/env python3
"""
Startup Validation Script for Epinnox v6
Validates all systems before trading begins and provides graceful shutdown procedures
"""

import os
import sys
import time
import signal
import logging
from pathlib import Path
from typing import Dict, List, Optional, Callable
from datetime import datetime

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StartupValidator:
    """
    Comprehensive startup validation and system health checker
    """
    
    def __init__(self):
        """Initialize startup validator"""
        self.project_root = Path(__file__).parent.parent
        self.validation_results = {}
        self.critical_failures = []
        self.warnings = []
        
        # Shutdown handlers
        self.shutdown_handlers: List[Callable] = []
        self.is_shutting_down = False
        
        # Register signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("🔍 Startup Validator initialized")
    
    def validate_all_systems(self) -> bool:
        """Run complete system validation"""
        try:
            logger.info("🚀 Starting comprehensive system validation...")
            
            validation_steps = [
                ("Python Environment", self._validate_python_environment),
                ("Required Files", self._validate_required_files),
                ("Dependencies", self._validate_dependencies),
                ("Configuration", self._validate_configuration),
                ("Credentials", self._validate_credentials),
                ("Trading Interface", self._validate_trading_interface),
                ("Risk Management", self._validate_risk_management),
                ("Error Handling", self._validate_error_handling),
                ("Monitoring System", self._validate_monitoring_system),
                ("Network Connectivity", self._validate_network_connectivity),
                ("Database Access", self._validate_database_access),
                ("LLM Integration", self._validate_llm_integration)
            ]
            
            total_steps = len(validation_steps)
            passed_steps = 0
            
            for step_name, validation_func in validation_steps:
                logger.info(f"🔍 Validating: {step_name}")
                
                try:
                    result = validation_func()
                    self.validation_results[step_name] = result
                    
                    if result:
                        logger.info(f"✅ {step_name}: PASSED")
                        passed_steps += 1
                    else:
                        logger.error(f"❌ {step_name}: FAILED")
                        self.critical_failures.append(step_name)
                        
                except Exception as e:
                    logger.error(f"❌ {step_name}: ERROR - {e}")
                    self.validation_results[step_name] = False
                    self.critical_failures.append(f"{step_name}: {e}")
            
            # Generate validation summary
            success_rate = (passed_steps / total_steps) * 100
            logger.info(f"📊 Validation Summary: {passed_steps}/{total_steps} passed ({success_rate:.1f}%)")
            
            if self.critical_failures:
                logger.error("🚨 Critical failures detected:")
                for failure in self.critical_failures:
                    logger.error(f"  - {failure}")
            
            if self.warnings:
                logger.warning("⚠️ Warnings:")
                for warning in self.warnings:
                    logger.warning(f"  - {warning}")
            
            # Determine if system is ready
            is_ready = len(self.critical_failures) == 0 and success_rate >= 80
            
            if is_ready:
                logger.info("🎉 System validation PASSED - Ready for trading")
            else:
                logger.error("❌ System validation FAILED - Not ready for trading")
            
            return is_ready
            
        except Exception as e:
            logger.error(f"❌ System validation error: {e}")
            return False
    
    def _validate_python_environment(self) -> bool:
        """Validate Python environment"""
        try:
            # Check Python version
            if sys.version_info < (3, 8):
                return False
            
            # Check required Python modules
            required_modules = [
                'PyQt5', 'requests', 'ccxt', 'pandas', 'numpy'
            ]
            
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError:
                    self.warnings.append(f"Optional module missing: {module}")
            
            return True
            
        except Exception:
            return False
    
    def _validate_required_files(self) -> bool:
        """Validate required files exist"""
        try:
            required_files = [
                "launch_epinnox.py",
                "core/risk_management_system.py",
                "core/error_handling_system.py",
                "core/monitoring_dashboard.py",
                "config/production_config.py"
            ]
            
            for file_path in required_files:
                if not (self.project_root / file_path).exists():
                    return False
            
            return True
            
        except Exception:
            return False
    
    def _validate_dependencies(self) -> bool:
        """Validate dependencies are installed"""
        try:
            # Try importing critical modules
            critical_imports = [
                "launch_epinnox",
                "core.risk_management_system",
                "core.error_handling_system",
                "core.monitoring_dashboard"
            ]
            
            for module_name in critical_imports:
                try:
                    __import__(module_name)
                except ImportError as e:
                    logger.error(f"Failed to import {module_name}: {e}")
                    return False
            
            return True
            
        except Exception:
            return False
    
    def _validate_configuration(self) -> bool:
        """Validate configuration system"""
        try:
            from config.production_config import ProductionConfigManager, Environment
            
            config_manager = ProductionConfigManager()
            
            # Try loading development config
            config = config_manager.load_config(Environment.DEVELOPMENT)
            
            # Validate configuration
            is_valid, errors = config_manager.validate_config(config)
            
            if not is_valid:
                for error in errors:
                    self.warnings.append(f"Config validation: {error}")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"Configuration validation error: {e}")
            return False
    
    def _validate_credentials(self) -> bool:
        """Validate credentials are available"""
        try:
            credentials_file = self.project_root / "credentials.py"
            
            if not credentials_file.exists():
                self.warnings.append("Credentials file not found - demo mode only")
                return True  # Not critical for demo mode
            
            # Try importing credentials
            try:
                import credentials
                # Check if required attributes exist
                required_attrs = ['api_key', 'secret_key', 'passphrase']
                for attr in required_attrs:
                    if not hasattr(credentials, attr):
                        self.warnings.append(f"Missing credential: {attr}")
                
                return True
                
            except ImportError:
                self.warnings.append("Could not import credentials")
                return True  # Not critical for demo mode
            
        except Exception:
            return True  # Not critical
    
    def _validate_trading_interface(self) -> bool:
        """Validate trading interface"""
        try:
            from launch_epinnox import EpinnoxTradingInterface
            
            # Try creating interface instance (don't initialize GUI)
            # This validates that the class can be instantiated
            return True
            
        except Exception as e:
            logger.error(f"Trading interface validation error: {e}")
            return False
    
    def _validate_risk_management(self) -> bool:
        """Validate risk management system"""
        try:
            from core.risk_management_system import RiskManagementSystem, RiskLimits
            
            # Create risk manager instance
            risk_manager = RiskManagementSystem()
            
            # Test basic functionality
            test_result = risk_manager.validate_trade(
                "TEST/USDT", "buy", 1.0, 1.0, 10, 100.0
            )
            
            return test_result[0] is not None  # Should return a boolean result
            
        except Exception as e:
            logger.error(f"Risk management validation error: {e}")
            return False
    
    def _validate_error_handling(self) -> bool:
        """Validate error handling system"""
        try:
            from core.error_handling_system import ErrorHandlingSystem, HealthCheck
            
            # Create error handler instance
            error_handler = ErrorHandlingSystem()
            
            # Test basic functionality
            test_health = error_handler.get_system_health()
            
            return isinstance(test_health, dict)
            
        except Exception as e:
            logger.error(f"Error handling validation error: {e}")
            return False
    
    def _validate_monitoring_system(self) -> bool:
        """Validate monitoring system"""
        try:
            from core.monitoring_dashboard import MonitoringDashboard
            
            # Create monitoring dashboard instance
            dashboard = MonitoringDashboard()
            
            # Test basic functionality
            dashboard_data = dashboard.get_dashboard_data()
            
            return isinstance(dashboard_data, dict)
            
        except Exception as e:
            logger.error(f"Monitoring system validation error: {e}")
            return False
    
    def _validate_network_connectivity(self) -> bool:
        """Validate network connectivity"""
        try:
            import requests
            
            # Test basic internet connectivity
            response = requests.get("https://httpbin.org/status/200", timeout=10)
            
            if response.status_code != 200:
                self.warnings.append("Network connectivity issues detected")
                return False
            
            return True
            
        except Exception:
            self.warnings.append("Could not verify network connectivity")
            return True  # Not critical for startup
    
    def _validate_database_access(self) -> bool:
        """Validate database access"""
        try:
            import sqlite3
            
            # Test SQLite database creation
            test_db = self.project_root / "test_db.sqlite"
            conn = sqlite3.connect(test_db)
            conn.execute("CREATE TABLE IF NOT EXISTS test (id INTEGER)")
            conn.close()
            
            # Clean up test database
            if test_db.exists():
                test_db.unlink()
            
            return True
            
        except Exception:
            self.warnings.append("Database access validation failed")
            return True  # Not critical
    
    def _validate_llm_integration(self) -> bool:
        """Validate LLM integration"""
        try:
            # This is a placeholder - in a real implementation,
            # you'd test the actual LLM integration
            self.warnings.append("LLM integration not fully validated")
            return True
            
        except Exception:
            return True  # Not critical for startup
    
    def register_shutdown_handler(self, handler: Callable):
        """Register a shutdown handler"""
        self.shutdown_handlers.append(handler)
    
    def graceful_shutdown(self):
        """Perform graceful shutdown"""
        if self.is_shutting_down:
            return
        
        self.is_shutting_down = True
        logger.info("🛑 Initiating graceful shutdown...")
        
        # Execute shutdown handlers
        for handler in self.shutdown_handlers:
            try:
                handler()
            except Exception as e:
                logger.error(f"Error in shutdown handler: {e}")
        
        logger.info("✅ Graceful shutdown completed")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, initiating shutdown...")
        self.graceful_shutdown()
        sys.exit(0)
    
    def generate_validation_report(self) -> Dict:
        """Generate validation report"""
        return {
            "timestamp": datetime.now().isoformat(),
            "validation_results": self.validation_results,
            "critical_failures": self.critical_failures,
            "warnings": self.warnings,
            "overall_status": "PASSED" if len(self.critical_failures) == 0 else "FAILED"
        }

def main():
    """Main startup validation function"""
    validator = StartupValidator()
    
    # Run validation
    is_ready = validator.validate_all_systems()
    
    # Generate report
    report = validator.generate_validation_report()
    
    # Save report
    report_file = Path("startup_validation_report.json")
    import json
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"📊 Validation report saved: {report_file}")
    
    if is_ready:
        logger.info("🎉 System is ready for trading!")
        return 0
    else:
        logger.error("❌ System is NOT ready for trading!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
