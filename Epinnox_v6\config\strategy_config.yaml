# Strategy Configuration - Optimized for Best Signal Generation

strategies:
  atr_ema_bands:
    # Feature toggles for indicators - enable all for optimal signal generation
    use_ema: true
    use_atr: true
    use_rsi: true
    use_macd: true
    use_ema_slope: true
    use_price_velocity: true
    use_atr_breakout: true
    use_band_break: true
    use_wick_rejection: true
    use_band_width: true
    use_htf_alignment: true

    # Signal mode - 'threshold' or 'crossover'
    signal_mode: crossover  # Default to crossover mode

    # Band crossover parameters
    use_ema_cross: false    # Disable EMA center line by default
    use_ema_plus_1_cross: false
    use_ema_plus_2_cross: true  # Enable EMA+2 for shorts
    use_ema_plus_3_cross: true  # Enable EMA+3 for shorts
    use_ema_minus_1_cross: false
    use_ema_minus_2_cross: true  # Enable EMA-2 for longs
    use_ema_minus_3_cross: true  # Enable EMA-3 for longs
    enable_long_crossovers: true
    enable_short_crossovers: true
    use_rsi_filter: true    # Enable RSI filter by default

    # ATR period - optimized for better volatility measurement
    atr_period: 14

    # EMA period - optimized for trend following
    ema_period: 21

    # Band multipliers - standard deviations from mean
    band_multipliers: [1, 2, 3]

    # Signal thresholds - slightly adjusted for better entry/exit points
    buy_threshold: 0.65
    sell_threshold: 0.35

    # Lookback periods for signal confirmation - increased for better confirmation
    lookback_periods: 5

    # EMA slope parameters - optimized for trend strength detection
    ema_slope_periods: 8
    ema_slope_weight: 0.6

    # RSI parameters - standard settings with increased weight
    rsi_period: 14
    rsi_overbought: 70
    rsi_oversold: 30
    rsi_weight: 0.3

    # MACD parameters - optimized for crypto markets
    macd_fast_period: 12
    macd_slow_period: 26
    macd_signal_period: 9
    macd_weight: 0.3

    # Price velocity parameters - adjusted for better momentum detection
    velocity_periods: 4
    velocity_threshold: 0.025
    velocity_weight: 0.15

    # ATR breakout parameters - increased for better breakout detection
    atr_breakout_multiplier: 1.8
    atr_breakout_weight: 0.2

    # Band bounce vs break parameters - increased for better trend continuation detection
    band_break_weight: 0.2

    # Wick rejection parameters - optimized for better reversal detection
    wick_rejection_threshold: 0.4
    wick_rejection_weight: 0.15

    # Dynamic band width parameters - adjusted for volatility detection
    band_width_threshold: 0.06
    band_width_weight: 0.15

    # Higher timeframe alignment - increased for better trend alignment
    htf_alignment_weight: 0.25

# Trading parameters - Optimized for Best Performance
trading:
  # Default symbol to trade
  symbol: "DOGE/USDT:USDT"

  # Default timeframe - 5m for more frequent signals
  timeframe: "5m"

  # Higher timeframe for alignment (now enabled)
  htf_timeframe: "1h"

  # Position sizing - slightly reduced for better risk management
  position_size_pct: 8.0  # Percentage of available balance

  # Leverage - optimized for balanced risk/reward
  leverage: 15

  # Take profit and stop loss settings - improved risk/reward ratio
  take_profit_pct: 0.5
  stop_loss_pct: 1.5

  # Auto trading settings
  auto_trading_enabled: true
  check_interval_seconds: 45  # More frequent checks for better entry timing
