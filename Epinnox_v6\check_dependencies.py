#!/usr/bin/env python3
"""
EPINNOX v6 Dependency Checker
Check which dependencies are installed and which are missing
"""

import importlib
import sys
import platform
import subprocess
from typing import Dict, List, Tuple

def check_package(package_name: str, import_name: str = None) -> Tuple[bool, str]:
    """Check if a package is installed and get version info"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'unknown')
        return True, version
    except ImportError:
        return False, 'not installed'

def check_gpu_support():
    """Check for GPU support"""
    gpu_info = {
        'nvidia': False,
        'cuda_available': False,
        'torch_cuda': False
    }
    
    # Check NVIDIA GPU
    try:
        result = subprocess.run("nvidia-smi", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            gpu_info['nvidia'] = True
    except:
        pass
    
    # Check CUDA availability
    try:
        import torch
        gpu_info['torch_cuda'] = torch.cuda.is_available()
        if gpu_info['torch_cuda']:
            gpu_info['cuda_available'] = True
    except:
        pass
    
    return gpu_info

def main():
    """Main dependency checking function"""
    print("🔍 EPINNOX v6 Dependency Check")
    print("=" * 50)
    
    # System info
    print(f"🖥️ System: {platform.system()} {platform.release()}")
    print(f"🐍 Python: {sys.version}")
    print()
    
    # Core dependencies
    print("📦 CORE DEPENDENCIES")
    print("-" * 30)
    
    core_deps = {
        'pandas': 'pandas',
        'numpy': 'numpy',
        'scipy': 'scipy',
        'requests': 'requests',
        'aiohttp': 'aiohttp',
        'websockets': 'websockets'
    }
    
    core_status = {}
    for package, import_name in core_deps.items():
        installed, version = check_package(package, import_name)
        status = "✅" if installed else "❌"
        print(f"{status} {package:<15} {version}")
        core_status[package] = installed
    
    # ML/RL dependencies
    print("\n🧠 ML/RL DEPENDENCIES")
    print("-" * 30)
    
    ml_deps = {
        'torch': 'torch',
        'stable-baselines3': 'stable_baselines3',
        'gymnasium': 'gymnasium',
        'gym': 'gym',
        'tensorflow': 'tensorflow',
        'scikit-learn': 'sklearn'
    }
    
    ml_status = {}
    for package, import_name in ml_deps.items():
        installed, version = check_package(package, import_name)
        status = "✅" if installed else "❌"
        print(f"{status} {package:<15} {version}")
        ml_status[package] = installed
    
    # Technical Analysis
    print("\n📈 TECHNICAL ANALYSIS")
    print("-" * 30)
    
    ta_deps = {
        'TA-Lib': 'talib',
        'ta': 'ta',
        'pandas-ta': 'pandas_ta'
    }
    
    ta_status = {}
    for package, import_name in ta_deps.items():
        installed, version = check_package(package, import_name)
        status = "✅" if installed else "❌"
        print(f"{status} {package:<15} {version}")
        ta_status[package] = installed
    
    # GUI dependencies
    print("\n🖼️ GUI DEPENDENCIES")
    print("-" * 30)
    
    gui_deps = {
        'PyQt5': 'PyQt5',
        'pyqtgraph': 'pyqtgraph',
        'streamlit': 'streamlit',
        'plotly': 'plotly',
        'matplotlib': 'matplotlib'
    }
    
    gui_status = {}
    for package, import_name in gui_deps.items():
        installed, version = check_package(package, import_name)
        status = "✅" if installed else "❌"
        print(f"{status} {package:<15} {version}")
        gui_status[package] = installed
    
    # Testing dependencies
    print("\n🧪 TESTING DEPENDENCIES")
    print("-" * 30)
    
    test_deps = {
        'pytest': 'pytest',
        'pytest-asyncio': 'pytest_asyncio',
        'pytest-mock': 'pytest_mock',
        'pytest-cov': 'pytest_cov'
    }
    
    test_status = {}
    for package, import_name in test_deps.items():
        installed, version = check_package(package, import_name)
        status = "✅" if installed else "❌"
        print(f"{status} {package:<15} {version}")
        test_status[package] = installed
    
    # GPU Support
    print("\n🎮 GPU SUPPORT")
    print("-" * 30)
    
    gpu_info = check_gpu_support()
    
    nvidia_status = "✅" if gpu_info['nvidia'] else "❌"
    print(f"{nvidia_status} NVIDIA GPU      {'detected' if gpu_info['nvidia'] else 'not detected'}")
    
    cuda_status = "✅" if gpu_info['cuda_available'] else "❌"
    print(f"{cuda_status} CUDA Support     {'available' if gpu_info['cuda_available'] else 'not available'}")
    
    torch_cuda_status = "✅" if gpu_info['torch_cuda'] else "❌"
    print(f"{torch_cuda_status} PyTorch CUDA    {'available' if gpu_info['torch_cuda'] else 'not available'}")
    
    # Summary
    print("\n📊 SUMMARY")
    print("-" * 30)
    
    total_core = len(core_deps)
    installed_core = sum(core_status.values())
    
    total_ml = len(ml_deps)
    installed_ml = sum(ml_status.values())
    
    total_ta = len(ta_deps)
    installed_ta = sum(ta_status.values())
    
    total_gui = len(gui_deps)
    installed_gui = sum(gui_status.values())
    
    total_test = len(test_deps)
    installed_test = sum(test_status.values())
    
    print(f"Core Dependencies:    {installed_core}/{total_core} ({installed_core/total_core*100:.0f}%)")
    print(f"ML/RL Dependencies:   {installed_ml}/{total_ml} ({installed_ml/total_ml*100:.0f}%)")
    print(f"Technical Analysis:   {installed_ta}/{total_ta} ({installed_ta/total_ta*100:.0f}%)")
    print(f"GUI Dependencies:     {installed_gui}/{total_gui} ({installed_gui/total_gui*100:.0f}%)")
    print(f"Testing Dependencies: {installed_test}/{total_test} ({installed_test/total_test*100:.0f}%)")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS")
    print("-" * 30)
    
    missing_critical = []
    missing_optional = []
    
    # Check critical missing packages
    if not core_status.get('pandas', False):
        missing_critical.append('pandas')
    if not core_status.get('numpy', False):
        missing_critical.append('numpy')
    if not ml_status.get('torch', False):
        missing_critical.append('torch')
    
    # Check optional missing packages
    if not ml_status.get('stable-baselines3', False):
        missing_optional.append('stable-baselines3 (for RL)')
    if not ta_status.get('TA-Lib', False):
        missing_optional.append('TA-Lib (for technical analysis)')
    if not gui_status.get('PyQt5', False):
        missing_optional.append('PyQt5 (for GUI)')
    if not gui_status.get('streamlit', False):
        missing_optional.append('streamlit (for web dashboard)')
    
    if missing_critical:
        print("🚨 CRITICAL: Install these packages immediately:")
        for package in missing_critical:
            print(f"   pip install {package}")
    
    if missing_optional:
        print("⚠️ OPTIONAL: Consider installing for full functionality:")
        for package in missing_optional:
            print(f"   pip install {package}")
    
    if not missing_critical and not missing_optional:
        print("🎉 All dependencies are installed!")
    
    # Installation commands
    print("\n🛠️ QUICK INSTALL COMMANDS")
    print("-" * 30)
    print("Install all dependencies:")
    print("   python install_dependencies.py")
    print()
    print("Install from requirements file:")
    print("   pip install -r requirements-full.txt")
    print()
    print("Install core only:")
    print("   pip install pandas numpy torch requests aiohttp")
    print()
    print("Install RL components:")
    print("   pip install stable-baselines3[extra] gymnasium")
    print()
    print("Install GUI components:")
    print("   pip install PyQt5 streamlit plotly")

if __name__ == "__main__":
    main()
