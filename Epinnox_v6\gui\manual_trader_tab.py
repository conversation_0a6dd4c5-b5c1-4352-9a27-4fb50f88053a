"""
Manual Trader Tab for Epinnox v6 Trading System
Enhanced manual trading interface with specific action buttons
"""

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
except ImportError:
    print("PyQt5 not installed. Install with: pip install PyQt5")
    raise

from .base_tab import BaseTab
from .matrix_theme import MatrixTheme
import logging

logger = logging.getLogger(__name__)

class ManualTraderTab(BaseTab):
    """Manual trading tab with enhanced order management"""
    
    # Signals
    order_placed = pyqtSignal(dict)  # order_data
    orders_cancelled = pyqtSignal()
    positions_requested = pyqtSignal()
    orders_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def setup_ui(self):
        """Setup the Manual Trader tab UI"""
        layout = QHBoxLayout(self)
        
        # Order entry panel
        order_panel = self.create_order_entry_panel()
        
        # Positions and orders panel
        positions_panel = self.create_positions_panel()
        
        layout.addWidget(order_panel)
        layout.addWidget(positions_panel, 2)
    
    def create_order_entry_panel(self):
        """Create the enhanced order entry panel"""
        panel = self.create_matrix_group_box("Manual Order Entry")
        layout = QVBoxLayout(panel)
        
        # Symbol selection
        symbol_layout = QHBoxLayout()
        self.manual_symbol = QComboBox()
        self.manual_symbol.addItems(["DOGE/USDT", "BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT"])
        
        symbol_layout.addWidget(QLabel("Symbol:"))
        symbol_layout.addWidget(self.manual_symbol)
        
        # Real-time Bid/Ask Display
        bid_ask_group = self.create_matrix_group_box("Live Market Data")
        bid_ask_layout = QGridLayout(bid_ask_group)

        # Best Bid
        bid_ask_layout.addWidget(QLabel("Best Bid:"), 0, 0)
        self.best_bid_label = QLabel("--")
        self.best_bid_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.GREEN};
                font-weight: bold;
                font-size: 14px;
                padding: 4px;
                background-color: rgba(0, 255, 68, 0.1);
                border: 1px solid {MatrixTheme.GREEN};
                border-radius: 3px;
            }}
        """)
        bid_ask_layout.addWidget(self.best_bid_label, 0, 1)

        # Best Ask
        bid_ask_layout.addWidget(QLabel("Best Ask:"), 0, 2)
        self.best_ask_label = QLabel("--")
        self.best_ask_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.RED};
                font-weight: bold;
                font-size: 14px;
                padding: 4px;
                background-color: rgba(255, 0, 0, 0.1);
                border: 1px solid {MatrixTheme.RED};
                border-radius: 3px;
            }}
        """)
        bid_ask_layout.addWidget(self.best_ask_label, 0, 3)

        # Spread calculation
        bid_ask_layout.addWidget(QLabel("Spread:"), 1, 0)
        self.spread_label = QLabel("--")
        self.spread_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.YELLOW};
                font-weight: bold;
                padding: 4px;
            }}
        """)
        bid_ask_layout.addWidget(self.spread_label, 1, 1, 1, 3)

        # Quantity and price
        qty_price_layout = QHBoxLayout()
        self.order_quantity = QDoubleSpinBox()
        self.order_quantity.setDecimals(4)
        self.order_quantity.setMaximum(999999)
        self.order_quantity.setValue(100.0)
        self.order_quantity.setToolTip("Enter the quantity to trade")

        self.order_price = QDoubleSpinBox()
        self.order_price.setDecimals(6)
        self.order_price.setMaximum(999999)
        self.order_price.setValue(0.1)
        self.order_price.setToolTip("Enter limit price (auto-filled from bid/ask)")

        # Auto-fill buttons for price
        fill_bid_btn = QPushButton("Use Bid")
        fill_bid_btn.clicked.connect(self.fill_bid_price)
        fill_bid_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.GREEN};
                color: {MatrixTheme.BLACK};
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 3px;
            }}
        """)

        fill_ask_btn = QPushButton("Use Ask")
        fill_ask_btn.clicked.connect(self.fill_ask_price)
        fill_ask_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.RED};
                color: {MatrixTheme.BLACK};
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 3px;
            }}
        """)

        qty_price_layout.addWidget(QLabel("Quantity:"))
        qty_price_layout.addWidget(self.order_quantity)
        qty_price_layout.addWidget(QLabel("Price:"))
        qty_price_layout.addWidget(self.order_price)
        qty_price_layout.addWidget(fill_bid_btn)
        qty_price_layout.addWidget(fill_ask_btn)
        
        # Leverage setting
        leverage_layout = QHBoxLayout()
        self.manual_leverage = QSpinBox()
        self.manual_leverage.setRange(1, 100)
        self.manual_leverage.setValue(1)
        
        leverage_layout.addWidget(QLabel("Leverage:"))
        leverage_layout.addWidget(self.manual_leverage)
        leverage_layout.addStretch()
        
        # Action buttons - 6 specific trading actions
        actions_group = self.create_matrix_group_box("Trading Actions")
        actions_layout = QVBoxLayout(actions_group)
        
        # Long actions
        long_layout = QHBoxLayout()
        self.limit_long_btn = self.create_matrix_button("LIMIT LONG", self.place_limit_long)
        self.market_long_btn = self.create_matrix_button("MARKET LONG", self.place_market_long)
        long_layout.addWidget(self.limit_long_btn)
        long_layout.addWidget(self.market_long_btn)
        
        # Short actions
        short_layout = QHBoxLayout()
        self.limit_short_btn = self.create_matrix_button("LIMIT SHORT", self.place_limit_short)
        self.market_short_btn = self.create_matrix_button("MARKET SHORT", self.place_market_short)
        short_layout.addWidget(self.limit_short_btn)
        short_layout.addWidget(self.market_short_btn)
        
        # Close actions
        close_layout = QHBoxLayout()
        self.limit_close_btn = self.create_matrix_button("LIMIT CLOSE", self.place_limit_close)
        self.market_close_btn = self.create_matrix_button("MARKET CLOSE", self.place_market_close)
        close_layout.addWidget(self.limit_close_btn)
        close_layout.addWidget(self.market_close_btn)
        
        # Cancel all button
        self.cancel_all_btn = self.create_matrix_button("CANCEL ALL OPEN ORDERS", self.cancel_all_orders)
        self.cancel_all_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.RED};
                color: {MatrixTheme.BLACK};
                font-weight: bold;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background-color: #CC0000;
            }}
        """)
        
        actions_layout.addLayout(long_layout)
        actions_layout.addLayout(short_layout)
        actions_layout.addLayout(close_layout)
        actions_layout.addWidget(self.cancel_all_btn)
        
        # Add all sections to main layout
        layout.addLayout(symbol_layout)
        layout.addWidget(bid_ask_group)  # Add bid/ask display
        layout.addLayout(qty_price_layout)
        layout.addLayout(leverage_layout)
        layout.addWidget(actions_group)
        layout.addStretch()

        # Initialize bid/ask tracking
        self.current_bid = None
        self.current_ask = None
        self.last_bid = None
        self.last_ask = None

        return panel

    def fill_bid_price(self):
        """Fill order price with current best bid"""
        if self.current_bid is not None:
            self.order_price.setValue(self.current_bid)
            self.log_message(f"Price set to best bid: {self.current_bid:.6f}")

    def fill_ask_price(self):
        """Fill order price with current best ask"""
        if self.current_ask is not None:
            self.order_price.setValue(self.current_ask)
            self.log_message(f"Price set to best ask: {self.current_ask:.6f}")

    def update_bid_ask_display(self, bid: float = None, ask: float = None):
        """Update the bid/ask display with real-time data"""
        try:
            # Update stored values
            if bid is not None:
                self.last_bid = self.current_bid
                self.current_bid = bid
            if ask is not None:
                self.last_ask = self.current_ask
                self.current_ask = ask

            # Update bid display with movement indicator
            if self.current_bid is not None:
                bid_text = f"{self.current_bid:.6f}"
                if self.last_bid is not None:
                    if self.current_bid > self.last_bid:
                        bid_text += " ↑"
                    elif self.current_bid < self.last_bid:
                        bid_text += " ↓"
                self.best_bid_label.setText(bid_text)

            # Update ask display with movement indicator
            if self.current_ask is not None:
                ask_text = f"{self.current_ask:.6f}"
                if self.last_ask is not None:
                    if self.current_ask > self.last_ask:
                        ask_text += " ↑"
                    elif self.current_ask < self.last_ask:
                        ask_text += " ↓"
                self.best_ask_label.setText(ask_text)

            # Calculate and display spread
            if self.current_bid is not None and self.current_ask is not None:
                spread = self.current_ask - self.current_bid
                spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0
                self.spread_label.setText(f"{spread:.6f} ({spread_pct:.3f}%)")

        except Exception as e:
            self.log_message(f"Error updating bid/ask display: {e}", logging.ERROR)

    def validate_order_inputs(self) -> tuple[bool, str]:
        """Validate order inputs before placement"""
        try:
            # Check quantity
            quantity = self.order_quantity.value()
            if quantity <= 0:
                return False, "Quantity must be greater than 0"

            # Check price for limit orders
            price = self.order_price.value()
            if price <= 0:
                return False, "Price must be greater than 0"

            # Check leverage
            leverage = self.manual_leverage.value()
            if leverage < 1 or leverage > 125:
                return False, "Leverage must be between 1 and 125"

            # Check if bid/ask data is available
            if self.current_bid is None or self.current_ask is None:
                return False, "Market data not available. Please wait for price updates."

            return True, "Validation passed"

        except Exception as e:
            return False, f"Validation error: {str(e)}"

    def create_positions_panel(self):
        """Create positions and orders display panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Open positions
        positions_group = self.create_matrix_group_box("Open Positions")
        positions_layout = QVBoxLayout(positions_group)
        
        self.positions_table = QTableWidget(0, 7)
        self.positions_table.setHorizontalHeaderLabels([
            "Symbol", "Side", "Size", "Entry Price", "Mark Price", "PnL", "Actions"
        ])
        self.apply_matrix_table_styling(self.positions_table)
        
        # Add refresh button for positions
        positions_controls = QHBoxLayout()
        self.refresh_positions_btn = self.create_matrix_button("REFRESH POSITIONS", self.refresh_positions)
        positions_controls.addWidget(self.refresh_positions_btn)
        positions_controls.addStretch()
        
        positions_layout.addLayout(positions_controls)
        positions_layout.addWidget(self.positions_table)
        
        # Open orders
        orders_group = self.create_matrix_group_box("Open Orders")
        orders_layout = QVBoxLayout(orders_group)
        
        self.orders_table = QTableWidget(0, 8)
        self.orders_table.setHorizontalHeaderLabels([
            "Symbol", "Side", "Type", "Size", "Price", "Filled", "Status", "Actions"
        ])
        self.apply_matrix_table_styling(self.orders_table)
        
        # Add refresh button for orders
        orders_controls = QHBoxLayout()
        self.refresh_orders_btn = self.create_matrix_button("REFRESH ORDERS", self.refresh_orders)
        orders_controls.addWidget(self.refresh_orders_btn)
        orders_controls.addStretch()
        
        orders_layout.addLayout(orders_controls)
        orders_layout.addWidget(self.orders_table)
        
        layout.addWidget(positions_group)
        layout.addWidget(orders_group)
        
        return panel
    
    def place_limit_long(self):
        """Place a limit long order with validation and confirmation"""
        if not self._validate_and_confirm_order("BUY", "LIMIT"):
            return
        order_data = self.get_order_data("BUY", "LIMIT")
        self.place_order(order_data)

    def place_market_long(self):
        """Place a market long order with validation and confirmation"""
        if not self._validate_and_confirm_order("BUY", "MARKET"):
            return
        order_data = self.get_order_data("BUY", "MARKET")
        self.place_order(order_data)

    def place_limit_short(self):
        """Place a limit short order with validation and confirmation"""
        if not self._validate_and_confirm_order("SELL", "LIMIT"):
            return
        order_data = self.get_order_data("SELL", "LIMIT")
        self.place_order(order_data)

    def place_market_short(self):
        """Place a market short order with validation and confirmation"""
        if not self._validate_and_confirm_order("SELL", "MARKET"):
            return
        order_data = self.get_order_data("SELL", "MARKET")
        self.place_order(order_data)
    
    def place_limit_close(self):
        """Place a limit close order"""
        # This would close existing positions at limit price
        order_data = self.get_close_order_data("LIMIT")
        self.place_order(order_data)
    
    def place_market_close(self):
        """Place a market close order"""
        # This would close existing positions at market price
        order_data = self.get_close_order_data("MARKET")
        self.place_order(order_data)
    
    def get_order_data(self, side, order_type):
        """Get order data from form inputs"""
        return {
            'symbol': self.manual_symbol.currentText(),
            'side': side,
            'type': order_type,
            'quantity': self.order_quantity.value(),
            'price': self.order_price.value() if order_type == "LIMIT" else None,
            'leverage': self.manual_leverage.value()
        }
    
    def get_close_order_data(self, order_type):
        """Get close order data - would determine side based on existing positions"""
        # This is a placeholder - in real implementation, would check existing positions
        return {
            'symbol': self.manual_symbol.currentText(),
            'side': 'CLOSE',  # Special close action
            'type': order_type,
            'quantity': self.order_quantity.value(),
            'price': self.order_price.value() if order_type == "LIMIT" else None,
            'leverage': self.manual_leverage.value()
        }
    
    def _validate_and_confirm_order(self, side: str, order_type: str) -> bool:
        """Validate inputs and show confirmation dialog"""
        try:
            # Validate inputs
            is_valid, error_msg = self.validate_order_inputs()
            if not is_valid:
                self.show_error_message("Invalid Order", error_msg)
                return False

            # Get order data for confirmation
            order_data = self.get_order_data(side, order_type)

            # Create confirmation dialog
            confirmation_msg = self._create_order_confirmation_message(order_data)

            reply = QMessageBox.question(
                self,
                "Confirm Order",
                confirmation_msg,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            return reply == QMessageBox.Yes

        except Exception as e:
            self.log_message(f"Error in order validation: {e}", logging.ERROR)
            self.show_error_message("Validation Error", f"Error validating order: {str(e)}")
            return False

    def _create_order_confirmation_message(self, order_data: dict) -> str:
        """Create a detailed confirmation message for the order"""
        side_color = "🟢" if order_data['side'] == "BUY" else "🔴"
        type_text = "LIMIT" if order_data['type'] == "LIMIT" else "MARKET"

        msg = f"""
{side_color} {order_data['side']} {type_text} ORDER

Symbol: {order_data['symbol']}
Quantity: {order_data['quantity']:.4f}
"""

        if order_data['type'] == "LIMIT":
            msg += f"Price: {order_data['price']:.6f}\n"
        else:
            msg += f"Price: MARKET (Best {'Ask' if order_data['side'] == 'BUY' else 'Bid'})\n"

        msg += f"Leverage: {order_data['leverage']}x\n"

        # Add current market data
        if self.current_bid and self.current_ask:
            msg += f"\nCurrent Market:\nBid: {self.current_bid:.6f}\nAsk: {self.current_ask:.6f}\n"

        msg += "\nDo you want to place this order?"

        return msg

    def place_order(self, order_data):
        """Place an order with enhanced error handling and loading state"""
        try:
            # Disable buttons during order placement
            self._set_buttons_enabled(False)

            self.log_message(f"Placing order: {order_data}")

            # Emit order to trading system
            self.order_placed.emit(order_data)

            # Show success message
            success_msg = f"Order placed: {order_data['side']} {order_data['quantity']} {order_data['symbol']}"
            if order_data['type'] == "LIMIT":
                success_msg += f" at {order_data['price']:.6f}"

            self.show_info_message("Order Placed", success_msg)

            # Re-enable buttons
            self._set_buttons_enabled(True)

        except Exception as e:
            self.log_message(f"Error placing order: {e}", logging.ERROR)
            self.show_error_message("Order Error", f"Failed to place order: {str(e)}")
            # Re-enable buttons on error
            self._set_buttons_enabled(True)

    def _set_buttons_enabled(self, enabled: bool):
        """Enable/disable trading buttons (for loading states)"""
        try:
            self.limit_long_btn.setEnabled(enabled)
            self.market_long_btn.setEnabled(enabled)
            self.limit_short_btn.setEnabled(enabled)
            self.market_short_btn.setEnabled(enabled)
            self.limit_close_btn.setEnabled(enabled)
            self.market_close_btn.setEnabled(enabled)

            # Update button text to show loading state
            if not enabled:
                buttons = [
                    (self.limit_long_btn, "PLACING..."),
                    (self.market_long_btn, "PLACING..."),
                    (self.limit_short_btn, "PLACING..."),
                    (self.market_short_btn, "PLACING..."),
                    (self.limit_close_btn, "PLACING..."),
                    (self.market_close_btn, "PLACING...")
                ]
                for btn, text in buttons:
                    btn.setText(text)
            else:
                # Restore original button text
                self.limit_long_btn.setText("LIMIT LONG")
                self.market_long_btn.setText("MARKET LONG")
                self.limit_short_btn.setText("LIMIT SHORT")
                self.market_short_btn.setText("MARKET SHORT")
                self.limit_close_btn.setText("LIMIT CLOSE")
                self.market_close_btn.setText("MARKET CLOSE")

        except Exception as e:
            self.log_message(f"Error setting button states: {e}", logging.ERROR)
    
    def cancel_all_orders(self):
        """Cancel all open orders"""
        try:
            self.log_message("Cancelling all open orders")
            self.orders_cancelled.emit()
            self.show_info_message("Orders Cancelled", "All open orders have been cancelled")
            self.refresh_orders()
        except Exception as e:
            self.log_message(f"Error cancelling orders: {e}", logging.ERROR)
            self.show_error_message("Cancel Error", f"Failed to cancel orders: {str(e)}")
    
    def refresh_positions(self):
        """Refresh positions table"""
        self.log_message("Refreshing positions")
        self.positions_requested.emit()
    
    def refresh_orders(self):
        """Refresh orders table"""
        self.log_message("Refreshing orders")
        self.orders_requested.emit()
    
    def update_positions_table(self, positions_data):
        """Update the positions table with real data"""
        try:
            self.positions_table.setRowCount(len(positions_data))
            
            for row, position in enumerate(positions_data):
                self.positions_table.setItem(row, 0, QTableWidgetItem(position.get('symbol', '')))
                self.positions_table.setItem(row, 1, QTableWidgetItem(position.get('side', '')))
                self.positions_table.setItem(row, 2, QTableWidgetItem(str(position.get('size', 0))))
                self.positions_table.setItem(row, 3, QTableWidgetItem(str(position.get('entry_price', 0))))
                self.positions_table.setItem(row, 4, QTableWidgetItem(str(position.get('mark_price', 0))))
                
                # Color code PnL
                pnl = position.get('pnl', 0)
                pnl_item = QTableWidgetItem(f"${pnl:.2f}")
                if pnl > 0:
                    pnl_item.setForeground(QColor(MatrixTheme.GREEN))
                elif pnl < 0:
                    pnl_item.setForeground(QColor(MatrixTheme.RED))
                self.positions_table.setItem(row, 5, pnl_item)
                
                # Add action buttons
                actions_widget = QWidget()
                actions_layout = QHBoxLayout(actions_widget)
                close_btn = QPushButton("Close")
                close_btn.clicked.connect(lambda checked, pos=position: self.close_position(pos))
                actions_layout.addWidget(close_btn)
                self.positions_table.setCellWidget(row, 6, actions_widget)
                
        except Exception as e:
            self.log_message(f"Error updating positions table: {e}", logging.ERROR)
    
    def update_orders_table(self, orders_data):
        """Update the orders table with real data"""
        try:
            self.orders_table.setRowCount(len(orders_data))
            
            for row, order in enumerate(orders_data):
                self.orders_table.setItem(row, 0, QTableWidgetItem(order.get('symbol', '')))
                self.orders_table.setItem(row, 1, QTableWidgetItem(order.get('side', '')))
                self.orders_table.setItem(row, 2, QTableWidgetItem(order.get('type', '')))
                self.orders_table.setItem(row, 3, QTableWidgetItem(str(order.get('size', 0))))
                self.orders_table.setItem(row, 4, QTableWidgetItem(str(order.get('price', 0))))
                self.orders_table.setItem(row, 5, QTableWidgetItem(str(order.get('filled', 0))))
                self.orders_table.setItem(row, 6, QTableWidgetItem(order.get('status', '')))
                
                # Add action buttons
                actions_widget = QWidget()
                actions_layout = QHBoxLayout(actions_widget)
                cancel_btn = QPushButton("Cancel")
                cancel_btn.clicked.connect(lambda checked, ord=order: self.cancel_order(ord))
                actions_layout.addWidget(cancel_btn)
                self.orders_table.setCellWidget(row, 7, actions_widget)
                
        except Exception as e:
            self.log_message(f"Error updating orders table: {e}", logging.ERROR)
    
    def close_position(self, position):
        """Close a specific position"""
        self.log_message(f"Closing position: {position}")
        # Implementation would place opposite order to close position
    
    def cancel_order(self, order):
        """Cancel a specific order"""
        self.log_message(f"Cancelling order: {order}")
        # Implementation would cancel the specific order
