
def fix_scalper_gpt_quantity(response_data, account_balance=1000.0, current_price=1.0):
    """Fix quantity calculation in ScalperGPT response"""
    if not isinstance(response_data, dict):
        return response_data
    
    # Get quantity from response
    quantity = response_data.get('quantity', response_data.get('QUANTITY', 0))
    
    # If quantity is 0 or invalid, calculate it
    if not quantity or quantity <= 0:
        # Get risk percentage
        risk_pct = response_data.get('risk_pct', response_data.get('RISK_PCT', 2.0))
        
        # Get leverage
        leverage = response_data.get('leverage', response_data.get('LEVERAGE', 1))
        
        # Calculate quantity based on risk
        if current_price > 0:
            risk_amount = account_balance * (risk_pct / 100.0)
            position_value = risk_amount * leverage
            quantity = position_value / current_price
        else:
            # Fallback calculation
            quantity = (account_balance * 0.01) / max(current_price, 1.0)
    
    # Ensure minimum quantity
    min_quantity = 0.001
    if quantity < min_quantity:
        quantity = min_quantity
    
    # Update response data
    response_data['quantity'] = quantity
    response_data['QUANTITY'] = quantity
    
    return response_data
