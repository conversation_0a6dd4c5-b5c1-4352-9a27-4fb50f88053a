"""
Stress Testing Framework
Simulate extreme market conditions and volatility events
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import logging
import asyncio

logger = logging.getLogger(__name__)

class StressScenario(Enum):
    """Types of stress test scenarios"""
    FLASH_CRASH = "flash_crash"
    VOLATILITY_SPIKE = "volatility_spike"
    LIQUIDITY_CRISIS = "liquidity_crisis"
    TRENDING_MARKET = "trending_market"
    SIDEWAYS_MARKET = "sideways_market"
    GAP_OPENING = "gap_opening"
    NEWS_SHOCK = "news_shock"
    CORRELATION_BREAKDOWN = "correlation_breakdown"

@dataclass
class StressTestConfig:
    """Configuration for stress testing"""
    scenario: StressScenario
    duration_minutes: int = 60
    intensity: float = 1.0  # 1.0 = normal, 2.0 = extreme
    symbols: List[str] = None
    initial_balance: float = 10000.0
    parameters: Dict[str, Any] = None

@dataclass
class StressTestResult:
    """Results from stress testing"""
    scenario: StressScenario
    config: StressTestConfig
    performance_metrics: Dict[str, float]
    risk_metrics: Dict[str, float]
    trade_summary: Dict[str, Any]
    system_stability: Dict[str, Any]
    recommendations: List[str]

class StressTestFramework:
    """
    Comprehensive stress testing framework for trading systems
    """
    
    def __init__(self, trading_system):
        self.trading_system = trading_system
        self.stress_results = []
        
    def generate_stress_scenario_data(self, config: StressTestConfig) -> Dict[str, pd.DataFrame]:
        """Generate market data for stress testing scenarios"""
        
        symbols = config.symbols or ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
        scenario_data = {}
        
        # Base time series
        start_time = datetime.now()
        timestamps = pd.date_range(start=start_time, 
                                 periods=config.duration_minutes, 
                                 freq='1min')
        
        for symbol in symbols:
            if config.scenario == StressScenario.FLASH_CRASH:
                data = self._generate_flash_crash_data(timestamps, symbol, config.intensity)
            elif config.scenario == StressScenario.VOLATILITY_SPIKE:
                data = self._generate_volatility_spike_data(timestamps, symbol, config.intensity)
            elif config.scenario == StressScenario.LIQUIDITY_CRISIS:
                data = self._generate_liquidity_crisis_data(timestamps, symbol, config.intensity)
            elif config.scenario == StressScenario.TRENDING_MARKET:
                data = self._generate_trending_market_data(timestamps, symbol, config.intensity)
            elif config.scenario == StressScenario.SIDEWAYS_MARKET:
                data = self._generate_sideways_market_data(timestamps, symbol, config.intensity)
            elif config.scenario == StressScenario.GAP_OPENING:
                data = self._generate_gap_opening_data(timestamps, symbol, config.intensity)
            elif config.scenario == StressScenario.NEWS_SHOCK:
                data = self._generate_news_shock_data(timestamps, symbol, config.intensity)
            elif config.scenario == StressScenario.CORRELATION_BREAKDOWN:
                data = self._generate_correlation_breakdown_data(timestamps, symbol, config.intensity)
            else:
                data = self._generate_normal_market_data(timestamps, symbol)
            
            scenario_data[symbol] = data
        
        return scenario_data
    
    def _generate_flash_crash_data(self, timestamps: pd.DatetimeIndex, 
                                 symbol: str, intensity: float) -> pd.DataFrame:
        """Generate flash crash scenario data"""
        
        base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 0.5
        
        # Normal market for first part
        normal_periods = len(timestamps) // 3
        crash_periods = len(timestamps) // 6
        recovery_periods = len(timestamps) - normal_periods - crash_periods
        
        prices = []
        volumes = []
        
        # Normal phase
        current_price = base_price
        for i in range(normal_periods):
            change = np.random.normal(0, 0.001)  # 0.1% normal volatility
            current_price *= (1 + change)
            prices.append(current_price)
            volumes.append(np.random.uniform(100, 500))
        
        # Flash crash phase
        crash_magnitude = 0.20 * intensity  # 20% crash at intensity 1.0
        crash_per_minute = crash_magnitude / crash_periods
        
        for i in range(crash_periods):
            # Accelerating crash
            crash_rate = crash_per_minute * (1 + i / crash_periods)
            current_price *= (1 - crash_rate)
            prices.append(current_price)
            volumes.append(np.random.uniform(1000, 5000))  # High volume during crash
        
        # Recovery phase
        recovery_target = base_price * (1 - crash_magnitude * 0.5)  # Partial recovery
        recovery_per_minute = (recovery_target - current_price) / recovery_periods
        
        for i in range(recovery_periods):
            current_price += recovery_per_minute * (1 - i / recovery_periods)
            current_price *= (1 + np.random.normal(0, 0.002))  # Higher volatility
            prices.append(current_price)
            volumes.append(np.random.uniform(500, 1500))
        
        # Create OHLCV data
        data = []
        for i, (timestamp, price, volume) in enumerate(zip(timestamps, prices, volumes)):
            volatility = 0.005 if i < normal_periods else 0.02 if i < normal_periods + crash_periods else 0.01
            
            high = price * (1 + abs(np.random.normal(0, volatility)))
            low = price * (1 - abs(np.random.normal(0, volatility)))
            open_price = prices[i-1] if i > 0 else price
            close_price = price
            
            data.append({
                'timestamp': timestamp,
                'open': open_price,
                'high': max(open_price, high, close_price),
                'low': min(open_price, low, close_price),
                'close': close_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def _generate_volatility_spike_data(self, timestamps: pd.DatetimeIndex, 
                                      symbol: str, intensity: float) -> pd.DataFrame:
        """Generate high volatility scenario data"""
        
        base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 0.5
        
        data = []
        current_price = base_price
        
        for i, timestamp in enumerate(timestamps):
            # Gradually increasing volatility
            base_volatility = 0.001
            spike_volatility = 0.05 * intensity  # 5% volatility at intensity 1.0
            
            # Volatility peaks in the middle
            progress = i / len(timestamps)
            volatility_multiplier = 1 + spike_volatility * np.sin(progress * np.pi)
            current_volatility = base_volatility * volatility_multiplier
            
            # Price change
            change = np.random.normal(0, current_volatility)
            current_price *= (1 + change)
            
            # OHLCV
            high = current_price * (1 + abs(np.random.normal(0, current_volatility)))
            low = current_price * (1 - abs(np.random.normal(0, current_volatility)))
            volume = np.random.uniform(100, 1000) * volatility_multiplier
            
            data.append({
                'timestamp': timestamp,
                'open': current_price,
                'high': high,
                'low': low,
                'close': current_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def _generate_liquidity_crisis_data(self, timestamps: pd.DatetimeIndex, 
                                      symbol: str, intensity: float) -> pd.DataFrame:
        """Generate liquidity crisis scenario data"""
        
        base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 0.5
        
        data = []
        current_price = base_price
        
        for i, timestamp in enumerate(timestamps):
            # Decreasing volume, increasing spreads
            base_volume = 500
            volume_reduction = 0.8 * intensity  # 80% volume reduction at intensity 1.0
            current_volume = base_volume * (1 - volume_reduction * (i / len(timestamps)))
            current_volume = max(current_volume, 10)  # Minimum volume
            
            # Wider spreads (simulated through higher volatility)
            spread_factor = 1 + 0.02 * intensity * (i / len(timestamps))
            volatility = 0.002 * spread_factor
            
            change = np.random.normal(0, volatility)
            current_price *= (1 + change)
            
            # Wider bid-ask spread
            spread = current_price * 0.001 * spread_factor
            high = current_price + spread
            low = current_price - spread
            
            data.append({
                'timestamp': timestamp,
                'open': current_price,
                'high': high,
                'low': low,
                'close': current_price,
                'volume': current_volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def _generate_trending_market_data(self, timestamps: pd.DatetimeIndex, 
                                     symbol: str, intensity: float) -> pd.DataFrame:
        """Generate strong trending market data"""
        
        base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 0.5
        
        # Trend direction (up or down)
        trend_direction = 1 if np.random.random() > 0.5 else -1
        trend_strength = 0.002 * intensity  # 0.2% per minute at intensity 1.0
        
        data = []
        current_price = base_price
        
        for timestamp in timestamps:
            # Consistent trend with some noise
            trend_change = trend_direction * trend_strength
            noise = np.random.normal(0, 0.001)
            total_change = trend_change + noise
            
            current_price *= (1 + total_change)
            
            # OHLCV
            volatility = 0.002
            high = current_price * (1 + abs(np.random.normal(0, volatility)))
            low = current_price * (1 - abs(np.random.normal(0, volatility)))
            volume = np.random.uniform(200, 800)
            
            data.append({
                'timestamp': timestamp,
                'open': current_price,
                'high': high,
                'low': low,
                'close': current_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def _generate_sideways_market_data(self, timestamps: pd.DatetimeIndex, 
                                     symbol: str, intensity: float) -> pd.DataFrame:
        """Generate sideways/ranging market data"""
        
        base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 0.5
        
        # Range bounds
        range_size = 0.05 * intensity  # 5% range at intensity 1.0
        upper_bound = base_price * (1 + range_size / 2)
        lower_bound = base_price * (1 - range_size / 2)
        
        data = []
        current_price = base_price
        
        for timestamp in timestamps:
            # Mean reversion within range
            if current_price > upper_bound:
                bias = -0.001  # Bias towards center
            elif current_price < lower_bound:
                bias = 0.001
            else:
                bias = 0
            
            change = bias + np.random.normal(0, 0.001)
            current_price *= (1 + change)
            
            # Keep within bounds
            current_price = max(min(current_price, upper_bound), lower_bound)
            
            # OHLCV
            volatility = 0.001
            high = min(current_price * (1 + abs(np.random.normal(0, volatility))), upper_bound)
            low = max(current_price * (1 - abs(np.random.normal(0, volatility))), lower_bound)
            volume = np.random.uniform(100, 400)  # Lower volume in sideways market
            
            data.append({
                'timestamp': timestamp,
                'open': current_price,
                'high': high,
                'low': low,
                'close': current_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def _generate_gap_opening_data(self, timestamps: pd.DatetimeIndex, 
                                 symbol: str, intensity: float) -> pd.DataFrame:
        """Generate gap opening scenario data"""
        
        base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 0.5
        
        # Gap occurs at 25% through the period
        gap_point = len(timestamps) // 4
        gap_size = 0.10 * intensity  # 10% gap at intensity 1.0
        gap_direction = 1 if np.random.random() > 0.5 else -1
        
        data = []
        current_price = base_price
        
        for i, timestamp in enumerate(timestamps):
            if i == gap_point:
                # Apply gap
                current_price *= (1 + gap_direction * gap_size)
            
            # Normal price movement
            change = np.random.normal(0, 0.001)
            current_price *= (1 + change)
            
            # OHLCV
            volatility = 0.003 if i >= gap_point else 0.001  # Higher volatility after gap
            high = current_price * (1 + abs(np.random.normal(0, volatility)))
            low = current_price * (1 - abs(np.random.normal(0, volatility)))
            volume = np.random.uniform(500, 1500) if i >= gap_point else np.random.uniform(100, 500)
            
            data.append({
                'timestamp': timestamp,
                'open': current_price,
                'high': high,
                'low': low,
                'close': current_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def _generate_news_shock_data(self, timestamps: pd.DatetimeIndex, 
                                symbol: str, intensity: float) -> pd.DataFrame:
        """Generate news shock scenario data"""
        
        # Similar to flash crash but with immediate recovery
        base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 0.5
        
        # Shock occurs at 50% through the period
        shock_point = len(timestamps) // 2
        shock_magnitude = 0.15 * intensity  # 15% shock at intensity 1.0
        shock_direction = 1 if np.random.random() > 0.5 else -1
        
        data = []
        current_price = base_price
        
        for i, timestamp in enumerate(timestamps):
            if i == shock_point:
                # Apply shock
                current_price *= (1 + shock_direction * shock_magnitude)
            
            # Gradual return to normal after shock
            if i > shock_point:
                reversion_rate = 0.001  # Gradual reversion
                target_price = base_price
                if current_price > target_price:
                    current_price *= (1 - reversion_rate)
                else:
                    current_price *= (1 + reversion_rate)
            
            # Normal noise
            change = np.random.normal(0, 0.001)
            current_price *= (1 + change)
            
            # OHLCV
            volatility = 0.005 if abs(i - shock_point) < 5 else 0.001
            high = current_price * (1 + abs(np.random.normal(0, volatility)))
            low = current_price * (1 - abs(np.random.normal(0, volatility)))
            volume = np.random.uniform(1000, 2000) if abs(i - shock_point) < 10 else np.random.uniform(100, 500)
            
            data.append({
                'timestamp': timestamp,
                'open': current_price,
                'high': high,
                'low': low,
                'close': current_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def _generate_correlation_breakdown_data(self, timestamps: pd.DatetimeIndex, 
                                           symbol: str, intensity: float) -> pd.DataFrame:
        """Generate correlation breakdown scenario data"""
        
        # Each symbol moves independently, breaking normal correlations
        base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 0.5
        
        # Random walk with higher volatility
        data = []
        current_price = base_price
        
        for timestamp in timestamps:
            # Higher volatility, more random movement
            volatility = 0.005 * intensity
            change = np.random.normal(0, volatility)
            current_price *= (1 + change)
            
            # OHLCV
            high = current_price * (1 + abs(np.random.normal(0, volatility)))
            low = current_price * (1 - abs(np.random.normal(0, volatility)))
            volume = np.random.uniform(200, 800)
            
            data.append({
                'timestamp': timestamp,
                'open': current_price,
                'high': high,
                'low': low,
                'close': current_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def _generate_normal_market_data(self, timestamps: pd.DatetimeIndex, 
                                   symbol: str) -> pd.DataFrame:
        """Generate normal market conditions for comparison"""
        
        base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 0.5
        
        data = []
        current_price = base_price
        
        for timestamp in timestamps:
            change = np.random.normal(0, 0.001)  # Normal 0.1% volatility
            current_price *= (1 + change)
            
            # OHLCV
            volatility = 0.001
            high = current_price * (1 + abs(np.random.normal(0, volatility)))
            low = current_price * (1 - abs(np.random.normal(0, volatility)))
            volume = np.random.uniform(100, 500)
            
            data.append({
                'timestamp': timestamp,
                'open': current_price,
                'high': high,
                'low': low,
                'close': current_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    async def run_stress_test(self, config: StressTestConfig) -> StressTestResult:
        """Run a comprehensive stress test"""
        
        logger.info(f"🧪 Running stress test: {config.scenario.value} (intensity: {config.intensity})")
        
        # Generate stress scenario data
        scenario_data = self.generate_stress_scenario_data(config)
        
        # Initialize trading system state
        initial_state = {
            'balance': config.initial_balance,
            'positions': {},
            'trades': [],
            'errors': [],
            'system_metrics': []
        }
        
        # Run trading system through stress scenario
        try:
            # Simulate trading through the stress scenario
            final_state = await self._simulate_trading_under_stress(
                scenario_data, initial_state, config
            )
            
            # Calculate performance metrics
            performance_metrics = self._calculate_stress_performance(initial_state, final_state)
            
            # Calculate risk metrics
            risk_metrics = self._calculate_stress_risk_metrics(final_state, scenario_data)
            
            # Assess system stability
            system_stability = self._assess_system_stability(final_state)
            
            # Generate recommendations
            recommendations = self._generate_stress_recommendations(
                config, performance_metrics, risk_metrics, system_stability
            )
            
            result = StressTestResult(
                scenario=config.scenario,
                config=config,
                performance_metrics=performance_metrics,
                risk_metrics=risk_metrics,
                trade_summary=self._summarize_trades(final_state['trades']),
                system_stability=system_stability,
                recommendations=recommendations
            )
            
            self.stress_results.append(result)
            
            logger.info(f"✅ Stress test completed: {config.scenario.value}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Stress test failed: {e}")
            raise
    
    async def _simulate_trading_under_stress(self, scenario_data: Dict[str, pd.DataFrame], 
                                           initial_state: Dict, config: StressTestConfig) -> Dict:
        """Simulate trading system behavior under stress conditions"""
        
        state = initial_state.copy()
        
        # Get all timestamps
        all_timestamps = set()
        for df in scenario_data.values():
            all_timestamps.update(df.index)
        
        timestamps = sorted(list(all_timestamps))
        
        for timestamp in timestamps:
            try:
                # Get current market data
                current_market = {}
                for symbol, df in scenario_data.items():
                    if timestamp in df.index:
                        current_market[symbol] = df.loc[timestamp].to_dict()
                
                # Simulate trading decision (simplified)
                decision = await self._simulate_trading_decision(current_market, state)
                
                if decision:
                    state['trades'].append({
                        'timestamp': timestamp,
                        'decision': decision,
                        'market_data': current_market
                    })
                
                # Update system metrics
                state['system_metrics'].append({
                    'timestamp': timestamp,
                    'balance': state['balance'],
                    'position_count': len(state['positions']),
                    'market_conditions': self._assess_market_conditions(current_market)
                })
                
            except Exception as e:
                state['errors'].append({
                    'timestamp': timestamp,
                    'error': str(e),
                    'error_type': type(e).__name__
                })
        
        return state
    
    async def _simulate_trading_decision(self, market_data: Dict, state: Dict) -> Optional[Dict]:
        """Simulate a trading decision under stress conditions"""
        
        # Simplified decision logic for stress testing
        if not market_data:
            return None
        
        # Random decision for simulation
        if np.random.random() < 0.1:  # 10% chance of trade
            symbol = np.random.choice(list(market_data.keys()))
            decision = np.random.choice(['BUY', 'SELL'])
            
            return {
                'symbol': symbol,
                'decision': decision,
                'confidence': np.random.uniform(50, 90),
                'price': market_data[symbol]['close']
            }
        
        return None
    
    def _assess_market_conditions(self, market_data: Dict) -> Dict:
        """Assess current market conditions"""
        
        if not market_data:
            return {'volatility': 0, 'volume': 0}
        
        # Simple volatility estimate
        volatilities = []
        volumes = []
        
        for symbol, data in market_data.items():
            if 'high' in data and 'low' in data and 'close' in data:
                volatility = (data['high'] - data['low']) / data['close']
                volatilities.append(volatility)
            
            if 'volume' in data:
                volumes.append(data['volume'])
        
        return {
            'avg_volatility': np.mean(volatilities) if volatilities else 0,
            'avg_volume': np.mean(volumes) if volumes else 0
        }
    
    def _calculate_stress_performance(self, initial_state: Dict, final_state: Dict) -> Dict:
        """Calculate performance metrics under stress"""
        
        return {
            'total_return': (final_state['balance'] - initial_state['balance']) / initial_state['balance'],
            'total_trades': len(final_state['trades']),
            'error_count': len(final_state['errors']),
            'max_drawdown': 0.0,  # Simplified
            'volatility': 0.0,    # Simplified
            'final_balance': final_state['balance']
        }
    
    def _calculate_stress_risk_metrics(self, final_state: Dict, scenario_data: Dict) -> Dict:
        """Calculate risk metrics under stress"""
        
        return {
            'max_position_exposure': 0.0,  # Simplified
            'correlation_risk': 0.0,       # Simplified
            'liquidity_risk': 0.0,         # Simplified
            'system_risk': len(final_state['errors']) / max(len(final_state['trades']), 1)
        }
    
    def _assess_system_stability(self, final_state: Dict) -> Dict:
        """Assess system stability under stress"""
        
        return {
            'error_rate': len(final_state['errors']) / max(len(final_state['system_metrics']), 1),
            'system_uptime': 1.0 - (len(final_state['errors']) / max(len(final_state['system_metrics']), 1)),
            'performance_degradation': 0.0,  # Simplified
            'recovery_time': 0.0             # Simplified
        }
    
    def _summarize_trades(self, trades: List[Dict]) -> Dict:
        """Summarize trading activity"""
        
        if not trades:
            return {'total': 0, 'buy_count': 0, 'sell_count': 0}
        
        buy_count = len([t for t in trades if t['decision']['decision'] == 'BUY'])
        sell_count = len([t for t in trades if t['decision']['decision'] == 'SELL'])
        
        return {
            'total': len(trades),
            'buy_count': buy_count,
            'sell_count': sell_count,
            'avg_confidence': np.mean([t['decision']['confidence'] for t in trades])
        }
    
    def _generate_stress_recommendations(self, config: StressTestConfig, 
                                       performance: Dict, risk: Dict, 
                                       stability: Dict) -> List[str]:
        """Generate recommendations based on stress test results"""
        
        recommendations = []
        
        if performance['error_count'] > 0:
            recommendations.append("Address system errors to improve reliability under stress")
        
        if performance['total_return'] < -0.10:
            recommendations.append("Implement stronger risk controls to limit losses during extreme events")
        
        if stability['error_rate'] > 0.05:
            recommendations.append("Improve error handling and system resilience")
        
        if risk['system_risk'] > 0.1:
            recommendations.append("Enhance system monitoring and circuit breakers")
        
        if config.scenario == StressScenario.FLASH_CRASH and performance['total_return'] < -0.05:
            recommendations.append("Implement flash crash protection mechanisms")
        
        if config.scenario == StressScenario.LIQUIDITY_CRISIS and performance['total_trades'] == 0:
            recommendations.append("Develop alternative liquidity sources and execution strategies")
        
        return recommendations
