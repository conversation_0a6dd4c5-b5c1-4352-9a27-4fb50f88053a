#!/usr/bin/env python3
"""
Comprehensive Error Handling System for Epinnox v6
Provides graceful degradation, health checks, and automatic recovery mechanisms
"""

import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ComponentStatus(Enum):
    """Component health status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILED = "failed"
    RECOVERING = "recovering"
    UNKNOWN = "unknown"

@dataclass
class HealthCheck:
    """Health check configuration"""
    name: str
    check_function: Callable
    interval_seconds: int = 30
    timeout_seconds: int = 10
    max_failures: int = 3
    recovery_function: Optional[Callable] = None

@dataclass
class ComponentHealth:
    """Component health status"""
    name: str
    status: ComponentStatus = ComponentStatus.UNKNOWN
    last_check: Optional[datetime] = None
    failure_count: int = 0
    last_error: Optional[str] = None
    uptime_start: Optional[datetime] = None

class ErrorHandlingSystem:
    """
    Comprehensive error handling and health monitoring system
    """
    
    def __init__(self):
        """Initialize error handling system"""
        self.components: Dict[str, ComponentHealth] = {}
        self.health_checks: Dict[str, HealthCheck] = {}
        self.error_history: List[Dict] = []
        self.recovery_attempts: Dict[str, int] = {}
        
        # Threading
        self.health_check_thread = None
        self.health_check_running = False
        self.health_check_lock = threading.Lock()
        
        # Error handling configuration
        self.max_error_history = 1000
        self.max_recovery_attempts = 3
        self.recovery_cooldown_minutes = 5
        
        logger.info("🛡️ Error Handling System initialized")
    
    def register_component(self, name: str, health_check: HealthCheck):
        """Register a component for health monitoring"""
        try:
            with self.health_check_lock:
                self.components[name] = ComponentHealth(
                    name=name,
                    uptime_start=datetime.now()
                )
                self.health_checks[name] = health_check
                
            logger.info(f"📊 Component registered: {name}")
            
        except Exception as e:
            logger.error(f"Error registering component {name}: {e}")
    
    def start_health_monitoring(self):
        """Start background health monitoring"""
        try:
            if self.health_check_running:
                logger.warning("Health monitoring already running")
                return
            
            self.health_check_running = True
            self.health_check_thread = threading.Thread(
                target=self._health_check_loop,
                daemon=True,
                name="HealthCheckThread"
            )
            self.health_check_thread.start()
            
            logger.info("🔍 Health monitoring started")
            
        except Exception as e:
            logger.error(f"Error starting health monitoring: {e}")
    
    def stop_health_monitoring(self):
        """Stop background health monitoring"""
        try:
            self.health_check_running = False
            if self.health_check_thread and self.health_check_thread.is_alive():
                self.health_check_thread.join(timeout=5)
            
            logger.info("🔍 Health monitoring stopped")
            
        except Exception as e:
            logger.error(f"Error stopping health monitoring: {e}")
    
    def handle_error(self, component_name: str, error: Exception, 
                    context: Optional[Dict] = None) -> bool:
        """
        Handle an error with automatic recovery attempts
        Returns True if error was handled successfully
        """
        try:
            error_data = {
                'component': component_name,
                'error': str(error),
                'error_type': type(error).__name__,
                'timestamp': datetime.now(),
                'context': context or {}
            }
            
            # Add to error history
            self.error_history.append(error_data)
            if len(self.error_history) > self.max_error_history:
                self.error_history.pop(0)
            
            # Update component status
            if component_name in self.components:
                component = self.components[component_name]
                component.failure_count += 1
                component.last_error = str(error)
                component.status = ComponentStatus.FAILED
            
            # Log error
            logger.error(f"🚨 Error in {component_name}: {error}")
            
            # Attempt recovery
            return self._attempt_recovery(component_name, error)
            
        except Exception as e:
            logger.error(f"Error in error handler: {e}")
            return False
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        try:
            with self.health_check_lock:
                total_components = len(self.components)
                healthy_components = sum(
                    1 for comp in self.components.values() 
                    if comp.status == ComponentStatus.HEALTHY
                )
                
                failed_components = [
                    comp.name for comp in self.components.values()
                    if comp.status == ComponentStatus.FAILED
                ]
                
                recent_errors = [
                    error for error in self.error_history
                    if error['timestamp'] > datetime.now() - timedelta(minutes=10)
                ]
                
                return {
                    'overall_status': self._calculate_overall_status(),
                    'total_components': total_components,
                    'healthy_components': healthy_components,
                    'failed_components': failed_components,
                    'recent_errors_count': len(recent_errors),
                    'components': {
                        name: {
                            'status': comp.status.value,
                            'last_check': comp.last_check.isoformat() if comp.last_check else None,
                            'failure_count': comp.failure_count,
                            'last_error': comp.last_error,
                            'uptime_hours': (datetime.now() - comp.uptime_start).total_seconds() / 3600 if comp.uptime_start else 0
                        }
                        for name, comp in self.components.items()
                    }
                }
                
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {'overall_status': 'error', 'error': str(e)}
    
    def force_component_recovery(self, component_name: str) -> bool:
        """Force recovery attempt for a specific component"""
        try:
            if component_name not in self.components:
                logger.error(f"Component {component_name} not found")
                return False
            
            logger.info(f"🔧 Forcing recovery for {component_name}")
            return self._attempt_recovery(component_name, Exception("Manual recovery"))
            
        except Exception as e:
            logger.error(f"Error forcing recovery for {component_name}: {e}")
            return False
    
    def _health_check_loop(self):
        """Background health check loop"""
        while self.health_check_running:
            try:
                for name, health_check in self.health_checks.items():
                    if not self.health_check_running:
                        break
                    
                    try:
                        self._perform_health_check(name, health_check)
                    except Exception as e:
                        logger.error(f"Error in health check for {name}: {e}")
                
                # Sleep for the shortest interval
                min_interval = min(
                    (hc.interval_seconds for hc in self.health_checks.values()),
                    default=30
                )
                time.sleep(min_interval)
                
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                time.sleep(30)  # Fallback sleep
    
    def _perform_health_check(self, name: str, health_check: HealthCheck):
        """Perform health check for a component"""
        try:
            component = self.components[name]
            
            # Check if it's time for this health check
            if (component.last_check and 
                datetime.now() - component.last_check < timedelta(seconds=health_check.interval_seconds)):
                return
            
            # Perform the health check
            start_time = time.time()
            try:
                result = health_check.check_function()
                check_time = time.time() - start_time
                
                if check_time > health_check.timeout_seconds:
                    raise TimeoutError(f"Health check timeout ({check_time:.1f}s > {health_check.timeout_seconds}s)")
                
                # Health check passed
                component.status = ComponentStatus.HEALTHY
                component.failure_count = 0
                component.last_error = None
                
            except Exception as e:
                # Health check failed
                component.failure_count += 1
                component.last_error = str(e)
                
                if component.failure_count >= health_check.max_failures:
                    component.status = ComponentStatus.FAILED
                    # Attempt recovery
                    self._attempt_recovery(name, e)
                else:
                    component.status = ComponentStatus.DEGRADED
            
            component.last_check = datetime.now()
            
        except Exception as e:
            logger.error(f"Error performing health check for {name}: {e}")
    
    def _attempt_recovery(self, component_name: str, error: Exception) -> bool:
        """Attempt to recover a failed component"""
        try:
            # Check recovery cooldown
            last_attempt = self.recovery_attempts.get(component_name, 0)
            if time.time() - last_attempt < self.recovery_cooldown_minutes * 60:
                logger.info(f"Recovery cooldown active for {component_name}")
                return False
            
            # Check max recovery attempts
            attempts_key = f"{component_name}_attempts"
            attempts = self.recovery_attempts.get(attempts_key, 0)
            if attempts >= self.max_recovery_attempts:
                logger.error(f"Max recovery attempts reached for {component_name}")
                return False
            
            # Attempt recovery
            health_check = self.health_checks.get(component_name)
            if health_check and health_check.recovery_function:
                logger.info(f"🔧 Attempting recovery for {component_name}")
                
                component = self.components[component_name]
                component.status = ComponentStatus.RECOVERING
                
                try:
                    recovery_result = health_check.recovery_function()
                    
                    if recovery_result:
                        logger.info(f"✅ Recovery successful for {component_name}")
                        component.status = ComponentStatus.HEALTHY
                        component.failure_count = 0
                        component.last_error = None
                        
                        # Reset recovery attempts
                        self.recovery_attempts[attempts_key] = 0
                        return True
                    else:
                        logger.error(f"❌ Recovery failed for {component_name}")
                        component.status = ComponentStatus.FAILED
                        
                except Exception as recovery_error:
                    logger.error(f"❌ Recovery error for {component_name}: {recovery_error}")
                    component.status = ComponentStatus.FAILED
            
            # Update recovery tracking
            self.recovery_attempts[component_name] = time.time()
            self.recovery_attempts[attempts_key] = attempts + 1
            
            return False
            
        except Exception as e:
            logger.error(f"Error in recovery attempt for {component_name}: {e}")
            return False
    
    def _calculate_overall_status(self) -> str:
        """Calculate overall system status"""
        try:
            if not self.components:
                return "unknown"
            
            statuses = [comp.status for comp in self.components.values()]
            
            if all(status == ComponentStatus.HEALTHY for status in statuses):
                return "healthy"
            elif any(status == ComponentStatus.FAILED for status in statuses):
                return "degraded"
            elif any(status == ComponentStatus.DEGRADED for status in statuses):
                return "warning"
            else:
                return "unknown"
                
        except Exception as e:
            logger.error(f"Error calculating overall status: {e}")
            return "error"
