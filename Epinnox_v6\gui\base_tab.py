"""
Base Tab Class for Epinnox v6 GUI Components
Provides common functionality and Matrix theme styling for all tabs
"""

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
except ImportError:
    print("PyQt5 not installed. Install with: pip install PyQt5")
    raise

from .matrix_theme import MatrixTheme
import logging

logger = logging.getLogger(__name__)

class BaseTab(QWidget):
    """Base class for all tab components with common Matrix theme functionality"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setup_ui()
        
    def setup_ui(self):
        """Override this method in subclasses to setup the UI"""
        pass
    
    def apply_matrix_table_styling(self, table_widget):
        """Apply consistent Matrix theme styling to a QTableWidget"""
        table_widget.verticalHeader().setVisible(False)
        table_widget.setStyleSheet(MatrixTheme.get_table_stylesheet())
        
        # Configure header resize behavior
        header = table_widget.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        header.setStretchLastSection(False)
        
        return table_widget
    
    def create_matrix_button(self, text, callback=None, enabled=True):
        """Create a button with Matrix theme styling"""
        button = QPushButton(text)
        if callback:
            button.clicked.connect(callback)
        button.setEnabled(enabled)
        return button
    
    def create_matrix_label(self, text, font_size=None, color=None):
        """Create a label with Matrix theme styling"""
        label = QLabel(text)
        if font_size:
            label.setStyleSheet(f"""
                font-size: {font_size}px;
                font-weight: bold;
                color: {color or MatrixTheme.GREEN};
            """)
        return label
    
    def create_matrix_group_box(self, title):
        """Create a group box with Matrix theme styling"""
        group_box = QGroupBox(title)
        return group_box
    
    def log_message(self, message, level=logging.INFO):
        """Log a message with the tab name"""
        logger.log(level, f"[{self.__class__.__name__}] {message}")
    
    def show_error_message(self, title, message):
        """Show an error message dialog with Matrix theme"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {MatrixTheme.BLACK};
                color: {MatrixTheme.GREEN};
            }}
            QMessageBox QPushButton {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                border: 1px solid {MatrixTheme.MID_GREEN};
                padding: 6px 12px;
                min-width: 60px;
            }}
        """)
        msg_box.exec_()
    
    def show_info_message(self, title, message):
        """Show an info message dialog with Matrix theme"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {MatrixTheme.BLACK};
                color: {MatrixTheme.GREEN};
            }}
            QMessageBox QPushButton {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                border: 1px solid {MatrixTheme.MID_GREEN};
                padding: 6px 12px;
                min-width: 60px;
            }}
        """)
        msg_box.exec_()
    
    def get_main_window(self):
        """Get reference to the main window"""
        return self.parent_window
