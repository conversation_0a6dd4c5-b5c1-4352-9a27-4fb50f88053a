#!/usr/bin/env python3
"""
Comprehensive Test Runner for EPINNOX v6
Automated testing suite for all components
"""

import os
import sys
import subprocess
import argparse
import time
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestRunner:
    """Automated test runner for EPINNOX v6"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        
    def run_unit_tests(self, verbose=False):
        """Run all unit tests"""
        logger.info("🧪 Running Unit Tests...")
        
        try:
            cmd = ['python', '-m', 'pytest', 'tests/', '-v'] if verbose else ['python', '-m', 'pytest', 'tests/']
            if not verbose:
                cmd.extend(['-q', '--tb=short'])
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
            
            self.test_results['unit_tests'] = {
                'status': 'PASSED' if result.returncode == 0 else 'FAILED',
                'output': result.stdout,
                'errors': result.stderr,
                'return_code': result.returncode
            }
            
            if result.returncode == 0:
                logger.info("✅ Unit tests PASSED")
            else:
                logger.error("❌ Unit tests FAILED")
                if verbose:
                    print(result.stdout)
                    print(result.stderr)
                    
        except Exception as e:
            logger.error(f"❌ Error running unit tests: {e}")
            self.test_results['unit_tests'] = {
                'status': 'ERROR',
                'error': str(e)
            }
    
    def run_integration_tests(self, verbose=False):
        """Run integration tests"""
        logger.info("🔗 Running Integration Tests...")
        
        try:
            cmd = ['python', '-m', 'pytest', 'tests/test_autonomous_integration.py', '-v'] if verbose else ['python', '-m', 'pytest', 'tests/test_autonomous_integration.py', '-q']
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
            
            self.test_results['integration_tests'] = {
                'status': 'PASSED' if result.returncode == 0 else 'FAILED',
                'output': result.stdout,
                'errors': result.stderr,
                'return_code': result.returncode
            }
            
            if result.returncode == 0:
                logger.info("✅ Integration tests PASSED")
            else:
                logger.error("❌ Integration tests FAILED")
                if verbose:
                    print(result.stdout)
                    print(result.stderr)
                    
        except Exception as e:
            logger.error(f"❌ Error running integration tests: {e}")
            self.test_results['integration_tests'] = {
                'status': 'ERROR',
                'error': str(e)
            }
    
    def run_import_tests(self):
        """Test that all modules can be imported"""
        logger.info("📦 Running Import Tests...")
        
        modules_to_test = [
            'execution.autonomous_executor',
            'portfolio.portfolio_manager',
            'monitoring.performance_tracker',
            'ml.rl_agent',
            'ml.trading_env',
            'ml.adaptive_updater',
            'core.autonomous_controller'
        ]
        
        failed_imports = []
        
        for module in modules_to_test:
            try:
                __import__(module)
                logger.info(f"✅ {module} imported successfully")
            except Exception as e:
                logger.error(f"❌ Failed to import {module}: {e}")
                failed_imports.append((module, str(e)))
        
        self.test_results['import_tests'] = {
            'status': 'PASSED' if not failed_imports else 'FAILED',
            'failed_imports': failed_imports,
            'total_modules': len(modules_to_test),
            'failed_count': len(failed_imports)
        }
        
        if not failed_imports:
            logger.info("✅ All import tests PASSED")
        else:
            logger.error(f"❌ {len(failed_imports)} import tests FAILED")
    
    def run_mock_exchange_test(self):
        """Test mock exchange functionality"""
        logger.info("🏦 Testing Mock Exchange...")
        
        try:
            from tests.mocks.mock_exchange import MockExchange
            
            exchange = MockExchange()
            
            # Test basic functionality
            balance = exchange.fetch_balance()
            assert 'USDT' in balance
            
            # Test order creation
            order = exchange.create_order('BTC/USDT', 'market', 'buy', 0.1)
            assert order['status'] == 'closed'
            
            # Test ticker
            ticker = exchange.fetch_ticker('BTC/USDT')
            assert 'last' in ticker
            
            self.test_results['mock_exchange'] = {
                'status': 'PASSED',
                'tests_run': ['balance', 'order_creation', 'ticker']
            }
            
            logger.info("✅ Mock exchange tests PASSED")
            
        except Exception as e:
            logger.error(f"❌ Mock exchange test FAILED: {e}")
            self.test_results['mock_exchange'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    def run_autonomous_system_test(self):
        """Test autonomous system components"""
        logger.info("🤖 Testing Autonomous System Components...")
        
        try:
            from execution.autonomous_executor import AutonomousTradeExecutor
            from portfolio.portfolio_manager import PortfolioManager
            from monitoring.performance_tracker import PerformanceTracker
            from tests.mocks.mock_exchange import MockExchange
            from unittest.mock import Mock
            
            # Test component initialization
            mock_exchange = MockExchange()
            executor = AutonomousTradeExecutor(mock_exchange, Mock(), 0.65)
            portfolio = PortfolioManager(1000.0, 3)
            tracker = PerformanceTracker("./test_performance.db")
            
            # Test basic functionality
            assert executor.min_confidence == 0.65
            assert portfolio.initial_balance == 1000.0
            assert tracker.db_path == "./test_performance.db"
            
            self.test_results['autonomous_system'] = {
                'status': 'PASSED',
                'components_tested': ['executor', 'portfolio', 'tracker']
            }
            
            logger.info("✅ Autonomous system tests PASSED")
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"❌ Autonomous system test FAILED: {e}")
            logger.error(f"Full traceback: {error_details}")
            self.test_results['autonomous_system'] = {
                'status': 'FAILED',
                'error': str(e),
                'traceback': error_details
            }
    
    def run_all_tests(self, verbose=False, quick=False):
        """Run all test suites"""
        self.start_time = time.time()
        
        logger.info("🚀 Starting Comprehensive Test Suite for EPINNOX v6")
        logger.info("=" * 60)
        
        # Run tests in order
        self.run_import_tests()
        self.run_mock_exchange_test()
        self.run_autonomous_system_test()
        
        if not quick:
            self.run_unit_tests(verbose)
            self.run_integration_tests(verbose)
        
        # Generate summary
        self.generate_summary()
    
    def generate_summary(self):
        """Generate test summary report"""
        end_time = time.time()
        duration = end_time - self.start_time if self.start_time else 0
        
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY REPORT")
        print("=" * 60)
        print(f"Test Run Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total Duration: {duration:.2f} seconds")
        print("-" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASSED')
        failed_tests = total_tests - passed_tests
        
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result['status'] == 'PASSED' else "❌"
            print(f"{status_icon} {test_name.replace('_', ' ').title()}: {result['status']}")
            
            if result['status'] == 'FAILED' and 'error' in result:
                print(f"   Error: {result['error']}")
        
        print("-" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED! System is ready for deployment.")
        else:
            print(f"\n⚠️  {failed_tests} test(s) failed. Please review and fix issues.")
        
        print("=" * 60)
    
    def save_report(self, filename=None):
        """Save test report to file"""
        if filename is None:
            filename = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        import json
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'duration': time.time() - self.start_time if self.start_time else 0,
            'results': self.test_results
        }
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📄 Test report saved to {filename}")

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description='EPINNOX v6 Test Runner')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--quick', '-q', action='store_true', help='Quick tests only (skip unit/integration)')
    parser.add_argument('--unit-only', action='store_true', help='Run unit tests only')
    parser.add_argument('--integration-only', action='store_true', help='Run integration tests only')
    parser.add_argument('--save-report', action='store_true', help='Save test report to file')
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.unit_only:
        runner.run_unit_tests(args.verbose)
    elif args.integration_only:
        runner.run_integration_tests(args.verbose)
    else:
        runner.run_all_tests(args.verbose, args.quick)
    
    if args.save_report:
        runner.save_report()

if __name__ == "__main__":
    main()
