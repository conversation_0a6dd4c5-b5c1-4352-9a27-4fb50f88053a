#!/usr/bin/env python3
"""Test NLP imports"""

print("Testing NLP module imports...")

try:
    import nltk
    print("✅ NLTK available")
except ImportError:
    print("❌ NLTK not available")

try:
    import textblob
    print("✅ TextBlob available")
except ImportError:
    print("❌ TextBlob not available")

try:
    import transformers
    print("✅ Transformers available")
except ImportError:
    print("❌ Transformers not available")

try:
    import aiohttp
    print("✅ aiohttp available")
except ImportError:
    print("❌ aiohttp not available")

try:
    import requests
    print("✅ requests available")
except ImportError:
    print("❌ requests not available")

try:
    import bs4
    print("✅ BeautifulSoup available")
except ImportError:
    print("❌ BeautifulSoup not available")

try:
    import feedparser
    print("✅ feedparser available")
except ImportError:
    print("❌ feedparser not available")

print("\nTesting NLP module imports...")
try:
    from nlp.sentiment_analyzer import SentimentAnalyzer
    print("✅ SentimentAnalyzer import successful")
except ImportError as e:
    print(f"❌ SentimentAnalyzer import failed: {e}")

try:
    from nlp.news_scraper import NewsScraperManager
    print("✅ NewsScraperManager import successful")
except ImportError as e:
    print(f"❌ NewsScraperManager import failed: {e}")

try:
    from nlp.social_monitor import SocialMediaMonitor
    print("✅ SocialMediaMonitor import successful")
except ImportError as e:
    print(f"❌ SocialMediaMonitor import failed: {e}")

try:
    from nlp.market_sentiment import MarketSentimentAggregator
    print("✅ MarketSentimentAggregator import successful")
except ImportError as e:
    print(f"❌ MarketSentimentAggregator import failed: {e}")

print("\nTesting basic sentiment analysis...")
try:
    analyzer = SentimentAnalyzer(use_transformer=False)
    result = analyzer.analyze_sentiment("DOGE is going to the moon! 🚀")
    print(f"✅ Basic sentiment test: {result.label.value} (confidence: {result.confidence:.2%})")
except Exception as e:
    print(f"❌ Basic sentiment test failed: {e}")

print("\nNLP testing complete!")
