#!/usr/bin/env python3
"""
Dynamic Symbol Scanner for Epinnox Trading System
Automatically selects the best tradable assets for scalping based on real-time metrics
"""

import time
import logging
import statistics
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from collections import defaultdict, deque

@dataclass
class SymbolMetrics:
    """Container for symbol trading metrics"""
    symbol: str
    spread: float
    spread_pct: float
    tick_atr: float
    flow_imbalance: float
    orderbook_depth: float
    volume_24h: float
    price: float
    timestamp: float
    score: float = 0.0

class SymbolScanner:
    """
    Dynamic symbol scanner that evaluates and ranks trading symbols
    based on real-time market metrics for optimal scalping opportunities
    """
    
    def __init__(self, market_api, symbols: List[str], metrics_weights: Dict[str, float] = None):
        """
        Initialize the symbol scanner
        
        Args:
            market_api: Market data API instance (ccxt exchange or similar)
            symbols: List of symbols to scan (e.g., ['DOGE/USDT:USDT', 'BTC/USDT:USDT'])
            metrics_weights: Dictionary of metric weights for scoring
        """
        self.market_api = market_api
        self.symbols = symbols
        self.logger = logging.getLogger(__name__)
        
        # Default metrics weights (higher = better for scalping)
        self.metrics_weights = metrics_weights or {
            'spread_score': 0.25,      # Lower spread is better
            'tick_atr_score': 0.20,    # Higher ATR is better for scalping
            'flow_score': 0.15,        # Balanced flow is better
            'depth_score': 0.20,       # Higher depth is better
            'volume_score': 0.20       # Higher volume is better
        }
        
        # Normalization ranges (will be updated dynamically)
        self.normalization_ranges = {
            'spread_pct': {'min': 0.001, 'max': 0.1},
            'tick_atr': {'min': 0.00001, 'max': 0.001},
            'flow_imbalance': {'min': -50.0, 'max': 50.0},
            'orderbook_depth': {'min': 1000, 'max': 100000},
            'volume_24h': {'min': 1000000, 'max': 1000000000}
        }

        # Performance optimization flags
        self.fast_mode = False  # Enable for sub-second scanning
        self.api_call_count = 0
        self.last_api_reset = time.time()
        
        # Rolling windows for dynamic normalization
        self.metric_history = defaultdict(lambda: deque(maxlen=100))
        self.last_scan_time = 0
        self.scan_interval = 5.0  # seconds
        self.cached_metrics = {}
        
        self.logger.info(f"SymbolScanner initialized with {len(symbols)} symbols")
    
    def fetch_metrics(self, symbol: str) -> Optional[SymbolMetrics]:
        """
        Fetch comprehensive trading metrics for a symbol
        
        Args:
            symbol: Trading symbol (e.g., 'DOGE/USDT:USDT')
            
        Returns:
            SymbolMetrics object or None if fetch fails
        """
        try:
            current_time = time.time()
            
            # Get current price and orderbook
            ticker = self.market_api.fetch_ticker(symbol)
            current_price = ticker['last']
            
            # Get orderbook depth (top 5 levels)
            orderbook = self.market_api.fetch_order_book(symbol, limit=5)
            bids = orderbook['bids'][:5]
            asks = orderbook['asks'][:5]
            
            if not bids or not asks:
                self.logger.warning(f"No orderbook data for {symbol}")
                return None
            
            # Calculate spread
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            spread = best_ask - best_bid
            spread_pct = (spread / current_price) * 100 if current_price > 0 else 0
            
            # Calculate orderbook depth (sum of top 5 volumes)
            bid_depth = sum(bid[1] for bid in bids)
            ask_depth = sum(ask[1] for ask in asks)
            orderbook_depth = bid_depth + ask_depth
            
            # Get recent trades for tick analysis
            try:
                trades = self.market_api.fetch_trades(symbol, limit=50)
                tick_atr = self._calculate_tick_atr(trades)
                flow_imbalance = self._calculate_flow_imbalance(trades)
            except Exception as e:
                self.logger.warning(f"Could not fetch trades for {symbol}: {e}")
                tick_atr = 0.0
                flow_imbalance = 0.0
            
            # Get 24h volume
            volume_24h = ticker.get('quoteVolume', 0) or ticker.get('baseVolume', 0)
            
            # Create metrics object
            metrics = SymbolMetrics(
                symbol=symbol,
                spread=spread,
                spread_pct=spread_pct,
                tick_atr=tick_atr,
                flow_imbalance=flow_imbalance,
                orderbook_depth=orderbook_depth,
                volume_24h=volume_24h,
                price=current_price,
                timestamp=current_time
            )
            
            # Update metric history for dynamic normalization
            self._update_metric_history(metrics)
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error fetching metrics for {symbol}: {e}")
            return None
    
    def _calculate_tick_atr(self, trades: List[Dict]) -> float:
        """Calculate tick-based Average True Range"""
        if len(trades) < 2:
            return 0.0
        
        try:
            prices = [float(trade['price']) for trade in trades[-20:]]  # Last 20 trades
            if len(prices) < 2:
                return 0.0
            
            # Calculate price changes
            price_changes = [abs(prices[i] - prices[i-1]) for i in range(1, len(prices))]
            return statistics.mean(price_changes) if price_changes else 0.0
            
        except Exception as e:
            self.logger.warning(f"Error calculating tick ATR: {e}")
            return 0.0
    
    def _calculate_flow_imbalance(self, trades: List[Dict]) -> float:
        """Calculate trade flow imbalance (buy vs sell pressure)"""
        if not trades:
            return 0.0
        
        try:
            buy_volume = 0.0
            sell_volume = 0.0
            
            for trade in trades[-30:]:  # Last 30 trades
                volume = float(trade['amount'])
                side = trade.get('side', 'unknown')
                
                if side == 'buy':
                    buy_volume += volume
                elif side == 'sell':
                    sell_volume += volume
                else:
                    # If side is unknown, use price movement heuristic
                    # This is a fallback for exchanges that don't provide side info
                    continue
            
            total_volume = buy_volume + sell_volume
            if total_volume == 0:
                return 0.0
            
            # Return imbalance as percentage (-100 to +100)
            imbalance = ((buy_volume - sell_volume) / total_volume) * 100
            return imbalance
            
        except Exception as e:
            self.logger.warning(f"Error calculating flow imbalance: {e}")
            return 0.0
    
    def _update_metric_history(self, metrics: SymbolMetrics):
        """Update rolling history for dynamic normalization"""
        self.metric_history['spread_pct'].append(metrics.spread_pct)
        self.metric_history['tick_atr'].append(metrics.tick_atr)
        self.metric_history['flow_imbalance'].append(metrics.flow_imbalance)
        self.metric_history['orderbook_depth'].append(metrics.orderbook_depth)
        self.metric_history['volume_24h'].append(metrics.volume_24h)
    
    def _normalize_metric(self, value: float, metric_name: str) -> float:
        """
        Normalize a metric value to 0-1 range using dynamic or static ranges
        
        Args:
            value: Raw metric value
            metric_name: Name of the metric for range lookup
            
        Returns:
            Normalized value between 0 and 1
        """
        try:
            # Use dynamic range if we have enough history
            if len(self.metric_history[metric_name]) >= 10:
                history = list(self.metric_history[metric_name])
                min_val = min(history)
                max_val = max(history)
                
                # Add some padding to avoid edge cases
                range_padding = (max_val - min_val) * 0.1
                min_val -= range_padding
                max_val += range_padding
            else:
                # Use static ranges as fallback
                range_config = self.normalization_ranges.get(metric_name, {'min': 0, 'max': 1})
                min_val = range_config['min']
                max_val = range_config['max']
            
            # Normalize to 0-1 range
            if max_val == min_val:
                return 0.5  # Default to middle if no range
            
            normalized = (value - min_val) / (max_val - min_val)
            return max(0.0, min(1.0, normalized))  # Clamp to 0-1
            
        except Exception as e:
            self.logger.warning(f"Error normalizing {metric_name}: {e}")
            return 0.5  # Default to middle value
    
    def score_symbol(self, metrics: SymbolMetrics) -> float:
        """
        Calculate a composite score for a symbol based on its metrics
        
        Args:
            metrics: SymbolMetrics object
            
        Returns:
            Composite score (0-100, higher is better for scalping)
        """
        try:
            # Normalize individual metrics
            spread_norm = self._normalize_metric(metrics.spread_pct, 'spread_pct')
            tick_atr_norm = self._normalize_metric(metrics.tick_atr, 'tick_atr')
            flow_norm = self._normalize_metric(abs(metrics.flow_imbalance), 'flow_imbalance')
            depth_norm = self._normalize_metric(metrics.orderbook_depth, 'orderbook_depth')
            volume_norm = self._normalize_metric(metrics.volume_24h, 'volume_24h')
            
            # Convert to scores (some metrics are inverted for scalping preference)
            spread_score = (1.0 - spread_norm)  # Lower spread is better
            tick_atr_score = tick_atr_norm      # Higher ATR is better
            flow_score = (1.0 - flow_norm)     # Balanced flow is better (less extreme)
            depth_score = depth_norm           # Higher depth is better
            volume_score = volume_norm         # Higher volume is better
            
            # Calculate weighted composite score
            composite_score = (
                spread_score * self.metrics_weights['spread_score'] +
                tick_atr_score * self.metrics_weights['tick_atr_score'] +
                flow_score * self.metrics_weights['flow_score'] +
                depth_score * self.metrics_weights['depth_score'] +
                volume_score * self.metrics_weights['volume_score']
            )
            
            # Scale to 0-100 range
            final_score = composite_score * 100
            
            self.logger.debug(f"Scored {metrics.symbol}: {final_score:.2f} "
                            f"(spread:{spread_score:.2f}, atr:{tick_atr_score:.2f}, "
                            f"flow:{flow_score:.2f}, depth:{depth_score:.2f}, vol:{volume_score:.2f})")
            
            return final_score
            
        except Exception as e:
            self.logger.error(f"Error scoring symbol {metrics.symbol}: {e}")
            return 0.0

    def find_best(self, n: int = 1) -> List[str]:
        """
        Find the best n symbols for trading based on current metrics

        Args:
            n: Number of top symbols to return (default 1)

        Returns:
            List of symbol names sorted by score (best first)
        """
        try:
            current_time = time.time()

            # Check if we need to scan (rate limiting)
            if current_time - self.last_scan_time < self.scan_interval:
                # Return cached results if available
                if self.cached_metrics:
                    sorted_symbols = sorted(
                        self.cached_metrics.items(),
                        key=lambda x: x[1].score,
                        reverse=True
                    )
                    return [symbol for symbol, _ in sorted_symbols[:n]]

            self.logger.info(f"Scanning {len(self.symbols)} symbols for best trading opportunities...")

            # Fetch metrics for all symbols
            symbol_metrics = {}
            for symbol in self.symbols:
                metrics = self.fetch_metrics(symbol)
                if metrics:
                    metrics.score = self.score_symbol(metrics)
                    symbol_metrics[symbol] = metrics
                else:
                    self.logger.warning(f"Failed to fetch metrics for {symbol}")

            # Cache results
            self.cached_metrics = symbol_metrics
            self.last_scan_time = current_time

            # Sort by score (highest first)
            sorted_symbols = sorted(
                symbol_metrics.items(),
                key=lambda x: x[1].score,
                reverse=True
            )

            # Log results
            self.logger.info(f"Symbol ranking (top {min(n, len(sorted_symbols))}):")
            for i, (symbol, metrics) in enumerate(sorted_symbols[:n]):
                self.logger.info(f"  {i+1}. {symbol}: {metrics.score:.2f} "
                               f"(spread: {metrics.spread_pct:.3f}%, "
                               f"atr: {metrics.tick_atr:.6f}, "
                               f"depth: {metrics.orderbook_depth:.0f})")

            return [symbol for symbol, _ in sorted_symbols[:n]]

        except Exception as e:
            self.logger.error(f"Error finding best symbols: {e}")
            return self.symbols[:n] if self.symbols else []

    def get_symbol_metrics(self, symbol: str) -> Optional[SymbolMetrics]:
        """
        Get cached metrics for a specific symbol

        Args:
            symbol: Symbol name

        Returns:
            SymbolMetrics object or None if not available
        """
        return self.cached_metrics.get(symbol)

    def get_all_metrics(self) -> Dict[str, SymbolMetrics]:
        """
        Get all cached symbol metrics

        Returns:
            Dictionary of symbol -> SymbolMetrics
        """
        return self.cached_metrics.copy()

    def update_weights(self, new_weights: Dict[str, float]):
        """
        Update the metrics weights for scoring

        Args:
            new_weights: Dictionary of new weights
        """
        self.metrics_weights.update(new_weights)
        self.logger.info(f"Updated metrics weights: {self.metrics_weights}")

        # Invalidate cache to force re-scoring
        self.cached_metrics = {}

    def add_symbol(self, symbol: str):
        """Add a new symbol to the scanning list"""
        if symbol not in self.symbols:
            self.symbols.append(symbol)
            self.logger.info(f"Added symbol {symbol} to scanner")

    def remove_symbol(self, symbol: str):
        """Remove a symbol from the scanning list"""
        if symbol in self.symbols:
            self.symbols.remove(symbol)
            if symbol in self.cached_metrics:
                del self.cached_metrics[symbol]
            self.logger.info(f"Removed symbol {symbol} from scanner")

    def get_scan_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the last scan results

        Returns:
            Dictionary with scan summary information
        """
        if not self.cached_metrics:
            return {"status": "no_data", "symbols_scanned": 0}

        metrics_list = list(self.cached_metrics.values())

        return {
            "status": "success",
            "symbols_scanned": len(metrics_list),
            "scan_time": self.last_scan_time,
            "best_symbol": max(metrics_list, key=lambda m: m.score).symbol if metrics_list else None,
            "avg_score": statistics.mean([m.score for m in metrics_list]) if metrics_list else 0,
            "score_range": {
                "min": min([m.score for m in metrics_list]) if metrics_list else 0,
                "max": max([m.score for m in metrics_list]) if metrics_list else 0
            }
        }

    def enable_fast_mode(self, enabled: bool = True):
        """Enable fast mode for sub-second scanning (reduced API calls)"""
        self.fast_mode = enabled
        if enabled:
            self.scan_interval = 2.0  # Faster scanning
            self.logger.info("Fast mode enabled - reduced API calls, faster scanning")
        else:
            self.scan_interval = 5.0  # Normal scanning
            self.logger.info("Fast mode disabled - full API calls, normal scanning")

    def get_api_call_rate(self) -> float:
        """Get current API call rate for monitoring"""
        current_time = time.time()
        time_diff = current_time - self.last_api_reset

        if time_diff >= 60:  # Reset every minute
            rate = self.api_call_count / time_diff
            self.api_call_count = 0
            self.last_api_reset = current_time
            return rate

        return self.api_call_count / max(time_diff, 1)


class SymbolScannerConfig:
    """Configuration class for SymbolScanner with sensible defaults"""

    # Default symbols for USDT futures
    DEFAULT_SYMBOLS = [
        'BTC/USDT:USDT',
        'ETH/USDT:USDT',
        'DOGE/USDT:USDT',
        'ADA/USDT:USDT',
        'SOL/USDT:USDT',
        'MATIC/USDT:USDT',
        'AVAX/USDT:USDT',
        'DOT/USDT:USDT'
    ]

    # Default metrics weights optimized for scalping
    DEFAULT_WEIGHTS = {
        'spread_score': 0.25,      # Lower spread = better for scalping
        'tick_atr_score': 0.20,    # Higher volatility = more opportunities
        'flow_score': 0.15,        # Balanced flow = stable conditions
        'depth_score': 0.20,       # Higher depth = better execution
        'volume_score': 0.20       # Higher volume = more liquidity
    }

    # Default normalization ranges
    DEFAULT_RANGES = {
        'spread_pct': {'min': 0.001, 'max': 0.1},
        'tick_atr': {'min': 0.00001, 'max': 0.001},
        'flow_imbalance': {'min': -50.0, 'max': 50.0},
        'orderbook_depth': {'min': 1000, 'max': 100000},
        'volume_24h': {'min': 1000000, 'max': 1000000000}
    }

    @classmethod
    def create_scanner(cls, market_api, symbols: List[str] = None, weights: Dict[str, float] = None,
                      mode: str = None, update_interval: float = 5.0, **kwargs):
        """
        Create a SymbolScanner with default configuration

        Args:
            market_api: Market data API instance
            symbols: List of symbols (uses defaults if None)
            weights: Metrics weights (uses defaults if None)
            mode: Trading mode ('scalping', 'swing', etc.) - optimizes settings
            update_interval: Update interval in seconds
            **kwargs: Additional parameters

        Returns:
            Configured SymbolScanner instance
        """
        # Apply mode-specific optimizations
        if mode == 'scalping':
            # Scalping-optimized settings
            optimized_weights = {
                'spread_score': 0.30,      # Higher weight on low spreads
                'tick_atr_score': 0.25,    # Higher volatility preference
                'flow_score': 0.10,        # Less concern about flow balance
                'depth_score': 0.25,       # High depth for quick execution
                'volume_score': 0.10       # Volume less critical for scalping
            }
            weights = weights or optimized_weights

        # Create scanner instance
        scanner = SymbolScanner(
            market_api=market_api,
            symbols=symbols or cls.DEFAULT_SYMBOLS,
            metrics_weights=weights or cls.DEFAULT_WEIGHTS
        )

        # Apply update interval if specified
        if update_interval is not None:
            scanner.scan_interval = update_interval

        # Apply any additional kwargs
        for key, value in kwargs.items():
            if hasattr(scanner, key):
                setattr(scanner, key, value)

        return scanner
