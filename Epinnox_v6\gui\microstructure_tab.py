"""
Microstructure Tab for Epinnox v6 Trading System
Order book analysis and market microstructure data
"""

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
except ImportError:
    print("PyQt5 not installed. Install with: pip install PyQt5")
    raise

from .base_tab import BaseTab
from .matrix_theme import MatrixTheme
import logging

logger = logging.getLogger(__name__)

class MicrostructureTab(BaseTab):
    """Microstructure analysis tab"""
    
    # Signals
    orderbook_requested = pyqtSignal(str)  # symbol
    trades_requested = pyqtSignal(str)  # symbol
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def setup_ui(self):
        """Setup the Microstructure tab UI"""
        layout = QHBoxLayout(self)
        
        # Order book panel
        orderbook_panel = self.create_orderbook_panel()
        
        # Market depth analysis
        depth_panel = self.create_depth_panel()
        
        # Recent trades
        trades_panel = self.create_trades_panel()
        
        layout.addWidget(orderbook_panel)
        layout.addWidget(depth_panel)
        layout.addWidget(trades_panel)
    
    def create_orderbook_panel(self):
        """Create order book panel"""
        panel = self.create_matrix_group_box("Order Book")
        layout = QVBoxLayout(panel)
        
        self.orderbook_symbol = QComboBox()
        self.orderbook_symbol.addItems(["DOGE/USDT", "BTC/USDT", "ETH/USDT"])
        self.orderbook_symbol.currentTextChanged.connect(self.on_symbol_changed)
        
        self.orderbook_table = QTableWidget(20, 3)
        self.orderbook_table.setHorizontalHeaderLabels(["Price", "Size", "Side"])
        self.apply_matrix_table_styling(self.orderbook_table)
        
        layout.addWidget(QLabel("Symbol:"))
        layout.addWidget(self.orderbook_symbol)
        layout.addWidget(self.orderbook_table)
        
        return panel
    
    def create_depth_panel(self):
        """Create market depth analysis panel"""
        panel = self.create_matrix_group_box("Market Depth Analysis")
        layout = QVBoxLayout(panel)
        
        self.bid_ask_spread_label = QLabel("Bid-Ask Spread: --")
        self.market_depth_label = QLabel("Market Depth: --")
        self.order_flow_label = QLabel("Order Flow: --")
        self.liquidity_score_label = QLabel("Liquidity Score: --")
        
        layout.addWidget(self.bid_ask_spread_label)
        layout.addWidget(self.market_depth_label)
        layout.addWidget(self.order_flow_label)
        layout.addWidget(self.liquidity_score_label)
        layout.addStretch()
        
        return panel
    
    def create_trades_panel(self):
        """Create recent trades panel"""
        panel = self.create_matrix_group_box("Recent Trades")
        layout = QVBoxLayout(panel)
        
        self.trades_table = QTableWidget(15, 4)
        self.trades_table.setHorizontalHeaderLabels(["Time", "Price", "Size", "Side"])
        self.apply_matrix_table_styling(self.trades_table)
        
        layout.addWidget(self.trades_table)
        return panel
    
    def on_symbol_changed(self, symbol):
        """Handle symbol change"""
        self.orderbook_requested.emit(symbol)
        self.trades_requested.emit(symbol)
        self.log_message(f"Symbol changed to {symbol}")
    
    def update_orderbook(self, orderbook_data):
        """Update order book display"""
        try:
            bids = orderbook_data.get('bids', [])
            asks = orderbook_data.get('asks', [])
            
            # Combine and sort for display
            all_orders = []
            for price, size in asks[:10]:  # Top 10 asks
                all_orders.append((price, size, 'ASK'))
            for price, size in bids[:10]:  # Top 10 bids
                all_orders.append((price, size, 'BID'))
            
            self.orderbook_table.setRowCount(len(all_orders))
            
            for row, (price, size, side) in enumerate(all_orders):
                self.orderbook_table.setItem(row, 0, QTableWidgetItem(f"{price:.6f}"))
                self.orderbook_table.setItem(row, 1, QTableWidgetItem(f"{size:.4f}"))
                
                side_item = QTableWidgetItem(side)
                if side == 'BID':
                    side_item.setForeground(QColor(MatrixTheme.GREEN))
                else:
                    side_item.setForeground(QColor(MatrixTheme.RED))
                self.orderbook_table.setItem(row, 2, side_item)
                
        except Exception as e:
            self.log_message(f"Error updating orderbook: {e}", logging.ERROR)
    
    def update_trades(self, trades_data):
        """Update recent trades display"""
        try:
            self.trades_table.setRowCount(len(trades_data))
            
            for row, trade in enumerate(trades_data):
                self.trades_table.setItem(row, 0, QTableWidgetItem(trade.get('time', '')))
                self.trades_table.setItem(row, 1, QTableWidgetItem(f"{trade.get('price', 0):.6f}"))
                self.trades_table.setItem(row, 2, QTableWidgetItem(f"{trade.get('size', 0):.4f}"))
                
                side = trade.get('side', '')
                side_item = QTableWidgetItem(side)
                if side == 'BUY':
                    side_item.setForeground(QColor(MatrixTheme.GREEN))
                else:
                    side_item.setForeground(QColor(MatrixTheme.RED))
                self.trades_table.setItem(row, 3, side_item)
                
        except Exception as e:
            self.log_message(f"Error updating trades: {e}", logging.ERROR)
