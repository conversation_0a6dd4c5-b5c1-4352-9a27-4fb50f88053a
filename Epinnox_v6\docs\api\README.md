# API Documentation

Complete API reference for Epinnox v6 Trading System.

## 📚 API Overview

Epinnox v6 provides several APIs for different components:

### Core APIs
- [Trading API](trading-api.md) - Order execution and position management
- [ML Prediction API](#ml-prediction-api) - Machine learning model predictions
- [Data API](#data-api) - Market data and WebSocket feeds
- [GUI API](#gui-api) - Interface components and events

## 🤖 ML Prediction API

### PredictionAccuracyTracker

Track and evaluate ML model prediction accuracy in real-time.

#### Class: `PredictionAccuracyTracker`

```python
from ml.prediction_accuracy_tracker import PredictionAccuracyTracker

# Initialize tracker
tracker = PredictionAccuracyTracker(evaluation_window_minutes=5)
```

#### Methods

##### `record_prediction(model_name, prediction, confidence, price, symbol)`
Record a new ML model prediction.

**Parameters:**
- `model_name` (str): Name of the ML model ("SVM", "Random Forest", "LSTM")
- `prediction` (str): Predicted direction ("LONG", "SHORT", "WAIT")
- `confidence` (float): Model confidence (0.0 to 1.0)
- `price` (float): Current market price
- `symbol` (str): Trading symbol

**Example:**
```python
tracker.record_prediction(
    model_name="SVM",
    prediction="LONG",
    confidence=0.75,
    price=0.169500,
    symbol="DOGE/USDT:USDT"
)
```

##### `update_current_price(symbol, price)`
Update current market price for accuracy calculations.

**Parameters:**
- `symbol` (str): Trading symbol
- `price` (float): Current market price

##### `get_model_accuracy(model_name)`
Get accuracy percentage for a specific model.

**Returns:** `float` - Accuracy percentage (0.0 to 100.0) or `None`

##### `get_all_accuracies()`
Get accuracy percentages for all models.

**Returns:** `dict` - Model names mapped to accuracy percentages

## 📊 Data API

### LiveDataManager

Manage real-time market data feeds.

#### Class: `LiveDataManager`

```python
from data.live_data_manager import LiveDataManager

# Initialize data manager
data_manager = LiveDataManager()
```

#### Methods

##### `subscribe_to_symbol(symbol, timeframes)`
Subscribe to live data for a trading symbol.

**Parameters:**
- `symbol` (str): Trading symbol
- `timeframes` (list): List of timeframes ["1m", "5m", "15m"]

##### `get_latest_candle(symbol, timeframe)`
Get the most recent candlestick data.

**Returns:** `dict` - OHLCV data

##### `get_orderbook(symbol)`
Get current order book data.

**Returns:** `dict` - Bid/ask prices and volumes

### WebSocketClient

Handle real-time WebSocket connections.

#### Class: `WebSocketClient`

```python
from data.websocket_client import WebSocketClient

# Initialize WebSocket client
ws_client = WebSocketClient("htx")
```

## 🎨 GUI API

### Matrix Theme

Professional dark theme for the trading interface.

#### Class: `MatrixTheme`

```python
from gui.matrix_theme import MatrixTheme

# Theme colors
MatrixTheme.GREEN      # Primary green
MatrixTheme.RED        # Error/sell red
MatrixTheme.YELLOW     # Warning/neutral yellow
MatrixTheme.BLUE       # Info blue
MatrixTheme.GRAY       # Secondary text
```

### Chart Components

#### CandlestickChart

Interactive candlestick chart with technical indicators.

```python
from gui.charts.candlestick_chart import CandlestickChart

# Create chart
chart = CandlestickChart()
chart.update_data(ohlcv_data)
```

## 🔧 Configuration API

### Config Management

Access and modify system configuration.

#### Module: `config.config`

```python
from config.config import *

# Trading parameters
SYMBOL = "DOGE/USDT:USDT"
LEVERAGE = 20
BALANCE = 50

# Exchange settings
EXCHANGE_NAME = "htx"
EXCHANGE_API_KEY = "your_api_key"
```

## 📈 Trading Events

### Event System

Subscribe to trading events and notifications.

#### Available Events

- `prediction_recorded` - New ML prediction recorded
- `accuracy_updated` - Model accuracy recalculated
- `order_placed` - Trading order executed
- `price_updated` - Market price changed
- `websocket_connected` - Live data connection established

#### Event Subscription

```python
# Subscribe to events
def on_prediction_recorded(model_name, prediction, accuracy):
    print(f"{model_name}: {prediction} (accuracy: {accuracy}%)")

# Register event handler
event_manager.subscribe("prediction_recorded", on_prediction_recorded)
```

## 🔍 Error Handling

### Exception Classes

#### `TradingError`
Base exception for trading-related errors.

#### `PredictionError`
Exception for ML prediction errors.

#### `DataError`
Exception for data feed errors.

#### `ConfigError`
Exception for configuration errors.

### Error Codes

| Code | Description |
|------|-------------|
| 1001 | API Authentication Failed |
| 1002 | WebSocket Connection Error |
| 1003 | Invalid Trading Symbol |
| 1004 | Insufficient Balance |
| 1005 | Model Loading Error |

## 📝 Response Formats

### Standard API Response

```json
{
  "success": true,
  "data": {
    "result": "...",
    "timestamp": 1234567890
  },
  "error": null
}
```

### Error Response

```json
{
  "success": false,
  "data": null,
  "error": {
    "code": 1001,
    "message": "API Authentication Failed",
    "details": "Invalid API key"
  }
}
```

## 🔐 Authentication

### API Key Management

```python
from config.config import EXCHANGE_API_KEY, EXCHANGE_SECRET

# Use in trading operations
trading_interface = RealTradingInterface(
    exchange="htx",
    api_key=EXCHANGE_API_KEY,
    secret=EXCHANGE_SECRET
)
```

## 📊 Rate Limits

### Exchange API Limits
- **HTX**: 100 requests per 10 seconds
- **WebSocket**: No limit on subscriptions
- **Order Placement**: 20 orders per second

### Internal Limits
- **ML Predictions**: 1 per minute per model
- **Accuracy Updates**: Every 10 seconds
- **GUI Updates**: 60 FPS maximum

## 🧪 Testing

### Mock APIs

For testing and development:

```python
from llama.mock_runner import MockLLMRunner
from trading.simulation_interface import SimulationInterface

# Use mock components
llm = MockLLMRunner()
trading = SimulationInterface()
```

## 📚 Examples

### Complete Trading Workflow

```python
from ml.prediction_accuracy_tracker import PredictionAccuracyTracker
from trading.real_trading_interface import RealTradingInterface
from data.live_data_manager import LiveDataManager

# Initialize components
tracker = PredictionAccuracyTracker()
trading = RealTradingInterface("htx")
data_manager = LiveDataManager()

# Record prediction
tracker.record_prediction("SVM", "LONG", 0.75, 0.169500, "DOGE/USDT:USDT")

# Update price
tracker.update_current_price("DOGE/USDT:USDT", 0.170000)

# Get accuracy
accuracy = tracker.get_model_accuracy("SVM")
print(f"SVM Accuracy: {accuracy}%")
```

---

**For more detailed examples, see the individual API documentation files.**
