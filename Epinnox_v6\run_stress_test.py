#!/usr/bin/env python3
"""
Stress Testing CLI for EPINNOX v6
Run comprehensive stress tests with extreme market scenarios
"""

import argparse
import sys
import os
import asyncio
from datetime import datetime
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from stress_testing.stress_tester import StressTestFramework, StressTestConfig, StressScenario

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockTradingSystem:
    """Mock trading system for stress testing"""
    
    def __init__(self):
        self.balance = 10000.0
        self.positions = {}
    
    async def process_market_data(self, market_data):
        """Process market data (mock implementation)"""
        pass

async def run_stress_test(args):
    """Run stress testing"""
    
    # Create mock trading system
    trading_system = MockTradingSystem()
    
    # Create stress test framework
    stress_tester = StressTestFramework(trading_system)
    
    # Parse scenario
    try:
        scenario = StressScenario(args.scenario)
    except ValueError:
        logger.error(f"Invalid scenario: {args.scenario}")
        logger.info(f"Valid scenarios: {[s.value for s in StressScenario]}")
        return 1
    
    # Create configuration
    config = StressTestConfig(
        scenario=scenario,
        duration_minutes=args.duration,
        intensity=args.intensity,
        symbols=args.symbols,
        initial_balance=args.initial_balance,
        parameters={}
    )
    
    # Display test info
    print(f"\n{'='*60}")
    print(f"EPINNOX v6 STRESS TESTING")
    print(f"{'='*60}")
    print(f"Scenario:         {scenario.value}")
    print(f"Duration:         {args.duration} minutes")
    print(f"Intensity:        {args.intensity}x")
    print(f"Symbols:          {', '.join(args.symbols)}")
    print(f"Initial Balance:  ${args.initial_balance:,.2f}")
    print(f"{'='*60}\n")
    
    try:
        # Run stress test
        logger.info(f"🧪 Starting stress test: {scenario.value}")
        result = await stress_tester.run_stress_test(config)
        
        # Display results
        display_results(result)
        
        # Save results if requested
        if args.save_results:
            filename = f"stress_test_{scenario.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            save_stress_results(result, filename)
            logger.info(f"📄 Results saved to {filename}")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Stress test failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

def display_results(result):
    """Display stress test results"""
    
    print(f"\n{'='*60}")
    print(f"STRESS TEST RESULTS: {result.scenario.value.upper()}")
    print(f"{'='*60}")
    
    # Performance metrics
    perf = result.performance_metrics
    print(f"📊 Performance Metrics:")
    print(f"  Total Return:     {perf['total_return']:.2%}")
    print(f"  Final Balance:    ${perf['final_balance']:,.2f}")
    print(f"  Total Trades:     {perf['total_trades']}")
    print(f"  Error Count:      {perf['error_count']}")
    print(f"  Max Drawdown:     {perf['max_drawdown']:.2%}")
    
    # Risk metrics
    risk = result.risk_metrics
    print(f"\n🛡️ Risk Metrics:")
    print(f"  System Risk:      {risk['system_risk']:.2%}")
    print(f"  Max Exposure:     {risk['max_position_exposure']:.2%}")
    print(f"  Liquidity Risk:   {risk['liquidity_risk']:.2%}")
    
    # System stability
    stability = result.system_stability
    print(f"\n⚙️ System Stability:")
    print(f"  Error Rate:       {stability['error_rate']:.2%}")
    print(f"  System Uptime:    {stability['system_uptime']:.2%}")
    print(f"  Recovery Time:    {stability['recovery_time']:.1f}s")
    
    # Trade summary
    trades = result.trade_summary
    print(f"\n📈 Trade Summary:")
    print(f"  Total Trades:     {trades['total']}")
    print(f"  Buy Orders:       {trades['buy_count']}")
    print(f"  Sell Orders:      {trades['sell_count']}")
    if trades['total'] > 0:
        print(f"  Avg Confidence:   {trades['avg_confidence']:.1f}%")
    
    # Recommendations
    if result.recommendations:
        print(f"\n💡 Recommendations:")
        for i, rec in enumerate(result.recommendations, 1):
            print(f"  {i}. {rec}")
    
    # Overall assessment
    print(f"\n🎯 Overall Assessment:")
    if perf['error_count'] == 0 and perf['total_return'] > -0.05:
        print("  ✅ PASSED - System handled stress scenario well")
    elif perf['error_count'] == 0 and perf['total_return'] > -0.15:
        print("  ⚠️ MARGINAL - System survived but with significant losses")
    else:
        print("  ❌ FAILED - System showed critical issues under stress")
    
    print(f"{'='*60}\n")

def save_stress_results(result, filename):
    """Save stress test results to file"""
    import json
    from dataclasses import asdict
    
    # Convert result to dictionary
    result_dict = {
        'scenario': result.scenario.value,
        'config': {
            'scenario': result.config.scenario.value,
            'duration_minutes': result.config.duration_minutes,
            'intensity': result.config.intensity,
            'symbols': result.config.symbols,
            'initial_balance': result.config.initial_balance
        },
        'performance_metrics': result.performance_metrics,
        'risk_metrics': result.risk_metrics,
        'trade_summary': result.trade_summary,
        'system_stability': result.system_stability,
        'recommendations': result.recommendations,
        'timestamp': datetime.now().isoformat()
    }
    
    with open(filename, 'w') as f:
        json.dump(result_dict, f, indent=2, default=str)

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description='EPINNOX v6 Stress Testing Framework')
    
    # Test configuration
    parser.add_argument('scenario', type=str, nargs='?',
                       help='Stress test scenario')
    parser.add_argument('--duration', type=int, default=60,
                       help='Test duration in minutes (default: 60)')
    parser.add_argument('--intensity', type=float, default=1.0,
                       help='Stress intensity multiplier (default: 1.0)')
    parser.add_argument('--symbols', type=str, nargs='+', 
                       default=['BTC/USDT', 'ETH/USDT', 'ADA/USDT'],
                       help='Trading symbols to test')
    parser.add_argument('--initial-balance', type=float, default=10000.0,
                       help='Initial balance for testing (default: 10000)')
    
    # Output options
    parser.add_argument('--save-results', action='store_true',
                       help='Save detailed results to file')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Quiet mode')
    
    # Preset scenarios
    parser.add_argument('--list-scenarios', action='store_true',
                       help='List available stress test scenarios')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    elif args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # List scenarios if requested
    if args.list_scenarios:
        print("Available Stress Test Scenarios:")
        print("=" * 40)
        scenarios = [
            ("flash_crash", "Sudden market crash with recovery"),
            ("volatility_spike", "Extreme price volatility"),
            ("liquidity_crisis", "Low liquidity and wide spreads"),
            ("trending_market", "Strong directional movement"),
            ("sideways_market", "Range-bound trading"),
            ("gap_opening", "Price gaps between periods"),
            ("news_shock", "Sudden news-driven price shock"),
            ("correlation_breakdown", "Normal correlations break down")
        ]
        
        for scenario, description in scenarios:
            print(f"  {scenario:<20} - {description}")
        
        print("\nExample usage:")
        print("  python run_stress_test.py flash_crash --intensity 2.0 --duration 30")
        return 0
    
    # Validate scenario
    valid_scenarios = [s.value for s in StressScenario]
    if args.scenario not in valid_scenarios:
        print(f"❌ Invalid scenario: {args.scenario}")
        print(f"Valid scenarios: {', '.join(valid_scenarios)}")
        print("Use --list-scenarios to see descriptions")
        return 1
    
    # Run stress test
    try:
        result = asyncio.run(run_stress_test(args))
        sys.exit(result)
    except KeyboardInterrupt:
        logger.info("🛑 Stress test interrupted by user")
        sys.exit(1)

if __name__ == "__main__":
    main()
