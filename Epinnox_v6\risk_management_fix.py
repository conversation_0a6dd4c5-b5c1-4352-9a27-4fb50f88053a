
# Risk management fix for Epinnox v6
class RiskManagementFix:
    """Fix risk management issues that might block orders"""
    
    def __init__(self):
        self.max_position_size = 0.1  # 10% of balance
        self.max_daily_loss = 0.05    # 5% daily loss limit
        
    def validate_trade_risk(self, trade_params, current_balance):
        """Validate trade against risk parameters"""
        try:
            position_size = trade_params.get('amount', 0) * trade_params.get('price', 0)
            position_pct = position_size / current_balance
            
            if position_pct > self.max_position_size:
                return False, f"Position size {position_pct:.1%} exceeds limit {self.max_position_size:.1%}"
            
            return True, "Risk validation passed"
            
        except Exception as e:
            return False, f"Risk validation error: {e}"

# Usage:
# risk_manager = RiskManagementFix()
# is_valid, message = risk_manager.validate_trade_risk(trade_params, balance)
