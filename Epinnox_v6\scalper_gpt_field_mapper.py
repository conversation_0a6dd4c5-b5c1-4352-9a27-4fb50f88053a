
def fix_scalper_gpt_json_fields(parsed_json):
    """Fix ScalperGPT JSON field mapping issues"""
    if not isinstance(parsed_json, dict):
        return parsed_json
    
    # Field mapping from various formats to standard format
    field_mappings = {
        # Action field variations
        'action': ['action', 'ACTION', 'Action', 'decision', 'DECISION'],
        'ACTION': ['action', 'ACTION', 'Action', 'decision', 'DECISION'],
        
        # Quantity field variations
        'quantity': ['quantity', 'QUANTITY', 'Quantity', 'size', 'SIZE'],
        'QUANTITY': ['quantity', 'QUANTITY', 'Quantity', 'size', 'SIZE'],
        
        # Leverage field variations
        'leverage': ['leverage', 'LEVERAGE', 'Leverage', 'lev', 'LEV'],
        'LEVERAGE': ['leverage', 'LEVERAGE', 'Leverage', 'lev', 'LEV'],
        
        # Risk percentage variations
        'risk_pct': ['risk_pct', 'RISK_PCT', 'risk_percent', 'risk', 'RISK'],
        'RISK_PCT': ['risk_pct', 'RISK_PCT', 'risk_percent', 'risk', 'RISK'],
        
        # Order type variations
        'order_type': ['order_type', 'ORDER_TYPE', 'orderType', 'type', 'TYPE'],
        'ORDER_TYPE': ['order_type', 'ORDER_TYPE', 'orderType', 'type', 'TYPE'],
        
        # Stop loss variations
        'stop_loss': ['stop_loss', 'STOP_LOSS', 'stopLoss', 'sl', 'SL'],
        
        # Take profit variations
        'take_profit': ['take_profit', 'TAKE_PROFIT', 'takeProfit', 'tp', 'TP']
    }
    
    # Create normalized result
    normalized = {}
    
    # Map fields to standard names
    for standard_field, possible_names in field_mappings.items():
        value_found = None
        for possible_name in possible_names:
            if possible_name in parsed_json:
                value_found = parsed_json[possible_name]
                break
        
        if value_found is not None:
            normalized[standard_field] = value_found
    
    # Ensure both uppercase and lowercase versions exist for critical fields
    critical_fields = ['action', 'quantity', 'leverage', 'risk_pct', 'order_type']
    
    for field in critical_fields:
        if field in normalized:
            # Add uppercase version
            normalized[field.upper()] = normalized[field]
        elif field.upper() in normalized:
            # Add lowercase version
            normalized[field] = normalized[field.upper()]
    
    # Set defaults for missing fields
    defaults = {
        'action': 'WAIT',
        'ACTION': 'WAIT',
        'quantity': 0.001,
        'QUANTITY': 0.001,
        'leverage': 1,
        'LEVERAGE': 1,
        'risk_pct': 1.0,
        'RISK_PCT': 1.0,
        'order_type': 'MARKET',
        'ORDER_TYPE': 'MARKET',
        'stop_loss': 1.0,
        'take_profit': 2.0
    }
    
    for field, default_value in defaults.items():
        if field not in normalized:
            normalized[field] = default_value
    
    return normalized
