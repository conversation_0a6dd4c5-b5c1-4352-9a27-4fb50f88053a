import pytest
from collections import deque
from types import SimpleNamespace

# Import the orchestrator and cache manager
from core.llm_orchestrator import LLMPromptOrchestrator
from utils.cache_manager import CacheManager
from core.llm_prompt_builders import LLMPromptBuilders

# --- Fakes ---
class FakeMarketAPI:
    def __init__(self, prices):
        self.prices = prices
        self.idx = 0
    def get_current_price(self, symbol):
        price = self.prices[self.idx]
        self.idx = min(self.idx + 1, len(self.prices) - 1)
        return price

class FakeLLMClient:
    def __init__(self, responses=None):
        self.calls = []
        self.responses = responses or {}
        self.tick = 0
    def call(self, prompt):
        self.calls.append(prompt)
        # Return a custom response if set for this tick
        resp = self.responses.get(self.tick, {'decision': 'WAIT', 'confidence': 0})
        self.tick += 1
        return resp

# --- Pytest Fixtures ---
@pytest.fixture
def orchestrator_test_setup():
    # Price sequence: start outside action zone, then move in
    # Action zone: 99.495 to 100.495 (support=99, resistance=101, zone=0.5%)
    prices = [99.0, 99.1, 99.2, 99.3, 100.0, 100.1, 100.2]
    market_api = FakeMarketAPI(prices)
    llm_client = FakeLLMClient()
    cache = CacheManager()
    intervals = []

    class TestOrchestrator(LLMPromptOrchestrator):
        def __init__(self, *a, **kw):
            super().__init__(*a, **kw)
            self.market_api = market_api
            self.llm_client = llm_client
            self.cache = cache
            self.current_support = 99.0
            self.current_resistance = 101.0
            self.logger = SimpleNamespace(info=lambda msg: None)
        def schedule_auto_refresh(self, interval):
            intervals.append(interval)

    orch = TestOrchestrator(None, None)
    return orch, market_api, llm_client, cache, intervals

# --- Tests ---
def test_outside_action_zone_waits(orchestrator_test_setup):
    orch, market_api, llm_client, cache, intervals = orchestrator_test_setup
    # Step through 3 prices outside action zone
    for _ in range(3):
        orch.run_cycle()
    # Should have 3 WAIT signals, no LLM calls
    assert all(s['decision'] == 'WAIT' for s in cache.signal_history)
    assert len(llm_client.calls) == 0
    # After 3 WAITs, interval should be 120, then 300
    assert intervals[-1] == 300
    assert len(cache.price_history) == 3
    assert len(cache.signal_history) == 3

def test_enters_action_zone_triggers_llm(orchestrator_test_setup):
    orch, market_api, llm_client, cache, intervals = orchestrator_test_setup
    # Step to just before action zone
    for _ in range(4):
        orch.run_cycle()
    # Now price is in action zone
    orch.run_cycle()
    # Should have 3 LLM calls (risk, timing, opp)
    assert len(llm_client.calls) == 3
    # Should have a non-WAIT signal at the end if LLM returns non-WAIT
    # But by default, FakeLLMClient returns WAIT, so signal_history[-1] is WAIT
    assert cache.signal_history[-1]['decision'] == 'WAIT'
    # Interval should be 15 after entering action zone
    assert intervals[-1] == 15
    assert len(cache.price_history) == 5
    assert len(cache.signal_history) == 5

@pytest.mark.parametrize("responses,expected_decision",[
    ({2: {'decision': 'LONG', 'confidence': 0.9}, 3: {'decision': 'LONG', 'confidence': 0.8}, 4: {'decision': 'LONG', 'confidence': 0.7}}, 'LONG'),
    ({2: {'decision': 'SHORT', 'confidence': 0.95}, 3: {'decision': 'WAIT', 'confidence': 0.0}, 4: {'decision': 'SHORT', 'confidence': 0.8}}, 'SHORT'),
])
def test_llm_decision_aggregation(orchestrator_test_setup, responses, expected_decision):
    orch, market_api, llm_client, cache, intervals = orchestrator_test_setup
    llm_client.responses = responses
    # Step to action zone
    for _ in range(4):
        orch.run_cycle()
    orch.run_cycle()
    # The last signal should match the expected decision
    assert cache.signal_history[-1]['decision'] == expected_decision
    # Should have 3 LLM calls
    assert len(llm_client.calls) == 3
    # Interval should be 15
    assert intervals[-1] == 15

def test_cache_growth_and_teardown(orchestrator_test_setup):
    orch, market_api, llm_client, cache, intervals = orchestrator_test_setup
    for _ in range(7):
        orch.run_cycle()
    # Should not exceed maxlen
    assert len(cache.price_history) <= 60
    assert len(cache.signal_history) <= 20
    # Clean teardown
    cache.price_history.clear()
    cache.signal_history.clear()
    assert len(cache.price_history) == 0
    assert len(cache.signal_history) == 0
