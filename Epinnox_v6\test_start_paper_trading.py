#!/usr/bin/env python3
"""
Test script for start_paper_trading.py to identify hanging issue
"""

import argparse
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_main():
    """Test main function with minimal imports"""
    print("Starting test_main...")
    
    parser = argparse.ArgumentParser(description='EPINNOX v6 Paper Trading Test')
    
    parser.add_argument('--balance', type=float, default=10000.0,
                       help='Initial balance (default: 10000)')
    parser.add_argument('--duration', type=int, 
                       help='Session duration in minutes')
    parser.add_argument('--max-positions', type=int, default=3,
                       help='Maximum concurrent positions (default: 3)')
    parser.add_argument('--min-confidence', type=float, default=0.65,
                       help='Minimum confidence threshold (default: 0.65)')
    parser.add_argument('--cycle-delay', type=int, default=30,
                       help='Delay between cycles in seconds (default: 30)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    parser.add_argument('--live', action='store_true',
                       help='Enable live trading mode (uses real money)')

    print("Parsing arguments...")
    args = parser.parse_args()
    
    print(f"Arguments parsed successfully:")
    print(f"  Balance: {args.balance}")
    print(f"  Live mode: {args.live}")
    print(f"  Verbose: {args.verbose}")
    
    return 0

if __name__ == "__main__":
    sys.exit(test_main())
