"""
Checkbox Functions Module

This module contains functions that are triggered by checkbox state changes in the UI.
Each function is designed to run in a separate thread and perform specific tasks.
"""

import time
import traceback
import os
import yaml
import pandas as pd
import numpy as np
from datetime import datetime
from threading import Thread, Event
from core.llm_orchestrator import LmStudioRunner

# Import strategy modules
try:
    from strategies import get_strategy
    from config import load_config, save_config
except ImportError:
    # Handle the case where the module is imported from a different directory
    from .strategies import get_strategy
    from .config import load_config, save_config

# These will be imported from the main module when initialized
exchange = None
demo_mode = None
fetch_open_positions = None
fetch_best_bid = None
fetch_best_ask = None
place_limit_order = None
place_market_order = None
close_position = None

# Debug flag - set to True to see detailed logs
DEBUG = True

# Thread control flags and events
audit_positions_stop_event = Event()
auto_trade_stop_event = Event()

# Strategy instance
strategy = None

def init_checkbox_functions(
    _exchange,
    _demo_mode,
    _fetch_open_positions,
    _fetch_best_bid,
    _fetch_best_ask,
    _place_limit_order,
    _place_market_order,
    _close_position,
    _fetch_ohlcv=None,
    _set_leverage=None,
    _debug=False
):
    """Initialize the module with necessary functions from the main application"""
    global exchange, demo_mode, fetch_open_positions, fetch_best_bid, fetch_best_ask
    global place_limit_order, place_market_order, close_position, fetch_ohlcv, set_leverage, DEBUG
    global strategy

    exchange = _exchange
    demo_mode = _demo_mode
    fetch_open_positions = _fetch_open_positions
    fetch_best_bid = _fetch_best_bid
    fetch_best_ask = _fetch_best_ask
    place_limit_order = _place_limit_order
    place_market_order = _place_market_order
    close_position = _close_position
    fetch_ohlcv = _fetch_ohlcv
    set_leverage = _set_leverage
    DEBUG = _debug

    # Initialize strategy (only once)
    if strategy is None:
        try:
            config = load_config()
            strategy = get_strategy('atr_ema_bands', config)
            print(f"Strategy ATR-EMA Bands initialized with ATR period: {strategy.atr_period}, EMA period: {strategy.ema_period}")

            # Log enabled indicators
            enabled_indicators = []
            if strategy.use_ema:
                enabled_indicators.append("EMA")
            if strategy.use_atr:
                enabled_indicators.append("ATR")
            if strategy.use_rsi:
                enabled_indicators.append("RSI")
            if strategy.use_macd:
                enabled_indicators.append("MACD")
            if strategy.use_ema_slope:
                enabled_indicators.append("EMA Slope")
            if strategy.use_price_velocity:
                enabled_indicators.append("Price Velocity")
            if strategy.use_atr_breakout:
                enabled_indicators.append("ATR Breakout")
            if strategy.use_band_break:
                enabled_indicators.append("Band Break")
            if strategy.use_wick_rejection:
                enabled_indicators.append("Wick Rejection")
            if strategy.use_band_width:
                enabled_indicators.append("Band Width")
            if strategy.use_htf_alignment:
                enabled_indicators.append("HTF Alignment")

            print(f"Enabled indicators: {', '.join(enabled_indicators)}")
        except Exception as e:
            print(f"Error initializing strategy: {e}")
            strategy = None

    print("Checkbox functions module initialized")

def update_strategy_config(config):
    """Update the strategy configuration."""
    global strategy

    if strategy is None:
        try:
            strategy = get_strategy('atr_ema_bands', config)
            print(f"Strategy {strategy.name} initialized with new configuration")
        except Exception as e:
            print(f"Error initializing strategy: {e}")
            return False
    else:
        try:
            strategy.update_config(config)
            print(f"Strategy {strategy.name} updated with new configuration")
        except Exception as e:
            print(f"Error updating strategy configuration: {e}")
            return False

    # Log enabled indicators
    enabled_indicators = []
    if strategy.use_ema:
        enabled_indicators.append("EMA")
    if strategy.use_atr:
        enabled_indicators.append("ATR")
    if strategy.use_rsi:
        enabled_indicators.append("RSI")
    if strategy.use_macd:
        enabled_indicators.append("MACD")
    if strategy.use_ema_slope:
        enabled_indicators.append("EMA Slope")
    if strategy.use_price_velocity:
        enabled_indicators.append("Price Velocity")
    if strategy.use_atr_breakout:
        enabled_indicators.append("ATR Breakout")
    if strategy.use_band_break:
        enabled_indicators.append("Band Break")
    if strategy.use_wick_rejection:
        enabled_indicators.append("Wick Rejection")
    if strategy.use_band_width:
        enabled_indicators.append("Band Width")
    if strategy.use_htf_alignment:
        enabled_indicators.append("HTF Alignment")

    print(f"Enabled indicators: {', '.join(enabled_indicators)}")
    return True

def audit_positions_function(tp_getter, sl_getter, status_callback=None):
    """
    Monitor open positions and close them when PnL ratio reaches TP or SL thresholds.

    Args:
        tp_getter (function): Function that returns the current take profit threshold
        sl_getter (function): Function that returns the current stop loss threshold
        status_callback (function): Optional callback to report status updates
    """
    # Reset the stop event
    audit_positions_stop_event.clear()

    # Get initial values for logging
    try:
        tp_value = float(tp_getter())
        sl_value = float(sl_getter())
    except (ValueError, TypeError) as e:
        error_msg = f"Error getting initial TP/SL values: {str(e)}"
        print(error_msg)
        if status_callback:
            status_callback(error_msg)
        return

    # Log the start of the audit with the initial values
    start_msg = f"Position audit started with TP: {tp_value:.4f}%, SL: {sl_value:.4f}%"
    print(start_msg)
    print(f"When PnL ratio >= {tp_value:.4f}%: Take Profit will trigger")
    print(f"When PnL ratio <= -{sl_value:.4f}%: Stop Loss will trigger")

    if status_callback:
        status_callback(start_msg)

    while not audit_positions_stop_event.is_set():
        try:
            # Get the current TP/SL values from the UI
            try:
                tp_value = float(tp_getter())
                sl_value = float(sl_getter())
            except (ValueError, TypeError) as e:
                error_msg = f"Error getting current TP/SL values: {str(e)}"
                print(error_msg)
                if status_callback:
                    status_callback(error_msg)
                # Use default values if there's an error
                tp_value = 0.3  # Default TP: 0.3%
                sl_value = 2.0  # Default SL: 2.0%

            # Fetch all open positions
            positions = fetch_open_positions(force_refresh=True)

            if not positions:
                if DEBUG:
                    print("No open positions found")
                time.sleep(5)  # Sleep for 5 seconds before checking again
                continue

            # Process each position
            for position in positions:
                # Skip if position has no contracts
                contracts = float(position.get('contracts', 0))
                if contracts == 0:
                    continue

                symbol = position.get('symbol')
                side = position.get('side')

                # Calculate PnL ratio using the same method as the positions table
                try:
                    # Use our helper function to calculate PnL ratio
                    pnl_ratio = check_position_pnl_ratio(position)

                    # Print debug info
                    if DEBUG:
                        print(f"Position: {symbol} {side}, PnL ratio: {pnl_ratio:.4f}%")

                except Exception as e:
                    # Handle conversion errors
                    error_msg = f"Error calculating PnL ratio for {symbol}: {str(e)}"
                    print(error_msg)
                    if status_callback:
                        status_callback(error_msg)
                    pnl_ratio = 0  # Default to 0 on error

                # Always log the current PnL ratio for debugging
                print(f"Position: {symbol} {side}, PnL ratio: {pnl_ratio:.4f}%, TP threshold: {tp_value:.4f}%, SL threshold: {sl_value:.4f}%")

                # Check if PnL ratio meets TP or SL criteria
                if pnl_ratio >= tp_value:
                    # Take profit - close position with limit order at best bid/ask
                    message = f"TP TRIGGERED for {symbol} {side} position. PnL ratio: {pnl_ratio:.4f}% >= TP: {tp_value:.4f}%"
                    print(message)
                    if status_callback:
                        status_callback(message)

                    # Determine the side for closing
                    close_side = 'sell' if side.lower() == 'long' else 'buy'

                    # Get the best price for the limit order
                    price = fetch_best_bid(symbol) if close_side == 'buy' else fetch_best_ask(symbol)

                    if price:
                        print(f"Placing TP limit order: {symbol}, {close_side}, {abs(contracts)}, {price}")
                        # Place a limit order to close the position
                        params = {'offset': 'close', 'reduceOnly': True}
                        order = place_limit_order(symbol, close_side, abs(contracts), price, params)

                        if order:
                            success_msg = f"TP limit order placed for {symbol} at {price}"
                            print(success_msg)
                            if status_callback:
                                status_callback(success_msg)
                        else:
                            # If limit order fails, try market order
                            print(f"Limit order failed, trying market order: {symbol}, {close_side}, {abs(contracts)}")
                            order = place_market_order(symbol, close_side, abs(contracts), params)
                            if order:
                                market_msg = f"TP market order placed for {symbol}"
                                print(market_msg)
                                if status_callback:
                                    status_callback(market_msg)
                            else:
                                error_msg = f"Failed to place TP order for {symbol}"
                                print(error_msg)
                                if status_callback:
                                    status_callback(error_msg)

                elif pnl_ratio <= -sl_value:
                    # Stop loss - close position with limit order at best bid/ask
                    message = f"SL TRIGGERED for {symbol} {side} position. PnL ratio: {pnl_ratio:.4f}% <= SL: -{sl_value:.4f}%"
                    print(message)
                    if status_callback:
                        status_callback(message)

                    # Determine the side for closing
                    close_side = 'sell' if side.lower() == 'long' else 'buy'

                    # Get the best price for the limit order
                    price = fetch_best_bid(symbol) if close_side == 'buy' else fetch_best_ask(symbol)

                    if price:
                        print(f"Placing SL limit order: {symbol}, {close_side}, {abs(contracts)}, {price}")
                        # Place a limit order to close the position
                        params = {'offset': 'close', 'reduceOnly': True}
                        order = place_limit_order(symbol, close_side, abs(contracts), price, params)

                        if order:
                            success_msg = f"SL limit order placed for {symbol} at {price}"
                            print(success_msg)
                            if status_callback:
                                status_callback(success_msg)
                        else:
                            # If limit order fails, try market order
                            print(f"Limit order failed, trying market order: {symbol}, {close_side}, {abs(contracts)}")
                            order = place_market_order(symbol, close_side, abs(contracts), params)
                            if order:
                                market_msg = f"SL market order placed for {symbol}"
                                print(market_msg)
                                if status_callback:
                                    status_callback(market_msg)
                            else:
                                error_msg = f"Failed to place SL order for {symbol}"
                                print(error_msg)
                                if status_callback:
                                    status_callback(error_msg)

            # Sleep for a few seconds before checking again
            time.sleep(5)

        except Exception as e:
            error_msg = f"Error in audit_positions_function: {str(e)}"
            print(error_msg)
            print(traceback.format_exc())
            if status_callback:
                status_callback(error_msg)
            time.sleep(10)  # Sleep longer after an error

    if status_callback:
        status_callback("Position audit stopped")
    print("Position audit stopped")

def stop_audit_positions():
    """Stop the audit positions thread"""
    audit_positions_stop_event.set()
    print("Stopping position audit...")

def start_audit_positions_thread(tp_getter, sl_getter, status_callback=None):
    """
    Start the audit positions function in a separate thread

    Args:
        tp_getter (function): Function that returns the current take profit threshold
        sl_getter (function): Function that returns the current stop loss threshold
        status_callback (function): Optional callback to report status updates
    """
    thread = Thread(
        target=audit_positions_function,
        args=(tp_getter, sl_getter, status_callback),
        daemon=True
    )
    thread.start()
    return thread

def auto_trade_function(symbol, timeframe, status_callback=None, quantity=10, leverage=20, max_margin_getter=None):
    """
    Automated trading function using the ATR-EMA Bands strategy.
    Only enters positions (no auto exit) and limits total margin to the specified value.

    Args:
        symbol (str): Trading symbol.
        timeframe (str): Timeframe for OHLCV data.
        status_callback (function): Optional callback to report status updates.
        quantity (float): Quantity to trade.
        leverage (int): Leverage to use.
        max_margin_getter (function): Function that returns the current max margin value.
    """
    # Reset the stop event
    auto_trade_stop_event.clear()

    # Load configuration
    config = load_config()
    trading_config = config.get('trading', {})

    # Get check interval from config, but use provided quantity and leverage
    check_interval_seconds = trading_config.get('check_interval_seconds', 60)

    # Get the initial max margin value
    if max_margin_getter is None:
        # Default to 10.0 if no getter is provided
        def max_margin_getter():
            return 10.0

    # Get the initial max margin value for logging
    MAX_MARGIN = max_margin_getter()

    # Log the trading parameters
    print(f"Auto trading with quantity: {quantity}, leverage: {leverage}x, initial max margin: {MAX_MARGIN}, check interval: {check_interval_seconds}s")

    # Check if strategy is initialized
    global strategy
    if strategy is None:
        error_msg = "Strategy not initialized. Please restart the application."
        print(error_msg)
        if status_callback:
            status_callback(error_msg)
        return

    # Log start of auto trading
    start_msg = f"Auto trading started for {symbol} on {timeframe} timeframe"
    print(start_msg)
    if status_callback:
        status_callback(start_msg)

    # Set leverage using the value from the UI
    if set_leverage:
        try:
            set_leverage(symbol, leverage)
            print(f"Leverage set to {leverage}x for {symbol}")
        except Exception as e:
            print(f"Error setting leverage: {e}")

    # Main trading loop
    while not auto_trade_stop_event.is_set():
        try:
            # Fetch OHLCV data
            if fetch_ohlcv:
                # Get enough candles for strategy calculation
                limit = max(strategy.atr_period, strategy.ema_period) + 50
                ohlcv_data = fetch_ohlcv(symbol, timeframe, limit=limit)

                if ohlcv_data is None or len(ohlcv_data) < limit:
                    error_msg = f"Not enough OHLCV data for {symbol} on {timeframe} timeframe"
                    print(error_msg)
                    if status_callback:
                        status_callback(error_msg)
                    time.sleep(check_interval_seconds)
                    continue

                # Convert to DataFrame
                df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

                # === LLM DECISION BLOCK ===
                # Fetch higher timeframe candles
                ohlcv_5m = fetch_ohlcv(symbol, '5m', limit=10)
                ohlcv_15m = fetch_ohlcv(symbol, '15m', limit=10)
                df5m = pd.DataFrame(ohlcv_5m, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']) if ohlcv_5m is not None else pd.DataFrame()
                df15m = pd.DataFrame(ohlcv_15m, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']) if ohlcv_15m is not None else pd.DataFrame()

                # Calculate current margin used by auto-traded positions
                positions = fetch_open_positions()
                current_margin_used = 0.0
                for pos in positions:
                    collateral_val = pos.get('collateral')
                    pos_margin = float(collateral_val) if collateral_val is not None else 0
                    if pos_margin == 0:
                        info_margin = pos.get('info', {}).get('margin')
                        pos_margin = float(info_margin) if info_margin is not None else 0
                    if pos_margin == 0:
                        notional = float(pos.get('notional', 0))
                        pos_leverage = float(pos.get('leverage', 0))
                        if pos_leverage > 0 and notional > 0:
                            pos_margin = notional / pos_leverage
                    current_margin_used += pos_margin

                # Get the current max margin value
                current_max_margin = max_margin_getter()

                # Position info (assume only one position per symbol for simplicity)
                existing_position_info = None
                for pos in positions:
                    if pos.get('symbol') == symbol:
                        existing_position_info = {
                            'side': pos.get('side'),
                            'size': pos.get('contracts', 0),
                            'avg_price': pos.get('avgEntryPrice', 0)
                        }
                        break
                if existing_position_info is None:
                    existing_position_info = {'side': None, 'size': 0, 'avg_price': 0}

                # Cache context (stub: replace with real cache manager if available)
                try:
                    from utils.cache_manager import CacheManager
                    cache = CacheManager()
                    cache_context = cache.get_context()
                except Exception:
                    cache_context = {}

                # LLM decision
                decision, confidence = llm_decision_auto_trade(
                    symbol,
                    df.tail(50),
                    df5m.tail(10),
                    df15m.tail(10),
                    current_margin_used,
                    existing_position_info,
                    cache_context
                )
                print(f"LLM decision: {decision} @ {confidence}%")
                if status_callback:
                    status_callback(f"LLM decision: {decision} @ {confidence}%")

                # === END LLM DECISION BLOCK ===

                # Get current price for margin calculation
                current_price = df['close'].iloc[-1]
                estimated_margin = (quantity * current_price) / leverage

                # Check if we're under the margin limit
                if current_margin_used < current_max_margin:
                    if current_margin_used + estimated_margin > current_max_margin:
                        limit_msg = f"Cannot open position - would exceed margin limit (${current_margin_used:.2f} + ${estimated_margin:.2f} > ${current_max_margin:.2f})"
                        print(limit_msg)
                        if status_callback:
                            status_callback(limit_msg)
                    else:
                        # Execute trades based on LLM decision
                        if decision == 'BUY':
                            open_msg = f"Opening LONG position for {symbol} based on LLM BUY decision ({confidence}%)"
                            print(open_msg)
                            if status_callback:
                                status_callback(open_msg)
                            params = {'offset': 'open', 'lever_rate': leverage}
                            order = place_market_order(symbol, 'buy', quantity, params)
                            if order:
                                success_msg = f"Successfully opened LONG position for {symbol}"
                                print(success_msg)
                                if status_callback:
                                    status_callback(success_msg)
                            else:
                                error_msg = f"Failed to open LONG position for {symbol}"
                                print(error_msg)
                                if status_callback:
                                    status_callback(error_msg)
                        elif decision == 'SELL':
                            open_msg = f"Opening SHORT position for {symbol} based on LLM SELL decision ({confidence}%)"
                            print(open_msg)
                            if status_callback:
                                status_callback(open_msg)
                            params = {'offset': 'open', 'lever_rate': leverage}
                            order = place_market_order(symbol, 'sell', quantity, params)
                            if order:
                                success_msg = f"Successfully opened SHORT position for {symbol}"
                                print(success_msg)
                                if status_callback:
                                    status_callback(success_msg)
                            else:
                                error_msg = f"Failed to open SHORT position for {symbol}"
                                print(error_msg)
                                if status_callback:
                                    status_callback(error_msg)
                        else:
                            wait_msg = f"LLM decision is WAIT or not confident enough. No action taken."
                            print(wait_msg)
                            if status_callback:
                                status_callback(wait_msg)
                else:
                    limit_msg = f"Maximum margin limit reached (${current_margin_used:.2f} >= ${current_max_margin:.2f}). No new positions will be opened."
                    print(limit_msg)
                    if status_callback:
                        status_callback(limit_msg)

            # Sleep for the check interval
            time.sleep(check_interval_seconds)

        except Exception as e:
            error_msg = f"Error in auto_trade_function: {str(e)}"
            print(error_msg)
            print(traceback.format_exc())
            if status_callback:
                status_callback(error_msg)
            time.sleep(check_interval_seconds)

    # Log end of auto trading
    end_msg = "Auto trading stopped"
    print(end_msg)
    if status_callback:
        status_callback(end_msg)

def stop_auto_trade():
    """Stop the auto trade thread"""
    auto_trade_stop_event.set()
    print("Stopping auto trading...")

def start_auto_trade_thread(symbol, timeframe, status_callback=None, quantity=10, leverage=20, max_margin_getter=None):
    """
    Start the auto trade function in a separate thread

    Args:
        symbol (str): Trading symbol.
        timeframe (str): Timeframe for OHLCV data.
        status_callback (function): Optional callback to report status updates.
        quantity (float): Quantity to trade.
        leverage (int): Leverage to use.
        max_margin_getter (function): Function that returns the current max margin value.
    """
    thread = Thread(
        target=auto_trade_function,
        args=(symbol, timeframe, status_callback, quantity, leverage, max_margin_getter),
        daemon=True
    )
    thread.start()
    return thread

def check_position_pnl_ratio(position):
    """
    Debug function to check the PnL ratio of a position.
    Returns the PnL ratio as a percentage.
    """
    try:
        # Get the unrealized PnL and collateral values
        unrealized_pnl = float(position.get('unrealizedPnl', 0))

        # Try to get collateral/margin with fallbacks
        collateral_val = position.get('collateral')
        collateral = float(collateral_val) if collateral_val is not None else 0

        # If collateral is still 0, try to get it from info
        if collateral == 0:
            info_margin = position.get('info', {}).get('margin')
            collateral = float(info_margin) if info_margin is not None else 0

        # If still 0, try to calculate from notional and leverage
        if collateral == 0:
            notional = float(position.get('notional', 0))
            leverage = float(position.get('leverage', 0))
            if leverage > 0 and notional > 0:
                collateral = notional / leverage

        # Calculate PnL ratio (return on margin)
        pnl_ratio = 0
        if collateral > 0:
            pnl_ratio = (unrealized_pnl / collateral) * 100

        # Print debug info
        print(f"Position: {position.get('symbol')} {position.get('side')}")
        print(f"Unrealized PnL: {unrealized_pnl:.4f}, Collateral: {collateral:.4f}")
        print(f"Calculated PnL ratio: {pnl_ratio:.4f}%")

        return pnl_ratio
    except Exception as e:
        print(f"Error calculating PnL ratio: {str(e)}")
        print(f"Position data: {position}")
        return 0

def llm_decision_auto_trade(symbol: str,
                            main_df: pd.DataFrame,
                            higher_df_5m: pd.DataFrame,
                            higher_df_15m: pd.DataFrame,
                            current_margin: float,
                            position_info: dict,
                            cache_context: dict) -> tuple:
    """
    Calls the LLM to decide BUY/SELL/WAIT for an auto-entry.
    See docstring in user request for details.
    Returns (decision, confidence).
    """
    import datetime
    from core import llm_prompt_builders, llm_response_parsers

    # 1) Gather indicators
    def get_ema_atr(df, period=20):
        ema = df['close'].ewm(span=period).mean()
        atr = (df['high'] - df['low']).rolling(window=period).mean()
        return ema, atr

    ema_main, atr_main = get_ema_atr(main_df)
    ema_5m, atr_5m = get_ema_atr(higher_df_5m)
    ema_15m, atr_15m = get_ema_atr(higher_df_15m)

    bands = {
        'main': {'ema': ema_main.iloc[-1], 'atr': atr_main.iloc[-1]},
        '5m': {'ema': ema_5m.iloc[-1], 'atr': atr_5m.iloc[-1]},
        '15m': {'ema': ema_15m.iloc[-1], 'atr': atr_15m.iloc[-1]},
    }

    # 2) Prepare candles for prompt
    main_candles = main_df.tail(50).values.tolist()
    candles_5m = higher_df_5m.tail(10).values.tolist()
    candles_15m = higher_df_15m.tail(10).values.tolist()

    # 3) Get spread & 24h volume (stub: replace with real data source)
    spread = cache_context.get('spread', 0.1)
    volume_24h = cache_context.get('volume_24h', 100000)

    # 4) Margin context
    margin_abs = current_margin
    margin_pct = (current_margin / cache_context.get('max_margin', 1)) * 100 if cache_context.get('max_margin') else 0
    margin_tuple = (margin_abs, margin_pct)

    # 5) Prompt version & timestamp
    version = "v1.0"
    timestamp = datetime.datetime.utcnow().isoformat()

    # 6) Build prompt
    prompt = llm_prompt_builders.build_auto_trade_prompt(
        symbol,
        main_candles,
        candles_5m,
        candles_15m,
        bands,
        spread,
        volume_24h,
        margin_tuple,
        position_info,
        cache_context,
        version=version
    )
    prompt = f"# VERSION: {version} | TIMESTAMP: {timestamp}\n" + prompt

    # 7) Log prompt
    print(f"[LLM PROMPT @ {timestamp} v{version}]\n{prompt}")

    # 8) Call LLM orchestrator
    raw = LmStudioRunner().call(prompt)
    print(f"[LLM RAW REPLY @ {timestamp} v{version}]\n{raw}")

    # 9) Parse reply
    parsed = llm_response_parsers.parse_llm_entry_decision(raw)
    decision = parsed.get('decision', 'WAIT').upper()
    confidence = float(parsed.get('confidence', 0.0))

    # 10) Enforce safety
    if decision not in ['BUY', 'SELL', 'WAIT'] or confidence < 60:
        decision = 'WAIT'
        confidence = 0.0
    return decision, confidence
