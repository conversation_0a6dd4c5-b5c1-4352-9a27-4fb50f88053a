"""
Performance Dashboard Tab for Epinnox v6 Trading System
Trading performance metrics and analytics
"""

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
except ImportError:
    print("PyQt5 not installed. Install with: pip install PyQt5")
    raise

from .base_tab import BaseTab
from .matrix_theme import MatrixTheme
import logging

logger = logging.getLogger(__name__)

class PerformanceDashboardTab(BaseTab):
    """Performance dashboard for trading analytics"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def setup_ui(self):
        """Setup the Performance Dashboard tab UI"""
        layout = QVBoxLayout(self)
        
        # Performance metrics
        metrics_layout = self.create_performance_metrics()
        
        # Trade history
        history_group = self.create_trade_history()
        
        layout.addLayout(metrics_layout)
        layout.addWidget(history_group)
    
    def create_performance_metrics(self):
        """Create performance metrics display"""
        layout = QHBoxLayout()
        
        # Overall performance
        overall_group = self.create_overall_performance()
        
        # Daily performance
        daily_group = self.create_daily_performance()
        
        # ML Model performance
        ml_perf_group = self.create_ml_performance()
        
        layout.addWidget(overall_group)
        layout.addWidget(daily_group)
        layout.addWidget(ml_perf_group)
        
        return layout
    
    def create_overall_performance(self):
        """Create overall performance metrics"""
        group = self.create_matrix_group_box("Overall Performance")
        layout = QVBoxLayout(group)
        
        self.total_pnl_label = self.create_matrix_label("Total PnL: $0.00", MatrixTheme.FONT_SIZE_LARGE)
        self.win_rate_label = QLabel("Win Rate: --%")
        self.total_trades_label = QLabel("Total Trades: 0")
        self.avg_trade_label = QLabel("Avg Trade: $0.00")
        self.max_drawdown_label = QLabel("Max Drawdown: --%")
        
        layout.addWidget(self.total_pnl_label)
        layout.addWidget(self.win_rate_label)
        layout.addWidget(self.total_trades_label)
        layout.addWidget(self.avg_trade_label)
        layout.addWidget(self.max_drawdown_label)
        
        return group
    
    def create_daily_performance(self):
        """Create daily performance metrics"""
        group = self.create_matrix_group_box("Daily Performance")
        layout = QVBoxLayout(group)
        
        self.daily_pnl_label = QLabel("Today's PnL: $0.00")
        self.daily_trades_label = QLabel("Today's Trades: 0")
        self.daily_win_rate_label = QLabel("Today's Win Rate: --%")
        
        layout.addWidget(self.daily_pnl_label)
        layout.addWidget(self.daily_trades_label)
        layout.addWidget(self.daily_win_rate_label)
        layout.addStretch()
        
        return group
    
    def create_ml_performance(self):
        """Create ML model performance metrics"""
        group = self.create_matrix_group_box("ML Model Performance")
        layout = QVBoxLayout(group)
        
        self.ml_accuracy_table = QTableWidget(3, 3)
        self.ml_accuracy_table.setHorizontalHeaderLabels(["Model", "Accuracy", "Last Trained"])
        self.ml_accuracy_table.setMaximumHeight(120)
        self.apply_matrix_table_styling(self.ml_accuracy_table)
        
        layout.addWidget(self.ml_accuracy_table)
        return group
    
    def create_trade_history(self):
        """Create trade history table"""
        group = self.create_matrix_group_box("Recent Trade History")
        layout = QVBoxLayout(group)
        
        self.trade_history_table = QTableWidget(0, 8)
        self.trade_history_table.setHorizontalHeaderLabels([
            "Time", "Symbol", "Side", "Size", "Entry", "Exit", "PnL", "Strategy"
        ])
        self.apply_matrix_table_styling(self.trade_history_table)
        
        layout.addWidget(self.trade_history_table)
        return group
    
    def update_performance_metrics(self, metrics_data):
        """Update performance metrics display"""
        try:
            # Overall performance
            total_pnl = metrics_data.get('total_pnl', 0)
            self.total_pnl_label.setText(f"Total PnL: ${total_pnl:.2f}")
            
            # Color code PnL
            if total_pnl > 0:
                color = MatrixTheme.GREEN
            elif total_pnl < 0:
                color = MatrixTheme.RED
            else:
                color = MatrixTheme.YELLOW
            
            self.total_pnl_label.setStyleSheet(f"color: {color}; font-size: {MatrixTheme.FONT_SIZE_LARGE}px; font-weight: bold;")
            
            self.win_rate_label.setText(f"Win Rate: {metrics_data.get('win_rate', 0):.1%}")
            self.total_trades_label.setText(f"Total Trades: {metrics_data.get('total_trades', 0)}")
            self.avg_trade_label.setText(f"Avg Trade: ${metrics_data.get('avg_trade', 0):.2f}")
            self.max_drawdown_label.setText(f"Max Drawdown: {metrics_data.get('max_drawdown', 0):.1%}")
            
            # Daily performance
            self.daily_pnl_label.setText(f"Today's PnL: ${metrics_data.get('daily_pnl', 0):.2f}")
            self.daily_trades_label.setText(f"Today's Trades: {metrics_data.get('daily_trades', 0)}")
            self.daily_win_rate_label.setText(f"Today's Win Rate: {metrics_data.get('daily_win_rate', 0):.1%}")
            
        except Exception as e:
            self.log_message(f"Error updating performance metrics: {e}", logging.ERROR)
    
    def update_trade_history(self, trades_data):
        """Update trade history table"""
        try:
            self.trade_history_table.setRowCount(len(trades_data))
            
            for row, trade in enumerate(trades_data):
                self.trade_history_table.setItem(row, 0, QTableWidgetItem(trade.get('time', '')))
                self.trade_history_table.setItem(row, 1, QTableWidgetItem(trade.get('symbol', '')))
                self.trade_history_table.setItem(row, 2, QTableWidgetItem(trade.get('side', '')))
                self.trade_history_table.setItem(row, 3, QTableWidgetItem(str(trade.get('size', 0))))
                self.trade_history_table.setItem(row, 4, QTableWidgetItem(str(trade.get('entry_price', 0))))
                self.trade_history_table.setItem(row, 5, QTableWidgetItem(str(trade.get('exit_price', 0))))
                
                # Color code PnL
                pnl = trade.get('pnl', 0)
                pnl_item = QTableWidgetItem(f"${pnl:.2f}")
                if pnl > 0:
                    pnl_item.setForeground(QColor(MatrixTheme.GREEN))
                elif pnl < 0:
                    pnl_item.setForeground(QColor(MatrixTheme.RED))
                self.trade_history_table.setItem(row, 6, pnl_item)
                
                self.trade_history_table.setItem(row, 7, QTableWidgetItem(trade.get('strategy', '')))
                
        except Exception as e:
            self.log_message(f"Error updating trade history: {e}", logging.ERROR)
