#!/usr/bin/env python3
"""
Test script to verify live data integration
"""
import json
import time
from datetime import datetime
from gui_integration import TradingSystemGUIIntegration

def test_live_data():
    """Test the live data integration"""
    print("🧪 Testing Live Data Integration...")
    
    # Create integration instance
    integration = TradingSystemGUIIntegration()
    
    # Test data capture
    print("📊 Capturing live data...")
    live_data = integration._capture_live_terminal_data()
    
    if live_data:
        print("✅ Live data captured successfully!")
        print(f"📈 Symbol: {live_data['market_data']['symbol']}")
        print(f"💰 Price: ${live_data['market_data']['price']}")
        print(f"🎯 AI Decision: {live_data['ai_analysis']['decision']}")
        print(f"🔥 Confidence: {live_data['ai_analysis']['confidence']}%")
        print(f"📊 Trend: {live_data['timeframe_analysis']['overall_trend']}")
        print(f"⚡ Regime: {live_data['market_regime']['current_regime']}")
        
        # Update the data file manually
        print("\n💾 Updating GUI data file...")
        integration._update_data_file(live_data)
        
        print("✅ Data file updated!")
        
        # Verify the file was updated
        with open('gui_data.json', 'r') as f:
            data = json.load(f)
            
        if 'ai_analysis' in data and data['ai_analysis']:
            print(f"✅ Verification: AI Analysis found - {data['ai_analysis']['decision']}")
        else:
            print("❌ Verification: AI Analysis not found in data file")
            
    else:
        print("❌ Failed to capture live data")

if __name__ == "__main__":
    test_live_data()
