#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Secure Authentication Module for EPINNOX Trading Platform
Provides a government-style login interface that authenticates users
before allowing access to the main application.
"""

import os
import sys
import time
import hashlib
import base64
from datetime import datetime
from functools import partial

from PySide6.QtCore import (
    Qt, QSize, QTimer, Signal, QObject, QEvent, QPropertyAnimation, 
    QEasingCurve, Property
)
from PySide6.QtGui import (
    QPixmap, QFont, QColor, QPalette, QIcon, QMovie, 
    QLinearGradient, QPainter, QBrush, QPen, QFontMetrics
)
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QLabel, QLineEdit, 
    QPushButton, QVBoxLayout, QHBoxLayout, QGridLayout, 
    QFrame, QMessageBox, QCheckBox, QProgressBar
)

# Hardcoded credentials (in a real app, these would be securely stored)
VALID_USERNAME = "1"
VALID_PASSWORD = "1"
# VALID_USERNAME = "epinnox"
# VALID_PASSWORD = "Welcome123!@#"

# Maximum login attempts before temporary lockout
MAX_LOGIN_ATTEMPTS = 5
LOCKOUT_DURATION = 30  # seconds

# Styling constants
DARK_BG = "#1a1a2e"
HEADER_BG = "#16213e"
ACCENT_COLOR = "#00ff44"
ERROR_COLOR = "#ff4444"
WARNING_COLOR = "#ffaa00"
SUCCESS_COLOR = "#00ff44"
BUTTON_BG = "#0f3460"
BUTTON_HOVER = "#1a4980"
FIELD_BG = "#0a0a1a"
FIELD_BORDER = "#2a2a4e"

class PasswordField(QLineEdit):
    """Enhanced password field with visibility toggle"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setEchoMode(QLineEdit.Password)
        self.setStyleSheet(f"""
            QLineEdit {{
                background-color: {FIELD_BG};
                color: white;
                border: 1px solid {FIELD_BORDER};
                border-radius: 4px;
                padding: 8px;
                font-size: 12pt;
            }}
            QLineEdit:focus {{
                border: 1px solid {ACCENT_COLOR};
            }}
        """)
        
        # Add show/hide password toggle
        self.toggle_visible = QCheckBox("Show", self)
        self.toggle_visible.setStyleSheet(f"""
            QCheckBox {{
                color: #aaaaaa;
                background: transparent;
                padding: 2px;
            }}
            QCheckBox:hover {{
                color: white;
            }}
        """)
        self.toggle_visible.stateChanged.connect(self.toggle_password_visibility)
        
        # Position the checkbox
        self.textChanged.connect(self.update_toggle_position)
        
    def toggle_password_visibility(self, state):
        self.setEchoMode(QLineEdit.Normal if state else QLineEdit.Password)
        
    def update_toggle_position(self):
        # Position the checkbox at the right side of the field
        self.toggle_visible.move(
            self.width() - self.toggle_visible.width() - 5,
            (self.height() - self.toggle_visible.height()) // 2
        )
        
    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.update_toggle_position()


class LoginWindow(QMainWindow):
    """Secure login window with government-style interface"""
    
    login_successful = Signal()
    
    def __init__(self):
        super().__init__()
        
        # Initialize login attempt tracking
        self.login_attempts = 0
        self.locked_until = 0
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the user interface"""
        self.setWindowTitle("EPINNOX Secure Access")
        self.setFixedSize(800, 600)
        
        # Set window icon if available
        icon_path = os.path.join("resources", "icon.png")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # Main widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Header section
        header = QFrame()
        header.setStyleSheet(f"background-color: {HEADER_BG};")
        header.setFixedHeight(80)
        header_layout = QHBoxLayout(header)
        
        # Logo or emblem
        logo_label = QLabel()
        logo_path = os.path.join("resources", "logo.png")
        if os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path)
            logo_label.setPixmap(logo_pixmap.scaled(60, 60, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        else:
            # Fallback text if logo not found
            logo_label.setText("EPINNOX")
            logo_label.setStyleSheet("color: white; font-size: 24pt; font-weight: bold;")
        
        header_layout.addWidget(logo_label)
        
        # Header title
        title_label = QLabel("SECURE ACCESS PORTAL")
        title_label.setStyleSheet("color: white; font-size: 20pt; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)
        
        # Current date/time
        self.datetime_label = QLabel()
        self.datetime_label.setStyleSheet("color: #cccccc; font-size: 10pt;")
        self.datetime_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        header_layout.addWidget(self.datetime_label)
        
        # Update time every second
        self.update_datetime()
        self.datetime_timer = QTimer(self)
        self.datetime_timer.timeout.connect(self.update_datetime)
        self.datetime_timer.start(1000)
        
        main_layout.addWidget(header)
        
        # Content area
        content = QFrame()
        content.setStyleSheet(f"background-color: {DARK_BG};")
        content_layout = QGridLayout(content)
        content_layout.setContentsMargins(40, 40, 40, 40)
        content_layout.setSpacing(20)
        
        # Left side - Image
        image_label = QLabel()
        image_path = os.path.join("resources", "secure_login.png")
        if os.path.exists(image_path):
            image_pixmap = QPixmap(image_path)
            image_label.setPixmap(image_pixmap.scaled(350, 400, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        else:
            # Fallback if image not found
            image_label.setText("SECURE\nACCESS\nREQUIRED")
            image_label.setStyleSheet("color: white; font-size: 24pt; font-weight: bold;")
            image_label.setAlignment(Qt.AlignCenter)
        
        content_layout.addWidget(image_label, 0, 0, 4, 1)
        
        # Right side - Login form
        form_widget = QWidget()
        form_layout = QVBoxLayout(form_widget)
        form_layout.setContentsMargins(20, 20, 20, 20)
        form_layout.setSpacing(15)
        
        # Form title
        form_title = QLabel("Authentication Required")
        form_title.setStyleSheet("color: white; font-size: 18pt; font-weight: bold;")
        form_title.setAlignment(Qt.AlignCenter)
        form_layout.addWidget(form_title)
        
        # Form subtitle
        form_subtitle = QLabel("Please enter your credentials to access the system")
        form_subtitle.setStyleSheet("color: #aaaaaa; font-size: 10pt;")
        form_subtitle.setAlignment(Qt.AlignCenter)
        form_layout.addWidget(form_subtitle)
        
        form_layout.addSpacing(20)
        
        # Username field
        username_label = QLabel("Username:")
        username_label.setStyleSheet("color: white; font-size: 12pt;")
        form_layout.addWidget(username_label)
        
        self.username_field = QLineEdit()
        self.username_field.setStyleSheet(f"""
            QLineEdit {{
                background-color: {FIELD_BG};
                color: white;
                border: 1px solid {FIELD_BORDER};
                border-radius: 4px;
                padding: 8px;
                font-size: 12pt;
            }}
            QLineEdit:focus {{
                border: 1px solid {ACCENT_COLOR};
            }}
        """)
        self.username_field.setPlaceholderText("Enter your username")
        form_layout.addWidget(self.username_field)
        
        form_layout.addSpacing(10)
        
        # Password field
        password_label = QLabel("Password:")
        password_label.setStyleSheet("color: white; font-size: 12pt;")
        form_layout.addWidget(password_label)
        
        self.password_field = PasswordField()
        self.password_field.setPlaceholderText("Enter your password")
        form_layout.addWidget(self.password_field)
        
        form_layout.addSpacing(20)
        
        # Status message
        self.status_label = QLabel()
        self.status_label.setStyleSheet(f"color: {WARNING_COLOR}; font-size: 10pt;")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setText("Enter your credentials to continue")
        form_layout.addWidget(self.status_label)
        
        # Login button
        self.login_button = QPushButton("LOGIN")
        self.login_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {BUTTON_BG};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px;
                font-size: 14pt;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {BUTTON_HOVER};
            }}
            QPushButton:pressed {{
                background-color: {ACCENT_COLOR};
                color: black;
            }}
            QPushButton:disabled {{
                background-color: #333333;
                color: #777777;
            }}
        """)
        self.login_button.setFixedHeight(50)
        self.login_button.clicked.connect(self.attempt_login)
        form_layout.addWidget(self.login_button)
        
        # Add form to content layout
        content_layout.addWidget(form_widget, 0, 1, 4, 1)
        
        main_layout.addWidget(content, 1)
        
        # Footer
        footer = QFrame()
        footer.setStyleSheet(f"background-color: {HEADER_BG};")
        footer.setFixedHeight(30)
        footer_layout = QHBoxLayout(footer)
        footer_layout.setContentsMargins(10, 0, 10, 0)
        
        # Footer text
        footer_text = QLabel("EPINNOX SECURE TRADING PLATFORM • AUTHORIZED ACCESS ONLY")
        footer_text.setStyleSheet("color: #aaaaaa; font-size: 8pt;")
        footer_layout.addWidget(footer_text)
        
        # Version info
        version_label = QLabel("v1.0.0")
        version_label.setStyleSheet("color: #aaaaaa; font-size: 8pt;")
        version_label.setAlignment(Qt.AlignRight)
        footer_layout.addWidget(version_label)
        
        main_layout.addWidget(footer)
        
        # Set focus to username field
        self.username_field.setFocus()
        
        # Connect enter key to login
        self.username_field.returnPressed.connect(self.attempt_login)
        self.password_field.returnPressed.connect(self.attempt_login)
        
    def update_datetime(self):
        """Update the date/time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.setText(current_time)
        
    def attempt_login(self):
        """Validate credentials and attempt login"""
        # Check if we're in a lockout period
        current_time = time.time()
        if current_time < self.locked_until:
            remaining = int(self.locked_until - current_time)
            self.status_label.setText(f"Account locked. Try again in {remaining} seconds.")
            self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 10pt;")
            return
            
        username = self.username_field.text()
        password = self.password_field.text()
        
        # Basic validation
        if not username or not password:
            self.status_label.setText("Username and password are required")
            self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 10pt;")
            return
            
        # Authenticate
        if username == VALID_USERNAME and password == VALID_PASSWORD:
            self.login_successful.emit()
            self.status_label.setText("Login successful. Initializing system...")
            self.status_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-size: 10pt;")
            self.login_button.setEnabled(False)
            
            # Simulate loading before proceeding
            QTimer.singleShot(1500, self.close)
        else:
            # Increment failed attempts
            self.login_attempts += 1
            remaining = MAX_LOGIN_ATTEMPTS - self.login_attempts
            
            if remaining > 0:
                self.status_label.setText(f"Invalid credentials. {remaining} attempts remaining.")
                self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 10pt;")
            else:
                # Lock the account
                self.locked_until = time.time() + LOCKOUT_DURATION
                self.login_attempts = 0
                self.status_label.setText(f"Too many failed attempts. Locked for {LOCKOUT_DURATION} seconds.")
                self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 10pt;")
                
                # Start a timer to update the lockout countdown
                self.lockout_timer = QTimer(self)
                self.lockout_timer.timeout.connect(self.update_lockout_status)
                self.lockout_timer.start(1000)
                
    def update_lockout_status(self):
        """Update the lockout status message"""
        current_time = time.time()
        if current_time < self.locked_until:
            remaining = int(self.locked_until - current_time)
            self.status_label.setText(f"Account locked. Try again in {remaining} seconds.")
        else:
            self.status_label.setText("Lockout period ended. You may try again.")
            self.status_label.setStyleSheet(f"color: {WARNING_COLOR}; font-size: 10pt;")
            self.lockout_timer.stop()


def authenticate():
    """
    Launch the authentication window and return True if login is successful.
    This is the main entry point for the secure module.
    """
    # Create a small application just for the login window
    app = QApplication.instance() or QApplication(sys.argv)
    
    login_window = LoginWindow()
    login_window.show()
    
    # Use a flag to track login success
    login_success = [False]
    
    def on_login_success():
        login_success[0] = True
    
    login_window.login_successful.connect(on_login_success)
    
    # Run the login window's event loop
    app.exec()
    
    return login_success[0]


if __name__ == "__main__":
    # Test the login window
    if authenticate():
        print("Authentication successful!")
    else:
        print("Authentication failed or cancelled.")
        sys.exit(1)
