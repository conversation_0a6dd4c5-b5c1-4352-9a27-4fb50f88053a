"""
Unit tests for AutonomousTradeExecutor
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from execution.autonomous_executor import Autonomous<PERSON>rade<PERSON>xecutor, TradeOrder, OrderType

class TestAutonomousTradeExecutor:
    
    @pytest.fixture
    def executor(self, mock_exchange):
        """Create executor instance for testing"""
        return AutonomousTradeExecutor(
            exchange=mock_exchange,
            risk_manager=Mock(),
            min_confidence=0.65
        )
    
    @pytest.mark.asyncio
    async def test_execute_trading_decision_high_confidence(self, executor):
        """Test execution with high confidence decision"""
        decision_data = {
            'decision': 'LONG',
            'confidence': 75,
            'selected_symbol': 'BTC/USDT',
            'leverage_position_sizing': {
                'position_units': 0.1,
                'position_usd': 5000.0,
                'effective_leverage': 2.0,
                'stop_loss_price': 49000.0,
                'take_profit_price': 52000.0
            }
        }
        
        result = await executor.execute_trading_decision(decision_data)
        
        assert result['status'] == 'FILLED'
        assert result['symbol'] == 'BTC/USDT'
        assert result['side'] == 'buy'
        assert result['amount'] == 0.1
    
    @pytest.mark.asyncio
    async def test_execute_trading_decision_low_confidence(self, executor):
        """Test execution with low confidence decision"""
        decision_data = {
            'decision': 'LONG',
            'confidence': 50,  # Below threshold
            'selected_symbol': 'BTC/USDT',
            'leverage_position_sizing': {
                'position_units': 0.1,
                'position_usd': 5000.0,
                'effective_leverage': 2.0
            }
        }
        
        result = await executor.execute_trading_decision(decision_data)
        
        assert result['status'] == 'SKIPPED'
        assert 'below threshold' in result['reason']
    
    @pytest.mark.asyncio
    async def test_execute_trading_decision_wait(self, executor):
        """Test execution with WAIT decision"""
        decision_data = {
            'decision': 'WAIT',
            'confidence': 75,
            'selected_symbol': 'BTC/USDT'
        }
        
        result = await executor.execute_trading_decision(decision_data)
        
        assert result['status'] == 'WAIT'
        assert result['reason'] == 'System decided to wait'
    
    @pytest.mark.asyncio
    async def test_pre_execution_risk_check_excessive_leverage(self, executor):
        """Test risk check with excessive leverage"""
        position_data = {
            'position_usd': 1000.0,
            'effective_leverage': 15.0  # Above limit
        }
        
        result = await executor.pre_execution_risk_check('BTC/USDT', position_data, 0.75)
        
        assert not result['approved']
        assert 'exceeds maximum' in result['reason']
    
    @pytest.mark.asyncio
    async def test_pre_execution_risk_check_large_position(self, executor):
        """Test risk check with large position size"""
        position_data = {
            'position_usd': 200.0,  # 20% of 1000 balance
            'effective_leverage': 2.0
        }
        
        result = await executor.pre_execution_risk_check('BTC/USDT', position_data, 0.75)
        
        assert len(result.get('warnings', [])) > 0
        assert 'exceeds 10% of balance' in result['warnings'][0]
    
    @pytest.mark.asyncio
    async def test_execute_order_success(self, executor):
        """Test successful order execution"""
        order = TradeOrder(
            symbol='BTC/USDT',
            side='buy',
            amount=0.1,
            leverage=2.0,
            order_type=OrderType.MARKET
        )
        
        result = await executor.execute_order(order)
        
        assert result['status'] == 'FILLED'
        assert result['symbol'] == 'BTC/USDT'
        assert result['side'] == 'buy'
        assert result['amount'] == 0.1
        assert 'order_id' in result
    
    @pytest.mark.asyncio
    async def test_execute_order_with_futures_symbol(self, executor):
        """Test order execution with futures symbol (leverage)"""
        order = TradeOrder(
            symbol='BTC/USDT:USDT',  # Futures symbol
            side='buy',
            amount=0.1,
            leverage=3.0,
            order_type=OrderType.MARKET
        )
        
        result = await executor.execute_order(order)
        
        assert result['status'] == 'FILLED'
        # Verify leverage was passed to exchange
        assert len(executor.active_orders) == 1
    
    @pytest.mark.asyncio
    async def test_get_account_balance(self, executor):
        """Test account balance retrieval"""
        balance = await executor.get_account_balance()
        
        assert balance == 1000.0  # From mock exchange
    
    @pytest.mark.asyncio
    async def test_setup_risk_orders(self, executor):
        """Test stop loss and take profit order setup"""
        original_order = TradeOrder(
            symbol='BTC/USDT',
            side='buy',
            amount=0.1,
            stop_loss=49000.0,
            take_profit=52000.0
        )
        
        execution_result = {
            'amount': 0.1,
            'symbol': 'BTC/USDT'
        }
        
        # Should not raise exception
        await executor.setup_risk_orders(original_order, execution_result)
        
        # Verify orders were created (2 additional orders: SL + TP)
        assert len(executor.exchange.orders) >= 2
    
    def test_trade_order_creation(self):
        """Test TradeOrder dataclass creation"""
        order = TradeOrder(
            symbol='ETH/USDT',
            side='sell',
            amount=1.0,
            price=3000.0,
            leverage=1.5,
            stop_loss=2900.0,
            take_profit=3100.0
        )
        
        assert order.symbol == 'ETH/USDT'
        assert order.side == 'sell'
        assert order.amount == 1.0
        assert order.price == 3000.0
        assert order.leverage == 1.5
        assert order.stop_loss == 2900.0
        assert order.take_profit == 3100.0
        assert order.order_type == OrderType.MARKET  # Default
    
    @pytest.mark.asyncio
    async def test_missing_position_sizing_data(self, executor):
        """Test execution with missing position sizing data"""
        decision_data = {
            'decision': 'LONG',
            'confidence': 75,
            'selected_symbol': 'BTC/USDT'
            # Missing leverage_position_sizing
        }
        
        result = await executor.execute_trading_decision(decision_data)
        
        assert result['status'] == 'ERROR'
        assert 'No position sizing data' in result['reason']
