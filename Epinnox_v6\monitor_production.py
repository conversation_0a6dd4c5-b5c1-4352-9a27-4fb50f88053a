#!/usr/bin/env python3
"""
Epinnox Production Monitor
Real-time monitoring for live trading
"""

import time
import json
import glob
import os
from datetime import datetime

def monitor_production():
    """Monitor production trading"""
    print("📊 EPINNOX PRODUCTION MONITOR")
    print("=" * 40)
    print(f"Account: EPX")
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 40)
    
    while True:
        try:
            # Check for latest log
            log_files = glob.glob("epinnox_production_*.log")
            if log_files:
                latest_log = max(log_files, key=os.path.getctime)
                
                # Show last few lines
                with open(latest_log, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"\n📈 Latest Activity ({datetime.now().strftime('%H:%M:%S')}):")
                        for line in lines[-3:]:
                            if line.strip():
                                print(f"   {line.strip()}")
            
            # Check for trade reports
            report_files = glob.glob("production_trade_report_*.json")
            if report_files:
                latest_report = max(report_files, key=os.path.getctime)
                
                with open(latest_report, 'r') as f:
                    report = json.load(f)
                    
                print(f"\n💰 Performance:")
                print(f"   • Total Trades: {report.get('total_trades', 0)}")
                print(f"   • Current Balance: ${report.get('current_balance', 0):.2f}")
                print(f"   • Total PnL: ${report.get('total_pnl', 0):.2f}")
            
            time.sleep(30)  # Update every 30 seconds
            
        except KeyboardInterrupt:
            print("\n👋 Production monitoring stopped")
            break
        except Exception as e:
            print(f"❌ Monitor error: {e}")
            time.sleep(10)

if __name__ == "__main__":
    monitor_production()
