# Epinnox v6 - Exchange Credentials Configuration
# Copy this file to credentials.yaml and fill in your actual credentials

# HTX Exchange Configuration
htx:
  apiKey: "your_htx_api_key_here"
  secret: "your_htx_secret_here"
  password: ""  # HTX passphrase if required
  sandbox: true  # Set to false for live trading
  
# OpenAI Configuration (Optional)
openai:
  api_key: "your_openai_api_key_here"
  model: "gpt-4"
  max_tokens: 1000
  temperature: 0.7

# LMStudio Configuration (Optional)
lmstudio:
  base_url: "http://localhost:1234/v1"
  model: "phi-3.1-mini-128k-instruct"
  api_key: "lm-studio"

# Security Notes:
# - Never commit this file with real credentials
# - Use environment variables for production
# - Keep API keys secure and rotate regularly
# - Use sandbox mode for testing
