#!/usr/bin/env python3
"""
Restart Epinnox v6 GUI with Fixed Resolution Settings
Addresses display scaling and resolution issues
"""

import sys
import os
import subprocess
import time

def kill_existing_processes():
    """Kill any existing Epinnox processes"""
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['taskkill', '/f', '/im', 'python.exe'], 
                         capture_output=True, text=True)
            subprocess.run(['taskkill', '/f', '/im', 'pythonw.exe'], 
                         capture_output=True, text=True)
        else:  # Unix/Linux
            subprocess.run(['pkill', '-f', 'launch_epinnox.py'], 
                         capture_output=True, text=True)
        print("✓ Killed existing processes")
        time.sleep(2)
    except Exception as e:
        print(f"Note: {e}")

def set_display_environment():
    """Set AGGRESSIVE environment variables for maximum display scaling"""
    try:
        # AGGRESSIVE scaling settings
        os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
        os.environ['QT_SCALE_FACTOR'] = '1.5'        # 50% larger
        os.environ['QT_SCREEN_SCALE_FACTORS'] = '1.5'
        os.environ['QT_DEVICE_PIXEL_RATIO'] = '1.5'

        # Large font rendering
        os.environ['QT_FONT_DPI'] = '144'            # Much larger fonts
        os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'
        os.environ['QT_HIGHDPI_DISABLE_2X_IMAGE_LOADING'] = '0'

        # Windows specific
        os.environ['QT_WIN_DEBUG_CONSOLE'] = '1'

        print("✓ AGGRESSIVE display environment configured")
        print(f"   - Scale Factor: 1.5x")
        print(f"   - Font DPI: 144")
        print(f"   - High DPI: Enabled")
    except Exception as e:
        print(f"Warning: Could not set display environment: {e}")

def main():
    """Main function to restart GUI with fixed resolution"""
    print("🔧 Restarting Epinnox v6 with Fixed Resolution Settings...")
    print("=" * 60)
    
    # Kill existing processes
    kill_existing_processes()
    
    # Set display environment
    set_display_environment()
    
    # Change to Epinnox directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("🚀 Starting Epinnox v6 with improved resolution...")
    print("=" * 60)
    
    try:
        # Start the GUI
        subprocess.run([sys.executable, 'launch_epinnox.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting GUI: {e}")
        return 1
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        return 0
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
