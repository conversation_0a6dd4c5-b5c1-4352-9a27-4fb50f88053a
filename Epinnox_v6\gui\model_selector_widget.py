"""
Model Selector Widget for LMStudio
Allows dynamic switching between available models during runtime
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QComboBox, 
                             QPushButton, QLabel, QGroupBox, QTextEdit)
from PyQt5.QtCore import pyqtSignal, QTimer
from PyQt5.QtGui import QFont
from gui.matrix_theme import MatrixTheme
import logging

logger = logging.getLogger(__name__)


class ModelSelectorWidget(QWidget):
    """
    Widget for selecting and switching LMStudio models
    """
    
    model_switch_requested = pyqtSignal(str)  # Emitted when user wants to switch models
    refresh_requested = pyqtSignal()  # Emitted when user wants to refresh model list
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_model = None
        self.available_models = []
        self.model_details = {}
        
        self.setup_ui()
        self.apply_matrix_theme()
        
        # Auto-refresh timer (reduced frequency to avoid lag)
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_requested.emit)
        self.refresh_timer.start(300000)  # Refresh every 5 minutes to reduce lag
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # Model Selection Group
        model_group = QGroupBox("LMStudio Model Selection")
        model_layout = QVBoxLayout(model_group)
        
        # Model selector row
        selector_layout = QHBoxLayout()
        
        # Model dropdown
        self.model_label = QLabel("Current Model:")
        self.model_combo = QComboBox()
        self.model_combo.setMinimumWidth(300)
        self.model_combo.currentTextChanged.connect(self.on_model_selected)
        
        # Refresh button
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.setMaximumWidth(80)
        self.refresh_btn.clicked.connect(self.refresh_requested.emit)
        
        selector_layout.addWidget(self.model_label)
        selector_layout.addWidget(self.model_combo)
        selector_layout.addWidget(self.refresh_btn)
        selector_layout.addStretch()
        
        model_layout.addLayout(selector_layout)
        
        # Model info display
        self.model_info = QTextEdit()
        self.model_info.setMaximumHeight(100)
        self.model_info.setReadOnly(True)
        self.model_info.setPlaceholderText("Model information will appear here...")
        
        model_layout.addWidget(QLabel("Model Information:"))
        model_layout.addWidget(self.model_info)
        
        # Status row
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("Status: Not connected")
        self.model_count_label = QLabel("Models: 0")
        
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.model_count_label)
        
        model_layout.addLayout(status_layout)
        
        layout.addWidget(model_group)
        layout.addStretch()
    
    def apply_matrix_theme(self):
        """Apply Matrix theme styling"""
        # Main widget styling
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {MatrixTheme.GREEN};
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: {MatrixTheme.BLACK};
                color: {MatrixTheme.GREEN};
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: {MatrixTheme.GREEN};
            }}
            
            QLabel {{
                color: {MatrixTheme.GREEN};
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }}
            
            QComboBox {{
                background-color: {MatrixTheme.BLACK};
                border: 1px solid {MatrixTheme.GREEN};
                border-radius: 4px;
                padding: 5px;
                color: {MatrixTheme.GREEN};
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                min-height: 20px;
            }}
            
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {MatrixTheme.GREEN};
                margin-right: 5px;
            }}
            
            QComboBox QAbstractItemView {{
                background-color: {MatrixTheme.BLACK};
                border: 1px solid {MatrixTheme.GREEN};
                selection-background-color: {MatrixTheme.GREEN};
                selection-color: {MatrixTheme.BLACK};
                color: {MatrixTheme.GREEN};
            }}
            
            QPushButton {{
                background-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.GREEN};
                border-radius: 4px;
                padding: 5px 10px;
                color: {MatrixTheme.GREEN};
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: {MatrixTheme.GREEN};
                color: {MatrixTheme.BLACK};
            }}
            
            QPushButton:pressed {{
                background-color: {MatrixTheme.BRIGHT_GREEN};
                color: {MatrixTheme.BLACK};
            }}
            
            QTextEdit {{
                background-color: {MatrixTheme.BLACK};
                border: 1px solid {MatrixTheme.GREEN};
                border-radius: 4px;
                color: {MatrixTheme.GREEN};
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                padding: 5px;
            }}
        """)
    
    def update_models(self, models: list):
        """
        Update the available models list
        
        Args:
            models: List of available model names
        """
        self.available_models = models
        
        # Update combo box
        current_text = self.model_combo.currentText()
        self.model_combo.clear()
        
        if models:
            self.model_combo.addItems(models)
            
            # Restore previous selection if still available
            if current_text in models:
                self.model_combo.setCurrentText(current_text)
        
        # Update status
        self.model_count_label.setText(f"Models: {len(models)}")
        
        if models:
            self.status_label.setText("Status: Connected")
            self.status_label.setStyleSheet(f"color: {MatrixTheme.GREEN};")
        else:
            self.status_label.setText("Status: No models found")
            self.status_label.setStyleSheet(f"color: {MatrixTheme.YELLOW};")
    
    def set_current_model(self, model_name: str):
        """
        Set the currently selected model
        
        Args:
            model_name: Name of the current model
        """
        self.current_model = model_name
        
        if model_name in self.available_models:
            self.model_combo.setCurrentText(model_name)
            self.update_model_info(model_name)
    
    def update_model_info(self, model_name: str):
        """
        Update the model information display
        
        Args:
            model_name: Name of the model to show info for
        """
        if not model_name:
            self.model_info.clear()
            return
        
        # Basic model info
        info_text = f"Model: {model_name}\n"
        info_text += f"Status: Active\n"
        
        # Add any additional details if available
        if hasattr(self, 'model_details') and model_name in self.model_details:
            details = self.model_details[model_name]
            info_text += f"Type: {details.get('object', 'Unknown')}\n"
            info_text += f"Owner: {details.get('owned_by', 'Unknown')}\n"
        
        info_text += f"Available for inference: Yes"
        
        self.model_info.setPlainText(info_text)
    
    def on_model_selected(self, model_name: str):
        """
        Handle model selection from dropdown
        
        Args:
            model_name: Selected model name
        """
        if model_name and model_name != self.current_model:
            logger.info(f"User selected model: {model_name}")
            self.model_switch_requested.emit(model_name)
            self.update_model_info(model_name)
    
    def set_connection_status(self, connected: bool):
        """
        Update connection status display
        
        Args:
            connected: Whether LMStudio is connected
        """
        if connected:
            self.status_label.setText("Status: Connected")
            self.status_label.setStyleSheet(f"color: {MatrixTheme.GREEN};")
        else:
            self.status_label.setText("Status: Disconnected")
            self.status_label.setStyleSheet(f"color: {MatrixTheme.RED};")
    
    def get_selected_model(self) -> str:
        """
        Get the currently selected model

        Returns:
            Selected model name
        """
        return self.model_combo.currentText()

    def set_auto_refresh_enabled(self, enabled: bool):
        """Enable or disable auto-refresh to reduce system lag"""
        if enabled:
            if not self.refresh_timer.isActive():
                self.refresh_timer.start(300000)  # 5 minutes
        else:
            self.refresh_timer.stop()

    def stop_auto_refresh(self):
        """Stop auto-refresh completely to improve performance"""
        self.refresh_timer.stop()
