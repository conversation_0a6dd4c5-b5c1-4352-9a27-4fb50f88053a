"""
LLaMA Integration Component
This module handles interaction with the LLaMA model.
"""
import os
import subprocess
import logging
import random
from pathlib import Path
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Default configuration values
USE_MOCK_LLAMA = False  # Default to not using mock
USE_CHATGPT = True  # Default to using ChatGPT
LLAMA_MODEL_PATH = "models/llama-2-7b-chat.gguf"
LLAMA_GUARD_PATH = "models/llama-guard.gguf"
LLAMA_BIN_PATH = "llama.cpp/main"

# OpenAI API key
OPENAI_API_KEY = ""

# Try to import from config
try:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from config.config import USE_MOCK_LLAMA, LLAMA_MODEL_PATH, LLAMA_GUARD_PATH, LLAM<PERSON>_BIN_PATH
    from config.config import MODELS_DIR

    # Try to import OpenAI API key from models_config.yaml
    try:
        import yaml
        models_config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'models_config.yaml')
        if os.path.exists(models_config_path):
            with open(models_config_path, 'r') as f:
                models_config = yaml.safe_load(f)
                if 'api_keys' in models_config and 'openai' in models_config['api_keys']:
                    OPENAI_API_KEY = models_config['api_keys']['openai']
                    logger.info("Loaded OpenAI API key from models_config.yaml")
                    USE_CHATGPT = True
    except Exception as e:
        logger.warning(f"Error loading OpenAI API key: {e}")

    # Convert relative paths to absolute if they're not already
    if not os.path.isabs(LLAMA_MODEL_PATH):
        LLAMA_MODEL_PATH = str(MODELS_DIR / os.path.basename(LLAMA_MODEL_PATH))

    if not os.path.isabs(LLAMA_GUARD_PATH):
        LLAMA_GUARD_PATH = str(MODELS_DIR / os.path.basename(LLAMA_GUARD_PATH))

    logger.info(f"Using model path: {LLAMA_MODEL_PATH}")
    logger.info(f"Using guard path: {LLAMA_GUARD_PATH}")
    logger.info(f"Using binary path: {LLAMA_BIN_PATH}")
except ImportError:
    logger.warning("Could not import LLaMA configuration from config. Using default values.")

class LlamaRunner:
    """
    Class to handle running inference with the LLaMA model.
    """
    def __init__(self, model_path=None, llama_bin_path=None, context_size=2048, max_tokens=512):
        """
        Initialize the LLaMA runner.

        Args:
            model_path: Path to the LLaMA model file
            llama_bin_path: Path to the llama.cpp binary
            context_size: Context size for the model
            max_tokens: Maximum tokens to generate
        """
        # Set default paths if not provided
        self.model_path = model_path or LLAMA_MODEL_PATH
        self.llama_bin_path = llama_bin_path or LLAMA_BIN_PATH

        # Set path to Llama Guard model for content moderation
        self.guard_model_path = LLAMA_GUARD_PATH

        # Ensure the model path is absolute
        self.model_path = str(Path(self.model_path).resolve())

        # Model parameters
        self.context_size = context_size
        self.max_tokens = max_tokens

        # Check if the model file exists
        if not os.path.exists(self.model_path):
            logger.warning(f"Model file not found at {self.model_path}")
            logger.info("You'll need to download a LLaMA model and place it in the models directory.")

        # Check if the llama.cpp binary exists
        if not os.path.exists(self.llama_bin_path):
            logger.warning(f"llama.cpp binary not found at {self.llama_bin_path}")
            logger.info("You'll need to compile llama.cpp and set the correct path.")

    def run_inference(self, prompt, temperature=0.7):
        """
        Run inference with the LLaMA model.

        Args:
            prompt: The prompt to send to the model
            temperature: Temperature for sampling

        Returns:
            str: Model's response
        """
        # First try LMStudio if available
        try:
            from .lmstudio_runner import LMStudioRunner
            lmstudio = LMStudioRunner()
            if lmstudio.test_connection():
                logger.info("Using LMStudio for inference")
                return lmstudio.run_inference(prompt, temperature)
        except Exception as e:
            logger.warning(f"LMStudio not available: {e}")

        # Check if we should use ChatGPT
        if USE_CHATGPT and OPENAI_API_KEY:
            logger.info("Using ChatGPT for inference")
            return self.run_inference_chatgpt(prompt, temperature)

        # Check if we should use the mock implementation
        if USE_MOCK_LLAMA or not os.path.exists(self.model_path) or not os.path.exists(self.llama_bin_path):
            logger.info("Using mock LLaMA implementation (configured or model/binary not found)")
            return self.run_inference_mock(prompt)

        try:
            # Create a temporary file for the prompt
            prompt_file = Path("temp_prompt.txt")
            with open(prompt_file, "w", encoding="utf-8") as f:
                f.write(prompt)

            # Build the command
            cmd = [
                self.llama_bin_path,
                "-m", self.model_path,
                "-f", str(prompt_file),
                "--ctx-size", str(self.context_size),
                "-n", str(self.max_tokens),
                "--temp", str(temperature),
                "--repeat-penalty", "1.1",
                "--threads", "4",
                "--no-mmap"
            ]

            logger.info(f"Running LLaMA with command: {' '.join(cmd)}")

            # Run the command
            result = subprocess.run(cmd, capture_output=True, text=True)

            # Clean up the temporary file
            if prompt_file.exists():
                prompt_file.unlink()

            # Check for errors
            if result.returncode != 0:
                logger.error(f"LLaMA inference failed with error: {result.stderr}")
                return f"Error running LLaMA: {result.stderr}"

            # Extract the response (remove the prompt from the output)
            response = result.stdout
            if prompt in response:
                response = response.split(prompt, 1)[1]

            return response.strip()

        except Exception as e:
            logger.exception(f"Error running LLaMA inference: {e}")
            return f"Error: {str(e)}"

    def run_content_moderation(self, content):
        """
        Run content moderation using Llama Guard.

        Args:
            content: The content to moderate

        Returns:
            tuple: (is_safe, explanation)
        """
        # Check if we should use the mock implementation
        if USE_MOCK_LLAMA:
            logger.info("Using mock content moderation")
            return True, "Content is safe (mock)"

        try:
            # Create a temporary file for the content
            content_file = Path("temp_content.txt")
            with open(content_file, "w", encoding="utf-8") as f:
                f.write(content)

            # Build the command for Llama Guard
            cmd = [
                self.llama_bin_path,
                "-m", self.guard_model_path,
                "-f", str(content_file),
                "--ctx-size", "2048",
                "-n", "256",
                "--temp", "0.1",
                "--threads", "4",
                "--no-mmap"
            ]

            logger.info(f"Running Llama Guard with command: {' '.join(cmd)}")

            # Run the command
            result = subprocess.run(cmd, capture_output=True, text=True)

            # Clean up the temporary file
            if content_file.exists():
                content_file.unlink()

            # Check for errors
            if result.returncode != 0:
                logger.error(f"Llama Guard inference failed with error: {result.stderr}")
                return True, f"Error running Llama Guard: {result.stderr}"

            # Parse the output
            output = result.stdout

            # Simple parsing of Llama Guard output
            is_safe = "unsafe" not in output.lower()
            explanation = output.strip()

            return is_safe, explanation

        except Exception as e:
            logger.exception(f"Error running Llama Guard: {e}")
            return True, f"Error running Llama Guard: {e}"

    def run_inference_mock(self, prompt):
        """
        Enhanced mock implementation that simulates a real LLaMA model.

        Args:
            prompt: The prompt to send to the model

        Returns:
            str: Simulated LLaMA response
        """
        logger.info("Running mock LLaMA inference")

        # Default decision and explanation
        decision = "WAIT"
        explanation = "Not enough clear signals to make a trade."

        # Extract market data from the prompt
        market_data = {}
        lines = prompt.split('\n')
        for line in lines:
            if ':' in line and '-' in line[:2]:
                parts = line[2:].split(':', 1)
                if len(parts) == 2:
                    key = parts[0].strip().lower()
                    value = parts[1].strip()
                    market_data[key] = value

        # Analyze RSI
        rsi_signal = 'neutral'
        if 'rsi' in market_data:
            rsi_value = float(market_data['rsi'].split()[0]) if ' ' in market_data['rsi'] else float(market_data['rsi'])
            if rsi_value > 70:
                rsi_signal = 'overbought'
            elif rsi_value < 30:
                rsi_signal = 'oversold'

        # Analyze MACD
        macd_signal = 'neutral'
        if 'macd_cross' in market_data:
            if 'bullish' in market_data['macd_cross'].lower():
                macd_signal = 'bullish'
            elif 'bearish' in market_data['macd_cross'].lower():
                macd_signal = 'bearish'

        # Analyze VWAP distance
        vwap_signal = 'neutral'
        if 'vwap_dist' in market_data:
            vwap_text = market_data['vwap_dist'].lower()
            if 'above' in vwap_text:
                vwap_value = float(vwap_text.split('%')[0])
                if vwap_value > 2:
                    vwap_signal = 'overbought'
            elif 'below' in vwap_text:
                vwap_value = float(vwap_text.split('%')[0])
                if vwap_value < -2:
                    vwap_signal = 'oversold'

        # Analyze order imbalance
        imbalance_signal = 'neutral'
        if 'order_imbalance' in market_data:
            imbalance_text = market_data['order_imbalance'].lower()
            if 'bid-heavy' in imbalance_text:
                imbalance_signal = 'bullish'
            elif 'ask-heavy' in imbalance_text:
                imbalance_signal = 'bearish'

        # Analyze recent trades
        trade_signal = 'neutral'
        if 'last_10_trades' in market_data:
            if 'mostly buys' in market_data['last_10_trades'].lower():
                trade_signal = 'bullish'
            elif 'mostly sells' in market_data['last_10_trades'].lower():
                trade_signal = 'bearish'

        # Check for live trades data
        mode = 'historical'
        if 'mode' in market_data:
            mode = market_data['mode'].lower()

        # Analyze buy ratio divergence in live mode
        buy_ratio_divergence_signal = 'neutral'
        if mode == 'live' and 'buy_ratio_divergence' in market_data:
            try:
                buy_ratio_divergence = float(market_data['buy_ratio_divergence'])
                if buy_ratio_divergence > 0.15:
                    buy_ratio_divergence_signal = 'futures_bullish'
                elif buy_ratio_divergence < -0.15:
                    buy_ratio_divergence_signal = 'spot_bullish'
            except:
                pass

        # Analyze trade frequency in live mode
        trade_frequency_signal = 'neutral'
        if mode == 'live' and 'trade_frequency_ratio' in market_data:
            try:
                trade_frequency_ratio = float(market_data['trade_frequency_ratio'])
                if trade_frequency_ratio > 1.5:
                    trade_frequency_signal = 'futures_active'
                elif trade_frequency_ratio < 0.67:
                    trade_frequency_signal = 'spot_active'
            except:
                pass

        # Count bullish and bearish signals
        bullish_signals = sum(1 for signal in [rsi_signal, macd_signal, vwap_signal, imbalance_signal, trade_signal]
                             if signal in ['bullish', 'oversold'])
        bearish_signals = sum(1 for signal in [rsi_signal, macd_signal, vwap_signal, imbalance_signal, trade_signal]
                             if signal in ['bearish', 'overbought'])

        # Add live trades signals if available
        if mode == 'live':
            # Add buy ratio divergence signals
            if buy_ratio_divergence_signal == 'spot_bullish':
                bullish_signals += 1
            elif buy_ratio_divergence_signal == 'futures_bullish':
                bearish_signals += 1  # Futures having more buys than spot can be a reversal signal

            # Add trade frequency signals
            if trade_frequency_signal == 'spot_active' and rsi_signal == 'oversold':
                bullish_signals += 1
            elif trade_frequency_signal == 'futures_active' and rsi_signal == 'overbought':
                bearish_signals += 1

        # Calculate confidence based on signal strength
        total_signals = bullish_signals + bearish_signals
        max_signals = 5  # Maximum number of signals we're checking

        # Calculate confidence (50% is neutral, higher for stronger signals)
        if total_signals > 0:
            if bullish_signals > bearish_signals:
                confidence = 50 + (bullish_signals / max_signals) * 50
                # Add a small random variation to make it look more realistic
                confidence += random.uniform(-0.5, 0.5)
                confidence = min(confidence, 95)  # Cap at 95%
            elif bearish_signals > bullish_signals:
                confidence = 50 + (bearish_signals / max_signals) * 50
                # Add a small random variation to make it look more realistic
                confidence += random.uniform(-0.5, 0.5)
                confidence = min(confidence, 95)  # Cap at 95%
            else:
                confidence = 50 + random.uniform(-0.5, 0.5)  # Neutral with slight variation
        else:
            confidence = 50 + random.uniform(-0.5, 0.5)  # Neutral with slight variation

        # Calculate take profit and stop loss based on volatility
        volatility = 1.0  # Default volatility
        if 'atr' in market_data:
            try:
                current_price = 100  # Default price if not available
                if 'close' in market_data:
                    current_price = float(market_data['close'])
                atr_value = float(market_data['atr'])
                volatility = (atr_value / current_price) * 100  # Convert to percentage
            except:
                pass

        # Set take profit and stop loss based on volatility
        take_profit = max(1.5, volatility * 2)  # At least 1.5%
        stop_loss = max(1.0, volatility * 1.5)  # At least 1.0%

        # Make a decision based on the signals
        if bullish_signals > bearish_signals and bullish_signals >= 2:
            decision = "LONG"
            explanation = f"Multiple bullish signals detected: "
            if rsi_signal in ['oversold', 'bullish']:
                explanation += "RSI indicates oversold conditions. "
            if macd_signal == 'bullish':
                explanation += "MACD shows bullish momentum. "
            if vwap_signal == 'oversold':
                explanation += "Price is significantly below VWAP. "
            if imbalance_signal == 'bullish':
                explanation += "Order book shows buying pressure. "
            if trade_signal == 'bullish':
                explanation += "Recent trades show buying activity. "
            if mode == 'live':
                if buy_ratio_divergence_signal == 'spot_bullish':
                    explanation += "Spot market has higher buy ratio than futures, suggesting potential upward movement. "
                if trade_frequency_signal == 'spot_active' and rsi_signal == 'oversold':
                    explanation += "Spot market is more active while price is oversold, indicating accumulation. "
            explanation += "These factors suggest upward price movement."
        elif bearish_signals > bullish_signals and bearish_signals >= 2:
            decision = "SHORT"
            explanation = f"Multiple bearish signals detected: "
            if rsi_signal in ['overbought', 'bearish']:
                explanation += "RSI indicates overbought conditions. "
            if macd_signal == 'bearish':
                explanation += "MACD shows bearish momentum. "
            if vwap_signal == 'overbought':
                explanation += "Price is significantly above VWAP. "
            if imbalance_signal == 'bearish':
                explanation += "Order book shows selling pressure. "
            if trade_signal == 'bearish':
                explanation += "Recent trades show selling activity. "
            if mode == 'live':
                if buy_ratio_divergence_signal == 'futures_bullish':
                    explanation += "Futures market has higher buy ratio than spot, suggesting potential reversal. "
                if trade_frequency_signal == 'futures_active' and rsi_signal == 'overbought':
                    explanation += "Futures market is more active while price is overbought, indicating distribution. "
            explanation += "These factors suggest downward price movement."
        else:
            decision = "WAIT"
            explanation = "Mixed or insufficient signals. "
            if bullish_signals == bearish_signals and bullish_signals > 0:
                explanation += f"Equal bullish and bearish signals ({bullish_signals} each). "
            elif bullish_signals > 0 and bearish_signals > 0:
                explanation += f"Some bullish signals ({bullish_signals}) and some bearish signals ({bearish_signals}), but not enough conviction. "
            else:
                explanation += "No strong signals detected. "
            explanation += "Better to wait for clearer market direction."

        # Format the response in the expected format
        response = f"DECISION: {decision}\n"
        response += f"CONFIDENCE: {confidence:.2f}%\n"
        response += f"TAKE_PROFIT: {take_profit:.2f}%\n"
        response += f"STOP_LOSS: {stop_loss:.2f}%\n"
        response += f"EXPLANATION: {explanation}"

        return response

    def run_inference_chatgpt(self, prompt, temperature=0.7):
        """Run inference using OpenAI's ChatGPT API"""
        try:
            from openai import OpenAI

            # Initialize the client
            client = OpenAI(api_key=OPENAI_API_KEY)

            # Call the OpenAI API
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",  # Use GPT-3.5-Turbo instead of GPT-4
                messages=[
                    {"role": "system", "content": "You are a trading assistant analyzing market data. Provide your analysis in the following format:\n\nDECISION: [LONG/SHORT/WAIT]\nCONFIDENCE: [50-100]%\nTAKE_PROFIT: [percentage]%\nSTOP_LOSS: [percentage]%\n\nEXPLANATION: [Your detailed analysis]\n\nMake sure to include all these elements in your response."},
                    {"role": "user", "content": prompt}
                ],
                temperature=temperature,
                max_tokens=1024
            )

            # Extract the response text
            response_text = response.choices[0].message.content

            logger.info("Successfully got response from ChatGPT")
            return response_text

        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error using ChatGPT API: {error_msg}")

            # Check for specific error types
            if "insufficient_quota" in error_msg or "exceeded your current quota" in error_msg:
                logger.warning("OpenAI API quota exceeded. Please check your billing details or use a different API key.")
            elif "model_not_found" in error_msg or "does not exist or you do not have access" in error_msg:
                logger.warning("The specified model is not available with your API key. Try using a different model.")

            # Fall back to mock implementation
            logger.info("Falling back to mock implementation")
            return self.run_inference_mock(prompt)
