#!/usr/bin/env python3
"""
Comprehensive GUI Test Suite for Epinnox v6
Tests all GUI components, interactions, and functionality
"""

import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveGUITestSuite:
    """Comprehensive test suite for all GUI components"""
    
    def __init__(self):
        self.test_results = {}
        self.failed_tests = []
        self.pyqt_available = False
        self.app = None
        
    def setup_test_environment(self):
        """Setup the test environment"""
        print("🔧 SETTING UP GUI TEST ENVIRONMENT")
        print("=" * 50)
        
        # Test PyQt5 availability
        try:
            from PyQt5.QtWidgets import QApplication, QWidget, <PERSON><PERSON>ush<PERSON>utton, <PERSON><PERSON><PERSON><PERSON>, QT<PERSON>Widget, QTabWidget
            from PyQt5.QtCore import QTimer, Qt, pyqtSignal
            from PyQt5.QtGui import QFont, QIcon
            
            self.pyqt_available = True
            print("   ✅ PyQt5 imported successfully")
            
            # Create QApplication
            self.app = QApplication.instance()
            if self.app is None:
                self.app = QApplication([])
            print("   ✅ QApplication created")
            
            return True
            
        except ImportError as e:
            print(f"   ❌ PyQt5 not available: {e}")
            self.pyqt_available = False
            return False
    
    def test_main_window_components(self):
        """Test main window and core components"""
        print("\n🏠 TESTING MAIN WINDOW COMPONENTS")
        print("-" * 40)
        
        if not self.pyqt_available:
            print("   ⚠️ Skipping - PyQt5 not available")
            return False
        
        try:
            from gui.main_window import TradingSystemGUI
            
            # Create main window
            main_window = TradingSystemGUI()
            print("   ✅ Main window created successfully")
            
            # Test window properties
            window_title = main_window.windowTitle()
            print(f"   ✅ Window title: {window_title}")
            
            # Test core components
            core_components = [
                ('tab_widget', 'Tab Widget'),
                ('start_button', 'Start Button'),
                ('stop_button', 'Stop Button'),
                ('status_label', 'Status Label')
            ]
            
            component_count = 0
            for attr_name, display_name in core_components:
                if hasattr(main_window, attr_name):
                    print(f"   ✅ {display_name} available")
                    component_count += 1
                else:
                    print(f"   ❌ {display_name} missing")
            
            # Test button functionality
            if hasattr(main_window, 'start_button'):
                main_window.start_button.click()
                print("   ✅ Start button click test passed")
            
            if hasattr(main_window, 'stop_button'):
                main_window.stop_button.click()
                print("   ✅ Stop button click test passed")
            
            main_window.close()
            
            self.test_results['main_window'] = {
                'status': 'PASSED',
                'components_found': component_count,
                'total_components': len(core_components)
            }
            
            return True
            
        except Exception as e:
            print(f"   ❌ Main window test failed: {e}")
            self.failed_tests.append(f"Main Window: {e}")
            self.test_results['main_window'] = {'status': 'FAILED', 'error': str(e)}
            return False
    
    def test_trading_tabs(self):
        """Test all trading tab components"""
        print("\n📑 TESTING TRADING TAB COMPONENTS")
        print("-" * 40)
        
        if not self.pyqt_available:
            print("   ⚠️ Skipping - PyQt5 not available")
            return False
        
        tab_tests = [
            ('gui.live_trading_tab', 'LiveTradingTab', 'Live Trading Tab'),
            ('gui.auto_trader_tab', 'AutoTraderTab', 'Auto Trader Tab'),
            ('gui.manual_trader_tab', 'ManualTraderTab', 'Manual Trader Tab'),
            ('gui.performance_dashboard_tab', 'PerformanceDashboardTab', 'Performance Dashboard Tab'),
            ('gui.scalping_scanner_tab', 'ScalpingScannerTab', 'Scalping Scanner Tab'),
            ('gui.settings_tab', 'SettingsTab', 'Settings Tab')
        ]
        
        successful_tabs = 0
        
        for module_name, class_name, display_name in tab_tests:
            try:
                module = __import__(module_name, fromlist=[class_name])
                tab_class = getattr(module, class_name)
                tab_instance = tab_class()
                
                print(f"   ✅ {display_name} created successfully")
                
                # Test tab-specific functionality
                if hasattr(tab_instance, 'setup_ui'):
                    print(f"   ✅ {display_name} has setup_ui method")
                
                if hasattr(tab_instance, 'close'):
                    tab_instance.close()
                
                successful_tabs += 1
                
            except Exception as e:
                print(f"   ❌ {display_name} failed: {e}")
                self.failed_tests.append(f"{display_name}: {e}")
        
        self.test_results['trading_tabs'] = {
            'status': 'PASSED' if successful_tabs == len(tab_tests) else 'PARTIAL',
            'successful_tabs': successful_tabs,
            'total_tabs': len(tab_tests)
        }
        
        return successful_tabs > 0
    
    def test_widget_interactions(self):
        """Test basic widget interactions"""
        print("\n🔄 TESTING WIDGET INTERACTIONS")
        print("-" * 40)
        
        if not self.pyqt_available:
            print("   ⚠️ Skipping - PyQt5 not available")
            return False
        
        try:
            from PyQt5.QtWidgets import QPushButton, QLabel, QTableWidget, QLineEdit, QCheckBox
            
            # Test button interactions
            test_button = QPushButton("Test Button")
            clicked = False
            
            def on_click():
                nonlocal clicked
                clicked = True
            
            test_button.clicked.connect(on_click)
            test_button.click()
            
            if clicked:
                print("   ✅ Button click handling works")
            else:
                print("   ❌ Button click handling failed")
                self.failed_tests.append("Button click handling")
            
            # Test label updates
            test_label = QLabel("Initial Text")
            test_label.setText("Updated Text")
            
            if test_label.text() == "Updated Text":
                print("   ✅ Label text updates work")
            else:
                print("   ❌ Label text updates failed")
                self.failed_tests.append("Label text updates")
            
            # Test input controls
            test_input = QLineEdit()
            test_input.setText("Test Input")
            
            if test_input.text() == "Test Input":
                print("   ✅ Input control text setting works")
            else:
                print("   ❌ Input control text setting failed")
                self.failed_tests.append("Input control text setting")
            
            # Test checkbox
            test_checkbox = QCheckBox("Test Checkbox")
            test_checkbox.setChecked(True)
            
            if test_checkbox.isChecked():
                print("   ✅ Checkbox state setting works")
            else:
                print("   ❌ Checkbox state setting failed")
                self.failed_tests.append("Checkbox state setting")
            
            # Test table operations
            test_table = QTableWidget(3, 3)
            test_table.setRowCount(5)
            test_table.setColumnCount(4)
            
            if test_table.rowCount() == 5 and test_table.columnCount() == 4:
                print("   ✅ Table operations work")
            else:
                print("   ❌ Table operations failed")
                self.failed_tests.append("Table operations")
            
            self.test_results['widget_interactions'] = {
                'status': 'PASSED' if len(self.failed_tests) == 0 else 'PARTIAL',
                'tests_run': 5
            }
            
            return True
            
        except Exception as e:
            print(f"   ❌ Widget interaction test failed: {e}")
            self.failed_tests.append(f"Widget interactions: {e}")
            self.test_results['widget_interactions'] = {'status': 'FAILED', 'error': str(e)}
            return False
    
    def test_matrix_theme_integration(self):
        """Test Matrix theme integration"""
        print("\n🎨 TESTING MATRIX THEME INTEGRATION")
        print("-" * 40)
        
        try:
            from gui.matrix_theme import MatrixTheme
            
            # Test theme properties
            theme_properties = [
                'PRIMARY_COLOR', 'SECONDARY_COLOR', 'BACKGROUND_COLOR',
                'TEXT_COLOR', 'GREEN', 'RED', 'YELLOW'
            ]
            
            property_count = 0
            for prop in theme_properties:
                if hasattr(MatrixTheme, prop):
                    value = getattr(MatrixTheme, prop)
                    print(f"   ✅ {prop}: {value}")
                    property_count += 1
                else:
                    print(f"   ❌ {prop} missing")
            
            self.test_results['matrix_theme'] = {
                'status': 'PASSED' if property_count == len(theme_properties) else 'PARTIAL',
                'properties_found': property_count,
                'total_properties': len(theme_properties)
            }
            
            return property_count > 0
            
        except Exception as e:
            print(f"   ❌ Matrix theme test failed: {e}")
            self.failed_tests.append(f"Matrix theme: {e}")
            self.test_results['matrix_theme'] = {'status': 'FAILED', 'error': str(e)}
            return False
    
    def generate_comprehensive_report(self):
        """Generate comprehensive test report"""
        print("\n📊 COMPREHENSIVE GUI TEST REPORT")
        print("=" * 60)
        
        # Calculate overall statistics
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('status') == 'PASSED')
        partial_tests = sum(1 for result in self.test_results.values() if result.get('status') == 'PARTIAL')
        failed_tests = sum(1 for result in self.test_results.values() if result.get('status') == 'FAILED')
        
        print(f"\n🎯 OVERALL STATISTICS:")
        print(f"   📈 Total Test Categories: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   🟡 Partial: {partial_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        
        if total_tests > 0:
            success_rate = ((passed_tests + partial_tests * 0.5) / total_tests) * 100
            print(f"   📊 Success Rate: {success_rate:.1f}%")
        
        # Detailed results
        print(f"\n📋 DETAILED TEST RESULTS:")
        for test_name, result in self.test_results.items():
            status = result.get('status', 'UNKNOWN')
            status_icon = {'PASSED': '✅', 'PARTIAL': '🟡', 'FAILED': '❌'}.get(status, '❓')
            print(f"   {status_icon} {test_name.replace('_', ' ').title()}: {status}")
            
            if 'error' in result:
                print(f"      Error: {result['error']}")
        
        # Failed tests summary
        if self.failed_tests:
            print(f"\n❌ FAILED TESTS SUMMARY:")
            for i, failed_test in enumerate(self.failed_tests, 1):
                print(f"   {i}. {failed_test}")
        
        # System readiness
        print(f"\n🚀 GUI SYSTEM READINESS:")
        if passed_tests == total_tests:
            readiness = "🟢 FULLY READY"
        elif passed_tests + partial_tests >= total_tests * 0.8:
            readiness = "🟡 MOSTLY READY"
        else:
            readiness = "🔴 NEEDS ATTENTION"
        
        print(f"   🎯 Status: {readiness}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if not self.pyqt_available:
            print("   🔧 Install PyQt5 for full GUI functionality")
        
        if self.failed_tests:
            print("   🔧 Address failed test issues before production deployment")
        
        if passed_tests == total_tests:
            print("   🎉 All GUI components are fully operational!")
        
        return passed_tests == total_tests
    
    def run_all_tests(self):
        """Run all GUI tests"""
        print("🧪 COMPREHENSIVE GUI TEST SUITE")
        print("=" * 60)
        
        # Setup test environment
        if not self.setup_test_environment():
            print("❌ Test environment setup failed")
            return False
        
        # Run all test categories
        self.test_main_window_components()
        self.test_trading_tabs()
        self.test_widget_interactions()
        self.test_matrix_theme_integration()
        
        # Generate comprehensive report
        success = self.generate_comprehensive_report()
        
        return success

def main():
    """Main test execution"""
    test_suite = ComprehensiveGUITestSuite()
    success = test_suite.run_all_tests()
    
    print(f"\n🎯 FINAL RESULT: {'✅ ALL TESTS PASSED' if success else '❌ SOME TESTS FAILED'}")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
