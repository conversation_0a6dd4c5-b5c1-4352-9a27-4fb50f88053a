
class TradingContext:
    """Proper TradingContext class with all required attributes"""
    
    def __init__(self, symbol='BTC/USDT:USDT', current_price=0.0, account_balance=1000.0):
        # Basic attributes
        self.symbol = symbol
        self.current_price = current_price
        self.account_balance = account_balance
        
        # Market data attributes
        self.market_data = {
            'symbol': symbol,
            'price': current_price,
            'volume': 1000000,
            'spread': 0.1
        }
        
        # Emergency and safety attributes
        self.emergency_flags = {}
        
        # Position attributes
        self.open_positions = []
        
        # Performance attributes
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0
        }
        
        # Additional attributes that might be accessed
        self.volume_24h = 1000000
        self.spread = 0.1
        self.avg_volume = 1000000
        self.recent_prices = []
        self.news_pending = False
    
    def to_dict(self):
        """Convert to dictionary for compatibility"""
        return {
            'symbol': self.symbol,
            'current_price': self.current_price,
            'account_balance': self.account_balance,
            'market_data': self.market_data,
            'emergency_flags': self.emergency_flags,
            'open_positions': self.open_positions,
            'performance_metrics': self.performance_metrics
        }
    
    @classmethod
    def from_dict(cls, data):
        """Create from dictionary"""
        context = cls(
            symbol=data.get('symbol', 'BTC/USDT:USDT'),
            current_price=data.get('current_price', 0.0),
            account_balance=data.get('account_balance', 1000.0)
        )
        
        # Update with additional data
        if 'market_data' in data:
            context.market_data.update(data['market_data'])
        if 'emergency_flags' in data:
            context.emergency_flags.update(data['emergency_flags'])
        if 'open_positions' in data:
            context.open_positions = data['open_positions']
        if 'performance_metrics' in data:
            context.performance_metrics.update(data['performance_metrics'])
        
        return context
