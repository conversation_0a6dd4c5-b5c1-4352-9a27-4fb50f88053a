# Testing requirements for EPINNOX v6

# Core testing framework
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
pytest-cov>=4.0.0
pytest-timeout>=2.1.0

# Data manipulation and analysis
pandas>=1.5.0
numpy>=1.21.0

# Optional ML/RL dependencies (for full functionality)
# Uncomment if you want full RL capabilities
# torch>=1.12.0
# stable-baselines3>=1.6.0
# gym>=0.21.0

# Database
sqlite3  # Built into Python

# Utilities
python-dateutil>=2.8.0
pytz>=2022.1

# Development tools
black>=22.0.0
flake8>=5.0.0
isort>=5.10.0

# Documentation
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0
