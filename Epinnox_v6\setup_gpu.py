#!/usr/bin/env python3
"""
GPU Setup Script for EPINNOX v6
Configure GPU acceleration for faster ML processing
"""

import subprocess
import sys
import platform
import os
from typing import Dict, List, Tuple

def run_command(command: str, description: str = "") -> Tuple[bool, str]:
    """Run a shell command and return success status and output"""
    try:
        print(f"🔄 {description or command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print(f"✅ Success: {description or command}")
            return True, result.stdout
        else:
            print(f"❌ Failed: {description or command}")
            print(f"Error: {result.stderr}")
            return False, result.stderr
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout: {description or command}")
        return False, "Command timed out"
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False, str(e)

def detect_gpu():
    """Detect available GPU hardware"""
    gpu_info = {
        'nvidia': False,
        'amd': False,
        'intel': False,
        'nvidia_driver': None,
        'cuda_version': None
    }
    
    print("🔍 Detecting GPU hardware...")
    
    # Check for NVIDIA GPU
    try:
        success, output = run_command("nvidia-smi --query-gpu=name,driver_version --format=csv,noheader,nounits", "Checking NVIDIA GPU")
        if success and output.strip():
            gpu_info['nvidia'] = True
            lines = output.strip().split('\n')
            if lines:
                parts = lines[0].split(', ')
                if len(parts) >= 2:
                    gpu_info['nvidia_driver'] = parts[1]
            print(f"✅ NVIDIA GPU detected: {lines[0] if lines else 'Unknown'}")
        else:
            print("❌ No NVIDIA GPU detected")
    except:
        print("❌ nvidia-smi not available")
    
    # Check CUDA version
    if gpu_info['nvidia']:
        try:
            success, output = run_command("nvcc --version", "Checking CUDA version")
            if success:
                for line in output.split('\n'):
                    if 'release' in line.lower():
                        # Extract version from line like "Cuda compilation tools, release 11.8, V11.8.89"
                        parts = line.split('release')
                        if len(parts) > 1:
                            version = parts[1].split(',')[0].strip()
                            gpu_info['cuda_version'] = version
                            print(f"✅ CUDA version: {version}")
                        break
            else:
                print("❌ CUDA toolkit not installed")
        except:
            print("❌ nvcc not available")
    
    # Check for AMD GPU (basic)
    if platform.system().lower() == "linux":
        try:
            success, output = run_command("lspci | grep -i 'vga\\|display'", "Checking for AMD GPU")
            if success and 'amd' in output.lower():
                gpu_info['amd'] = True
                print("✅ AMD GPU detected")
        except:
            pass
    
    return gpu_info

def install_pytorch_gpu(cuda_version: str = None):
    """Install PyTorch with GPU support"""
    print("\n🔥 Installing PyTorch with GPU support...")
    
    if cuda_version:
        # Map CUDA versions to PyTorch index URLs
        cuda_urls = {
            '11.8': 'https://download.pytorch.org/whl/cu118',
            '11.7': 'https://download.pytorch.org/whl/cu117',
            '12.1': 'https://download.pytorch.org/whl/cu121'
        }
        
        # Find best matching CUDA version
        best_url = None
        for version, url in cuda_urls.items():
            if cuda_version.startswith(version):
                best_url = url
                break
        
        if not best_url:
            # Default to CUDA 11.8 if no exact match
            best_url = cuda_urls['11.8']
            print(f"⚠️ No exact CUDA match for {cuda_version}, using CUDA 11.8")
        
        # Install PyTorch with CUDA
        command = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url {best_url}"
        success, _ = run_command(command, f"Installing PyTorch with CUDA {cuda_version}")
        
        if success:
            print("✅ PyTorch with GPU support installed successfully")
            return True
        else:
            print("❌ Failed to install PyTorch with GPU support")
            return False
    else:
        print("❌ No CUDA version detected, cannot install GPU PyTorch")
        return False

def install_tensorflow_gpu():
    """Install TensorFlow with GPU support"""
    print("\n🧠 Installing TensorFlow with GPU support...")
    
    # TensorFlow 2.10+ has built-in GPU support
    command = f"{sys.executable} -m pip install tensorflow[and-cuda]"
    success, _ = run_command(command, "Installing TensorFlow with GPU support")
    
    if success:
        print("✅ TensorFlow with GPU support installed")
        return True
    else:
        print("❌ Failed to install TensorFlow with GPU support")
        # Fallback to regular TensorFlow
        command = f"{sys.executable} -m pip install tensorflow"
        success, _ = run_command(command, "Installing TensorFlow (CPU fallback)")
        return success

def verify_gpu_setup():
    """Verify GPU setup is working"""
    print("\n🔍 Verifying GPU setup...")
    
    # Test PyTorch CUDA
    try:
        import torch
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
            print(f"✅ PyTorch CUDA: {device_count} device(s) available")
            print(f"   Primary device: {device_name}")
            
            # Test tensor operations
            x = torch.randn(3, 3).cuda()
            y = torch.randn(3, 3).cuda()
            z = torch.matmul(x, y)
            print("✅ PyTorch GPU tensor operations working")
            
        else:
            print("❌ PyTorch CUDA not available")
    except Exception as e:
        print(f"❌ PyTorch GPU test failed: {e}")
    
    # Test TensorFlow GPU
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"✅ TensorFlow GPU: {len(gpus)} device(s) available")
            for i, gpu in enumerate(gpus):
                print(f"   Device {i}: {gpu.name}")
        else:
            print("❌ TensorFlow GPU not available")
    except Exception as e:
        print(f"❌ TensorFlow GPU test failed: {e}")

def install_cuda_toolkit():
    """Provide instructions for CUDA toolkit installation"""
    print("\n🛠️ CUDA Toolkit Installation Guide")
    print("=" * 40)
    
    system = platform.system().lower()
    
    if system == "windows":
        print("🪟 Windows CUDA Installation:")
        print("1. Download CUDA Toolkit from: https://developer.nvidia.com/cuda-downloads")
        print("2. Select Windows > x86_64 > your Windows version")
        print("3. Download and run the installer")
        print("4. Restart your computer after installation")
        print("5. Verify with: nvcc --version")
    
    elif system == "linux":
        print("🐧 Linux CUDA Installation:")
        print("1. Add NVIDIA package repository:")
        print("   wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-keyring_1.0-1_all.deb")
        print("   sudo dpkg -i cuda-keyring_1.0-1_all.deb")
        print("   sudo apt-get update")
        print("2. Install CUDA:")
        print("   sudo apt-get -y install cuda")
        print("3. Add to PATH in ~/.bashrc:")
        print("   export PATH=/usr/local/cuda/bin:$PATH")
        print("   export LD_LIBRARY_PATH=/usr/local/cuda/lib64:$LD_LIBRARY_PATH")
        print("4. Restart terminal and verify: nvcc --version")
    
    elif system == "darwin":
        print("🍎 macOS CUDA Installation:")
        print("❌ NVIDIA CUDA is not supported on Apple Silicon Macs")
        print("✅ Use Metal Performance Shaders (MPS) with PyTorch:")
        print("   pip install torch torchvision torchaudio")
        print("   # Use device='mps' in PyTorch code")

def setup_environment_variables():
    """Setup environment variables for GPU acceleration"""
    print("\n🔧 Setting up environment variables...")
    
    env_vars = {
        'CUDA_VISIBLE_DEVICES': '0',  # Use first GPU by default
        'TF_FORCE_GPU_ALLOW_GROWTH': 'true',  # Allow TensorFlow GPU memory growth
        'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:128'  # PyTorch memory management
    }
    
    for var, value in env_vars.items():
        os.environ[var] = value
        print(f"✅ Set {var}={value}")
    
    print("\n💡 To make these permanent, add to your shell profile:")
    for var, value in env_vars.items():
        print(f"export {var}={value}")

def main():
    """Main GPU setup process"""
    print("🎮 EPINNOX v6 GPU Acceleration Setup")
    print("=" * 40)
    
    # Detect GPU hardware
    gpu_info = detect_gpu()
    
    if not gpu_info['nvidia']:
        print("\n❌ No NVIDIA GPU detected")
        print("💡 GPU acceleration requires NVIDIA GPU with CUDA support")
        
        if platform.system().lower() == "darwin":
            print("🍎 For Apple Silicon Macs, PyTorch MPS acceleration is available")
            print("   Install with: pip install torch torchvision torchaudio")
        
        return 1
    
    if not gpu_info['cuda_version']:
        print("\n❌ CUDA toolkit not installed")
        install_cuda_toolkit()
        print("\n⚠️ Please install CUDA toolkit first, then re-run this script")
        return 1
    
    print(f"\n✅ GPU setup requirements met:")
    print(f"   NVIDIA GPU: Available")
    print(f"   CUDA Version: {gpu_info['cuda_version']}")
    print(f"   Driver Version: {gpu_info['nvidia_driver']}")
    
    # Install GPU-accelerated packages
    try:
        # Install PyTorch with GPU support
        pytorch_success = install_pytorch_gpu(gpu_info['cuda_version'])
        
        # Install TensorFlow with GPU support
        tensorflow_success = install_tensorflow_gpu()
        
        # Setup environment variables
        setup_environment_variables()
        
        # Verify installation
        verify_gpu_setup()
        
        print("\n🎉 GPU setup completed!")
        
        if pytorch_success or tensorflow_success:
            print("✅ GPU acceleration is now available for ML models")
            print("💡 Restart your Python environment to use GPU acceleration")
        else:
            print("⚠️ Some GPU packages failed to install")
            print("💡 Check error messages above and try manual installation")
        
        return 0
        
    except Exception as e:
        print(f"\n💥 GPU setup failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
