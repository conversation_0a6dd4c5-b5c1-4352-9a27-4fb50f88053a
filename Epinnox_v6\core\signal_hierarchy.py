"""
Intelligent Signal Hierarchy System

This module resolves conflicting signals from different analysis layers
using weighted decision trees and confidence-based arbitration.
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SignalInput:
    """Represents a signal from any analysis layer"""
    source: str
    decision: str  # LONG, SHORT, WAIT
    confidence: float  # 0.0 to 1.0
    weight: float  # Importance weight
    reasoning: str
    metadata: Dict[str, Any] = None

class IntelligentSignalHierarchy:
    """
    Resolves conflicting signals using intelligent hierarchy and confidence weighting
    """
    
    def __init__(self):
        """Initialize signal hierarchy system"""
        # Define source weights (higher = more important)
        self.source_weights = {
            'ml_ensemble': 0.35,      # ML models get highest weight when confident
            'technical_signals': 0.25, # Traditional indicators
            'multi_timeframe': 0.20,   # Multi-timeframe analysis
            'market_regime': 0.15,     # Market regime context
            'llm_analysis': 0.05       # LLM gets lowest base weight (can be boosted)
        }
        
        # Confidence thresholds for different actions
        self.confidence_thresholds = {
            'high_confidence': 0.80,   # Strong signal threshold
            'medium_confidence': 0.65, # Moderate signal threshold
            'low_confidence': 0.50     # Minimum threshold for action
        }
        
        # Contradiction resolution rules
        self.resolution_rules = {
            'ml_override_threshold': 0.85,  # ML can override if confidence > 85%
            'consensus_requirement': 0.60,  # Require 60% consensus for action
            'safety_bias': 'WAIT'           # Default to WAIT when uncertain
        }
        
        logger.info("Intelligent Signal Hierarchy initialized")
    
    def resolve_signals(self, signals: List[SignalInput]) -> Dict[str, Any]:
        """
        Resolve conflicting signals using intelligent hierarchy
        
        Args:
            signals: List of SignalInput objects from different sources
            
        Returns:
            Dict with resolved decision and analysis
        """
        try:
            if not signals:
                return self._create_default_decision("No signals provided")
            
            # Step 1: Validate and normalize signals
            validated_signals = self._validate_signals(signals)
            
            # Step 2: Calculate weighted scores for each decision
            decision_scores = self._calculate_weighted_scores(validated_signals)
            
            # Step 3: Apply confidence-based adjustments
            adjusted_scores = self._apply_confidence_adjustments(decision_scores, validated_signals)
            
            # Step 4: Check for high-confidence overrides
            override_result = self._check_override_conditions(validated_signals)
            if override_result:
                return override_result
            
            # Step 5: Apply consensus requirements
            consensus_result = self._apply_consensus_rules(adjusted_scores, validated_signals)
            
            # Step 6: Generate final decision with reasoning
            final_decision = self._generate_final_decision(consensus_result, validated_signals)
            
            logger.info(f"Signal hierarchy resolved: {final_decision['decision']} "
                       f"(confidence: {final_decision['confidence']:.1%})")
            
            return final_decision
            
        except Exception as e:
            logger.error(f"Error resolving signals: {e}")
            return self._create_default_decision(f"Error in signal resolution: {e}")
    
    def _validate_signals(self, signals: List[SignalInput]) -> List[SignalInput]:
        """Validate and normalize signal inputs"""
        validated = []
        
        for signal in signals:
            # Normalize confidence to 0-1 range
            confidence = max(0.0, min(1.0, signal.confidence))
            
            # Normalize decision
            decision = signal.decision.upper()
            if decision not in ['LONG', 'SHORT', 'WAIT']:
                logger.warning(f"Invalid decision '{decision}' from {signal.source}, defaulting to WAIT")
                decision = 'WAIT'
            
            # Apply source weight
            weight = self.source_weights.get(signal.source, 0.1)
            
            validated.append(SignalInput(
                source=signal.source,
                decision=decision,
                confidence=confidence,
                weight=weight,
                reasoning=signal.reasoning,
                metadata=signal.metadata or {}
            ))
        
        return validated
    
    def _calculate_weighted_scores(self, signals: List[SignalInput]) -> Dict[str, float]:
        """Calculate weighted scores for each decision"""
        scores = {'LONG': 0.0, 'SHORT': 0.0, 'WAIT': 0.0}
        total_weight = 0.0
        
        for signal in signals:
            # Weight = base_weight * confidence * confidence_bonus
            confidence_bonus = 1.0 + (signal.confidence - 0.5)  # Bonus for high confidence
            effective_weight = signal.weight * signal.confidence * confidence_bonus
            
            scores[signal.decision] += effective_weight
            total_weight += effective_weight
        
        # Normalize scores
        if total_weight > 0:
            for decision in scores:
                scores[decision] /= total_weight
        
        return scores
    
    def _apply_confidence_adjustments(self, scores: Dict[str, float], 
                                    signals: List[SignalInput]) -> Dict[str, float]:
        """Apply confidence-based adjustments to scores"""
        adjusted_scores = scores.copy()
        
        # Boost scores for high-confidence signals
        for signal in signals:
            if signal.confidence >= self.confidence_thresholds['high_confidence']:
                boost = 0.1 * signal.weight  # 10% boost for high confidence
                adjusted_scores[signal.decision] += boost
                logger.debug(f"High confidence boost: {signal.source} -> {signal.decision} (+{boost:.3f})")
        
        # Penalize low-confidence signals
        for signal in signals:
            if signal.confidence < self.confidence_thresholds['low_confidence']:
                penalty = 0.05 * signal.weight  # 5% penalty for low confidence
                adjusted_scores[signal.decision] -= penalty
                logger.debug(f"Low confidence penalty: {signal.source} -> {signal.decision} (-{penalty:.3f})")
        
        # Ensure scores remain positive
        for decision in adjusted_scores:
            adjusted_scores[decision] = max(0.0, adjusted_scores[decision])
        
        return adjusted_scores
    
    def _check_override_conditions(self, signals: List[SignalInput]) -> Optional[Dict[str, Any]]:
        """Check for high-confidence override conditions"""
        
        # ML ensemble override
        ml_signals = [s for s in signals if s.source == 'ml_ensemble']
        if ml_signals:
            ml_signal = ml_signals[0]
            if (ml_signal.confidence >= self.resolution_rules['ml_override_threshold'] and 
                ml_signal.decision != 'WAIT'):
                
                return {
                    'decision': ml_signal.decision,
                    'confidence': ml_signal.confidence,
                    'reasoning': f"ML Override: {ml_signal.reasoning}",
                    'override_type': 'ml_high_confidence',
                    'original_confidence': ml_signal.confidence,
                    'signals_analysis': self._create_signals_summary(signals)
                }
        
        # Check for unanimous high-confidence signals
        high_conf_signals = [s for s in signals if s.confidence >= self.confidence_thresholds['high_confidence']]
        if len(high_conf_signals) >= 2:
            decisions = [s.decision for s in high_conf_signals]
            if len(set(decisions)) == 1 and decisions[0] != 'WAIT':  # All agree on same non-WAIT decision
                avg_confidence = np.mean([s.confidence for s in high_conf_signals])
                
                return {
                    'decision': decisions[0],
                    'confidence': avg_confidence,
                    'reasoning': f"Unanimous high-confidence consensus: {decisions[0]}",
                    'override_type': 'unanimous_high_confidence',
                    'original_confidence': avg_confidence,
                    'signals_analysis': self._create_signals_summary(signals)
                }
        
        return None
    
    def _apply_consensus_rules(self, scores: Dict[str, float], 
                             signals: List[SignalInput]) -> Dict[str, float]:
        """Apply consensus requirements"""
        # Find the highest scoring decision
        best_decision = max(scores.items(), key=lambda x: x[1])
        best_score = best_decision[1]
        
        # Check if it meets consensus requirement
        if best_score < self.resolution_rules['consensus_requirement']:
            # Insufficient consensus, bias toward safety
            scores['WAIT'] += 0.2  # Boost WAIT score
            logger.info(f"Insufficient consensus (best: {best_score:.3f}), biasing toward WAIT")
        
        return scores
    
    def _generate_final_decision(self, scores: Dict[str, float], 
                               signals: List[SignalInput]) -> Dict[str, Any]:
        """Generate final decision with comprehensive reasoning"""
        
        # Select decision with highest score
        final_decision = max(scores.items(), key=lambda x: x[1])
        decision = final_decision[0]
        confidence = final_decision[1]
        
        # Generate reasoning
        reasoning_parts = []
        
        # Add signal summary
        signal_summary = self._create_signals_summary(signals)
        reasoning_parts.append(f"Signal Analysis: {signal_summary}")
        
        # Add decision rationale
        if decision == 'WAIT':
            reasoning_parts.append("Conservative approach due to conflicting signals or insufficient confidence.")
        else:
            supporting_signals = [s for s in signals if s.decision == decision]
            if supporting_signals:
                avg_conf = np.mean([s.confidence for s in supporting_signals])
                reasoning_parts.append(f"{decision} decision supported by {len(supporting_signals)} signals "
                                     f"with average confidence {avg_conf:.1%}")
        
        # Add risk considerations
        if confidence < self.confidence_thresholds['medium_confidence']:
            reasoning_parts.append("Lower confidence suggests reduced position sizing or additional confirmation.")
        
        return {
            'decision': decision,
            'confidence': confidence,
            'reasoning': ' '.join(reasoning_parts),
            'scores': scores,
            'signals_analysis': signal_summary,
            'recommendation': self._generate_recommendation(decision, confidence),
            'risk_level': self._assess_risk_level(confidence, signals)
        }
    
    def _create_signals_summary(self, signals: List[SignalInput]) -> str:
        """Create a summary of all input signals"""
        summary_parts = []
        
        for signal in signals:
            summary_parts.append(f"{signal.source}: {signal.decision} ({signal.confidence:.1%})")
        
        return "; ".join(summary_parts)
    
    def _generate_recommendation(self, decision: str, confidence: float) -> str:
        """Generate trading recommendation based on decision and confidence"""
        if decision == 'WAIT':
            return "Hold current position or wait for clearer signals"
        elif confidence >= self.confidence_thresholds['high_confidence']:
            return f"Strong {decision} signal - consider full position size"
        elif confidence >= self.confidence_thresholds['medium_confidence']:
            return f"Moderate {decision} signal - consider reduced position size"
        else:
            return f"Weak {decision} signal - consider minimal position or wait"
    
    def _assess_risk_level(self, confidence: float, signals: List[SignalInput]) -> str:
        """Assess overall risk level"""
        if confidence >= self.confidence_thresholds['high_confidence']:
            return "LOW"
        elif confidence >= self.confidence_thresholds['medium_confidence']:
            return "MEDIUM"
        else:
            return "HIGH"
    
    def _create_default_decision(self, reason: str) -> Dict[str, Any]:
        """Create default WAIT decision"""
        return {
            'decision': 'WAIT',
            'confidence': 0.33,
            'reasoning': f"Default decision: {reason}",
            'scores': {'LONG': 0.0, 'SHORT': 0.0, 'WAIT': 1.0},
            'signals_analysis': "No valid signals",
            'recommendation': "Wait for valid signals",
            'risk_level': "HIGH"
        }
    
    def update_weights(self, performance_data: Dict[str, float]):
        """Update source weights based on historical performance"""
        try:
            for source, performance in performance_data.items():
                if source in self.source_weights:
                    # Adjust weight based on performance (0.5 = no change, >0.5 = increase, <0.5 = decrease)
                    adjustment = (performance - 0.5) * 0.1  # Max 5% adjustment
                    self.source_weights[source] = max(0.05, min(0.5, 
                                                              self.source_weights[source] + adjustment))
            
            logger.info(f"Updated source weights: {self.source_weights}")
            
        except Exception as e:
            logger.error(f"Error updating weights: {e}")
