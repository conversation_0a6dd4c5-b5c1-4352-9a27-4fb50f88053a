"""
Performance Tracker for Autonomous Trading System
Tracks trading performance, model effectiveness, and provides analytics
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import numpy as np
import os
import logging

logger = logging.getLogger(__name__)

class PerformanceTracker:
    """
    Tracks and analyzes trading performance and model effectiveness
    """
    
    def __init__(self, db_path="data/trading_performance.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database for tracking performance"""
        # Ensure directory exists
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create trades table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME,
                symbol TEXT,
                decision TEXT,
                confidence REAL,
                entry_price REAL,
                exit_price REAL,
                position_size REAL,
                leverage REAL,
                pnl_usd REAL,
                pnl_pct REAL,
                duration_minutes REAL,
                ml_predictions TEXT,
                signal_scores TEXT,
                execution_status TEXT,
                trade_source TEXT
            )
        ''')
        
        # Create performance metrics table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE,
                total_trades INTEGER,
                winning_trades INTEGER,
                losing_trades INTEGER,
                win_rate REAL,
                total_pnl REAL,
                sharpe_ratio REAL,
                max_drawdown REAL,
                profit_factor REAL,
                avg_trade_duration REAL
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"Performance tracking database initialized: {self.db_path}")
    
    def record_trade(self, trade_data: Dict):
        """Record a completed trade"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO trades (
                timestamp, symbol, decision, confidence, entry_price, exit_price,
                position_size, leverage, pnl_usd, pnl_pct, duration_minutes,
                ml_predictions, signal_scores, execution_status, trade_source
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            trade_data.get('timestamp', datetime.now()),
            trade_data.get('symbol'),
            trade_data.get('decision'),
            trade_data.get('confidence'),
            trade_data.get('entry_price'),
            trade_data.get('exit_price'),
            trade_data.get('position_size'),
            trade_data.get('leverage'),
            trade_data.get('pnl_usd'),
            trade_data.get('pnl_pct'),
            trade_data.get('duration_minutes'),
            str(trade_data.get('ml_predictions', {})),
            str(trade_data.get('signal_scores', {})),
            trade_data.get('execution_status'),
            trade_data.get('trade_source', 'autonomous')
        ))
        
        conn.commit()
        conn.close()
        logger.info(f"Trade recorded: {trade_data.get('symbol')} {trade_data.get('decision')} PnL: ${trade_data.get('pnl_usd', 0):.2f}")
    
    def calculate_daily_metrics(self) -> Dict:
        """Calculate daily performance metrics"""
        conn = sqlite3.connect(self.db_path)
        
        # Get today's trades
        today = datetime.now().date()
        df = pd.read_sql_query('''
            SELECT * FROM trades 
            WHERE DATE(timestamp) = ?
        ''', conn, params=(today,))
        
        conn.close()
        
        if df.empty:
            return {'date': today, 'total_trades': 0}
        
        metrics = {
            'date': today,
            'total_trades': len(df),
            'winning_trades': len(df[df['pnl_usd'] > 0]),
            'losing_trades': len(df[df['pnl_usd'] < 0]),
            'win_rate': len(df[df['pnl_usd'] > 0]) / len(df) if len(df) > 0 else 0,
            'total_pnl': df['pnl_usd'].sum(),
            'avg_trade_duration': df['duration_minutes'].mean(),
            'sharpe_ratio': self.calculate_sharpe_ratio(df['pnl_pct']),
            'max_drawdown': self.calculate_max_drawdown(df['pnl_usd']),
            'profit_factor': self.calculate_profit_factor(df['pnl_usd'])
        }
        
        # Save metrics to database
        self.save_daily_metrics(metrics)
        
        return metrics
    
    def calculate_sharpe_ratio(self, returns: pd.Series) -> float:
        """Calculate Sharpe ratio"""
        if len(returns) < 2:
            return 0.0
        return (returns.mean() / returns.std()) * np.sqrt(252) if returns.std() > 0 else 0.0
    
    def calculate_max_drawdown(self, pnl_series: pd.Series) -> float:
        """Calculate maximum drawdown"""
        cumulative = pnl_series.cumsum()
        running_max = cumulative.expanding().max()
        drawdown = cumulative - running_max
        return drawdown.min()
    
    def calculate_profit_factor(self, pnl_series: pd.Series) -> float:
        """Calculate profit factor"""
        profits = pnl_series[pnl_series > 0].sum()
        losses = abs(pnl_series[pnl_series < 0].sum())
        return profits / losses if losses > 0 else float('inf')
    
    def save_daily_metrics(self, metrics: Dict):
        """Save daily metrics to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO performance_metrics (
                date, total_trades, winning_trades, losing_trades, win_rate,
                total_pnl, sharpe_ratio, max_drawdown, profit_factor, avg_trade_duration
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            metrics['date'], metrics['total_trades'], metrics['winning_trades'],
            metrics['losing_trades'], metrics['win_rate'], metrics['total_pnl'],
            metrics['sharpe_ratio'], metrics['max_drawdown'], metrics['profit_factor'],
            metrics['avg_trade_duration']
        ))
        
        conn.commit()
        conn.close()
    
    def get_model_performance(self, days=30) -> Dict:
        """Get ML model performance over time"""
        conn = sqlite3.connect(self.db_path)
        
        cutoff_date = datetime.now() - timedelta(days=days)
        df = pd.read_sql_query('''
            SELECT * FROM trades 
            WHERE timestamp > ?
        ''', conn, params=(cutoff_date,))
        
        conn.close()
        
        if df.empty:
            return {}
        
        # Analyze model performance
        model_stats = {}
        
        # Overall performance
        model_stats['overall'] = {
            'total_trades': len(df),
            'win_rate': len(df[df['pnl_usd'] > 0]) / len(df),
            'avg_pnl': df['pnl_usd'].mean(),
            'total_pnl': df['pnl_usd'].sum()
        }
        
        # Performance by confidence level
        if 'confidence' in df.columns:
            df['confidence_bucket'] = pd.cut(df['confidence'], 
                                           bins=[0, 60, 75, 90, 100], 
                                           labels=['Low', 'Medium', 'High', 'Very High'])
            confidence_performance = df.groupby('confidence_bucket')['pnl_usd'].agg(['count', 'mean', 'sum']).to_dict()
            model_stats['by_confidence'] = confidence_performance
        
        # Performance by trade source (RL vs traditional)
        if 'trade_source' in df.columns:
            source_performance = df.groupby('trade_source')['pnl_usd'].agg(['count', 'mean', 'sum']).to_dict()
            model_stats['by_source'] = source_performance
        
        return model_stats
    
    def get_recent_performance_summary(self, hours=24) -> Dict:
        """Get recent performance summary"""
        conn = sqlite3.connect(self.db_path)
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        df = pd.read_sql_query('''
            SELECT * FROM trades 
            WHERE timestamp > ?
            ORDER BY timestamp DESC
        ''', conn, params=(cutoff_time,))
        
        conn.close()
        
        if df.empty:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'total_pnl': 0,
                'avg_pnl_per_trade': 0,
                'best_trade': 0,
                'worst_trade': 0
            }
        
        return {
            'total_trades': len(df),
            'win_rate': len(df[df['pnl_usd'] > 0]) / len(df),
            'total_pnl': df['pnl_usd'].sum(),
            'avg_pnl_per_trade': df['pnl_usd'].mean(),
            'best_trade': df['pnl_usd'].max(),
            'worst_trade': df['pnl_usd'].min(),
            'avg_confidence': df['confidence'].mean() if 'confidence' in df.columns else 0,
            'most_traded_symbol': df['symbol'].mode().iloc[0] if len(df) > 0 else 'N/A'
        }

    def detect_drift(self, lookback_days: int = 7) -> Dict:
        """
        Detect model and signal drift based on performance metrics
        Returns drift status and recommended actions
        """
        recent_performance = self.get_recent_performance_summary(hours=lookback_days * 24)

        if recent_performance['total_trades'] < 5:
            return {'status': 'insufficient_data', 'message': 'Not enough trades for drift analysis'}

        # Performance thresholds
        sharpe_threshold = 0.5
        win_rate_threshold = 0.40
        drawdown_threshold = 0.15

        # Calculate approximate Sharpe ratio
        avg_return = recent_performance['avg_pnl_per_trade'] / 1000  # Normalize
        sharpe_ratio = avg_return * np.sqrt(252) if avg_return != 0 else 0

        # Calculate approximate drawdown
        drawdown = abs(recent_performance['worst_trade']) / 1000 if recent_performance['worst_trade'] < 0 else 0

        # Drift detection logic
        alerts = []

        if sharpe_ratio < sharpe_threshold:
            alerts.append(f"Sharpe ratio ({sharpe_ratio:.2f}) below threshold ({sharpe_threshold})")

        if recent_performance['win_rate'] < win_rate_threshold:
            alerts.append(f"Win rate ({recent_performance['win_rate']:.1%}) below threshold ({win_rate_threshold:.1%})")

        if drawdown > drawdown_threshold:
            alerts.append(f"Drawdown ({drawdown:.1%}) above threshold ({drawdown_threshold:.1%})")

        # Determine overall status
        if len(alerts) >= 2:
            status = "critical"
            action = "Stop trading and perform comprehensive system review"
        elif len(alerts) == 1:
            status = "warning"
            action = "Review strategy parameters and consider model retraining"
        else:
            status = "normal"
            action = "Continue monitoring"

        return {
            'status': status,
            'alerts': alerts,
            'recommended_action': action,
            'metrics': {
                'sharpe_ratio': sharpe_ratio,
                'win_rate': recent_performance['win_rate'],
                'drawdown': drawdown,
                'total_trades': recent_performance['total_trades']
            },
            'timestamp': datetime.now().isoformat()
        }
