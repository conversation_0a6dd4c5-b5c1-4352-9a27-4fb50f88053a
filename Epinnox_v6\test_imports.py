#!/usr/bin/env python3
"""
Test script to check if all imports and basic functionality work in me2_stable.py
"""

import sys
import traceback

def test_imports():
    """Test all imports"""
    print("Testing imports...")
    
    try:
        # Test basic imports
        import pandas as pd
        import numpy as np
        import ccxt
        import yaml
        print("✓ Basic imports successful")
        
        # Test PySide6 imports
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        print("✓ PySide6 imports successful")
        
        # Test pyqtgraph
        import pyqtgraph as pg
        print("✓ PyQtGraph imports successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Import error: {e}")
        traceback.print_exc()
        return False

def test_basic_functionality():
    """Test basic functionality of the main module"""
    print("\nTesting basic functionality...")
    
    try:
        # Import the main module
        import me2_stable
        print("✓ Main module import successful")
        
        # Test if key functions exist
        required_functions = [
            'fetch_open_positions',
            'fetch_order_book', 
            'fetch_account_info',
            'place_limit_order',
            'place_market_order',
            'set_leverage',
            'initialize_exchange'
        ]
        
        for func_name in required_functions:
            if hasattr(me2_stable, func_name):
                print(f"✓ Function {func_name} exists")
            else:
                print(f"✗ Function {func_name} missing")
        
        # Test if main class exists
        if hasattr(me2_stable, 'EpinnoxTraderInterface'):
            print("✓ Main class EpinnoxTraderInterface exists")
        else:
            print("✗ Main class EpinnoxTraderInterface missing")
            
        return True
        
    except Exception as e:
        print(f"✗ Functionality test error: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("=" * 50)
    print("EPINNOX ME2_STABLE.PY DIAGNOSTIC TEST")
    print("=" * 50)
    
    # Test imports
    imports_ok = test_imports()
    
    if imports_ok:
        # Test basic functionality
        functionality_ok = test_basic_functionality()
        
        if functionality_ok:
            print("\n" + "=" * 50)
            print("✓ ALL TESTS PASSED - me2_stable.py appears to be working")
            print("=" * 50)
            return True
        else:
            print("\n" + "=" * 50)
            print("✗ FUNCTIONALITY TESTS FAILED")
            print("=" * 50)
            return False
    else:
        print("\n" + "=" * 50)
        print("✗ IMPORT TESTS FAILED")
        print("=" * 50)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
