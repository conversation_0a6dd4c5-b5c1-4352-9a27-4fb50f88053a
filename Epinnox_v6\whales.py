from PySide6.QtWidgets import (QApplication, QWidget, QVBoxLayout, QComboBox, QTableWidget,
                          QTableWidgetItem, QPushButton, QHBoxLayout, QLabel, QFrame,
                          QGridLayout, QSplitter, QHeaderView, QGroupBox, QSpacerItem,
                          QSizePolicy, QProgressBar, QGraphicsDropShadowEffect, QMainWindow,
                          QTabWidget, QLineEdit)
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QSize, QPropertyAnimation, QEasingCurve, QRect, QPoint, Slot, QSettings
from PySide6.QtGui import QFont, QColor, QPalette, QIcon, QPixmap, QLinearGradient, QGradient, QPainter, QPen
import sys
import json
import time
import asyncio
import websockets
import logging
import gzip
from datetime import datetime, timezone
import re
import yaml
import os
import requests
import hmac
import hashlib
import base64
import urllib.parse
from urllib.parse import urlencode
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class TradingApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.threads = {}  # Store WebSocket threads
        self.liq_threads = {}  # Store liquidation threads
        self.max_table_rows = 1000  # Limit rows to prevent memory issues
        self.whale_count = 0
        self.total_trades = 0
        self.last_whale_time = None

        # Autotrader state
        self.autotrader_enabled = False
        self.autotrader_strategy = "ATR-EMA Bands"
        self.autotrader_risk_percent = 1.0
        self.autotrader_max_positions = 3
        self.autotrader_symbol = "MOODENG/USDT"
        self.autotrader_account = None
        self.autotrader_last_price = 0.0
        self.autotrader_positions = []
        self.api_client = None
        self.strategy_data = {}  # Store strategy-specific data
        self.autotrader_leverage = 5  # Default leverage
        self.autotrader_quantity_type = "Auto (Risk-Based)"  # Default quantity type
        self.autotrader_quantity_value = 0.0  # Default quantity value

        # Load trading accounts from credentials.yaml
        self.trading_accounts = self.load_trading_accounts()

        # Settings for column persistence
        self.settings = QSettings("EPI", "WhaleAlertDashboard")

        # Set up window properties
        self.setWindowTitle("EPI's Whale Alert Dashboard")
        self.setMinimumSize(1200, 800)

        # Create tab widget
        self.tabs = QTabWidget(self)
        self.setCentralWidget(self.tabs)

        # Whale tab (existing stuff)
        self.whale_tab = QWidget()
        self.whale_layout = QVBoxLayout(self.whale_tab)
        self.whale_layout.setSpacing(10)
        self.whale_layout.setContentsMargins(10, 10, 10, 10)
        self.tabs.addTab(self.whale_tab, "Whale Watcher")

        # Liquidation tab (new)
        self.liq_tab = QWidget()
        self.liq_layout = QVBoxLayout(self.liq_tab)
        self.liq_layout.setSpacing(10)
        self.liq_layout.setContentsMargins(10, 10, 10, 10)
        self.tabs.addTab(self.liq_tab, "Liquidation Watcher")

        # Auto Trader tab (new)
        self.autotrader_tab = QWidget()
        self.autotrader_layout = QVBoxLayout(self.autotrader_tab)
        self.autotrader_layout.setSpacing(10)
        self.autotrader_layout.setContentsMargins(10, 10, 10, 10)
        self.tabs.addTab(self.autotrader_tab, "Auto Trader")

        # Initialize liquidation threads dictionary
        self.liq_threads = {}

        # Create header with title
        self.create_header()

        # Create control panel
        self.create_control_panel()

        # Create main content area
        self.create_main_content()

        # Create status bar
        self.create_status_bar()

        # Apply stylesheet
        self.apply_stylesheet()

        # Initialize thresholds
        self.volume_threshold = float(self.volume_combo.currentText())
        self.dollar_threshold = float(self.dollar_combo.currentText())
        self.threshold_type = 'dollar'  # Default to dollar-based detection

        # Create liquidation tab
        self.create_liquidation_tab()

        # Create autotrader tab
        self.create_autotrader_tab()

        # Add shadow effects to panels
        self.add_shadow_effects()

        # Start monitoring live trades
        self.start_monitoring()



    def create_liquidation_tab(self):
        """Create the liquidation watcher tab"""
        # Create header for liquidation tab
        header_frame = QFrame()
        header_frame.setObjectName("liqHeaderFrame")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 5, 10, 5)

        # Let the layout determine the height
        header_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)

        # Left side - Logo and title
        left_layout = QHBoxLayout()
        left_layout.setSpacing(12)  # Reduced spacing

        # Create a liquidation icon logo
        logo_label = QLabel()
        logo_label.setFixedSize(40, 40)  # Smaller logo

        # Create a liquidation icon using Unicode character
        liq_icon = QPixmap(40, 40)
        liq_icon.fill(Qt.transparent)
        painter = QPainter(liq_icon)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw a circular background - different color for liquidation tab
        painter.setPen(Qt.NoPen)
        painter.setBrush(QColor("#C3073F"))  # Red circle for liquidations
        painter.drawEllipse(0, 0, 40, 40)

        # Draw the lightning symbol for liquidations
        painter.setPen(QPen(QColor("#FFFFFF"), 2))  # White text
        painter.setFont(QFont("Arial", 24, QFont.Bold))
        painter.drawText(liq_icon.rect(), Qt.AlignCenter, "⚡")
        painter.end()

        logo_label.setPixmap(liq_icon)
        left_layout.addWidget(logo_label)

        # Title and subtitle in vertical layout
        text_layout = QVBoxLayout()
        text_layout.setSpacing(0)  # Minimal spacing
        text_layout.setContentsMargins(0, 0, 0, 0)

        title_label = QLabel("EPI's Liquidation Watcher")
        title_label.setObjectName("liqTitleLabel")
        text_layout.addWidget(title_label)

        subtitle_label = QLabel("Futures Liquidation Monitor")
        subtitle_label.setObjectName("liqSubtitleLabel")
        text_layout.addWidget(subtitle_label)

        left_layout.addLayout(text_layout)
        header_layout.addLayout(left_layout)

        # Add spacer to push everything to the left
        header_layout.addStretch(1)

        # Add header to layout
        self.liq_layout.addWidget(header_frame)

        # Create control panel for liquidation tab
        control_frame = QFrame()
        control_frame.setObjectName("liqControlFrame")
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(10, 10, 10, 10)
        control_layout.setSpacing(10)

        # Exchange selection group
        exchange_group = QGroupBox("Exchanges")
        exchange_group.setObjectName("controlGroupBox")
        exchange_layout = QVBoxLayout(exchange_group)
        exchange_layout.setContentsMargins(10, 20, 10, 10)
        exchange_layout.setSpacing(5)

        # Add header
        exchange_header = QHBoxLayout()
        exchange_header.setSpacing(5)

        # Icon for the exchanges section
        exchange_icon = QLabel()
        exchange_icon.setFixedSize(16, 16)
        exchange_icon.setStyleSheet("""
            background-color: #1E88E5;
            border-radius: 8px;
            border: 1px solid #FFFFFF;
        """)
        exchange_header.addWidget(exchange_icon)

        # Label for the exchanges section
        exchange_label = QLabel("Select exchanges to monitor")
        exchange_label.setObjectName("controlSectionLabel")
        exchange_header.addWidget(exchange_label)
        exchange_header.addStretch()

        exchange_layout.addLayout(exchange_header)

        # Exchange buttons in a grid
        buttons_grid = QGridLayout()
        buttons_grid.setSpacing(8)

        # Create exchange buttons
        self.liq_binance_btn = QPushButton("Binance")
        self.liq_binance_btn.setCheckable(True)
        self.liq_binance_btn.setChecked(True)
        self.liq_binance_btn.clicked.connect(lambda: self.toggle_liq_exchange('Binance'))
        self.liq_binance_btn.setObjectName("exchangeButton")
        self.liq_binance_btn.setFixedHeight(25)
        buttons_grid.addWidget(self.liq_binance_btn, 0, 0)

        self.liq_huobi_btn = QPushButton("Huobi")
        self.liq_huobi_btn.setCheckable(True)
        self.liq_huobi_btn.setChecked(True)
        self.liq_huobi_btn.clicked.connect(lambda: self.toggle_liq_exchange('Huobi'))
        self.liq_huobi_btn.setObjectName("exchangeButton")
        self.liq_huobi_btn.setFixedHeight(25)
        buttons_grid.addWidget(self.liq_huobi_btn, 0, 1)

        self.liq_bybit_btn = QPushButton("Bybit")
        self.liq_bybit_btn.setCheckable(True)
        self.liq_bybit_btn.setChecked(True)
        self.liq_bybit_btn.clicked.connect(lambda: self.toggle_liq_exchange('Bybit'))
        self.liq_bybit_btn.setObjectName("exchangeButton")
        self.liq_bybit_btn.setFixedHeight(25)
        buttons_grid.addWidget(self.liq_bybit_btn, 1, 0)

        exchange_layout.addLayout(buttons_grid)
        control_layout.addWidget(exchange_group)

        # Statistics group
        stats_group = QGroupBox("Liquidation Statistics")
        stats_group.setObjectName("controlGroupBox")
        stats_layout = QVBoxLayout(stats_group)
        stats_layout.setContentsMargins(10, 20, 10, 10)
        stats_layout.setSpacing(5)

        # Add header
        stats_header = QHBoxLayout()
        stats_header.setSpacing(5)

        # Icon for the stats section
        stats_icon = QLabel()
        stats_icon.setFixedSize(16, 16)
        stats_icon.setStyleSheet("""
            background-color: #FFC107;
            border-radius: 8px;
            border: 1px solid #FFFFFF;
        """)
        stats_header.addWidget(stats_icon)

        # Label for the stats section
        stats_label = QLabel("Liquidation statistics")
        stats_label.setObjectName("controlSectionLabel")
        stats_header.addWidget(stats_label)
        stats_header.addStretch()

        stats_layout.addLayout(stats_header)

        # Stats grid
        stats_grid = QGridLayout()
        stats_grid.setSpacing(8)

        # Total events
        total_events_label = QLabel("Total Events:")
        total_events_label.setObjectName("statsLabel")
        stats_grid.addWidget(total_events_label, 0, 0)

        self.liq_total_events_label = QLabel("0")
        self.liq_total_events_label.setObjectName("statsValue")
        self.liq_total_events_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        stats_grid.addWidget(self.liq_total_events_label, 0, 1)

        # Total value
        total_value_label = QLabel("Total $ Value:")
        total_value_label.setObjectName("statsLabel")
        stats_grid.addWidget(total_value_label, 1, 0)

        self.liq_total_value_label = QLabel("$0")
        self.liq_total_value_label.setObjectName("statsValue")
        self.liq_total_value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        stats_grid.addWidget(self.liq_total_value_label, 1, 1)

        # Largest single
        largest_single_label = QLabel("Largest Single:")
        largest_single_label.setObjectName("statsLabel")
        stats_grid.addWidget(largest_single_label, 2, 0)

        self.liq_largest_single_label = QLabel("$0")
        self.liq_largest_single_label.setObjectName("statsValue")
        self.liq_largest_single_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        stats_grid.addWidget(self.liq_largest_single_label, 2, 1)

        # Last event
        last_event_label = QLabel("Last Event:")
        last_event_label.setObjectName("statsLabel")
        stats_grid.addWidget(last_event_label, 3, 0)

        self.liq_last_event_label = QLabel("None")
        self.liq_last_event_label.setObjectName("statsValue")
        self.liq_last_event_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        stats_grid.addWidget(self.liq_last_event_label, 3, 1)

        stats_layout.addLayout(stats_grid)

        # Reset button
        self.liq_reset_stats_btn = QPushButton("Reset Stats")
        self.liq_reset_stats_btn.clicked.connect(self.reset_liq_statistics)
        self.liq_reset_stats_btn.setObjectName("resetButton")
        self.liq_reset_stats_btn.setFixedHeight(25)
        stats_layout.addWidget(self.liq_reset_stats_btn)

        control_layout.addWidget(stats_group)

        # Add control panel to layout
        self.liq_layout.addWidget(control_frame)

        # Create main content frame
        frame = QFrame()
        frame.setObjectName("liqFrame")
        lay = QVBoxLayout(frame)
        lay.setContentsMargins(10, 10, 10, 10)
        lay.setSpacing(5)

        hdr = QLabel("Live Liquidations")
        hdr.setObjectName("sectionHeader")
        lay.addWidget(hdr)

        self.liq_table = QTableWidget(0, 7, self)
        self.liq_table.setHorizontalHeaderLabels(
            ["Exchange", "Symbol", "Price", "Qty", "Dollar Value", "Side", "Timestamp"]
        )
        self.liq_table.verticalHeader().setVisible(False)
        self.liq_table.setEditTriggers(QTableWidget.NoEditTriggers)

        # Set column widths
        header = self.liq_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setDefaultSectionSize(85)  # Wider first column
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.Stretch)

        # Set row height
        self.liq_table.verticalHeader().setDefaultSectionSize(35)

        # Set table properties
        self.liq_table.setSortingEnabled(True)
        self.liq_table.setAlternatingRowColors(True)
        self.liq_table.setShowGrid(True)
        self.liq_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.liq_table.setSelectionMode(QTableWidget.SingleSelection)

        # Set default sort order to timestamp descending
        self.liq_table.sortItems(6, Qt.DescendingOrder)

        # Restore column state if available
        header_state = self.settings.value("liq_table_header_state")
        if header_state:
            self.liq_table.horizontalHeader().restoreState(header_state)

        lay.addWidget(self.liq_table)
        self.liq_layout.addWidget(frame)

        # Initialize liquidation statistics
        self.liq_total_events = 0
        self.liq_total_value = 0
        self.liq_largest_single = 0

    def toggle_liq_exchange(self, exchange):
        """Toggle WebSocket connection for a liquidation exchange"""
        # Get button state based on exchange name
        exchange_buttons = {
            'Binance': self.liq_binance_btn,
            'Huobi': self.liq_huobi_btn,
            'Bybit': self.liq_bybit_btn
        }

        if exchange not in exchange_buttons:
            return

        is_checked = exchange_buttons[exchange].isChecked()

        if is_checked and exchange not in self.liq_threads:
            # Start the thread
            self.start_liq_thread(exchange)
            self.status_label.setText(f"Status: Started {exchange} liquidation feed")
        elif not is_checked and exchange in self.liq_threads:
            # Stop the thread
            self.stop_liq_thread(exchange)
            self.status_label.setText(f"Status: Stopped {exchange} liquidation feed")

    def reset_liq_statistics(self):
        """Reset all liquidation statistics"""
        self.liq_total_events = 0
        self.liq_total_value = 0
        self.liq_largest_single = 0

        # Update UI
        self.liq_total_events_label.setText("0")
        self.liq_total_value_label.setText("$0")
        self.liq_largest_single_label.setText("$0")
        self.liq_last_event_label.setText("None")

        # Clear the table
        self.liq_table.setRowCount(0)

        # Show confirmation
        self.status_label.setText("Status: Liquidation statistics reset")

    def create_autotrader_tab(self):
        """Create the autotrader tab"""
        # Create header for autotrader tab
        header_frame = QFrame()
        header_frame.setObjectName("autotraderHeaderFrame")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 5, 10, 5)

        # Let the layout determine the height
        header_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)

        # Left side - Logo and title
        left_layout = QHBoxLayout()
        left_layout.setSpacing(12)  # Reduced spacing

        # Create an autotrader icon logo
        logo_label = QLabel()
        logo_label.setFixedSize(40, 40)  # Smaller logo

        # Create an autotrader icon using Unicode character
        auto_icon = QPixmap(40, 40)
        auto_icon.fill(Qt.transparent)
        painter = QPainter(auto_icon)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw a circular background - different color for autotrader tab
        painter.setPen(Qt.NoPen)
        painter.setBrush(QColor("#4CAF50"))  # Green circle for autotrader
        painter.drawEllipse(0, 0, 40, 40)

        # Draw the robot symbol for autotrader
        painter.setPen(QPen(QColor("#FFFFFF"), 2))  # White text
        painter.setFont(QFont("Arial", 24, QFont.Bold))
        painter.drawText(auto_icon.rect(), Qt.AlignCenter, "🤖")
        painter.end()

        logo_label.setPixmap(auto_icon)
        left_layout.addWidget(logo_label)

        # Title and subtitle in vertical layout
        text_layout = QVBoxLayout()
        text_layout.setSpacing(0)  # Minimal spacing
        text_layout.setContentsMargins(0, 0, 0, 0)

        title_label = QLabel("EPI's Auto Trader")
        title_label.setObjectName("autotraderTitleLabel")
        text_layout.addWidget(title_label)

        subtitle_label = QLabel("Automated Trading System")
        subtitle_label.setObjectName("autotraderSubtitleLabel")
        text_layout.addWidget(subtitle_label)

        left_layout.addLayout(text_layout)
        header_layout.addLayout(left_layout)

        # Add spacer to push everything to the left
        header_layout.addStretch(1)

        # Add header to layout
        self.autotrader_layout.addWidget(header_frame)

        # Create main content area with two columns
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(10, 10, 10, 10)
        content_layout.setSpacing(10)

        # Left column - Settings
        settings_frame = QFrame()
        settings_frame.setObjectName("autotraderSettingsFrame")
        settings_layout = QVBoxLayout(settings_frame)
        settings_layout.setContentsMargins(10, 10, 10, 10)
        settings_layout.setSpacing(10)

        # Enable/Disable section
        enable_group = QGroupBox("Trading Status")
        enable_group.setObjectName("controlGroupBox")
        enable_layout = QVBoxLayout(enable_group)
        enable_layout.setContentsMargins(10, 20, 10, 10)
        enable_layout.setSpacing(5)

        # Status indicator
        status_layout = QHBoxLayout()
        status_layout.setSpacing(10)

        status_indicator = QLabel()
        status_indicator.setFixedSize(16, 16)
        status_indicator.setObjectName("statusIndicator")
        status_indicator.setStyleSheet("""
            background-color: #FF3B30;
            border-radius: 8px;
        """)
        status_layout.addWidget(status_indicator)

        self.autotrader_status_label = QLabel("Auto Trader is DISABLED")
        self.autotrader_status_label.setObjectName("statusLabel")
        status_layout.addWidget(self.autotrader_status_label)
        status_layout.addStretch()

        enable_layout.addLayout(status_layout)

        # Enable/Disable button
        self.autotrader_toggle_btn = QPushButton("Enable Auto Trader")
        self.autotrader_toggle_btn.setObjectName("enableButton")
        self.autotrader_toggle_btn.setCheckable(True)
        self.autotrader_toggle_btn.clicked.connect(self.toggle_autotrader)
        enable_layout.addWidget(self.autotrader_toggle_btn)

        settings_layout.addWidget(enable_group)

        # Strategy selection
        strategy_group = QGroupBox("Trading Strategy")
        strategy_group.setObjectName("controlGroupBox")
        strategy_layout = QVBoxLayout(strategy_group)
        strategy_layout.setContentsMargins(10, 20, 10, 10)
        strategy_layout.setSpacing(5)

        # Strategy dropdown
        self.strategy_combo = QComboBox()
        self.strategy_combo.setObjectName("mainComboBox")
        self.strategy_combo.addItems(['ATR-EMA Bands', 'MACD Crossover', 'RSI Divergence', 'Bollinger Bands'])
        self.strategy_combo.setCurrentText('ATR-EMA Bands')
        self.strategy_combo.currentTextChanged.connect(self.on_strategy_changed)
        strategy_layout.addWidget(self.strategy_combo)

        # Strategy description
        self.strategy_description = QLabel("ATR-EMA Bands strategy uses ATR for volatility and EMA for trend direction.")
        self.strategy_description.setObjectName("descriptionLabel")
        self.strategy_description.setWordWrap(True)
        strategy_layout.addWidget(self.strategy_description)

        settings_layout.addWidget(strategy_group)

        # Risk management
        risk_group = QGroupBox("Risk Management")
        risk_group.setObjectName("controlGroupBox")
        risk_layout = QGridLayout(risk_group)
        risk_layout.setContentsMargins(10, 20, 10, 10)
        risk_layout.setSpacing(5)

        # Risk per trade
        risk_label = QLabel("Risk per Trade (%):")
        risk_label.setObjectName("controlLabel")
        risk_layout.addWidget(risk_label, 0, 0)

        self.risk_combo = QComboBox()
        self.risk_combo.setObjectName("mainComboBox")
        self.risk_combo.addItems(['0.5', '1.0', '2.0', '3.0', '5.0'])
        self.risk_combo.setCurrentText('1.0')
        self.risk_combo.currentTextChanged.connect(self.on_risk_changed)
        risk_layout.addWidget(self.risk_combo, 0, 1)

        # Leverage
        leverage_label = QLabel("Leverage:")
        leverage_label.setObjectName("controlLabel")
        risk_layout.addWidget(leverage_label, 1, 0)

        self.leverage_combo = QComboBox()
        self.leverage_combo.setObjectName("mainComboBox")
        self.leverage_combo.addItems(['1x', '2x', '3x', '5x', '10x', '20x', '50x', '100x'])
        self.leverage_combo.setCurrentText('5x')
        self.leverage_combo.currentTextChanged.connect(lambda x: self.on_leverage_changed(x.replace('x', '')))
        risk_layout.addWidget(self.leverage_combo, 1, 1)

        # Quantity type
        quantity_type_label = QLabel("Quantity Type:")
        quantity_type_label.setObjectName("controlLabel")
        risk_layout.addWidget(quantity_type_label, 2, 0)

        self.quantity_type_combo = QComboBox()
        self.quantity_type_combo.setObjectName("mainComboBox")
        self.quantity_type_combo.addItems(['Auto (Risk-Based)', 'Fixed Quantity', 'Fixed USD Value'])
        self.quantity_type_combo.setCurrentText('Auto (Risk-Based)')
        self.quantity_type_combo.currentTextChanged.connect(self.on_quantity_type_changed)
        risk_layout.addWidget(self.quantity_type_combo, 2, 1)

        # Quantity value
        quantity_value_label = QLabel("Quantity Value:")
        quantity_value_label.setObjectName("controlLabel")
        risk_layout.addWidget(quantity_value_label, 3, 0)

        self.quantity_value_edit = QLineEdit()
        self.quantity_value_edit.setObjectName("mainLineEdit")
        self.quantity_value_edit.setPlaceholderText("Auto calculated")
        self.quantity_value_edit.setEnabled(False)  # Disabled for Auto mode
        self.quantity_value_edit.textChanged.connect(self.on_quantity_value_changed)
        risk_layout.addWidget(self.quantity_value_edit, 3, 1)

        # Max positions
        positions_label = QLabel("Max Positions:")
        positions_label.setObjectName("controlLabel")
        risk_layout.addWidget(positions_label, 4, 0)

        self.positions_combo = QComboBox()
        self.positions_combo.setObjectName("mainComboBox")
        self.positions_combo.addItems(['1', '2', '3', '5', '10'])
        self.positions_combo.setCurrentText('3')
        self.positions_combo.currentTextChanged.connect(self.on_positions_changed)
        risk_layout.addWidget(self.positions_combo, 4, 1)

        settings_layout.addWidget(risk_group)

        # Exchange selection
        exchange_group = QGroupBox("Exchange")
        exchange_group.setObjectName("controlGroupBox")
        exchange_layout = QVBoxLayout(exchange_group)
        exchange_layout.setContentsMargins(10, 20, 10, 10)
        exchange_layout.setSpacing(5)

        # Exchange dropdown
        exchange_label = QLabel("Exchange:")
        exchange_label.setObjectName("controlLabel")
        exchange_layout.addWidget(exchange_label)

        self.exchange_combo = QComboBox()
        self.exchange_combo.setObjectName("mainComboBox")
        self.exchange_combo.addItems(['Huobi', 'Binance', 'Bybit'])
        self.exchange_combo.setCurrentText('Huobi')
        self.exchange_combo.currentTextChanged.connect(self.on_exchange_changed)
        exchange_layout.addWidget(self.exchange_combo)

        # Symbol selection
        symbol_label = QLabel("Trading Symbol:")
        symbol_label.setObjectName("controlLabel")
        exchange_layout.addWidget(symbol_label)

        self.autotrader_symbol_combo = QComboBox()
        self.autotrader_symbol_combo.setObjectName("mainComboBox")
        self.autotrader_symbol_combo.addItems(['BTC/USDT', 'ETH/USDT', 'XRP/USDT', 'SOL/USDT', 'ADA/USDT', 'DOGE/USDT'])
        self.autotrader_symbol_combo.setCurrentText(self.autotrader_symbol)
        self.autotrader_symbol_combo.currentTextChanged.connect(self.on_autotrader_symbol_changed)
        exchange_layout.addWidget(self.autotrader_symbol_combo)

        # Account selection
        account_label = QLabel("Trading Account:")
        account_label.setObjectName("controlLabel")
        exchange_layout.addWidget(account_label)

        self.account_combo = QComboBox()
        self.account_combo.setObjectName("mainComboBox")

        # Add accounts from credentials.yaml
        account_names = []
        for account in self.trading_accounts:
            name = account.get('name', '')
            exchange = account.get('exchange', '').capitalize()
            description = account.get('description', '')
            display_name = f"{name} ({exchange})"
            if description:
                display_name += f" - {description}"
            self.account_combo.addItem(display_name, account)
            account_names.append(name)

        # Set current account if available
        if self.autotrader_account and self.autotrader_account.get('name') in account_names:
            index = account_names.index(self.autotrader_account.get('name'))
            self.account_combo.setCurrentIndex(index)

        self.account_combo.currentIndexChanged.connect(self.on_account_changed)
        exchange_layout.addWidget(self.account_combo)

        # Account info
        account_info_layout = QHBoxLayout()
        account_info_layout.setSpacing(5)

        balance_label = QLabel("Account Balance:")
        balance_label.setObjectName("controlLabel")
        account_info_layout.addWidget(balance_label)

        self.balance_label = QLabel("$80.00")
        self.balance_label.setObjectName("balanceLabel")
        account_info_layout.addWidget(self.balance_label)
        account_info_layout.addStretch()

        exchange_layout.addLayout(account_info_layout)

        settings_layout.addWidget(exchange_group)

        # Add stretch to push everything to the top
        settings_layout.addStretch(1)

        # Right column - Trading activity and positions
        activity_frame = QFrame()
        activity_frame.setObjectName("autotraderActivityFrame")
        activity_layout = QVBoxLayout(activity_frame)
        activity_layout.setContentsMargins(10, 10, 10, 10)
        activity_layout.setSpacing(10)

        # Open positions
        positions_label = QLabel("Open Positions")
        positions_label.setObjectName("sectionHeader")
        activity_layout.addWidget(positions_label)

        self.positions_table = QTableWidget(0, 7, self)
        self.positions_table.setHorizontalHeaderLabels(
            ["Symbol", "Entry Price", "Current Price", "Size", "P&L", "Side", "Leverage"]
        )
        self.positions_table.verticalHeader().setVisible(False)
        self.positions_table.setEditTriggers(QTableWidget.NoEditTriggers)

        # Set column widths
        header = self.positions_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.Stretch)

        # Set row height
        self.positions_table.verticalHeader().setDefaultSectionSize(35)

        # Set table properties
        self.positions_table.setAlternatingRowColors(True)
        self.positions_table.setShowGrid(True)
        self.positions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.positions_table.setSelectionMode(QTableWidget.SingleSelection)

        activity_layout.addWidget(self.positions_table)

        # Trading history
        history_label = QLabel("Trading History")
        history_label.setObjectName("sectionHeader")
        activity_layout.addWidget(history_label)

        self.history_table = QTableWidget(0, 5, self)
        self.history_table.setHorizontalHeaderLabels(
            ["Time", "Symbol", "Side", "Price", "P&L"]
        )
        self.history_table.verticalHeader().setVisible(False)
        self.history_table.setEditTriggers(QTableWidget.NoEditTriggers)

        # Set column widths
        header = self.history_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.Stretch)

        # Set row height
        self.history_table.verticalHeader().setDefaultSectionSize(35)

        # Set table properties
        self.history_table.setAlternatingRowColors(True)
        self.history_table.setShowGrid(True)
        self.history_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.history_table.setSelectionMode(QTableWidget.SingleSelection)

        activity_layout.addWidget(self.history_table)

        # Add frames to content layout
        content_layout.addWidget(settings_frame, 1)  # 1/3 width
        content_layout.addWidget(activity_frame, 2)  # 2/3 width

        # Add content layout to main layout
        self.autotrader_layout.addLayout(content_layout)

    def toggle_autotrader(self):
        """Toggle the autotrader on/off"""
        self.autotrader_enabled = self.autotrader_toggle_btn.isChecked()

        if self.autotrader_enabled:
            # Update UI
            self.autotrader_status_label.setText("Auto Trader is ENABLED")
            self.autotrader_toggle_btn.setText("Disable Auto Trader")

            # Change status indicator to green
            for child in self.findChildren(QLabel):
                if child.objectName() == "statusIndicator":
                    child.setStyleSheet("""
                        background-color: #4CAF50;
                        border-radius: 8px;
                    """)

            # Start the autotrader
            self.start_autotrader()

            # Update status
            self.status_label.setText(f"Status: Auto Trader enabled with {self.autotrader_strategy} strategy")
        else:
            # Update UI
            self.autotrader_status_label.setText("Auto Trader is DISABLED")
            self.autotrader_toggle_btn.setText("Enable Auto Trader")

            # Change status indicator to red
            for child in self.findChildren(QLabel):
                if child.objectName() == "statusIndicator":
                    child.setStyleSheet("""
                        background-color: #FF3B30;
                        border-radius: 8px;
                    """)

            # Stop the autotrader
            self.stop_autotrader()

            # Update status
            self.status_label.setText("Status: Auto Trader disabled")

    def on_strategy_changed(self, strategy):
        """Handle strategy change"""
        self.autotrader_strategy = strategy

        # Update strategy description
        descriptions = {
            'ATR-EMA Bands': "ATR-EMA Bands strategy uses ATR for volatility and EMA for trend direction.",
            'MACD Crossover': "MACD Crossover strategy uses Moving Average Convergence Divergence for trend detection.",
            'RSI Divergence': "RSI Divergence strategy identifies divergences between price and RSI indicator.",
            'Bollinger Bands': "Bollinger Bands strategy uses standard deviations from moving averages for volatility."
        }

        self.strategy_description.setText(descriptions.get(strategy, ""))

        # Update status
        if self.autotrader_enabled:
            self.status_label.setText(f"Status: Changed strategy to {strategy}")

    def on_risk_changed(self, risk):
        """Handle risk percentage change"""
        self.autotrader_risk_percent = float(risk)

        # Update status
        if self.autotrader_enabled:
            self.status_label.setText(f"Status: Changed risk per trade to {risk}%")

    def on_positions_changed(self, positions):
        """Handle max positions change"""
        self.autotrader_max_positions = int(positions)

        # Update status
        if self.autotrader_enabled:
            self.status_label.setText(f"Status: Changed max positions to {positions}")

    def on_leverage_changed(self, leverage):
        """Handle leverage change"""
        self.autotrader_leverage = int(leverage)

        # Update status
        if self.autotrader_enabled:
            self.status_label.setText(f"Status: Changed leverage to {leverage}x")

        # If we have an API client, try to set the leverage on the exchange
        if self.api_client and self.autotrader_account and hasattr(self.api_client, 'set_leverage'):
            try:
                # Format symbol for API request
                formatted_symbol = self.autotrader_symbol.split('/')[0].lower()

                # Set leverage on exchange
                result = self.api_client.set_leverage(formatted_symbol, self.autotrader_leverage)
                if result:
                    logging.info(f"Set leverage to {self.autotrader_leverage}x for {self.autotrader_symbol}")
                else:
                    logging.error(f"Failed to set leverage to {self.autotrader_leverage}x for {self.autotrader_symbol}")
            except Exception as e:
                logging.error(f"Error setting leverage: {e}")

    def on_quantity_type_changed(self, quantity_type):
        """Handle quantity type change"""
        self.autotrader_quantity_type = quantity_type

        # Enable/disable quantity value input based on type
        if quantity_type == "Auto (Risk-Based)":
            self.quantity_value_edit.setEnabled(False)
            self.quantity_value_edit.setPlaceholderText("Auto calculated")
            self.quantity_value_edit.setText("")
        else:
            self.quantity_value_edit.setEnabled(True)
            if quantity_type == "Fixed Quantity":
                self.quantity_value_edit.setPlaceholderText("Enter quantity (e.g., 0.01 BTC)")
            else:  # Fixed USD Value
                self.quantity_value_edit.setPlaceholderText("Enter USD value (e.g., 100)")

        # Update status
        if self.autotrader_enabled:
            self.status_label.setText(f"Status: Changed quantity type to {quantity_type}")

    def on_quantity_value_changed(self, value):
        """Handle quantity value change"""
        if not value:
            self.autotrader_quantity_value = 0.0
            return

        try:
            self.autotrader_quantity_value = float(value)

            # Update status
            if self.autotrader_enabled:
                if self.autotrader_quantity_type == "Fixed Quantity":
                    self.status_label.setText(f"Status: Changed fixed quantity to {value}")
                else:  # Fixed USD Value
                    self.status_label.setText(f"Status: Changed fixed USD value to ${value}")
        except ValueError:
            # Invalid number
            self.status_label.setText("Status: Invalid quantity value")

    def on_exchange_changed(self, exchange):
        """Handle exchange change"""
        # Filter accounts by exchange
        self.account_combo.clear()

        account_names = []
        filtered_accounts = [account for account in self.trading_accounts
                            if account.get('exchange', '').lower() == exchange.lower()]

        for account in filtered_accounts:
            name = account.get('name', '')
            exchange = account.get('exchange', '').capitalize()
            description = account.get('description', '')
            display_name = f"{name} ({exchange})"
            if description:
                display_name += f" - {description}"
            self.account_combo.addItem(display_name, account)
            account_names.append(name)

        # Update status
        if self.autotrader_enabled:
            self.status_label.setText(f"Status: Changed exchange to {exchange}")

    def on_autotrader_symbol_changed(self, symbol):
        """Handle symbol change for autotrader"""
        self.autotrader_symbol = symbol

        # Update positions with new symbol
        if self.autotrader_enabled:
            # Clear positions table
            self.positions_table.setRowCount(0)

            # Add sample position with new symbol
            self.add_sample_position()

            # Update status
            self.status_label.setText(f"Status: Changed trading symbol to {symbol}")

    def on_account_changed(self, index):
        """Handle account selection change"""
        if index < 0 or self.account_combo.count() == 0:
            return

        # Get the selected account data
        account_data = self.account_combo.itemData(index)
        if not account_data:
            return

        self.autotrader_account = account_data

        # Update balance display
        name = account_data.get('name', '')
        exchange = account_data.get('exchange', '').capitalize()

        # Initialize API client
        api_key = account_data.get('api_key', '')
        secret_key = account_data.get('secret_key', '')

        if api_key and secret_key and api_key != 'your_api_key' and secret_key != 'your_secret_key':
            try:
                # Connect to exchange API
                self.api_client = self.connect_to_exchange_api(exchange.lower(), api_key, secret_key)
                if self.api_client:
                    logging.info(f"Connected to {exchange} API for account {name}")

                    # Update the global demo_mode flag in me2_stable.py to False
                    try:
                        import me2_stable
                        me2_stable.demo_mode = False
                        logging.info("Set demo_mode to False in me2_stable module")

                        # Update workers module with the new demo_mode value
                        me2_stable.init_workers()
                        logging.info("Updated workers module with new demo_mode value")
                    except Exception as e:
                        logging.error(f"Error updating demo_mode: {e}")
            except Exception as e:
                logging.error(f"Error connecting to {exchange} API: {e}")
                self.api_client = None

        # Fetch actual balance from the exchange API
        self.fetch_account_balance()

        # Fetch open positions to update the positions table
        if self.api_client:
            self.fetch_open_positions()

            # Update orders table if we're in the main window
            try:
                import me2_stable
                if hasattr(me2_stable, 'main_window') and me2_stable.main_window:
                    if hasattr(me2_stable.main_window, '_update_orders'):
                        me2_stable.main_window._update_orders()
            except Exception as e:
                logging.error(f"Error updating orders table: {e}")

        # Update status
        if self.autotrader_enabled:
            self.status_label.setText(f"Status: Changed trading account to {name} ({exchange})")
        else:
            self.status_label.setText(f"Status: Selected account {name} ({exchange})")

    def fetch_account_balance(self):
        """Fetch actual account balance from the exchange"""
        if not self.autotrader_account:
            self.balance_label.setText("$0.00")
            return

        exchange = self.autotrader_account.get('exchange', '').lower()
        api_key = self.autotrader_account.get('api_key', '')
        secret_key = self.autotrader_account.get('secret_key', '')

        # Check if API keys are valid
        if not api_key or not secret_key or api_key == 'your_api_key' or secret_key == 'your_secret_key':
            self.balance_label.setText("$0.00")
            self.status_label.setText("Status: Invalid API keys")
            return

        try:
            # Set logging level to DEBUG temporarily to see detailed API responses
            original_level = logging.getLogger().level
            logging.getLogger().setLevel(logging.DEBUG)

            # Log account details (without sensitive info)
            account_name = self.autotrader_account.get('name', 'Unknown')
            logging.info(f"Fetching balance for account: {account_name} on {exchange.capitalize()}")

            # Use existing API client or create a new one
            client = self.api_client
            if not client:
                logging.info(f"Creating new API client for {exchange.capitalize()}")
                client = self.connect_to_exchange_api(exchange, api_key, secret_key)
                if not client:
                    self.balance_label.setText("$0.00")
                    self.status_label.setText(f"Status: Failed to connect to {exchange.capitalize()} API")
                    logging.error(f"Failed to create API client for {exchange.capitalize()}")
                    logging.getLogger().setLevel(original_level)
                    return

                # Save the client for future use
                self.api_client = client

            # Get account balance
            logging.info(f"Requesting balance from {exchange.capitalize()} API")
            balance = client.get_account_balance()

            # Update UI
            self.balance_label.setText(f"${balance:.2f}")

            # Log the balance
            logging.info(f"Fetched {exchange.capitalize()} balance for {account_name}: ${balance:.2f}")

            # Reset logging level
            logging.getLogger().setLevel(original_level)

        except Exception as e:
            logging.error(f"Error fetching account balance: {e}")
            self.status_label.setText(f"Status: Error fetching account balance")
            self.balance_label.setText("$0.00")

            # Reset logging level
            if 'original_level' in locals():
                logging.getLogger().setLevel(original_level)

    def start_autotrader(self):
        """Start the autotrader"""
        # Check if we have a valid account
        if not self.autotrader_account:
            self.status_label.setText("Status: No trading account selected")
            self.autotrader_toggle_btn.setChecked(False)
            self.autotrader_enabled = False
            return

        # Validate trading setup
        errors = self.validate_trading_setup()
        if errors:
            error_message = "Status: Cannot start autotrader - " + errors[0]
            self.status_label.setText(error_message)
            self.autotrader_toggle_btn.setChecked(False)
            self.autotrader_enabled = False
            return

        # Connect to the trading API
        exchange = self.autotrader_account.get('exchange', '').lower()
        api_key = self.autotrader_account.get('api_key', '')
        secret_key = self.autotrader_account.get('secret_key', '')

        # Create API client
        self.api_client = self.connect_to_exchange_api(exchange, api_key, secret_key)
        if not self.api_client:
            self.status_label.setText(f"Status: Failed to connect to {exchange.capitalize()} API")
            self.autotrader_toggle_btn.setChecked(False)
            self.autotrader_enabled = False
            return

        # Fetch open positions
        self.fetch_open_positions()

        # Add trading history
        self.fetch_trading_history()

        # Log the start of autotrading
        logging.info(f"Started autotrader with {self.autotrader_strategy} strategy on {self.autotrader_symbol} using account {self.autotrader_account.get('name')}")

    def validate_trading_setup(self):
        """Validate that all required components are set up for trading"""
        errors = []

        # Check if account is selected
        if not self.autotrader_account:
            errors.append("No trading account selected")

        # Check if API keys are valid
        api_key = self.autotrader_account.get('api_key', '') if self.autotrader_account else ''
        secret_key = self.autotrader_account.get('secret_key', '') if self.autotrader_account else ''

        if not api_key or api_key == 'your_api_key':
            errors.append("Invalid API key")

        if not secret_key or secret_key == 'your_secret_key':
            errors.append("Invalid secret key")

        # Check if symbol is selected
        if not self.autotrader_symbol:
            errors.append("No trading symbol selected")

        # Check if risk parameters are valid
        if self.autotrader_risk_percent <= 0 or self.autotrader_risk_percent > 10:
            errors.append(f"Invalid risk percentage: {self.autotrader_risk_percent}%")

        if self.autotrader_max_positions <= 0:
            errors.append(f"Invalid max positions: {self.autotrader_max_positions}")

        return errors

    def fetch_open_positions(self):
        """Fetch actual open positions from the exchange"""
        if not self.autotrader_account or not self.api_client:
            return

        exchange = self.autotrader_account.get('exchange', '').lower()

        try:
            # Clear positions table
            self.positions_table.setRowCount(0)
            self.autotrader_positions = []

            # Format symbol for API request - for futures contracts use the correct format
            if '-' in self.autotrader_symbol:
                # Already in the correct format for futures (e.g., MOODENG-USDT)
                formatted_symbol = self.autotrader_symbol
            elif '/' in self.autotrader_symbol:
                # Convert from "MOODENG/USDT" to "MOODENG-USDT" for futures
                parts = self.autotrader_symbol.split('/')
                if len(parts) == 2 and parts[1] == 'USDT':
                    formatted_symbol = f"{parts[0]}-{parts[1]}"
                else:
                    formatted_symbol = self.autotrader_symbol.replace('/', '').lower()
            else:
                formatted_symbol = self.autotrader_symbol

            # Get positions using the correct API method for futures
            logging.info(f"Fetching positions for {formatted_symbol}")
            positions = self.api_client.get_futures_positions(formatted_symbol)

            if not positions:
                # No open positions
                logging.info(f"No open positions found for {formatted_symbol}")
                return

            # Process positions based on exchange
            if exchange == 'huobi':
                for position in positions:
                    # Extract position data - handle futures position format
                    # For HTX futures, the position data format is different
                    if 'direction' in position:
                        # This is a futures position
                        side = position.get('direction', '').upper()
                        amount = float(position.get('volume', 0))
                        price = float(position.get('cost_open', 0))
                        leverage = float(position.get('lever_rate', 5))

                        # Get current price - use mark price if available, otherwise use last price
                        current_price = float(position.get('last_price', 0))
                        if current_price == 0:
                            current_price = self.autotrader_last_price if self.autotrader_last_price > 0 else price

                        # Add to positions table
                        self.add_real_position({
                            'symbol': position.get('contract_code', self.autotrader_symbol),
                            'side': side,
                            'amount': amount,
                            'entry_price': price,
                            'current_price': current_price,
                            'leverage': leverage,
                            'is_futures': True
                        })
                    else:
                        # This is a spot position or old format
                        side = position.get('type', '').split('-')[0].upper()
                        amount = float(position.get('amount', 0))
                        price = float(position.get('price', 0))

                        # Add to positions table
                        self.add_real_position({
                            'symbol': self.autotrader_symbol,
                            'side': side,
                            'amount': amount,
                            'entry_price': price,
                            'current_price': self.autotrader_last_price if self.autotrader_last_price > 0 else price
                        })

            elif exchange == 'binance':
                for position in positions:
                    # Extract position data
                    side = position.get('side', '').upper()
                    amount = float(position.get('origQty', 0))
                    price = float(position.get('price', 0))

                    # Add to positions table
                    self.add_real_position({
                        'symbol': self.autotrader_symbol,
                        'side': side,
                        'amount': amount,
                        'entry_price': price,
                        'current_price': self.autotrader_last_price if self.autotrader_last_price > 0 else price
                    })

            elif exchange == 'bybit':
                for position in positions:
                    # Extract position data
                    side = position.get('side', '').upper()
                    amount = float(position.get('qty', 0))
                    price = float(position.get('price', 0))

                    # Add to positions table
                    self.add_real_position({
                        'symbol': self.autotrader_symbol,
                        'side': side,
                        'amount': amount,
                        'entry_price': price,
                        'current_price': self.autotrader_last_price if self.autotrader_last_price > 0 else price
                    })

        except Exception as e:
            logging.error(f"Error fetching open positions: {e}")
            self.status_label.setText(f"Status: Error fetching open positions")

    def add_real_position(self, position_data):
        """Add a real position to the positions table"""
        tbl = self.positions_table

        # Add a row
        row = tbl.rowCount()
        tbl.insertRow(row)

        # Extract position data
        symbol = position_data.get('symbol', self.autotrader_symbol)
        entry_price = position_data.get('entry_price', 0)
        current_price = position_data.get('current_price', entry_price)
        size = position_data.get('amount', 0)
        side = position_data.get('side', 'BUY')

        # Check if this is a futures position
        is_futures = '-' in symbol
        leverage = position_data.get('leverage', 1) if is_futures else 1

        # Calculate P&L
        pnl = 0
        if is_futures:
            # For futures, P&L calculation depends on position side and leverage
            if side.upper() == 'BUY' or side.upper() == 'LONG':
                # Long position: profit when price goes up
                pnl = (current_price - entry_price) * size * leverage
            else:
                # Short position: profit when price goes down
                pnl = (entry_price - current_price) * size * leverage
        else:
            # For spot, simple P&L calculation
            if side.upper() == 'BUY':
                pnl = (current_price - entry_price) * size
            else:
                pnl = (entry_price - current_price) * size

        # Store position data for later updates
        position = {
            'symbol': symbol,
            'entry_price': entry_price,
            'current_price': current_price,
            'size': size,
            'side': side,
            'leverage': leverage,
            'is_futures': is_futures,
            'pnl': pnl
        }
        self.autotrader_positions.append(position)

        # Add data to table
        tbl.setItem(row, 0, QTableWidgetItem(symbol))

        entry_item = QTableWidgetItem(f"${entry_price:.2f}")
        entry_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        tbl.setItem(row, 1, entry_item)

        current_item = QTableWidgetItem(f"${current_price:.2f}")
        current_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        tbl.setItem(row, 2, current_item)

        size_item = QTableWidgetItem(f"{size:.4f}")
        size_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        tbl.setItem(row, 3, size_item)

        pnl_item = QTableWidgetItem(f"${pnl:.2f}")
        pnl_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # Color PnL based on profit/loss
        if pnl > 0:
            pnl_item.setForeground(QColor("#4CAF50"))
        else:
            pnl_item.setForeground(QColor("#FF3B30"))

        tbl.setItem(row, 4, pnl_item)

        # Add side and leverage for futures positions
        side_item = QTableWidgetItem(side)
        side_item.setTextAlignment(Qt.AlignCenter)
        tbl.setItem(row, 5, side_item)

        leverage_item = QTableWidgetItem(f"{leverage}x" if is_futures else "1x")
        leverage_item.setTextAlignment(Qt.AlignCenter)
        tbl.setItem(row, 6, leverage_item)

    def fetch_trading_history(self):
        """Fetch trading history from local storage or add sample data if none exists"""
        # In a real implementation, we would fetch trading history from the exchange API
        # or from a local database. For now, we'll just add sample data.

        # Clear history table
        self.history_table.setRowCount(0)

        # Try to load history from file
        history_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'trade_history.json')

        if os.path.exists(history_file):
            try:
                with open(history_file, 'r') as f:
                    history = json.load(f)

                # Filter history for current account and symbol
                filtered_history = []
                account_name = self.autotrader_account.get('name') if self.autotrader_account else None

                for entry in history:
                    if entry.get('symbol') == self.autotrader_symbol and entry.get('account') == account_name:
                        filtered_history.append(entry)

                # Add history entries to table
                for entry in filtered_history:
                    self.add_trade_to_history(entry)

                return
            except Exception as e:
                logging.error(f"Error loading trade history: {e}")

        # If no history file or error loading, add sample data
        self.add_sample_history()

    def add_trade_to_history(self, trade_data):
        """Add a real trade to the history table"""
        tbl = self.history_table

        # Add a row
        row = tbl.rowCount()
        tbl.insertRow(row)

        # Format timestamp
        timestamp = trade_data.get('timestamp', int(time.time() * 1000))
        if isinstance(timestamp, int):
            # Convert milliseconds to seconds if needed
            if timestamp > *************:  # If in milliseconds
                timestamp = timestamp / 1000
            formatted_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
        else:
            formatted_time = str(timestamp)

        # Add data to table
        tbl.setItem(row, 0, QTableWidgetItem(formatted_time))
        tbl.setItem(row, 1, QTableWidgetItem(trade_data.get('symbol', self.autotrader_symbol)))

        side_item = QTableWidgetItem(trade_data.get('side', 'BUY').upper())
        side_item.setTextAlignment(Qt.AlignCenter)

        # Color side based on buy/sell
        if trade_data.get('side', '').upper() == "BUY":
            side_item.setForeground(QColor("#4CAF50"))
        else:
            side_item.setForeground(QColor("#FF3B30"))

        tbl.setItem(row, 2, side_item)

        price_item = QTableWidgetItem(f"${trade_data.get('price', 0):.2f}")
        price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        tbl.setItem(row, 3, price_item)

        pnl_item = QTableWidgetItem(f"${trade_data.get('pnl', 0):.2f}")
        pnl_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # Color PnL based on profit/loss
        if trade_data.get('pnl', 0) > 0:
            pnl_item.setForeground(QColor("#4CAF50"))
        elif trade_data.get('pnl', 0) < 0:
            pnl_item.setForeground(QColor("#FF3B30"))

        tbl.setItem(row, 4, pnl_item)

    def save_trade_history(self, trade_data):
        """Save trade history to a file for persistence"""
        try:
            history_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'trade_history.json')

            # Load existing history
            history = []
            if os.path.exists(history_file):
                with open(history_file, 'r') as f:
                    history = json.load(f)

            # Add new trade
            history.append({
                'timestamp': trade_data.get('timestamp', int(time.time() * 1000)),
                'symbol': trade_data.get('symbol', self.autotrader_symbol),
                'side': trade_data.get('side', 'BUY').upper(),
                'price': trade_data.get('price', 0),
                'quantity': trade_data.get('quantity', 0),
                'value': trade_data.get('value', 0),
                'pnl': trade_data.get('pnl', 0),
                'account': self.autotrader_account.get('name') if self.autotrader_account else None
            })

            # Save updated history
            with open(history_file, 'w') as f:
                json.dump(history, f, indent=2)

        except Exception as e:
            logging.error(f"Error saving trade history: {e}")

    def stop_autotrader(self):
        """Stop the autotrader"""
        # Disconnect from the trading API
        self.api_client = None

        # Clear positions table
        self.positions_table.setRowCount(0)
        self.autotrader_positions = []

        # Clear history table
        self.history_table.setRowCount(0)

        # Log the stop of autotrading
        logging.info(f"Stopped autotrader for {self.autotrader_symbol}")

    def add_sample_position(self):
        """Add a sample position to the positions table"""
        tbl = self.positions_table

        # Add a row
        row = tbl.rowCount()
        tbl.insertRow(row)

        # Add data
        tbl.setItem(row, 0, QTableWidgetItem(self.autotrader_symbol))

        # Set appropriate sample prices based on the symbol
        if self.autotrader_symbol == "BTC/USDT":
            entry_price = 30000.00
            current_price = self.autotrader_last_price if self.autotrader_last_price > 0 else 30450.00
            size = 0.01
        elif self.autotrader_symbol == "ETH/USDT":
            entry_price = 1800.00
            current_price = self.autotrader_last_price if self.autotrader_last_price > 0 else 1850.00
            size = 0.1
        else:
            # Default values for other symbols
            entry_price = 100.00
            current_price = self.autotrader_last_price if self.autotrader_last_price > 0 else 105.00
            size = 1.0

        pnl = (current_price - entry_price) * size

        # Store position data for later updates
        position = {
            'symbol': self.autotrader_symbol,
            'entry_price': entry_price,
            'current_price': current_price,
            'size': size,
            'side': 'BUY',
            'leverage': self.autotrader_leverage if hasattr(self, 'autotrader_leverage') else 1,
            'is_futures': '-' in self.autotrader_symbol,
            'pnl': pnl
        }
        self.autotrader_positions.append(position)

        entry_item = QTableWidgetItem(f"${entry_price:.2f}")
        entry_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        tbl.setItem(row, 1, entry_item)

        current_item = QTableWidgetItem(f"${current_price:.2f}")
        current_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        tbl.setItem(row, 2, current_item)

        size_item = QTableWidgetItem(f"{size:.4f}")
        size_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        tbl.setItem(row, 3, size_item)

        pnl_item = QTableWidgetItem(f"${pnl:.2f}")
        pnl_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # Color PnL based on profit/loss
        if pnl > 0:
            pnl_item.setForeground(QColor("#4CAF50"))
        else:
            pnl_item.setForeground(QColor("#FF3B30"))

        tbl.setItem(row, 4, pnl_item)

        # Add side and leverage
        is_futures = '-' in self.autotrader_symbol
        leverage = self.autotrader_leverage if hasattr(self, 'autotrader_leverage') else 1

        side_item = QTableWidgetItem('BUY')
        side_item.setTextAlignment(Qt.AlignCenter)
        tbl.setItem(row, 5, side_item)

        leverage_item = QTableWidgetItem(f"{leverage}x" if is_futures else "1x")
        leverage_item.setTextAlignment(Qt.AlignCenter)
        tbl.setItem(row, 6, leverage_item)

    def add_sample_history(self):
        """Add sample history entries to the history table"""
        tbl = self.history_table

        # Generate sample data based on the selected symbol
        if self.autotrader_symbol == "BTC/USDT":
            entries = [
                {"time": "2023-05-01 10:15:22", "symbol": "BTC/USDT", "side": "BUY", "price": 29850.00, "pnl": 0.00},
                {"time": "2023-05-02 14:30:45", "symbol": "BTC/USDT", "side": "SELL", "price": 30200.00, "pnl": 35.00},
                {"time": "2023-05-03 09:45:12", "symbol": "BTC/USDT", "side": "BUY", "price": 30100.00, "pnl": 0.00},
                {"time": "2023-05-04 16:20:33", "symbol": "BTC/USDT", "side": "SELL", "price": 30450.00, "pnl": 35.00}
            ]
        elif self.autotrader_symbol == "ETH/USDT":
            entries = [
                {"time": "2023-05-01 10:15:22", "symbol": "ETH/USDT", "side": "BUY", "price": 1750.00, "pnl": 0.00},
                {"time": "2023-05-02 14:30:45", "symbol": "ETH/USDT", "side": "SELL", "price": 1820.00, "pnl": 70.00},
                {"time": "2023-05-03 09:45:12", "symbol": "ETH/USDT", "side": "BUY", "price": 1800.00, "pnl": 0.00},
                {"time": "2023-05-04 16:20:33", "symbol": "ETH/USDT", "side": "SELL", "price": 1850.00, "pnl": 50.00}
            ]
        else:
            # Default sample data for other symbols
            base_price = 100.00
            entries = [
                {"time": "2023-05-01 10:15:22", "symbol": self.autotrader_symbol, "side": "BUY", "price": base_price, "pnl": 0.00},
                {"time": "2023-05-02 14:30:45", "symbol": self.autotrader_symbol, "side": "SELL", "price": base_price * 1.05, "pnl": base_price * 0.05},
                {"time": "2023-05-03 09:45:12", "symbol": self.autotrader_symbol, "side": "BUY", "price": base_price * 1.02, "pnl": 0.00},
                {"time": "2023-05-04 16:20:33", "symbol": self.autotrader_symbol, "side": "SELL", "price": base_price * 1.08, "pnl": base_price * 0.06}
            ]

        for entry in entries:
            row = tbl.rowCount()
            tbl.insertRow(row)

            tbl.setItem(row, 0, QTableWidgetItem(entry["time"]))
            tbl.setItem(row, 1, QTableWidgetItem(entry["symbol"]))

            side_item = QTableWidgetItem(entry["side"])
            side_item.setTextAlignment(Qt.AlignCenter)

            # Color side based on buy/sell
            if entry["side"] == "BUY":
                side_item.setForeground(QColor("#4CAF50"))
            else:
                side_item.setForeground(QColor("#FF3B30"))

            tbl.setItem(row, 2, side_item)

            price_item = QTableWidgetItem(f"${entry['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            tbl.setItem(row, 3, price_item)

            pnl_item = QTableWidgetItem(f"${entry['pnl']:.2f}")
            pnl_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

            # Color PnL based on profit/loss
            if entry["pnl"] > 0:
                pnl_item.setForeground(QColor("#4CAF50"))
            elif entry["pnl"] < 0:
                pnl_item.setForeground(QColor("#FF3B30"))

            tbl.setItem(row, 4, pnl_item)

    def add_shadow_effects(self):
        """Add shadow effects to panels for a modern look"""
        # Add shadows to all frames
        for frame in self.findChildren(QFrame):
            if frame.objectName() in ["headerFrame", "controlFrame", "contentFrame", "statusFrame",
                                     "liqFrame", "liqControlFrame", "liqHeaderFrame",
                                     "autotraderHeaderFrame", "autotraderSettingsFrame", "autotraderActivityFrame"]:
                shadow = QGraphicsDropShadowEffect()
                shadow.setBlurRadius(15)
                shadow.setColor(QColor(0, 0, 0, 80))
                shadow.setOffset(0, 3)
                frame.setGraphicsEffect(shadow)

    def create_header(self):
        """Create header with title and subtitle"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 5, 10, 5)

        # Let the layout determine the height
        header_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)

        # Left side - Logo and title
        left_layout = QHBoxLayout()
        left_layout.setSpacing(12)  # Reduced spacing

        # Create a whale icon logo
        logo_label = QLabel()
        logo_label.setFixedSize(40, 40)  # Smaller logo

        # Create a whale icon using Unicode character
        whale_icon = QPixmap(40, 40)
        whale_icon.fill(Qt.transparent)
        painter = QPainter(whale_icon)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw a circular background
        painter.setPen(Qt.NoPen)
        painter.setBrush(QColor("#1E88E5"))  # Blue circle
        painter.drawEllipse(0, 0, 40, 40)

        # Draw the whale symbol
        painter.setPen(QPen(QColor("#FFFFFF"), 2))  # White text
        painter.setFont(QFont("Arial", 24, QFont.Bold))
        painter.drawText(whale_icon.rect(), Qt.AlignCenter, "🐳")
        painter.end()

        logo_label.setPixmap(whale_icon)
        left_layout.addWidget(logo_label)

        # Title and subtitle in vertical layout
        text_layout = QVBoxLayout()
        text_layout.setSpacing(0)  # Minimal spacing
        text_layout.setContentsMargins(0, 0, 0, 0)

        title_label = QLabel("EPI's Whale Alert")
        title_label.setObjectName("titleLabel")
        text_layout.addWidget(title_label)

        subtitle_label = QLabel("Cryptocurrency Whale Trade Monitor")
        subtitle_label.setObjectName("subtitleLabel")
        text_layout.addWidget(subtitle_label)

        left_layout.addLayout(text_layout)
        header_layout.addLayout(left_layout)

        # Add spacer to push everything to the left
        header_layout.addStretch(1)

        self.whale_layout.addWidget(header_frame)

    def create_control_panel(self):
        """Create control panel with all settings"""
        control_frame = QFrame()
        control_frame.setObjectName("controlFrame")
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(10, 10, 10, 10)
        control_layout.setSpacing(10)

        # Set fixed height for the control panel
        control_frame.setFixedHeight(200)

        # Symbol selection group
        symbol_group = QGroupBox("Trading Pair")
        symbol_group.setObjectName("controlGroupBox")
        symbol_layout = QVBoxLayout(symbol_group)
        symbol_layout.setContentsMargins(10, 20, 10, 10)
        symbol_layout.setSpacing(5)

        # Add icon and label in a horizontal layout with better styling
        symbol_header = QHBoxLayout()
        symbol_header.setSpacing(5)

        # Icon for the symbol section - more attractive
        symbol_icon = QLabel()
        symbol_icon.setFixedSize(16, 16)
        symbol_icon.setStyleSheet("""
            background-color: #4CAF50;
            border-radius: 8px;
            border: 1px solid #FFFFFF;
        """)
        symbol_header.addWidget(symbol_icon)

        # Label for the symbol section with better styling
        symbol_label = QLabel("Select cryptocurrency pair")
        symbol_label.setObjectName("controlSectionLabel")
        symbol_header.addWidget(symbol_label)
        symbol_header.addStretch()

        symbol_layout.addLayout(symbol_header)

        # Symbol dropdown with popular cryptocurrencies - styled
        self.symbol_combo = QComboBox()
        self.symbol_combo.setObjectName("mainComboBox")
        self.symbol_combo.addItems(['BTC/USDT', 'ETH/USDT', 'XRP/USDT', 'SOL/USDT', 'ADA/USDT', 'DOGE/USDT', 'DOT/USDT', 'LINK/USDT'])
        self.symbol_combo.setCurrentText('BTC/USDT')
        self.symbol_combo.currentTextChanged.connect(self.on_symbol_changed)
        symbol_layout.addWidget(self.symbol_combo)

        control_layout.addWidget(symbol_group)

        # Whale detection group
        threshold_group = QGroupBox("Whale Detection")
        threshold_group.setObjectName("controlGroupBox")
        threshold_layout = QVBoxLayout(threshold_group)
        threshold_layout.setContentsMargins(10, 20, 10, 10)
        threshold_layout.setSpacing(5)

        # Add icon and label in a horizontal layout
        detection_header = QHBoxLayout()
        detection_header.setSpacing(5)

        # Icon for the detection section
        detection_icon = QLabel()
        detection_icon.setFixedSize(16, 16)
        detection_icon.setStyleSheet("""
            background-color: #C3073F;
            border-radius: 8px;
            border: 1px solid #FFFFFF;
        """)
        detection_header.addWidget(detection_icon)

        # Label for the detection section
        detection_label = QLabel("Configure detection settings")
        detection_label.setObjectName("controlSectionLabel")
        detection_header.addWidget(detection_label)
        detection_header.addStretch()

        threshold_layout.addLayout(detection_header)

        # Threshold type selection with better styling
        threshold_type_layout = QHBoxLayout()
        threshold_type_layout.setSpacing(10)

        threshold_type_label = QLabel("Detection Type:")
        threshold_type_label.setObjectName("controlLabel")
        threshold_type_layout.addWidget(threshold_type_label)

        self.threshold_type_combo = QComboBox()
        self.threshold_type_combo.setObjectName("mainComboBox")
        self.threshold_type_combo.addItems(['Volume Based', 'Dollar Value Based'])
        self.threshold_type_combo.setCurrentText('Dollar Value Based')
        self.threshold_type_combo.currentTextChanged.connect(self.on_threshold_type_changed)
        threshold_type_layout.addWidget(self.threshold_type_combo)
        threshold_layout.addLayout(threshold_type_layout)

        # Volume threshold with better styling
        self.volume_threshold_layout = QHBoxLayout()
        self.volume_threshold_layout.setSpacing(10)

        volume_label = QLabel("Volume Threshold:")
        volume_label.setObjectName("controlLabel")
        self.volume_threshold_layout.addWidget(volume_label)

        self.volume_combo = QComboBox()
        self.volume_combo.setObjectName("valueComboBox")
        self.volume_combo.addItems(['0.1', '0.5', '1', '5', '10', '50', '100'])
        self.volume_combo.setCurrentText('1')
        self.volume_combo.currentTextChanged.connect(self.on_volume_threshold_changed)
        self.volume_threshold_layout.addWidget(self.volume_combo)
        threshold_layout.addLayout(self.volume_threshold_layout)

        # Dollar value threshold with better styling
        self.dollar_threshold_layout = QHBoxLayout()
        self.dollar_threshold_layout.setSpacing(10)

        dollar_label = QLabel("Dollar Threshold ($):")
        dollar_label.setObjectName("controlLabel")
        self.dollar_threshold_layout.addWidget(dollar_label)

        self.dollar_combo = QComboBox()
        self.dollar_combo.setObjectName("valueComboBox")
        self.dollar_combo.addItems(['1000', '5000', '10000', '50000', '100000', '500000', '1000000'])
        self.dollar_combo.setCurrentText('10000')
        self.dollar_combo.currentTextChanged.connect(self.on_dollar_threshold_changed)
        self.dollar_threshold_layout.addWidget(self.dollar_combo)
        threshold_layout.addLayout(self.dollar_threshold_layout)

        # Initially hide volume threshold since we're defaulting to dollar-based
        self.volume_threshold_layout.setEnabled(False)
        for i in range(self.volume_threshold_layout.count()):
            widget = self.volume_threshold_layout.itemAt(i).widget()
            if widget:
                widget.setVisible(False)

        control_layout.addWidget(threshold_group)

        # Exchange selection group
        exchange_group = QGroupBox("Exchanges")
        exchange_group.setObjectName("controlGroupBox")
        exchange_layout = QVBoxLayout(exchange_group)
        exchange_layout.setContentsMargins(10, 20, 10, 10)
        exchange_layout.setSpacing(5)

        # Add icon and label in a horizontal layout
        exchange_header = QHBoxLayout()
        exchange_header.setSpacing(5)

        # Icon for the exchanges section
        exchange_icon = QLabel()
        exchange_icon.setFixedSize(16, 16)
        exchange_icon.setStyleSheet("""
            background-color: #1E88E5;
            border-radius: 8px;
            border: 1px solid #FFFFFF;
        """)
        exchange_header.addWidget(exchange_icon)

        # Label for the exchanges section
        exchange_label = QLabel("Select exchanges to monitor")
        exchange_label.setObjectName("controlSectionLabel")
        exchange_header.addWidget(exchange_label)
        exchange_header.addStretch()

        exchange_layout.addLayout(exchange_header)

        # Exchange buttons in a grid with better styling
        buttons_grid = QGridLayout()
        buttons_grid.setSpacing(8)

        # Create exchange buttons with improved styling
        # Row 1
        self.binance_btn = QPushButton("Binance")
        self.binance_btn.setCheckable(True)
        self.binance_btn.setChecked(True)
        self.binance_btn.clicked.connect(lambda: self.toggle_exchange('Binance'))
        self.binance_btn.setObjectName("exchangeButton")
        self.binance_btn.setFixedHeight(25)
        buttons_grid.addWidget(self.binance_btn, 0, 0)

        self.coinbase_btn = QPushButton("Coinbase")
        self.coinbase_btn.setCheckable(True)
        self.coinbase_btn.setChecked(False)
        self.coinbase_btn.clicked.connect(lambda: self.toggle_exchange('Coinbase'))
        self.coinbase_btn.setObjectName("exchangeButton")
        self.coinbase_btn.setFixedHeight(25)
        buttons_grid.addWidget(self.coinbase_btn, 0, 1)

        # Row 2
        self.huobi_btn = QPushButton("Huobi")
        self.huobi_btn.setCheckable(True)
        self.huobi_btn.setChecked(True)
        self.huobi_btn.clicked.connect(lambda: self.toggle_exchange('Huobi'))
        self.huobi_btn.setObjectName("exchangeButton")
        self.huobi_btn.setFixedHeight(25)
        buttons_grid.addWidget(self.huobi_btn, 1, 0)

        self.kraken_btn = QPushButton("Kraken")
        self.kraken_btn.setCheckable(True)
        self.kraken_btn.setChecked(False)
        self.kraken_btn.clicked.connect(lambda: self.toggle_exchange('Kraken'))
        self.kraken_btn.setObjectName("exchangeButton")
        self.kraken_btn.setFixedHeight(25)
        buttons_grid.addWidget(self.kraken_btn, 1, 1)

        # Add Bybit button
        self.bybit_btn = QPushButton("Bybit")
        self.bybit_btn.setCheckable(True)
        self.bybit_btn.setChecked(False)
        self.bybit_btn.clicked.connect(lambda: self.toggle_exchange('Bybit'))
        self.bybit_btn.setObjectName("exchangeButton")
        self.bybit_btn.setFixedHeight(25)
        buttons_grid.addWidget(self.bybit_btn, 2, 0)  # new row

        exchange_layout.addLayout(buttons_grid)

        control_layout.addWidget(exchange_group)

        # Statistics group
        stats_group = QGroupBox("Statistics")
        stats_group.setObjectName("controlGroupBox")
        stats_layout = QVBoxLayout(stats_group)
        stats_layout.setContentsMargins(10, 20, 10, 10)
        stats_layout.setSpacing(5)

        # Add icon and label in a horizontal layout
        stats_header = QHBoxLayout()
        stats_header.setSpacing(5)

        # Icon for the stats section
        stats_icon = QLabel()
        stats_icon.setFixedSize(16, 16)
        stats_icon.setStyleSheet("""
            background-color: #FFC107;
            border-radius: 8px;
            border: 1px solid #FFFFFF;
        """)
        stats_header.addWidget(stats_icon)

        # Label for the stats section
        stats_label = QLabel("Trading statistics")
        stats_label.setObjectName("controlSectionLabel")
        stats_header.addWidget(stats_label)
        stats_header.addStretch()

        stats_layout.addLayout(stats_header)

        # Stats grid with better styling
        stats_grid = QGridLayout()
        stats_grid.setSpacing(8)

        # Total trades with better styling
        total_trades_label = QLabel("Total Trades:")
        total_trades_label.setObjectName("statsLabel")
        stats_grid.addWidget(total_trades_label, 0, 0)

        self.total_trades_label = QLabel("0")
        self.total_trades_label.setObjectName("statsValue")
        self.total_trades_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        stats_grid.addWidget(self.total_trades_label, 0, 1)

        # Whale alerts with better styling
        whale_alerts_label = QLabel("Whale Alerts:")
        whale_alerts_label.setObjectName("statsLabel")
        stats_grid.addWidget(whale_alerts_label, 1, 0)

        self.whale_count_label = QLabel("0")
        self.whale_count_label.setObjectName("statsValue")
        self.whale_count_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        stats_grid.addWidget(self.whale_count_label, 1, 1)

        # Last whale with better styling
        last_whale_label = QLabel("Last Whale:")
        last_whale_label.setObjectName("statsLabel")
        stats_grid.addWidget(last_whale_label, 2, 0)

        self.last_whale_label = QLabel("None")
        self.last_whale_label.setObjectName("statsValue")
        self.last_whale_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        stats_grid.addWidget(self.last_whale_label, 2, 1)

        stats_layout.addLayout(stats_grid)

        # Reset button with better styling
        self.reset_stats_btn = QPushButton("Reset Stats")
        self.reset_stats_btn.clicked.connect(self.reset_statistics)
        self.reset_stats_btn.setObjectName("resetButton")
        self.reset_stats_btn.setFixedHeight(25)
        stats_layout.addWidget(self.reset_stats_btn)

        control_layout.addWidget(stats_group)

        self.whale_layout.addWidget(control_frame)

    def create_main_content(self):
        """Create main content area with table"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(10, 10, 10, 10)
        content_layout.setSpacing(5)

        # Set minimum height for the content area
        content_frame.setMinimumHeight(300)

        # Create header with icon and title
        header_layout = QHBoxLayout()

        # Icon for the table section
        icon_label = QLabel()
        icon_label.setFixedSize(24, 24)
        icon_label.setStyleSheet("background-color: #C3073F; border-radius: 12px;")
        header_layout.addWidget(icon_label)

        # Table header
        table_header = QLabel("Live Trade Data")
        table_header.setObjectName("sectionHeader")
        header_layout.addWidget(table_header)
        header_layout.addStretch()

        content_layout.addLayout(header_layout)

        # Table to show live trades
        self.table = QTableWidget(self)
        self.table.setColumnCount(7)  # Exchange, Symbol, Price, Volume, Dollar Value, Buy/Sell, Timestamp
        self.table.setHorizontalHeaderLabels(["Exchange", "Symbol", "Price", "Volume", "Dollar Value", "Buy/Sell", "Timestamp"])
        self.table.setSortingEnabled(True)
        self.table.setAlternatingRowColors(True)
        self.table.setShowGrid(True)
        self.table.setObjectName("tradesTable")
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.verticalHeader().setVisible(False)  # Hide row numbers
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)  # Make table read-only

        # Set row height
        self.table.verticalHeader().setDefaultSectionSize(35)

        # Set column widths
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.Stretch)

        # Add some padding around the table
        table_container = QFrame()
        table_container.setObjectName("tableContainer")
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(0, 0, 0, 0)
        table_layout.addWidget(self.table)

        # Set table to take up available space
        table_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        content_layout.addWidget(table_container)

        self.whale_layout.addWidget(content_frame)

    def create_status_bar(self):
        """Create status bar at the bottom"""
        status_frame = QFrame()
        status_frame.setObjectName("statusFrame")
        status_frame.setFixedHeight(40)
        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(10, 5, 10, 5)
        status_layout.setSpacing(10)

        # Add status icon
        self.status_icon = QLabel()
        self.status_icon.setFixedSize(16, 16)
        self.status_icon.setStyleSheet("background-color: #4CAF50; border-radius: 8px;")  # Green for active
        status_layout.addWidget(self.status_icon)

        # Add status text
        self.status_label = QLabel("Status: Monitoring Binance, Huobi for BTC/USDT")
        self.status_label.setObjectName("statusLabel")
        status_layout.addWidget(self.status_label)

        # Add version info on the right
        version_label = QLabel("v1.0.0")
        version_label.setObjectName("versionLabel")
        version_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        status_layout.addStretch()
        status_layout.addWidget(version_label)

        self.whale_layout.addWidget(status_frame)

    @Slot(dict)
    def add_liq_row(self, liq):
        """Add a liquidation row to the liquidation table"""
        tbl = self.liq_table

        # Turn off sorting temporarily for better performance
        was_sorting_enabled = tbl.isSortingEnabled()
        tbl.setSortingEnabled(False)

        # Limit table size to prevent memory issues
        if tbl.rowCount() >= self.max_table_rows:
            tbl.removeRow(tbl.rowCount() - 1)

        tbl.insertRow(0)
        tbl.setItem(0, 0, QTableWidgetItem(liq['exchange']))
        tbl.setItem(0, 1, QTableWidgetItem(liq['symbol']))
        pi = QTableWidgetItem(f"{liq['price']:.2f}")
        pi.setTextAlignment(Qt.AlignRight)
        qi = QTableWidgetItem(f"{liq['qty']:.4f}")
        qi.setTextAlignment(Qt.AlignRight)
        vi = QTableWidgetItem(f"${liq['value']:,.2f}")
        vi.setTextAlignment(Qt.AlignRight)
        si = QTableWidgetItem(liq['side'].upper())
        si.setTextAlignment(Qt.AlignCenter)
        ts = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(liq['timestamp']/1000))
        ti = QTableWidgetItem(ts)
        tbl.setItem(0, 2, pi)
        tbl.setItem(0, 3, qi)
        tbl.setItem(0, 4, vi)
        tbl.setItem(0, 5, si)
        tbl.setItem(0, 6, ti)

        # Color the side text
        color = QColor("#00FF00") if liq['side']=='buy' else QColor("#FF3B30")
        si.setForeground(color)

        # Gradient background based on dollar value
        intensity = min(liq['value'] / 50_000, 1.0)  # 50k = full intensity
        if liq['side'] == 'buy':
            r = 30
            g = int(30 + 150 * intensity)
            b = 30
        else:
            r = int(30 + 180 * intensity)
            g = 30
            b = 30

        row_color = QColor(r, g, b)
        for col in range(tbl.columnCount()):
            tbl.item(0, col).setBackground(row_color)

        # Update statistics
        self.liq_total_events += 1
        self.liq_total_value += liq['value']
        if liq['value'] > self.liq_largest_single:
            self.liq_largest_single = liq['value']

        # Update statistics display
        self.liq_total_events_label.setText(f"{self.liq_total_events}")
        self.liq_total_value_label.setText(f"${self.liq_total_value:,.2f}")
        self.liq_largest_single_label.setText(f"${self.liq_largest_single:,.2f}")
        self.liq_last_event_label.setText(f"{liq['symbol']} {liq['value']:,.0f} {liq['side'].upper()}")

        # Re-enable sorting
        tbl.setSortingEnabled(was_sorting_enabled)

        # Play sound or show notification for large liquidations
        if liq['value'] >= 1_000_000:  # $1M or more
            self.status_label.setText(f"Status: LARGE LIQUIDATION - {liq['symbol']} ${liq['value']:,.2f} {liq['side'].upper()}")

    def start_liq_thread(self, exchange):
        """Start a liquidation thread for a specific exchange"""
        if exchange in self.liq_threads:
            return
        t = LiquidationThread(exchange, self.symbol_combo.currentText())
        t.update_liq.connect(self.add_liq_row)
        t.start()
        self.liq_threads[exchange] = t

    def stop_liq_thread(self, exchange):
        """Stop a liquidation thread for a specific exchange"""
        if exchange in self.liq_threads:
            self.liq_threads[exchange].stop()
            self.liq_threads[exchange].wait(1000)
            del self.liq_threads[exchange]

    def apply_stylesheet(self):
        """Apply stylesheet to the application"""
        self.setStyleSheet("""
            QWidget {
                background-color: #1A1A2E;
                color: #E0E0E0;
                font-size: 10pt;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QMainWindow {
                background-color: #121225;
            }
            QFrame {
                border-radius: 8px;
            }
            QFrame#headerFrame {
                background-color: transparent;
                border-radius: 0px;
                padding: 5px;
                margin-bottom: 5px;
                border: none;
            }
            QFrame#liqHeaderFrame {
                background-color: transparent;
                border-radius: 0px;
                padding: 5px;
                margin-bottom: 5px;
                border: none;
            }
            QFrame#controlFrame {
                background-color: #1F1F3D;
                border-radius: 8px;
                padding: 8px;
                margin-bottom: 5px;
            }
            QFrame#contentFrame {
                background-color: #1F1F3D;
                border-radius: 8px;
                padding: 10px;
                margin-bottom: 5px;
            }
            QFrame#liqFrame {
                background-color: #1F1F3D;
                border-radius: 8px;
                padding: 10px;
                margin-bottom: 5px;
            }
            QFrame#statusFrame {
                background-color: #1F1F3D;
                border-radius: 8px;
                padding: 5px;
            }
            QFrame#tableContainer {
                border: none;
                background-color: transparent;
            }
            QLabel#titleLabel {
                color: #FFFFFF;
                font-size: 18pt;
                font-weight: bold;
                letter-spacing: 0.5px;
            }
            QLabel#subtitleLabel {
                color: #AAAAAA;
                font-size: 9pt;
                letter-spacing: 0.5px;
            }
            QLabel#liqTitleLabel {
                color: #FFFFFF;
                font-size: 18pt;
                font-weight: bold;
                letter-spacing: 0.5px;
            }
            QLabel#liqSubtitleLabel {
                color: #AAAAAA;
                font-size: 9pt;
                letter-spacing: 0.5px;
            }
            QLabel#autotraderTitleLabel {
                color: #FFFFFF;
                font-size: 18pt;
                font-weight: bold;
                letter-spacing: 0.5px;
            }
            QLabel#autotraderSubtitleLabel {
                color: #AAAAAA;
                font-size: 9pt;
                letter-spacing: 0.5px;
            }
            QLabel#descriptionLabel {
                color: #AAAAAA;
                font-size: 9pt;
                font-style: italic;
            }
            QLabel#statusLabel {
                color: #FFFFFF;
                font-weight: bold;
            }
            QLabel#balanceLabel {
                color: #4CAF50;
                font-weight: bold;
            }
            QPushButton#enableButton {
                background-color: #4CAF50;
                border: 1px solid #2E7D32;
                border-radius: 4px;
                color: white;
                font-weight: bold;
                padding: 8px;
                min-height: 30px;
            }
            QPushButton#enableButton:checked {
                background-color: #FF3B30;
                border: 1px solid #C62828;
            }
            QLabel#headerStatsTitle {
                color: #DDDDDD;
                font-size: 8pt;
                font-weight: bold;
            }
            QLabel#headerStatsValue {
                color: #FFFFFF;
                font-size: 10pt;
                font-weight: bold;
            }
            QLabel#sectionHeader {
                color: #FFFFFF;
                font-size: 14pt;
                font-weight: bold;
                margin-bottom: 5px;
            }
            QLabel#statusLabel {
                color: #FFFFFF;
                font-weight: bold;
                font-size: 11pt;
            }
            QLabel#versionLabel {
                color: #AAAAAA;
                font-size: 9pt;
            }
            QLabel#controlSectionLabel {
                color: #FFFFFF;
                font-size: 9pt;
                font-weight: bold;
            }
            QLabel#controlLabel {
                color: #DDDDDD;
                font-size: 9pt;
            }
            QLabel#statsLabel {
                color: #DDDDDD;
                font-size: 9pt;
            }
            QLabel#statsValue {
                color: #FFFFFF;
                font-size: 9pt;
                font-weight: bold;
            }
            QGroupBox {
                border: 1px solid #4A4A82;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 12px;
                font-weight: bold;
                color: #FFFFFF;
                font-size: 9pt;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 8px;
                background-color: #1F1F3D;
            }
            QGroupBox#controlGroupBox {
                background-color: rgba(40, 40, 70, 0.5);
            }
            QFrame#headerSeparator {
                color: rgba(255, 255, 255, 0.3);
            }
            QPushButton {
                background-color: #2D2D5B;
                border: 1px solid #4A4A82;
                border-radius: 4px;
                padding: 4px;
                min-height: 22px;
                color: #FFFFFF;
                font-weight: bold;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #3E3E7E;
                border: 1px solid #6A6AE2;
            }
            QPushButton:pressed {
                background-color: #C3073F;
                border: 1px solid #FF0266;
            }
            QPushButton:checked {
                background-color: #C3073F;
                border: 1px solid #FF0266;
            }
            QPushButton#resetButton {
                background-color: #950740;
                border: 1px solid #C3073F;
                font-weight: bold;
                padding: 3px 10px;
                font-size: 9pt;
            }
            QPushButton#resetButton:hover {
                background-color: #C3073F;
                border: 1px solid #FF0266;
            }
            QPushButton#exchangeButton {
                background-color: #2D2D5B;
                border: 1px solid #4A4A82;
                border-radius: 4px;
                padding: 3px;
                font-weight: bold;
                min-width: 80px;
                font-size: 9pt;
            }
            QPushButton#exchangeButton:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #C3073F, stop:1 #950740);
                border: 1px solid #FF0266;
            }
            QComboBox {
                background-color: #2D2D5B;
                border: 1px solid #4A4A82;
                border-radius: 4px;
                padding: 4px;
                min-height: 22px;
                color: #FFFFFF;
                font-size: 9pt;
            }
            QComboBox:hover {
                background-color: #3E3E7E;
                border: 1px solid #6A6AE2;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 16px;
                border-left-width: 1px;
                border-left-color: #4A4A82;
                border-left-style: solid;
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }
            QComboBox::down-arrow {
                width: 10px;
                height: 10px;
            }
            QComboBox#mainComboBox {
                background-color: #2D2D5B;
                border: 1px solid #4A4A82;
                border-radius: 4px;
                padding: 4px;
                min-height: 22px;
                font-weight: bold;
                font-size: 9pt;
            }
            QComboBox#valueComboBox {
                background-color: #2D2D5B;
                border: 1px solid #4A4A82;
                border-radius: 4px;
                padding: 4px 8px;
                min-height: 22px;
                font-weight: bold;
                color: #FFFFFF;
                font-size: 9pt;
            }
            QTableWidget {
                background-color: #121225;
                alternate-background-color: #1A1A2E;
                border: 1px solid #4A4A82;
                border-radius: 8px;
                gridline-color: #4A4A82;
                selection-background-color: #C3073F;
                selection-color: #FFFFFF;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #2D2D5B;
            }
            QTableWidget::item:selected {
                background-color: #C3073F;
            }
            QHeaderView::section {
                background-color: #2D2D5B;
                color: #FFFFFF;
                padding: 10px;
                border: 1px solid #4A4A82;
                font-weight: bold;
            }
            QScrollBar:vertical {
                border: none;
                background-color: #1A1A2E;
                width: 12px;
                margin: 15px 0 15px 0;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #4A4A82;
                min-height: 30px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #6A6AE2;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
                height: 15px;
                subcontrol-position: bottom;
                subcontrol-origin: margin;
            }
            QScrollBar:horizontal {
                border: none;
                background-color: #1A1A2E;
                height: 12px;
                margin: 0 15px 0 15px;
                border-radius: 6px;
            }
            QScrollBar::handle:horizontal {
                background-color: #4A4A82;
                min-width: 30px;
                border-radius: 6px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #6A6AE2;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                border: none;
                background: none;
                width: 15px;
                subcontrol-position: right;
                subcontrol-origin: margin;
            }
        """)

    def on_symbol_changed(self, symbol):
        """Handle symbol change by restarting WebSocket connections"""
        self.stop_all_threads()
        self.start_monitoring()
        self.status_label.setText(f"Status: Changed symbol to {symbol}")

        # Add 'ALL' option to the symbol combo if it doesn't exist
        if 'ALL' not in [self.symbol_combo.itemText(i) for i in range(self.symbol_combo.count())]:
            self.symbol_combo.addItem('ALL')

    def on_threshold_type_changed(self, threshold_type):
        """Handle change between volume-based and dollar-based detection"""
        if threshold_type == 'Volume Based':
            self.threshold_type = 'volume'
            # Show volume threshold controls, hide dollar threshold controls
            for i in range(self.volume_threshold_layout.count()):
                widget = self.volume_threshold_layout.itemAt(i).widget()
                if widget:
                    widget.setVisible(True)
            self.volume_threshold_layout.setEnabled(True)

            for i in range(self.dollar_threshold_layout.count()):
                widget = self.dollar_threshold_layout.itemAt(i).widget()
                if widget:
                    widget.setVisible(False)
            self.dollar_threshold_layout.setEnabled(False)

            self.status_label.setText(f"Status: Using volume-based whale detection (threshold: {self.volume_threshold})")
        else:
            self.threshold_type = 'dollar'
            # Show dollar threshold controls, hide volume threshold controls
            for i in range(self.dollar_threshold_layout.count()):
                widget = self.dollar_threshold_layout.itemAt(i).widget()
                if widget:
                    widget.setVisible(True)
            self.dollar_threshold_layout.setEnabled(True)

            for i in range(self.volume_threshold_layout.count()):
                widget = self.volume_threshold_layout.itemAt(i).widget()
                if widget:
                    widget.setVisible(False)
            self.volume_threshold_layout.setEnabled(False)

            self.status_label.setText(f"Status: Using dollar-based whale detection (threshold: ${self.dollar_threshold:,.0f})")

    def on_volume_threshold_changed(self, threshold):
        """Update volume threshold for highlighting"""
        self.volume_threshold = float(threshold)
        if self.threshold_type == 'volume':
            self.status_label.setText(f"Status: Whale volume threshold set to {threshold}")

    def on_dollar_threshold_changed(self, threshold):
        """Update dollar threshold for highlighting"""
        self.dollar_threshold = float(threshold)
        if self.threshold_type == 'dollar':
            self.status_label.setText(f"Status: Whale dollar threshold set to ${float(threshold):,.0f}")

    def reset_statistics(self):
        """Reset all statistics counters"""
        self.total_trades = 0
        self.whale_count = 0
        self.last_whale_time = None

        # Update UI
        self.total_trades_label.setText("0")
        self.whale_count_label.setText("0")
        self.last_whale_label.setText("None")

        # Clear the table
        self.table.setRowCount(0)

        # Show confirmation
        self.status_label.setText("Status: Statistics reset")

        # Flash the status bar to confirm reset
        status_frame = self.status_label.parent()
        original_style = status_frame.styleSheet()
        status_frame.setStyleSheet("background-color: #1E88E5; border-radius: 5px; padding: 5px;")
        QTimer.singleShot(1000, lambda: status_frame.setStyleSheet(original_style))

    def toggle_exchange(self, exchange):
        """Toggle WebSocket connection for an exchange"""
        # Get button state based on exchange name
        exchange_buttons = {
            'Binance': self.binance_btn,
            'Huobi': self.huobi_btn,
            'Coinbase': self.coinbase_btn,
            'Kraken': self.kraken_btn,
            'Bybit': self.bybit_btn
        }

        if exchange not in exchange_buttons:
            return

        is_checked = exchange_buttons[exchange].isChecked()

        if is_checked and exchange not in self.threads:
            # Start the thread
            self.start_exchange_thread(exchange)
            # Also start liquidation thread if supported
            if exchange in ['Binance', 'Huobi', 'Bybit'] and exchange not in self.liq_threads:
                self.start_liq_thread(exchange)
            self.status_label.setText(f"Status: Started {exchange} connection")
        elif not is_checked and exchange in self.threads:
            # Stop the thread
            self.stop_thread(exchange)
            # Also stop liquidation thread if running
            if exchange in self.liq_threads:
                self.stop_liq_thread(exchange)
            self.status_label.setText(f"Status: Stopped {exchange} connection")

    def start_monitoring(self):
        """Start monitoring live trades on selected exchanges"""
        exchanges = []
        exchange_buttons = {
            'Binance': self.binance_btn,
            'Huobi': self.huobi_btn,
            'Coinbase': self.coinbase_btn,
            'Kraken': self.kraken_btn,
            'Bybit': self.bybit_btn
        }

        for exchange, button in exchange_buttons.items():
            if button.isChecked():
                exchanges.append(exchange)

        for exchange in exchanges:
            self.start_exchange_thread(exchange)
            # Also start liquidation threads for supported exchanges
            if exchange in ['Binance', 'Huobi', 'Bybit']:
                self.start_liq_thread(exchange)

        if exchanges:
            self.status_label.setText(f"Status: Monitoring {', '.join(exchanges)} for {self.symbol_combo.currentText()}")
        else:
            self.status_label.setText(f"Status: No exchanges selected. Please select at least one exchange.")

    def start_exchange_thread(self, exchange):
        """Start a WebSocket thread for a specific exchange"""
        if exchange in self.threads:
            self.stop_thread(exchange)

        symbol = self.symbol_combo.currentText()
        thread = LiveTradeThread(exchange, symbol)
        thread.update_trade.connect(self.add_trade_to_table)
        thread.start()
        self.threads[exchange] = thread
        logging.info(f"Started {exchange} WebSocket thread for {symbol}")

    def stop_thread(self, exchange):
        """Stop a specific WebSocket thread"""
        if exchange in self.threads:
            self.threads[exchange].stop()
            self.threads[exchange].wait(1000)  # Wait for thread to finish
            del self.threads[exchange]
            logging.info(f"Stopped {exchange} WebSocket thread")

    def stop_all_threads(self):
        """Stop all WebSocket threads"""
        for exchange in list(self.threads.keys()):
            self.stop_thread(exchange)

        # Also stop all liquidation threads
        for exchange in list(self.liq_threads.keys()):
            self.stop_liq_thread(exchange)

    def add_trade_to_table(self, trade):
        """Add a trade to the table and highlight if it's a whale trade"""
        # Check if this is an info message (for disabled exchanges)
        if 'side' in trade and trade['side'] == 'info':
            row_position = 0
            self.table.insertRow(row_position)  # Insert at top

            # Add info data to table
            exchange_item = QTableWidgetItem(trade['exchange'])
            self.table.setItem(row_position, 0, exchange_item)

            symbol_item = QTableWidgetItem(trade['symbol'])
            self.table.setItem(row_position, 1, symbol_item)

            # Use the info_message for the price column
            info_message = trade.get('info_message', 'Exchange information')
            info_item = QTableWidgetItem(info_message)
            info_item.setForeground(QColor('#FFCC00'))  # Yellow for info
            self.table.setItem(row_position, 2, info_item)

            # Fill remaining columns with empty items
            for col in range(3, 7):
                self.table.setItem(row_position, col, QTableWidgetItem(""))

            # Set background color for the entire row
            info_color = QColor('#2D4263')  # Dark blue for info rows
            for col in range(self.table.columnCount()):
                if self.table.item(row_position, col):
                    self.table.item(row_position, col).setBackground(info_color)

            return

        # Update total trades counter
        self.total_trades += 1
        self.total_trades_label.setText(str(self.total_trades))

        # Limit table size to prevent memory issues
        if self.table.rowCount() >= self.max_table_rows:
            self.table.removeRow(self.table.rowCount() - 1)

        row_position = 0
        self.table.insertRow(row_position)  # Insert at top

        # Add trade data to table
        exchange_item = QTableWidgetItem(trade['exchange'])
        self.table.setItem(row_position, 0, exchange_item)

        symbol_item = QTableWidgetItem(trade['symbol'])
        self.table.setItem(row_position, 1, symbol_item)

        price_item = QTableWidgetItem(f"{trade['price']:.2f}")
        price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.table.setItem(row_position, 2, price_item)

        volume_item = QTableWidgetItem(f"{trade['amount']:.4f}")
        volume_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.table.setItem(row_position, 3, volume_item)

        # Calculate and add dollar value
        dollar_value = trade['price'] * trade['amount']
        dollar_item = QTableWidgetItem(f"${dollar_value:,.2f}")
        dollar_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.table.setItem(row_position, 4, dollar_item)

        # Set buy/sell with appropriate styling
        side_text = 'BUY' if trade['side'] == 'buy' else 'SELL'
        side_item = QTableWidgetItem(side_text)
        side_item.setTextAlignment(Qt.AlignCenter)

        # Set text color based on trade side
        if trade['side'] == 'buy':
            side_item.setForeground(QColor('#00FF00'))  # Green for buys
        else:
            side_item.setForeground(QColor('#FF4444'))  # Red for sells

        self.table.setItem(row_position, 5, side_item)

        # Format timestamp
        timestamp = trade['timestamp']
        if isinstance(timestamp, int):
            # Convert milliseconds to seconds if needed
            if timestamp > *************:  # If in milliseconds
                timestamp = timestamp / 1000
            formatted_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
        else:
            formatted_time = str(timestamp)

        time_item = QTableWidgetItem(formatted_time)
        self.table.setItem(row_position, 6, time_item)

        # Determine if this is a whale trade based on the selected threshold type
        is_whale = False
        if self.threshold_type == 'volume':
            is_whale = trade['amount'] >= self.volume_threshold
        else:  # dollar-based threshold
            is_whale = dollar_value >= self.dollar_threshold

        if is_whale:
            # Update whale counter
            self.whale_count += 1
            self.whale_count_label.setText(str(self.whale_count))

            # Update last whale time
            self.last_whale_time = formatted_time
            self.last_whale_label.setText(formatted_time)

            # Highlight the entire row
            row_color = QColor('#1E5128') if trade['side'] == 'buy' else QColor('#5C1E1E')

            for col in range(self.table.columnCount()):
                if self.table.item(row_position, col):
                    self.table.item(row_position, col).setBackground(row_color)
                    # Make text bold for whale trades
                    font = self.table.item(row_position, col).font()
                    font.setBold(True)
                    self.table.item(row_position, col).setFont(font)

            # Update status bar with alert
            if self.threshold_type == 'volume':
                threshold_info = f"volume: {trade['amount']:.4f}"
            else:
                threshold_info = f"value: ${dollar_value:,.2f}"

            alert_message = (
                f"🚨 WHALE ALERT! {trade['exchange']}: {trade['symbol']} {side_text} "
                f"({threshold_info}) at {trade['price']:.2f}"
            )
            self.status_label.setText(alert_message)

            # Flash the status bar by changing its color temporarily
            status_frame = self.status_label.parent()
            original_style = status_frame.styleSheet()

            # Change to alert color based on trade side
            alert_color = "#007700" if trade['side'] == 'buy' else "#770000"
            status_frame.setStyleSheet(f"background-color: {alert_color}; border-radius: 5px; padding: 5px;")

            # Reset after 2 seconds
            QTimer.singleShot(2000, lambda: status_frame.setStyleSheet(original_style))

        # Update autotrader with live price data if enabled
        self.update_position_with_live_price(trade)

    def load_trading_accounts(self):
        """Load trading accounts from credentials.yaml file"""
        try:
            credentials_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'credentials.yaml')
            if not os.path.exists(credentials_path):
                logging.warning(f"Credentials file not found: {credentials_path}")
                return []

            with open(credentials_path, 'r') as file:
                credentials = yaml.safe_load(file)

            if not credentials or 'accounts' not in credentials:
                logging.warning("No accounts found in credentials file")
                return []

            accounts = credentials.get('accounts', [])
            default_account = credentials.get('default_account')

            # Set default account if available
            if default_account and self.autotrader_account is None:
                for account in accounts:
                    if account.get('name') == default_account:
                        self.autotrader_account = account
                        break

            return accounts
        except Exception as e:
            logging.error(f"Error loading credentials: {e}")
            return []

    def connect_to_exchange_api(self, exchange_name, api_key, secret_key):
        """Connect to the exchange API using credentials"""
        try:
            if exchange_name.lower() == 'huobi':
                # Create a Huobi API client
                return self.create_huobi_client(api_key, secret_key)
            elif exchange_name.lower() == 'binance':
                # Create a Binance API client
                return self.create_binance_client(api_key, secret_key)
            elif exchange_name.lower() == 'bybit':
                # Create a Bybit API client
                return self.create_bybit_client(api_key, secret_key)
            else:
                logging.error(f"Unsupported exchange: {exchange_name}")
                return None
        except Exception as e:
            logging.error(f"Error connecting to {exchange_name} API: {e}")
            return None

    def create_huobi_client(self, api_key, secret_key):
        """Create a Huobi/HTX API client"""
        class HuobiClient:
            def __init__(self, api_key, secret_key):
                self.api_key = api_key
                self.secret_key = secret_key
                # Try both Huobi and HTX base URLs
                self.base_urls = ["https://api.huobi.pro", "https://api.htx.com"]
                self.base_url = self.base_urls[0]  # Default to Huobi

            def _generate_signature(self, method, endpoint, additional_params=None):
                """Generate signature for Huobi/HTX API request"""
                timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%S')

                # Initialize params
                params = {}

                # Add additional params if provided
                if additional_params:
                    params.update(additional_params)

                # Add standard auth params
                params.update({
                    'AccessKeyId': self.api_key,
                    'SignatureMethod': 'HmacSHA256',
                    'SignatureVersion': '2',
                    'Timestamp': timestamp
                })

                # Sort parameters
                sorted_params = sorted(params.items(), key=lambda d: d[0])
                query_string = urllib.parse.urlencode(sorted_params)

                # Determine host based on endpoint
                if 'linear-swap-api' in endpoint:
                    host = "api.hbdm.com"  # Use futures API host for linear swap
                elif 'swap-api' in endpoint:
                    host = "api.hbdm.com"  # Use futures API host for swap
                elif endpoint.startswith('/api/v1/contract'):
                    host = "api.hbdm.com"  # Use futures API host for contract
                else:
                    host = "api.huobi.pro"  # Default to main API host

                # Create signature payload
                payload = '\n'.join([method, host, endpoint, query_string])

                # Generate signature
                signature = base64.b64encode(
                    hmac.new(
                        self.secret_key.encode('utf-8'),
                        payload.encode('utf-8'),
                        hashlib.sha256
                    ).digest()
                ).decode('utf-8')

                params['Signature'] = signature
                return params

            def get_account_balance(self):
                """Get account balance from Huobi"""
                logging.info("Fetching Huobi account balance...")

                # Try spot account first
                try:
                    spot_balance = self._get_spot_account_balance()
                    if spot_balance > 0:
                        logging.info(f"Found Huobi spot balance: ${spot_balance:.2f}")
                        return spot_balance
                except Exception as e:
                    logging.error(f"Error fetching Huobi spot balance: {e}")

                # Try futures account if spot returns 0
                try:
                    futures_balance = self._get_futures_account_balance()
                    if futures_balance > 0:
                        logging.info(f"Found Huobi futures balance: ${futures_balance:.2f}")
                        return futures_balance
                except Exception as e:
                    logging.error(f"Error fetching Huobi futures balance: {e}")

                logging.warning("No balance found in Huobi spot or futures accounts")
                return 0

            def _get_spot_account_balance(self):
                """Get spot account balance from Huobi/HTX"""
                # Try each base URL (Huobi and HTX)
                for base_url in self.base_urls:
                    try:
                        logging.debug(f"Trying spot account with base URL: {base_url}")

                        endpoint = "/v1/account/accounts"
                        params = self._generate_signature('GET', endpoint)

                        url = f"{base_url}{endpoint}?{urllib.parse.urlencode(params)}"
                        logging.debug(f"Requesting HTX spot accounts: {url}")
                        response = requests.get(url)

                        if response.status_code == 200:
                            accounts = response.json()
                            logging.debug(f"HTX spot accounts response: {accounts}")

                            if accounts.get('status') == 'ok' and accounts.get('data'):
                                # Find the spot account
                                for account in accounts['data']:
                                    if account.get('type') == 'spot':
                                        account_id = account['id']
                                        logging.debug(f"Using HTX spot account ID: {account_id}")

                                        # Get balance for this account
                                        balance_endpoint = f"/v1/account/accounts/{account_id}/balance"
                                        balance_params = self._generate_signature('GET', balance_endpoint)

                                        balance_url = f"{base_url}{balance_endpoint}?{urllib.parse.urlencode(balance_params)}"
                                        logging.debug(f"Requesting HTX spot balance: {balance_url}")
                                        balance_response = requests.get(balance_url)

                                        if balance_response.status_code == 200:
                                            balance_data = balance_response.json()
                                            logging.debug(f"HTX spot balance response: {balance_data}")

                                            if balance_data.get('status') == 'ok':
                                                # Find USDT balance
                                                usdt_balance = 0
                                                for currency in balance_data['data']['list']:
                                                    if currency['currency'] == 'usdt' and currency['type'] == 'trade':
                                                        usdt_balance = float(currency['balance'])
                                                        if usdt_balance > 0:
                                                            logging.info(f"Found HTX spot USDT balance: {usdt_balance}")
                                                            return usdt_balance
                    except Exception as e:
                        logging.error(f"Error fetching spot balance with base URL {base_url}: {e}")

                return 0

            def _get_futures_account_balance(self):
                """Get futures account balance from Huobi/HTX"""
                # Try all possible Huobi/HTX futures API base URLs
                futures_base_urls = [
                    "https://api.hbdm.com",     # Original Huobi futures API
                    "https://api.htx.com",       # New HTX API
                    "https://api.huobi.pro"      # Main Huobi API that might redirect
                ]

                for futures_base_url in futures_base_urls:
                    logging.debug(f"Trying futures base URL: {futures_base_url}")

                    # Try each of the possible futures endpoints

                    # 1. Try linear swap cross margin account info (for USDT contracts like MOODENG-USDT)
                    try:
                        linear_swap_endpoint = "/linear-swap-api/v1/swap_cross_account_info"
                        linear_swap_params = self._generate_signature('POST', linear_swap_endpoint)

                        linear_swap_url = f"{futures_base_url}{linear_swap_endpoint}?{urllib.parse.urlencode(linear_swap_params)}"
                        logging.debug(f"Requesting HTX linear swap cross account info: {linear_swap_url}")
                        linear_swap_response = requests.post(linear_swap_url)

                        if linear_swap_response.status_code == 200:
                            linear_swap_result = linear_swap_response.json()
                            logging.debug(f"HTX linear swap cross account info response: {linear_swap_result}")

                            if linear_swap_result.get('status') == 'ok' and linear_swap_result.get('data'):
                                # Find USDT balance in cross margin account
                                for account in linear_swap_result['data']:
                                    if account.get('margin_asset') == 'USDT':
                                        usdt_balance = float(account.get('margin_balance', 0))
                                        if usdt_balance > 0:
                                            logging.info(f"Found HTX linear swap cross margin USDT balance: {usdt_balance}")
                                            return usdt_balance
                    except Exception as e:
                        logging.error(f"Error fetching HTX linear swap cross account info: {e}")

                    # 2. Try linear swap isolated margin account info (specific contract)
                    try:
                        # Try with MOODENG-USDT contract specifically
                        contract_params = {
                            'contract_code': 'MOODENG/USDT:USDT'
                        }
                        isolated_endpoint = "/linear-swap-api/v1/swap_account_info"
                        isolated_params = self._generate_signature('POST', isolated_endpoint, contract_params)

                        isolated_url = f"{futures_base_url}{isolated_endpoint}?{urllib.parse.urlencode(isolated_params)}"
                        logging.debug(f"Requesting HTX linear swap isolated account info for MOODENG-USDT: {isolated_url}")
                        isolated_response = requests.post(isolated_url, json=contract_params)

                        if isolated_response.status_code == 200:
                            isolated_result = isolated_response.json()
                            logging.debug(f"HTX linear swap isolated account info response: {isolated_result}")

                            if isolated_result.get('status') == 'ok' and isolated_result.get('data'):
                                # Get balance from isolated margin account
                                for account in isolated_result['data']:
                                    if account.get('contract_code') == 'MOODENG-USDT':
                                        usdt_balance = float(account.get('margin_balance', 0))
                                        if usdt_balance > 0:
                                            logging.info(f"Found HTX linear swap isolated margin MOODENG-USDT balance: {usdt_balance}")
                                            return usdt_balance
                    except Exception as e:
                        logging.error(f"Error fetching HTX linear swap isolated account info: {e}")

                    # 3. Try standard contract account info (for inverse contracts)
                    try:
                        contract_endpoint = "/api/v1/contract_account_info"
                        contract_params = self._generate_signature('POST', contract_endpoint)

                        contract_url = f"{futures_base_url}{contract_endpoint}?{urllib.parse.urlencode(contract_params)}"
                        logging.debug(f"Requesting HTX futures contract account info: {contract_url}")
                        contract_response = requests.post(contract_url)

                        if contract_response.status_code == 200:
                            contract_result = contract_response.json()
                            logging.debug(f"HTX futures contract account info response: {contract_result}")

                            if contract_result.get('status') == 'ok' and contract_result.get('data'):
                                # Find USDT balance
                                for account in contract_result['data']:
                                    if account.get('symbol') == 'USDT':
                                        usdt_balance = float(account.get('margin_balance', 0))
                                        if usdt_balance > 0:
                                            logging.info(f"Found HTX futures contract USDT balance: {usdt_balance}")
                                            return usdt_balance
                    except Exception as e:
                        logging.error(f"Error fetching HTX futures contract account info: {e}")

                    # 4. Try coin-margined swap account info
                    try:
                        swap_endpoint = "/swap-api/v1/swap_account_info"
                        swap_params = self._generate_signature('POST', swap_endpoint)

                        swap_url = f"{futures_base_url}{swap_endpoint}?{urllib.parse.urlencode(swap_params)}"
                        logging.debug(f"Requesting HTX coin-margined swap account info: {swap_url}")
                        swap_response = requests.post(swap_url)

                        if swap_response.status_code == 200:
                            swap_result = swap_response.json()
                            logging.debug(f"HTX coin-margined swap account info response: {swap_result}")

                            if swap_result.get('status') == 'ok' and swap_result.get('data'):
                                # Find USDT balance
                                for account in swap_result['data']:
                                    if account.get('margin_asset') == 'USDT':
                                        usdt_balance = float(account.get('margin_balance', 0))
                                        if usdt_balance > 0:
                                            logging.info(f"Found HTX coin-margined swap USDT balance: {usdt_balance}")
                                            return usdt_balance
                    except Exception as e:
                        logging.error(f"Error fetching HTX coin-margined swap account info: {e}")

                # If we've tried all URLs and endpoints and still haven't found a balance, return 0
                logging.warning("No balance found in any HTX futures account type")
                return 0

            def get_open_orders(self, symbol):
                """Get open orders from Huobi"""
                endpoint = "/v1/order/openOrders"
                params = {
                    'symbol': symbol
                }

                signed_params = self._generate_signature('GET', endpoint, params)
                url = f"{self.base_url}{endpoint}?{urllib.parse.urlencode(signed_params)}"
                response = requests.get(url)

                if response.status_code == 200:
                    orders = response.json()
                    if orders['status'] == 'ok':
                        return orders['data']
                return []

            def create_order(self, symbol, amount, price=None, order_type=None):
                """Create an order on Huobi/HTX"""
                # Check if this is a futures contract (contains '-')
                if '-' in symbol and 'USDT' in symbol:
                    return self.create_futures_order(symbol, amount, price, order_type)
                else:
                    return self.create_spot_order(symbol, amount, price, order_type)

            def create_spot_order(self, symbol, amount, price=None, order_type=None):
                """Create a spot order on Huobi/HTX"""
                endpoint = "/v1/order/orders/place"

                # Get account ID
                accounts_endpoint = "/v1/account/accounts"
                accounts_params = self._generate_signature('GET', accounts_endpoint)

                # Try each base URL
                for base_url in self.base_urls:
                    try:
                        accounts_url = f"{base_url}{accounts_endpoint}?{urllib.parse.urlencode(accounts_params)}"
                        accounts_response = requests.get(accounts_url)

                        if accounts_response.status_code != 200:
                            continue

                        accounts = accounts_response.json()
                        if accounts.get('status') != 'ok' or not accounts.get('data'):
                            continue

                        # Find spot account
                        account_id = None
                        for account in accounts['data']:
                            if account.get('type') == 'spot':
                                account_id = account['id']
                                break

                        if not account_id:
                            continue

                        # Prepare order parameters
                        params = {
                            'account-id': account_id,
                            'symbol': symbol,
                            'amount': str(amount)
                        }

                        # Set order type and price
                        if order_type == 'buy-limit' or order_type == 'sell-limit':
                            params['type'] = order_type
                            params['price'] = str(price)
                        else:
                            params['type'] = 'buy-market' if order_type == 'buy-market' else 'sell-market'

                        # Sign and send request
                        signed_params = self._generate_signature('POST', endpoint)
                        url = f"{base_url}{endpoint}?{urllib.parse.urlencode(signed_params)}"

                        headers = {"Content-Type": "application/json"}
                        logging.debug(f"Creating spot order: {url}, params: {params}")
                        response = requests.post(url, json=params, headers=headers)

                        if response.status_code == 200:
                            result = response.json()
                            logging.debug(f"Spot order response: {result}")
                            if result.get('status') == 'ok':
                                return result.get('data')
                    except Exception as e:
                        logging.error(f"Error creating spot order: {e}")

                return None

            def create_futures_order(self, symbol, amount, price=None, order_type=None):
                """Create a futures order on HTX"""
                # Parse order type to determine direction and offset
                direction = None
                offset = None

                if order_type:
                    if order_type.startswith('buy'):
                        direction = 'buy'
                    elif order_type.startswith('sell'):
                        direction = 'sell'

                    # Default to open position
                    offset = 'open'

                    # Check if this is a close order
                    if 'close_long' in order_type:
                        direction = 'sell'
                        offset = 'close'
                    elif 'close_short' in order_type:
                        direction = 'buy'
                        offset = 'close'
                else:
                    # Default to buy open
                    direction = 'buy'
                    offset = 'open'

                # Determine order price type
                order_price_type = 'limit'
                if order_type and 'market' in order_type:
                    order_price_type = 'market'
                elif not price:
                    order_price_type = 'market'

                # Prepare order parameters
                order_params = {
                    'contract_code': symbol,
                    'volume': amount,
                    'direction': direction,
                    'offset': offset,
                    'lever_rate': self.lever_rate if hasattr(self, 'lever_rate') else 5,
                    'order_price_type': order_price_type
                }

                # Add price for limit orders
                if order_price_type == 'limit' and price:
                    order_params['price'] = price

                # Linear swap API endpoint
                endpoint = "/linear-swap-api/v1/swap_order"

                # Generate signature
                signed_params = self._generate_signature('POST', endpoint)

                # Try each futures base URL
                futures_base_urls = [
                    "https://api.hbdm.com",
                    "https://api.htx.com"
                ]

                for base_url in futures_base_urls:
                    try:
                        url = f"{base_url}{endpoint}?{urllib.parse.urlencode(signed_params)}"

                        # Send request
                        headers = {
                            'Content-Type': 'application/json'
                        }

                        logging.debug(f"Creating futures order: {url}, params: {order_params}")
                        response = requests.post(url, headers=headers, json=order_params)

                        if response.status_code == 200:
                            result = response.json()
                            logging.debug(f"Futures order response: {result}")
                            if result.get('status') == 'ok':
                                return {
                                    'order_id': result.get('data', {}).get('order_id'),
                                    'client_order_id': result.get('data', {}).get('order_id_str')
                                }
                    except Exception as e:
                        logging.error(f"Error creating futures order: {e}")

                return None

            def set_leverage(self, symbol, leverage):
                """Set leverage for a futures contract"""
                # Linear swap API endpoint for setting leverage
                endpoint = "/linear-swap-api/v1/swap_cross_switch_lever_rate"

                # Prepare parameters
                params = {
                    'contract_code': symbol,
                    'lever_rate': leverage
                }

                # Generate signature
                signed_params = self._generate_signature('POST', endpoint)

                # Try each futures base URL
                futures_base_urls = [
                    "https://api.hbdm.com",
                    "https://api.htx.com"
                ]

                for base_url in futures_base_urls:
                    try:
                        url = f"{base_url}{endpoint}?{urllib.parse.urlencode(signed_params)}"

                        # Send request
                        headers = {
                            'Content-Type': 'application/json'
                        }

                        logging.debug(f"Setting leverage: {url}, params: {params}")
                        response = requests.post(url, headers=headers, json=params)

                        if response.status_code == 200:
                            result = response.json()
                            logging.debug(f"Set leverage response: {result}")
                            if result.get('status') == 'ok':
                                self.lever_rate = leverage
                                return True
                    except Exception as e:
                        logging.error(f"Error setting leverage: {e}")

                return False

            def get_futures_positions(self, symbol=None):
                """Get futures positions"""
                # Linear swap API endpoint for positions
                endpoint = "/linear-swap-api/v1/swap_cross_position_info"

                # Prepare parameters
                params = {}
                if symbol:
                    params['contract_code'] = symbol

                # Generate signature
                signed_params = self._generate_signature('POST', endpoint, params)

                # Try each futures base URL
                futures_base_urls = [
                    "https://api.hbdm.com",
                    "https://api.htx.com"
                ]

                for base_url in futures_base_urls:
                    try:
                        url = f"{base_url}{endpoint}?{urllib.parse.urlencode(signed_params)}"

                        # Send request
                        headers = {
                            'Content-Type': 'application/json'
                        }

                        logging.debug(f"Getting futures positions: {url}")
                        response = requests.post(url, headers=headers, json=params if params else None)

                        if response.status_code == 200:
                            result = response.json()
                            logging.debug(f"Futures positions response: {result}")
                            if result.get('status') == 'ok':
                                return result.get('data', [])
                    except Exception as e:
                        logging.error(f"Error getting futures positions: {e}")

                return []

        return HuobiClient(api_key, secret_key)

    def create_binance_client(self, api_key, secret_key):
        """Create a Binance API client"""
        class BinanceClient:
            def __init__(self, api_key, secret_key):
                self.api_key = api_key
                self.secret_key = secret_key
                self.base_url = "https://api.binance.com"

            def _generate_signature(self, params):
                """Generate signature for Binance API request"""
                query_string = urllib.parse.urlencode(params)
                signature = hmac.new(
                    self.secret_key.encode('utf-8'),
                    query_string.encode('utf-8'),
                    hashlib.sha256
                ).hexdigest()
                return signature

            def get_account_balance(self):
                """Get account balance from Binance"""
                logging.info("Fetching Binance account balance...")

                # Try spot account first
                try:
                    spot_balance = self._get_spot_account_balance()
                    if spot_balance > 0:
                        logging.info(f"Found Binance spot balance: ${spot_balance:.2f}")
                        return spot_balance
                except Exception as e:
                    logging.error(f"Error fetching Binance spot balance: {e}")

                # Try futures account if spot returns 0
                try:
                    futures_balance = self._get_futures_account_balance()
                    if futures_balance > 0:
                        logging.info(f"Found Binance futures balance: ${futures_balance:.2f}")
                        return futures_balance
                except Exception as e:
                    logging.error(f"Error fetching Binance futures balance: {e}")

                logging.warning("No balance found in Binance spot or futures accounts")
                return 0

            def _get_spot_account_balance(self):
                """Get spot account balance from Binance"""
                endpoint = "/api/v3/account"
                timestamp = int(time.time() * 1000)

                params = {
                    'timestamp': timestamp
                }

                # Generate signature
                signature = self._generate_signature(params)
                params['signature'] = signature

                # Send request
                headers = {
                    'X-MBX-APIKEY': self.api_key
                }

                url = f"{self.base_url}{endpoint}?{urllib.parse.urlencode(params)}"
                logging.debug(f"Requesting Binance spot account: {url}")
                response = requests.get(url, headers=headers)

                if response.status_code == 200:
                    account = response.json()
                    logging.debug(f"Binance spot account response: {account}")

                    # Find USDT balance
                    usdt_balance = 0
                    for asset in account['balances']:
                        if asset['asset'] == 'USDT':
                            usdt_balance = float(asset['free'])
                            break
                    return usdt_balance
                return 0

            def _get_futures_account_balance(self):
                """Get futures account balance from Binance"""
                # Binance futures API uses a different base URL
                futures_base_url = "https://fapi.binance.com"

                endpoint = "/fapi/v2/account"
                timestamp = int(time.time() * 1000)

                params = {
                    'timestamp': timestamp
                }

                # Generate signature
                signature = self._generate_signature(params)
                params['signature'] = signature

                # Send request
                headers = {
                    'X-MBX-APIKEY': self.api_key
                }

                url = f"{futures_base_url}{endpoint}?{urllib.parse.urlencode(params)}"
                logging.debug(f"Requesting Binance futures account: {url}")
                response = requests.get(url, headers=headers)

                if response.status_code == 200:
                    account = response.json()
                    logging.debug(f"Binance futures account response: {account}")

                    # Find USDT balance in assets
                    usdt_balance = 0
                    if 'assets' in account:
                        for asset in account['assets']:
                            if asset['asset'] == 'USDT':
                                usdt_balance = float(asset['walletBalance'])
                                break

                    # If not found in assets, try availableBalance
                    if usdt_balance == 0 and 'availableBalance' in account:
                        usdt_balance = float(account['availableBalance'])

                    return usdt_balance
                return 0

            def get_open_orders(self, symbol):
                """Get open orders from Binance"""
                endpoint = "/api/v3/openOrders"
                timestamp = int(time.time() * 1000)

                params = {
                    'symbol': symbol,
                    'timestamp': timestamp
                }

                # Generate signature
                signature = self._generate_signature(params)
                params['signature'] = signature

                # Send request
                headers = {
                    'X-MBX-APIKEY': self.api_key
                }

                url = f"{self.base_url}{endpoint}?{urllib.parse.urlencode(params)}"
                response = requests.get(url, headers=headers)

                if response.status_code == 200:
                    return response.json()
                return []

            def create_order(self, symbol, side, quantity, price=None, order_type='LIMIT'):
                """Create an order on Binance"""
                endpoint = "/api/v3/order"
                timestamp = int(time.time() * 1000)

                params = {
                    'symbol': symbol,
                    'side': side,
                    'quantity': quantity,
                    'timestamp': timestamp
                }

                # Set order type and price
                if order_type == 'LIMIT':
                    params['type'] = 'LIMIT'
                    params['price'] = price
                    params['timeInForce'] = 'GTC'  # Good Till Canceled
                else:
                    params['type'] = 'MARKET'

                # Generate signature
                signature = self._generate_signature(params)
                params['signature'] = signature

                # Send request
                headers = {
                    'X-MBX-APIKEY': self.api_key
                }

                url = f"{self.base_url}{endpoint}"
                response = requests.post(url, headers=headers, params=params)

                if response.status_code == 200:
                    return response.json()
                return None

        return BinanceClient(api_key, secret_key)

    def create_bybit_client(self, api_key, secret_key):
        """Create a Bybit API client"""
        class BybitClient:
            def __init__(self, api_key, secret_key):
                self.api_key = api_key
                self.secret_key = secret_key
                self.base_url = "https://api.bybit.com"

            def _generate_signature(self, params):
                """Generate signature for Bybit API request"""
                sorted_params = sorted(params.items(), key=lambda d: d[0])
                query_string = urllib.parse.urlencode(sorted_params)
                signature = hmac.new(
                    self.secret_key.encode('utf-8'),
                    query_string.encode('utf-8'),
                    hashlib.sha256
                ).hexdigest()
                return signature

            def get_account_balance(self):
                """Get account balance from Bybit"""
                logging.info("Fetching Bybit account balance...")

                # Try spot account first
                try:
                    spot_balance = self._get_spot_account_balance()
                    if spot_balance > 0:
                        logging.info(f"Found Bybit spot balance: ${spot_balance:.2f}")
                        return spot_balance
                except Exception as e:
                    logging.error(f"Error fetching Bybit spot balance: {e}")

                # Try futures account if spot returns 0
                try:
                    futures_balance = self._get_futures_account_balance()
                    if futures_balance > 0:
                        logging.info(f"Found Bybit futures balance: ${futures_balance:.2f}")
                        return futures_balance
                except Exception as e:
                    logging.error(f"Error fetching Bybit futures balance: {e}")

                # Try USDC account if futures returns 0
                try:
                    usdc_balance = self._get_usdc_account_balance()
                    if usdc_balance > 0:
                        logging.info(f"Found Bybit USDC balance: ${usdc_balance:.2f}")
                        return usdc_balance
                except Exception as e:
                    logging.error(f"Error fetching Bybit USDC balance: {e}")

                logging.warning("No balance found in Bybit spot, futures, or USDC accounts")
                return 0

            def _get_spot_account_balance(self):
                """Get spot account balance from Bybit"""
                endpoint = "/v2/private/wallet/balance"
                timestamp = int(time.time() * 1000)

                params = {
                    'api_key': self.api_key,
                    'timestamp': timestamp
                }

                # Generate signature
                signature = self._generate_signature(params)
                params['sign'] = signature

                # Send request
                url = f"{self.base_url}{endpoint}?{urllib.parse.urlencode(params)}"
                logging.debug(f"Requesting Bybit spot balance: {url}")
                response = requests.get(url)

                if response.status_code == 200:
                    result = response.json()
                    logging.debug(f"Bybit spot balance response: {result}")

                    if result['ret_code'] == 0:
                        # Find USDT balance
                        usdt_balance = 0
                        if 'USDT' in result['result']:
                            usdt_balance = float(result['result']['USDT']['available_balance'])
                        return usdt_balance
                return 0

            def _get_futures_account_balance(self):
                """Get futures account balance from Bybit"""
                # Try the v5 API first (newer version)
                endpoint = "/v5/account/wallet-balance"
                timestamp = int(time.time() * 1000)

                params = {
                    'api_key': self.api_key,
                    'timestamp': timestamp,
                    'accountType': 'CONTRACT'  # For futures
                }

                # Generate signature
                signature = self._generate_signature(params)
                params['sign'] = signature

                # Send request
                url = f"{self.base_url}{endpoint}?{urllib.parse.urlencode(params)}"
                logging.debug(f"Requesting Bybit v5 futures balance: {url}")
                response = requests.get(url)

                if response.status_code == 200:
                    result = response.json()
                    logging.debug(f"Bybit v5 futures balance response: {result}")

                    if result.get('retCode') == 0 and result.get('result'):
                        # Find USDT balance
                        usdt_balance = 0
                        for coin in result['result'].get('list', []):
                            for asset in coin.get('coin', []):
                                if asset.get('coin') == 'USDT':
                                    usdt_balance = float(asset.get('walletBalance', 0))
                                    break

                        if usdt_balance > 0:
                            return usdt_balance

                # If v5 API didn't work, try the v2 API
                endpoint = "/v2/private/position/list"
                timestamp = int(time.time() * 1000)

                params = {
                    'api_key': self.api_key,
                    'timestamp': timestamp,
                    'symbol': 'BTCUSDT'  # Use a common symbol to get account info
                }

                # Generate signature
                signature = self._generate_signature(params)
                params['sign'] = signature

                # Send request
                url = f"{self.base_url}{endpoint}?{urllib.parse.urlencode(params)}"
                logging.debug(f"Requesting Bybit v2 futures position: {url}")
                response = requests.get(url)

                if response.status_code == 200:
                    result = response.json()
                    logging.debug(f"Bybit v2 futures position response: {result}")

                    if result['ret_code'] == 0 and 'result' in result:
                        # Extract wallet balance from position info
                        usdt_balance = 0
                        if 'wallet_balance' in result['result']:
                            usdt_balance = float(result['result']['wallet_balance'])
                        return usdt_balance

                return 0

            def _get_usdc_account_balance(self):
                """Get USDC account balance from Bybit"""
                endpoint = "/v5/account/wallet-balance"
                timestamp = int(time.time() * 1000)

                params = {
                    'api_key': self.api_key,
                    'timestamp': timestamp,
                    'accountType': 'UNIFIED'  # For USDC
                }

                # Generate signature
                signature = self._generate_signature(params)
                params['sign'] = signature

                # Send request
                url = f"{self.base_url}{endpoint}?{urllib.parse.urlencode(params)}"
                logging.debug(f"Requesting Bybit USDC balance: {url}")
                response = requests.get(url)

                if response.status_code == 200:
                    result = response.json()
                    logging.debug(f"Bybit USDC balance response: {result}")

                    if result.get('retCode') == 0 and result.get('result'):
                        # Find USDC balance
                        usdc_balance = 0
                        for coin in result['result'].get('list', []):
                            for asset in coin.get('coin', []):
                                if asset.get('coin') == 'USDC':
                                    usdc_balance = float(asset.get('walletBalance', 0))
                                    break

                        return usdc_balance

                return 0

            def get_open_orders(self, symbol):
                """Get open orders from Bybit"""
                endpoint = "/v2/private/order/list"
                timestamp = int(time.time() * 1000)

                params = {
                    'api_key': self.api_key,
                    'symbol': symbol,
                    'timestamp': timestamp
                }

                # Generate signature
                signature = self._generate_signature(params)
                params['sign'] = signature

                # Send request
                url = f"{self.base_url}{endpoint}?{urllib.parse.urlencode(params)}"
                response = requests.get(url)

                if response.status_code == 200:
                    result = response.json()
                    if result['ret_code'] == 0:
                        return result['result']['data']
                return []

            def create_order(self, symbol, side, quantity, price=None, order_type='Limit'):
                """Create an order on Bybit"""
                endpoint = "/v2/private/order/create"
                timestamp = int(time.time() * 1000)

                params = {
                    'api_key': self.api_key,
                    'symbol': symbol,
                    'side': side,
                    'qty': quantity,
                    'timestamp': timestamp
                }

                # Set order type and price
                if order_type == 'Limit':
                    params['order_type'] = 'Limit'
                    params['price'] = price
                    params['time_in_force'] = 'GoodTillCancel'
                else:
                    params['order_type'] = 'Market'

                # Generate signature
                signature = self._generate_signature(params)
                params['sign'] = signature

                # Send request
                url = f"{self.base_url}{endpoint}"
                response = requests.post(url, data=params)

                if response.status_code == 200:
                    result = response.json()
                    if result['ret_code'] == 0:
                        return result['result']
                return None

        return BybitClient(api_key, secret_key)

    def execute_trade(self, symbol, side, quantity, price=None):
        """Execute a trade on the exchange"""
        if not self.autotrader_account or not self.autotrader_enabled or not self.api_client:
            logging.error("Cannot execute trade: autotrader not enabled or no account selected")
            return False

        exchange = self.autotrader_account.get('exchange', '').lower()

        try:
            # Check if this is a futures contract
            is_futures = '-' in symbol and 'USDT' in symbol

            # Format symbol for API request
            if is_futures:
                # For futures, keep the original format (e.g., MOODENG-USDT)
                formatted_symbol = symbol
            else:
                # For spot, format as usual
                formatted_symbol = symbol.replace('/', '').lower()

            # Execute trade based on exchange
            if exchange in ['huobi', 'htx']:
                # Set leverage for futures contracts if needed
                if is_futures and hasattr(self, 'autotrader_leverage') and self.autotrader_leverage > 0:
                    try:
                        self.api_client.set_leverage(formatted_symbol, self.autotrader_leverage)
                        logging.info(f"Set leverage to {self.autotrader_leverage}x for {formatted_symbol}")
                    except Exception as e:
                        logging.error(f"Error setting leverage: {e}")

                # Determine order type
                if is_futures:
                    # For futures, we need to specify if it's opening or closing a position
                    if side.lower() == 'buy':
                        # Check if we're closing a short position
                        positions = self.api_client.get_futures_positions(formatted_symbol)
                        has_short_position = False
                        for position in positions:
                            if position.get('contract_code') == formatted_symbol and position.get('direction') == 'sell' and float(position.get('volume', 0)) > 0:
                                has_short_position = True
                                break

                        if has_short_position:
                            order_type = 'close_short'
                        else:
                            order_type = 'buy-limit' if price else 'buy-market'
                    else:  # sell
                        # Check if we're closing a long position
                        positions = self.api_client.get_futures_positions(formatted_symbol)
                        has_long_position = False
                        for position in positions:
                            if position.get('contract_code') == formatted_symbol and position.get('direction') == 'buy' and float(position.get('volume', 0)) > 0:
                                has_long_position = True
                                break

                        if has_long_position:
                            order_type = 'close_long'
                        else:
                            order_type = 'sell-limit' if price else 'sell-market'
                else:
                    # For spot, use standard order types
                    if price:
                        # Limit order
                        order_type = 'buy-limit' if side.lower() == 'buy' else 'sell-limit'
                    else:
                        # Market order
                        order_type = 'buy-market' if side.lower() == 'buy' else 'sell-market'

                # Execute order
                result = self.api_client.create_order(
                    symbol=formatted_symbol,
                    amount=quantity,
                    price=price,
                    order_type=order_type
                )

            elif exchange == 'binance':
                # Execute order
                result = self.api_client.create_order(
                    symbol=formatted_symbol.upper(),
                    side=side.upper(),
                    quantity=quantity,
                    price=price,
                    order_type='LIMIT' if price else 'MARKET'
                )

            elif exchange == 'bybit':
                # Execute order
                result = self.api_client.create_order(
                    symbol=formatted_symbol.upper(),
                    side=side.upper(),
                    quantity=quantity,
                    price=price,
                    order_type='Limit' if price else 'Market'
                )

            else:
                logging.error(f"Unsupported exchange: {exchange}")
                return False

            if result:
                # Log the trade
                if is_futures:
                    logging.info(f"Executed {side.upper()} order for {quantity} contracts of {symbol} at {price if price else 'market price'} with {self.autotrader_leverage}x leverage")
                else:
                    logging.info(f"Executed {side.upper()} order for {quantity} {symbol} at {price if price else 'market price'}")

                # Update status
                if is_futures:
                    self.status_label.setText(f"Status: Executed {side.upper()} order for {quantity} contracts of {symbol}")
                else:
                    self.status_label.setText(f"Status: Executed {side.upper()} order for {quantity} {symbol}")

                # Save trade to history
                trade_data = {
                    'timestamp': int(time.time() * 1000),
                    'symbol': symbol,
                    'side': side.upper(),
                    'price': price if price else self.autotrader_last_price,
                    'quantity': quantity,
                    'value': (price if price else self.autotrader_last_price) * quantity,
                    'leverage': self.autotrader_leverage if is_futures and hasattr(self, 'autotrader_leverage') else 1,
                    'is_futures': is_futures,
                    'pnl': 0  # P&L will be calculated when the position is closed
                }

                # Save to history
                self.save_trade_history(trade_data)

                # Add to history table
                self.add_trade_to_history(trade_data)

                # Refresh positions
                self.fetch_open_positions()

                return True
            else:
                logging.error(f"Failed to execute {side.upper()} order for {quantity} {symbol}")
                self.status_label.setText(f"Status: Failed to execute {side.upper()} order")
                return False

        except Exception as e:
            logging.error(f"Error executing trade: {e}")
            self.status_label.setText(f"Status: Error executing trade: {str(e)}")
            return False

    def calculate_position_size(self, entry_price, stop_loss):
        """Calculate position size based on risk parameters"""
        if not entry_price or not stop_loss or entry_price <= stop_loss:
            return 0

        # Get account balance
        account_balance = 0
        try:
            balance_text = self.balance_label.text().replace('$', '').replace(',', '')
            account_balance = float(balance_text)
        except ValueError:
            return 0

        # Calculate risk amount
        risk_amount = account_balance * (self.autotrader_risk_percent / 100)

        # Calculate position size
        risk_per_unit = abs(entry_price - stop_loss)
        if risk_per_unit == 0:
            return 0

        position_size = risk_amount / risk_per_unit

        # Round to appropriate precision based on symbol
        if self.autotrader_symbol == "BTC/USDT":
            position_size = round(position_size, 4)  # 0.0001 BTC precision
        elif self.autotrader_symbol == "ETH/USDT":
            position_size = round(position_size, 3)  # 0.001 ETH precision
        else:
            position_size = round(position_size, 2)  # 0.01 precision for other coins

        return position_size

    def update_position_with_live_price(self, trade_data):
        """Update position with live price from WebSocket data and run strategy"""
        if not self.autotrader_enabled or not trade_data:
            return

        # Check if the trade is for the symbol we're trading
        # For futures, we need to handle the different format (e.g., MOODENG-USDT vs BTC/USDT)
        is_futures = '-' in self.autotrader_symbol

        if is_futures:
            # For futures, compare the base symbol (e.g., MOODENG from MOODENG-USDT)
            trade_base = trade_data.get('symbol', '').split('/')[0] if '/' in trade_data.get('symbol', '') else trade_data.get('symbol', '').split('-')[0] if '-' in trade_data.get('symbol', '') else trade_data.get('symbol', '')
            position_base = self.autotrader_symbol.split('-')[0]

            if trade_base.upper() != position_base.upper():
                return
        else:
            # For spot, compare the full symbol
            if trade_data.get('symbol') != self.autotrader_symbol:
                return

        # Update the last price
        self.autotrader_last_price = trade_data.get('price', self.autotrader_last_price)

        # Update positions table with new price
        self.update_positions_table()

        # Run trading strategy with new data
        if self.autotrader_enabled and self.api_client:
            self.run_strategy(trade_data)

    def run_strategy(self, trade_data):
        """Run the selected trading strategy with live data"""
        if not self.autotrader_enabled or not trade_data or not self.api_client:
            return

        # Only process if the symbol matches our trading symbol
        if trade_data.get('symbol') != self.autotrader_symbol:
            return

        # Get current price
        current_price = trade_data.get('price')
        if not current_price:
            return

        # Run the selected strategy
        if self.autotrader_strategy == "ATR-EMA Bands":
            self.run_atr_ema_strategy(current_price)
        elif self.autotrader_strategy == "MACD Crossover":
            self.run_macd_strategy(current_price)
        elif self.autotrader_strategy == "RSI Divergence":
            self.run_rsi_strategy(current_price)
        elif self.autotrader_strategy == "Bollinger Bands":
            self.run_bollinger_strategy(current_price)

    def run_atr_ema_strategy(self, current_price):
        """Run ATR-EMA Bands strategy"""
        # Initialize strategy data if not exists
        if 'atr_ema' not in self.strategy_data:
            self.strategy_data['atr_ema'] = {
                'prices': [],
                'ema': 0,
                'atr': 0,
                'last_signal': None,
                'position_open': False,
                'entry_price': 0,
                'stop_loss': 0
            }

        data = self.strategy_data['atr_ema']

        # Add current price to price history
        data['prices'].append(current_price)

        # Keep only the last 100 prices
        if len(data['prices']) > 100:
            data['prices'] = data['prices'][-100:]

        # Need at least 20 prices to calculate indicators
        if len(data['prices']) < 20:
            return

        # Calculate EMA (Exponential Moving Average)
        if data['ema'] == 0:
            # First EMA is simple average
            data['ema'] = sum(data['prices']) / len(data['prices'])
        else:
            # EMA formula: EMA = Price(t) * k + EMA(y) * (1 – k)
            # where k = 2/(N+1), N = number of periods (20)
            k = 2 / (20 + 1)
            data['ema'] = current_price * k + data['ema'] * (1 - k)

        # Calculate ATR (Average True Range)
        if len(data['prices']) >= 14:
            true_ranges = []
            for i in range(1, 14):
                high = max(data['prices'][-i], data['prices'][-i-1])
                low = min(data['prices'][-i], data['prices'][-i-1])
                true_range = high - low
                true_ranges.append(true_range)

            data['atr'] = sum(true_ranges) / len(true_ranges)

        # Skip if ATR is not calculated yet
        if data['atr'] == 0:
            return

        # Calculate upper and lower bands
        upper_band = data['ema'] + 2 * data['atr']
        lower_band = data['ema'] - 2 * data['atr']

        # Trading logic
        if not data['position_open']:
            # Look for entry signals
            if current_price > upper_band and data['last_signal'] != 'buy':
                # Buy signal
                data['last_signal'] = 'buy'

                # Calculate position size
                stop_loss = data['ema'] - data['atr']
                position_size = self.calculate_position_size(current_price, stop_loss)

                if position_size > 0:
                    # Execute buy order
                    success = self.execute_trade(
                        symbol=self.autotrader_symbol,
                        side='buy',
                        quantity=position_size,
                        price=current_price  # Limit order at current price
                    )

                    if success:
                        data['position_open'] = True
                        data['entry_price'] = current_price
                        data['stop_loss'] = stop_loss

            elif current_price < lower_band and data['last_signal'] != 'sell':
                # Sell signal
                data['last_signal'] = 'sell'

                # Calculate position size
                stop_loss = data['ema'] + data['atr']
                position_size = self.calculate_position_size(current_price, stop_loss)

                if position_size > 0:
                    # Execute sell order
                    success = self.execute_trade(
                        symbol=self.autotrader_symbol,
                        side='sell',
                        quantity=position_size,
                        price=current_price  # Limit order at current price
                    )

                    if success:
                        data['position_open'] = True
                        data['entry_price'] = current_price
                        data['stop_loss'] = stop_loss
        else:
            # Check for exit signals
            if data['last_signal'] == 'buy':
                # Exit buy position if price falls below stop loss
                if current_price < data['stop_loss']:
                    # Execute sell order to close position
                    success = self.execute_trade(
                        symbol=self.autotrader_symbol,
                        side='sell',
                        quantity=self.get_position_size(),
                        price=current_price  # Limit order at current price
                    )

                    if success:
                        data['position_open'] = False
                        data['last_signal'] = None

            elif data['last_signal'] == 'sell':
                # Exit sell position if price rises above stop loss
                if current_price > data['stop_loss']:
                    # Execute buy order to close position
                    success = self.execute_trade(
                        symbol=self.autotrader_symbol,
                        side='buy',
                        quantity=self.get_position_size(),
                        price=current_price  # Limit order at current price
                    )

                    if success:
                        data['position_open'] = False
                        data['last_signal'] = None

    def calculate_position_size(self, entry_price, stop_loss):
        """Calculate position size based on risk parameters and quantity settings"""
        # Check if using fixed quantity or fixed USD value
        if self.autotrader_quantity_type == "Fixed Quantity":
            return self.autotrader_quantity_value
        elif self.autotrader_quantity_type == "Fixed USD Value":
            # Convert USD value to quantity based on entry price
            if entry_price <= 0:
                return 0
            quantity = self.autotrader_quantity_value / entry_price

            # Round to appropriate precision based on symbol
            if self.autotrader_symbol.startswith("BTC"):
                quantity = round(quantity, 4)  # 0.0001 BTC precision
            elif self.autotrader_symbol.startswith("ETH"):
                quantity = round(quantity, 3)  # 0.001 ETH precision
            else:
                quantity = round(quantity, 2)  # 0.01 precision for other coins

            return quantity

        # Auto (Risk-Based) calculation
        if not entry_price or not stop_loss or entry_price <= stop_loss:
            return 0

        # Get account balance
        account_balance = 0
        try:
            balance_text = self.balance_label.text().replace('$', '').replace(',', '')
            account_balance = float(balance_text)
        except ValueError:
            return 0

        # Calculate risk amount
        risk_amount = account_balance * (self.autotrader_risk_percent / 100)

        # Calculate position size
        risk_per_unit = abs(entry_price - stop_loss)
        if risk_per_unit == 0:
            return 0

        # Calculate base position size
        position_size = risk_amount / risk_per_unit

        # Apply leverage
        position_size = position_size * self.autotrader_leverage

        # Round to appropriate precision based on symbol
        if self.autotrader_symbol.startswith("BTC"):
            position_size = round(position_size, 4)  # 0.0001 BTC precision
        elif self.autotrader_symbol.startswith("ETH"):
            position_size = round(position_size, 3)  # 0.001 ETH precision
        else:
            position_size = round(position_size, 2)  # 0.01 precision for other coins

        return position_size

    def get_position_size(self):
        """Get the size of the current position"""
        if not self.autotrader_positions:
            return 0

        # Sum up all position sizes for the current symbol
        total_size = 0
        for position in self.autotrader_positions:
            if position['symbol'] == self.autotrader_symbol:
                total_size += position['size']

        return total_size

    def run_macd_strategy(self, current_price):
        """Run MACD Crossover strategy"""
        # This would be a full implementation of the MACD strategy
        # For now, we'll just log that the strategy is running
        logging.info(f"MACD strategy processing price: {current_price}")

    def run_rsi_strategy(self, current_price):
        """Run RSI Divergence strategy"""
        # This would be a full implementation of the RSI strategy
        # For now, we'll just log that the strategy is running
        logging.info(f"RSI strategy processing price: {current_price}")

    def run_bollinger_strategy(self, current_price):
        """Run Bollinger Bands strategy"""
        # This would be a full implementation of the Bollinger Bands strategy
        # For now, we'll just log that the strategy is running
        logging.info(f"Bollinger Bands strategy processing price: {current_price}")

    def update_positions_table(self):
        """Update positions table with current prices"""
        if not hasattr(self, 'positions_table') or self.positions_table.rowCount() == 0:
            return

        # Update each position with the current price
        for row in range(self.positions_table.rowCount()):
            symbol_item = self.positions_table.item(row, 0)
            if not symbol_item:
                continue

            # For futures, we need to handle the different format (e.g., MOODENG-USDT vs BTC/USDT)
            is_futures = '-' in self.autotrader_symbol

            if is_futures:
                # For futures, compare the base symbol (e.g., MOODENG from MOODENG-USDT)
                table_symbol = symbol_item.text()
                table_base = table_symbol.split('/')[0] if '/' in table_symbol else table_symbol.split('-')[0] if '-' in table_symbol else table_symbol
                position_base = self.autotrader_symbol.split('-')[0]

                if table_base.upper() != position_base.upper():
                    continue
            else:
                # For spot, compare the full symbol
                if symbol_item.text() != self.autotrader_symbol:
                    continue

            # Get entry price
            entry_item = self.positions_table.item(row, 1)
            if not entry_item:
                continue

            entry_text = entry_item.text().replace('$', '').replace(',', '')
            try:
                entry_price = float(entry_text)
            except ValueError:
                continue

            # Get size
            size_item = self.positions_table.item(row, 3)
            if not size_item:
                continue

            size_text = size_item.text()
            try:
                size = float(size_text)
            except ValueError:
                continue

            # Get side (for futures positions)
            side_item = self.positions_table.item(row, 5) if self.positions_table.columnCount() > 5 else None
            side = side_item.text() if side_item else "BUY"

            # Update current price
            current_item = QTableWidgetItem(f"${self.autotrader_last_price:.2f}")
            current_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.positions_table.setItem(row, 2, current_item)

            # Calculate and update P&L
            if is_futures:
                # For futures, P&L calculation depends on position side and leverage
                leverage_item = self.positions_table.item(row, 6) if self.positions_table.columnCount() > 6 else None
                leverage = float(leverage_item.text().replace('x', '')) if leverage_item else 1.0

                if side.upper() == "BUY" or side.upper() == "LONG":
                    # Long position: profit when price goes up
                    pnl = (self.autotrader_last_price - entry_price) * size * leverage
                else:
                    # Short position: profit when price goes down
                    pnl = (entry_price - self.autotrader_last_price) * size * leverage
            else:
                # For spot, simple P&L calculation
                pnl = (self.autotrader_last_price - entry_price) * size

            pnl_item = QTableWidgetItem(f"${pnl:.2f}")
            pnl_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

            # Color PnL based on profit/loss
            if pnl > 0:
                pnl_item.setForeground(QColor("#4CAF50"))
            else:
                pnl_item.setForeground(QColor("#FF3B30"))

            self.positions_table.setItem(row, 4, pnl_item)

    def closeEvent(self, event):
        """Clean up threads when closing the application"""
        # Save column state
        if hasattr(self, 'liq_table'):
            self.settings.setValue("liq_table_header_state", self.liq_table.horizontalHeader().saveState())

        # Stop all threads
        self.stop_all_threads()
        event.accept()


class LiveTradeThread(QThread):
    update_trade = Signal(dict)

    def __init__(self, exchange, symbol='BTC/USDT'):
        super().__init__()
        self.exchange = exchange
        self.symbol = symbol
        self.is_running = True
        self.reconnect_delay = 5  # seconds
        self.loop = None

        # Format symbol for different exchanges
        formatted_symbol = symbol.lower().replace('/', '')

        # Define exchange-specific WebSocket URLs and handlers
        self.exchange_configs = {
            'Binance': {
                'url': f"wss://stream.binance.com:9443/ws/{formatted_symbol}@trade",
                'parser': self.parse_binance_message
            },
            'Huobi': {
                'url': "wss://api.huobi.pro/ws",
                'parser': self.parse_huobi_message,
                'subscription': {
                    'sub': f"market.{formatted_symbol}.trade.detail",
                    'id': 'id1'
                }
            },
            'Coinbase': {
                'url': "wss://ws-feed.exchange.coinbase.com",
                'parser': self.parse_coinbase_message,
                'subscription': {
                    'type': 'subscribe',
                    'product_ids': [symbol.replace('/', '-')],
                    'channels': ['matches']
                }
            },
            'Kraken': {
                'url': "wss://ws.kraken.com",
                'parser': self.parse_kraken_message,
                'subscription': {
                    'name': 'subscribe',
                    'pair': [symbol.replace('/', '')],
                    'subscription': {
                        'name': 'trade'
                    }
                }
            },
            'KuCoin': {
                # KuCoin requires a token from REST API before connecting to WebSocket
                # For simplicity, we'll disable it in this demo
                'url': None,  # Disabled
                'parser': self.parse_kucoin_message,
                'requires_auth': True,
                'disabled': True,
                'disabled_reason': "Requires authentication token from REST API"
            },
            'Bybit': {
                'url': "wss://stream.bybit.com/v5/public/spot",
                'parser': self.parse_bybit_message,
                'subscription': {
                    'op': 'subscribe',
                    'args': [f"publicTrade.{symbol.split('/')[0]}{symbol.split('/')[1]}"]
                }
            }
        }

        self.logger = logging.getLogger(f"{exchange}_WebSocket")

    def parse_binance_message(self, message):
        data = json.loads(message)
        return {
            'exchange': self.exchange,
            'symbol': self.symbol,
            'price': float(data['p']),  # trade price
            'amount': float(data['q']),  # trade amount
            'side': 'sell' if data['m'] else 'buy',  # market maker (true=sell, false=buy)
            'timestamp': data['T']
        }

    def parse_huobi_message(self, message):
        # Check if the message is gzipped (Huobi sends compressed data)
        if isinstance(message, bytes) and message.startswith(b'\x1f\x8b'):
            try:
                # Decompress the message
                message = gzip.decompress(message).decode('utf-8')
            except Exception as e:
                self.logger.error(f"Error decompressing message: {e}")
                return None

        try:
            data = json.loads(message)

            # Handle ping messages from Huobi
            if 'ping' in data:
                return None

            # Handle subscription confirmation
            if 'subbed' in data or 'id' in data:
                self.logger.info(f"Subscription response: {data}")
                return None

            # Handle trade data
            if 'ch' in data and 'tick' in data:
                tick = data['tick']
                if 'data' in tick:
                    # Get the most recent trade (first in the list)
                    trade_data = tick['data'][0]
                    return {
                        'exchange': self.exchange,
                        'symbol': self.symbol,
                        'price': float(trade_data['price']),
                        'amount': float(trade_data['amount']),
                        'side': 'sell' if trade_data['direction'] == 'sell' else 'buy',
                        'timestamp': trade_data['ts']
                    }
        except Exception as e:
            self.logger.error(f"Error parsing message: {e}")
            self.logger.error(f"Message content: {message[:100]}...")

        return None

    def parse_coinbase_message(self, message):
        try:
            data = json.loads(message)

            # Handle subscription confirmation
            if data.get('type') == 'subscriptions':
                self.logger.info(f"Subscription response: {data}")
                return None

            # Handle trade data
            if data.get('type') == 'match' or data.get('type') == 'last_match':
                return {
                    'exchange': self.exchange,
                    'symbol': self.symbol,
                    'price': float(data['price']),
                    'amount': float(data['size']),
                    'side': 'buy' if data['side'] == 'buy' else 'sell',
                    'timestamp': datetime.datetime.fromisoformat(data['time'].replace('Z', '+00:00')).timestamp() * 1000
                }
        except Exception as e:
            self.logger.error(f"Error parsing Coinbase message: {e}")
            self.logger.error(f"Message content: {message[:100]}...")

        return None

    def parse_kraken_message(self, message):
        try:
            data = json.loads(message)

            # Handle subscription confirmation
            if isinstance(data, list) and len(data) >= 2:
                if isinstance(data[1], str) and data[1] == 'subscriptionStatus':
                    self.logger.info(f"Subscription response: {data}")
                    return None

                # Handle trade data
                if isinstance(data[1], str) and data[1] == 'trade':
                    # Kraken sends an array of trades
                    trades = data[1]
                    if trades and len(trades) > 0:
                        trade = trades[0]  # Get the most recent trade
                        # Format: [price, volume, time, side, order_type, misc]
                        return {
                            'exchange': self.exchange,
                            'symbol': self.symbol,
                            'price': float(trade[0]),
                            'amount': float(trade[1]),
                            'side': 'sell' if trade[3] == 's' else 'buy',
                            'timestamp': float(trade[2]) * 1000  # Convert to milliseconds
                        }
        except Exception as e:
            self.logger.error(f"Error parsing Kraken message: {e}")
            self.logger.error(f"Message content: {message[:100]}...")

        return None

    def parse_kucoin_message(self, message):
        try:
            data = json.loads(message)

            # Handle ping messages
            if 'type' in data and data['type'] == 'ping':
                return None

            # Handle subscription confirmation
            if 'type' in data and data['type'] == 'ack':
                self.logger.info(f"Subscription response: {data}")
                return None

            # Handle trade data
            if 'type' in data and data['type'] == 'message' and 'subject' in data and data['subject'] == 'trade.l3match':
                trade_data = data['data']
                return {
                    'exchange': self.exchange,
                    'symbol': self.symbol,
                    'price': float(trade_data['price']),
                    'amount': float(trade_data['size']),
                    'side': 'sell' if trade_data['side'] == 'sell' else 'buy',
                    'timestamp': int(trade_data['time'])
                }
        except Exception as e:
            self.logger.error(f"Error parsing KuCoin message: {e}")
            self.logger.error(f"Message content: {message[:100]}...")

        return None

    def parse_bybit_message(self, message):
        try:
            data = json.loads(message)

            # Handle subscription confirmation
            if 'success' in data:
                self.logger.info(f"Subscription response: {data}")
                return None

            # Handle trade data
            if 'topic' in data and 'data' in data and 'publicTrade' in data['topic']:
                # Bybit sends an array of trades
                trades = data['data']
                if trades and len(trades) > 0:
                    trade = trades[0]  # Get the most recent trade
                    return {
                        'exchange': self.exchange,
                        'symbol': self.symbol,
                        'price': float(trade['p']),
                        'amount': float(trade['v']),
                        'side': 'sell' if trade['S'] == 'Sell' else 'buy',
                        'timestamp': int(trade['T'])
                    }
        except Exception as e:
            self.logger.error(f"Error parsing Bybit message: {e}")
            self.logger.error(f"Message content: {message[:100]}...")

        return None

    async def handle_websocket(self):
        """Main WebSocket handler using asyncio and websockets"""
        if self.exchange not in self.exchange_configs:
            self.logger.error(f"Exchange {self.exchange} not supported")
            return

        config = self.exchange_configs[self.exchange]

        # Check if exchange is disabled
        if config.get('disabled', False):
            reason = config.get('disabled_reason', 'No reason provided')
            self.logger.warning(f"Exchange {self.exchange} is disabled: {reason}")
            # Emit a special message to inform the UI
            self.update_trade.emit({
                'exchange': self.exchange,
                'symbol': self.symbol,
                'price': 0.0,
                'amount': 0.0,
                'side': 'info',
                'timestamp': int(time.time() * 1000),
                'info_message': f"Exchange disabled: {reason}"
            })
            return

        url = config['url']
        if not url:
            self.logger.error(f"No URL configured for {self.exchange}")
            return

        while self.is_running:
            try:
                self.logger.info(f"Connecting to {self.exchange} WebSocket: {url}")

                async with websockets.connect(url) as websocket:
                    # Send subscription message for exchanges that require it
                    if 'subscription' in config:
                        subscription = config['subscription']
                        self.logger.info(f"Sending subscription: {subscription}")
                        await websocket.send(json.dumps(subscription))

                    # Main message loop
                    while self.is_running:
                        try:
                            message = await websocket.recv()
                            parser = config['parser']
                            trade = parser(message)
                            if trade:
                                self.update_trade.emit(trade)

                            # Handle exchange-specific ping messages
                            if self.exchange == 'Huobi':
                                if isinstance(message, str) and '"ping"' in message:
                                    pong_msg = message.replace('"ping"', '"pong"')
                                    await websocket.send(pong_msg)
                                elif isinstance(message, bytes) and b'"ping"' in message:
                                    # Decompress and handle ping
                                    try:
                                        decompressed = gzip.decompress(message).decode('utf-8')
                                        if '"ping"' in decompressed:
                                            pong_msg = decompressed.replace('"ping"', '"pong"')
                                            await websocket.send(pong_msg)
                                    except Exception as e:
                                        self.logger.error(f"Error handling ping message: {e}")
                            elif self.exchange == 'Coinbase' and isinstance(message, str) and '"type":"heartbeat"' in message:
                                # Coinbase heartbeat, no response needed
                                pass
                            elif self.exchange == 'Kraken' and isinstance(message, str) and '"heartbeat"' in message:
                                # Kraken heartbeat, no response needed
                                pass
                            elif self.exchange == 'Bybit' and isinstance(message, str) and '"ping"' in message:
                                # Bybit ping
                                data = json.loads(message)
                                if 'ping' in data:
                                    pong_msg = json.dumps({"pong": data['ping']})
                                    await websocket.send(pong_msg)

                        except websockets.exceptions.ConnectionClosed:
                            self.logger.warning(f"WebSocket connection closed for {self.exchange}")
                            break
                        except Exception as e:
                            self.logger.error(f"Error processing message: {e}")
                            if isinstance(message, (str, bytes)):
                                preview = message[:100] if isinstance(message, str) else str(message[:100])
                                self.logger.error(f"Message preview: {preview}...")

            except Exception as e:
                self.logger.error(f"WebSocket connection error: {e}")

            if self.is_running:
                self.logger.info(f"Attempting to reconnect in {self.reconnect_delay} seconds...")
                await asyncio.sleep(self.reconnect_delay)
            else:
                break

    def run(self):
        """Run the WebSocket connection in a new event loop"""
        try:
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.loop.run_until_complete(self.handle_websocket())
        except Exception as e:
            self.logger.error(f"Error in WebSocket thread: {e}")
        finally:
            if self.loop and self.loop.is_running():
                self.loop.close()

    def stop(self):
        """Stop the WebSocket connection"""
        self.logger.info(f"Stopping {self.exchange} WebSocket thread")
        self.is_running = False
        if self.loop and self.loop.is_running():
            asyncio.run_coroutine_threadsafe(asyncio.sleep(0), self.loop)  # Wake up the event loop

class LiquidationThread(QThread):
    update_liq = Signal(dict)        # emits parsed dict (see schema above)

    def __init__(self, exchange, symbol='BTC/USDT'):
        super().__init__()
        self.exchange = exchange
        self.symbol = symbol
        self.is_running = True
        self.loop = None
        self.logger = logging.getLogger(f"{exchange}_LIQ")

        # Skip unsupported exchanges
        if exchange not in {'Binance', 'Huobi', 'Bybit'}:
            self.logger.warning(f"Exchange {exchange} not supported for liquidations")
            self.is_running = False
            return

        sym = symbol.replace('/','').upper()

        # --- endpoint mapping ---
        self.cfg = {
            'Binance': {
                'url': "wss://fstream.binance.com/stream?streams=!forceOrder@arr",
                'parser': self.parse_binance
            },
            'Huobi': {
                'url': "wss://api.hbdm.com/notification",
                'sub': {'op':'sub', 'topic':'public.*.liquidation_orders'},
                'parser': self.parse_huobi
            },
            'Bybit': {
                'url': "wss://stream.bybit.com/v5/public/linear",
                'sub': {'op':'subscribe','args':[f"liquidation.{sym}"]},
                'parser': self.parse_bybit
            },
            # Kraken / Coinbase skipped
        }[exchange]

    # ---------- parsers ----------
    def parse_binance(self, msg):
        data = json.loads(msg)
        if 'data' in data:        # wrapper
            data = data['data']
        o = data['o']

        # convert "1000PEPEUSDT" → "1000PEPE/USDT"
        symbol = f"{o['s'][:-4]}/{o['s'][-4:]}"

        # Filter by symbol
        if self.symbol != 'ALL' and symbol.upper() != self.symbol.upper():
            return None           # ignore other pairs

        return {
            'exchange': 'Binance',
            'symbol': symbol,
            'side': 'sell' if o['S']=='SELL' else 'buy',
            'price': float(o['p']),
            'qty': float(o['q']),
            'value': float(o['p']) * float(o['q']),
            'timestamp': o['T']
        }

    def parse_huobi(self, msg):
        if isinstance(msg, bytes):
            msg = gzip.decompress(msg).decode()
        j = json.loads(msg)
        if 'ping' in j: return None
        if 'data' not in j: return None
        d = j['data'][0]
        m = re.search(r'public\.([A-Z]+)[-_]', j['topic'])
        sym = f"{m.group(1)}/USDT" if m else 'UNKNOWN/USDT'

        # Filter by symbol
        if self.symbol != 'ALL' and sym.upper() != self.symbol.upper():
            return None           # ignore other pairs

        return {
            'exchange':'Huobi',
            'symbol': sym,
            'side': d['direction'],
            'price': float(d['price']),
            'qty': float(d['volume']),
            'value': float(d['price'])*float(d['volume']),
            'timestamp': d['ts']
        }

    def parse_bybit(self, msg):
        j = json.loads(msg)
        if 'data' not in j: return None
        d = j['data'][0]

        symbol = d['symbol'][:-4]+'/USDT'

        # Filter by symbol
        if self.symbol != 'ALL' and symbol.upper() != self.symbol.upper():
            return None           # ignore other pairs

        return {
            'exchange':'Bybit',
            'symbol': symbol,
            'side': 'buy' if d['side']=='Buy' else 'sell',
            'price': float(d['price']),
            'qty': float(d['qty']),
            'value': float(d['price'])*float(d['qty']),
            'timestamp': int(time.time()*1000)
        }

    # ---------- run loop ----------
    async def _run_ws(self):
        url = self.cfg['url']
        async with websockets.connect(url) as ws:
            if 'sub' in self.cfg:
                await ws.send(json.dumps(self.cfg['sub']))
            while self.is_running:
                try:
                    msg = await asyncio.wait_for(ws.recv(), timeout=5)
                    parsed = self.cfg['parser'](msg)
                    if parsed:
                        self.update_liq.emit(parsed)
                except asyncio.TimeoutError:
                    continue

    def run(self):
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.loop.run_until_complete(self._run_ws())

    def stop(self):
        self.is_running = False
        if self.loop and self.loop.is_running():
            asyncio.run_coroutine_threadsafe(asyncio.sleep(0), self.loop)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TradingApp()
    window.show()
    sys.exit(app.exec())
