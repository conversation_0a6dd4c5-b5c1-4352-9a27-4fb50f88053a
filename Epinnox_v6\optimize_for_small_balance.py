#!/usr/bin/env python3
"""
EPINNOX V6 SYSTEM OPTIMIZATION FOR $3.25 BALANCE
Complete solution for balance requirements and symbol scanner optimization
"""

import json
import yaml
import os
from datetime import datetime

def optimize_system_for_small_balance():
    """Optimize the system for trading with $3.25 balance"""
    
    print("🔧 EPINNOX V6 SYSTEM OPTIMIZATION")
    print("=" * 60)
    print("Target: Optimize for $3.25 balance autonomous trading")
    print()

    # 1. Fix minimum balance requirements
    print("💰 1. ADJUSTING BALANCE REQUIREMENTS")
    print("-" * 40)
    
    # Update autonomous deployment config for lower balance
    deployment_config_path = "config/autonomous_deployment.yaml"
    if os.path.exists(deployment_config_path):
        try:
            with open(deployment_config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Lower the initial balance requirement
            config['trading']['initial_balance'] = 3.0  # Down from 50.0
            
            # Adjust risk management for micro balance
            config['risk_management'].update({
                'max_portfolio_risk': 0.01,  # 1% risk (ultra-conservative for $3)
                'max_position_size': 0.005,   # 0.5% position size
                'max_leverage': 25.0,         # Higher leverage to overcome minimum trade sizes
                'portfolio_exposure_limit': 0.95,  # Use almost all balance due to constraints
                'max_concurrent_positions': 1,     # Only 1 position
                'stop_loss_pct': 0.01,       # 1% stop loss
                'take_profit_pct': 0.02,     # 2% take profit
            })
            
            # Adjust autonomous settings
            config['autonomous'].update({
                'min_confidence_threshold': 0.85,  # Higher confidence for small balance
                'cycle_delay_seconds': 120,        # Slower cycles to avoid overtrading
            })
            
            with open(deployment_config_path, 'w') as f:
                yaml.dump(config, f, default_flow_style=False, indent=2)
            
            print(f"✅ Updated {deployment_config_path}")
            print(f"   • Initial balance: $3.00 (was $50.00)")
            print(f"   • Position size: 0.5% (was 1%)")
            print(f"   • Max leverage: 25x (was 2x)")
            print(f"   • Min confidence: 85% (was 80%)")
            
        except Exception as e:
            print(f"❌ Error updating deployment config: {e}")
    
    # 2. Update autonomous trading config
    print("\n⚙️ 2. UPDATING AUTONOMOUS TRADING CONFIG")
    print("-" * 40)
    
    autonomous_config_path = "configs/autonomous_trading.yaml"
    if os.path.exists(autonomous_config_path):
        try:
            with open(autonomous_config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Update trading settings
            config['trading'].update({
                'initial_balance': 3.0,      # Down from 50.0
                'max_positions': 1,          # Ultra-conservative
                'min_confidence': 0.85,      # Higher confidence
                'cycle_delay': 120,          # Slower cycles
            })
            
            # Update risk settings
            config['risk'].update({
                'max_portfolio_risk': 0.01,     # 1% risk
                'max_position_size': 0.005,     # 0.5% position
                'max_leverage': 25.0,           # Higher leverage for minimum trade sizes
                'max_concurrent_positions': 1,  # Only 1 position
                'portfolio_exposure_limit': 0.95, # Use almost all balance
            })
            
            with open(autonomous_config_path, 'w') as f:
                yaml.dump(config, f, default_flow_style=False, indent=2)
            
            print(f"✅ Updated {autonomous_config_path}")
            print(f"   • Balance: $3.00, Position: 0.5%, Risk: 1%")
            
        except Exception as e:
            print(f"❌ Error updating autonomous config: {e}")
    
    # 3. Optimize symbol scanner for stability
    print("\n🎯 3. OPTIMIZING SYMBOL SCANNER")
    print("-" * 40)
    
    scanner_optimization = {
        'enabled': True,
        'mode': 'conservative',  # More stable selection
        'update_interval': 30.0,  # Slower updates (30s instead of 5s)
        'stability_threshold': 60,  # Require 60s of stability before switching
        'min_score_improvement': 10,  # Require 10% score improvement to switch
        'preferred_symbols': [
            'DOGE/USDT:USDT',  # Start with DOGE (good for small accounts)
            'SHIB/USDT:USDT',  # SHIB as backup
            'ADA/USDT:USDT'    # ADA as third option
        ],
        'metrics_weights': {
            'spread_score': 0.25,      # Emphasize spread (important for small trades)
            'volume_score': 0.20,      # Volume for liquidity
            'depth_score': 0.20,       # Market depth
            'tick_atr_score': 0.15,    # Volatility (reduced weight)
            'flow_score': 0.05,        # Order flow (minimal weight)
            'affordability_score': 0.15  # 🚀 NEW: Cheaper symbols better for small accounts
        }
    }
    
    # Update scanner config in autonomous trading config
    if os.path.exists(autonomous_config_path):
        try:
            with open(autonomous_config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            config['scanner'] = scanner_optimization
            
            with open(autonomous_config_path, 'w') as f:
                yaml.dump(config, f, default_flow_style=False, indent=2)
            
            print("✅ Symbol scanner optimized:")
            print("   • Update interval: 30s (was 5s)")
            print("   • Mode: conservative (was scalping)")
            print("   • Focus: DOGE, SHIB, ADA")
            print("   • Reduced symbol switching frequency")
            
        except Exception as e:
            print(f"❌ Error updating scanner config: {e}")
    
    # 4. Create position sizing optimizer
    print("\n📊 4. POSITION SIZING FOR $3.25 BALANCE")
    print("-" * 40)
    
    balance = 3.25
    max_position_pct = 0.005  # 0.5%
    max_leverage = 25
    min_trade_size = 5.0  # HTX minimum
    
    # Calculate optimal position
    max_position_value = balance * max_position_pct
    required_margin = min_trade_size / max_leverage
    
    print(f"💰 Available balance: ${balance:.2f}")
    print(f"📈 Max position size: 0.5% = ${max_position_value:.4f}")
    print(f"⚡ With 25x leverage:")
    print(f"   • Required margin: ${required_margin:.4f}")
    print(f"   • Position notional: ${min_trade_size:.2f}")
    print(f"   • Effective leverage: {min_trade_size/balance:.1f}x")
    
    if required_margin <= balance:
        print("✅ Minimum trade feasible with current balance")
        daily_profit_1pct = min_trade_size * 0.01  # 1% move
        print(f"📊 Potential daily profit (1% move): ${daily_profit_1pct:.4f}")
    else:
        print("❌ Insufficient balance for minimum trade")
    
    # 5. Update minimum balance checks in system
    print("\n🛡️ 5. UPDATING BALANCE SAFETY CHECKS")
    print("-" * 40)
    
    # Create override config for minimum balance
    override_config = {
        "minimum_balance_override": True,
        "original_minimum": 10.0,
        "override_minimum": 3.0,
        "override_reason": "Small account optimization",
        "risk_multiplier": 2.0,  # 2x risk monitoring for small accounts
        "position_size_cap": 0.005,  # 0.5% max position
        "leverage_cap": 25.0,
        "emergency_balance": 2.0,  # Emergency stop at $2
        "created": datetime.now().isoformat()
    }
    
    with open("small_balance_override.json", "w") as f:
        json.dump(override_config, f, indent=2)
    
    print("✅ Created small_balance_override.json")
    print("   • Minimum balance: $3.00 (was $10.00)")
    print("   • Emergency threshold: $2.00")
    print("   • Enhanced risk monitoring enabled")
    
    # 6. Generate restart recommendations
    print("\n🚀 6. SYSTEM RESTART RECOMMENDATIONS")
    print("-" * 40)
    
    print("To apply these optimizations:")
    print("1. 🛑 Stop current system if running")
    print("2. ✅ Configurations updated automatically")
    print("3. 🔄 Restart with: python launch_epinnox.py")
    print("4. ✅ Enable autonomous checkboxes")
    print("5. 📊 Monitor first few trades closely")
    
    print("\n⚠️ IMPORTANT WARNINGS FOR $3.25 BALANCE:")
    print("• Position sizes will be larger than ideal relative to balance")
    print("• Higher leverage increases both profit and loss potential")
    print("• One bad trade could significantly impact account")
    print("• Capital preservation mode may activate more frequently")
    print("• Consider adding funds for better risk management")
    
    print("\n🎯 EXPECTED PERFORMANCE WITH $3.25:")
    print("• Daily position size: ~$5.00 (25x leverage)")
    print("• Margin required: ~$0.20 per trade")
    print("• Potential daily profit: $0.05-$0.15 (1-3% moves)")
    print("• Time to double balance: 2-4 weeks (if successful)")
    
    print(f"\n{'='*60}")
    print("✅ SYSTEM OPTIMIZATION COMPLETE")
    print("Ready for $3.25 balance autonomous trading")
    print(f"{'='*60}")

if __name__ == "__main__":
    optimize_system_for_small_balance()
