"""
Market Regime Detection Module

This module provides functionality for detecting the current market regime
(trending, ranging, volatile) and adjusting trading parameters accordingly.
"""
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import math

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import TA-Lib for faster calculations
try:
    import talib
    TALIB_AVAILABLE = True
    logger.info("Using TA-Lib for technical indicators")
except ImportError:
    TALIB_AVAILABLE = False
    logger.info("TA-Lib not available, using pandas-ta fallback")

class MarketRegimeDetector:
    """
    Detects the current market regime and provides parameter adjustments.
    """
    def __init__(self):
        """
        Initialize the market regime detector.
        """
        # Define regime thresholds
        self.volatility_threshold_high = 2.0  # High volatility threshold (ATR as % of price)
        self.volatility_threshold_low = 0.5   # Low volatility threshold
        self.trend_strength_threshold = 0.5   # Strong trend threshold
        self.range_bound_threshold = 0.3      # Range-bound threshold for price movement
        
        # Define regime adjustment factors
        self.regime_adjustments = {
            'strong_trend': {
                'leverage_factor': 1.0,       # No change to leverage in strong trend
                'position_size_factor': 1.0,  # No change to position size
                'stop_loss_factor': 1.0,      # No change to stop loss
                'take_profit_factor': 1.2,    # Increase take profit by 20%
                'entry_confidence': 0.7       # Minimum confidence for entry
            },
            'weak_trend': {
                'leverage_factor': 0.8,       # Reduce leverage by 20%
                'position_size_factor': 0.9,  # Reduce position size by 10%
                'stop_loss_factor': 1.0,      # No change to stop loss
                'take_profit_factor': 1.0,    # No change to take profit
                'entry_confidence': 0.6       # Minimum confidence for entry
            },
            'range_bound': {
                'leverage_factor': 0.7,       # Reduce leverage by 30%
                'position_size_factor': 0.8,  # Reduce position size by 20%
                'stop_loss_factor': 1.2,      # Increase stop loss by 20%
                'take_profit_factor': 0.8,    # Decrease take profit by 20%
                'entry_confidence': 0.7       # Minimum confidence for entry
            },
            'high_volatility': {
                'leverage_factor': 0.5,       # Reduce leverage by 50%
                'position_size_factor': 0.6,  # Reduce position size by 40%
                'stop_loss_factor': 1.5,      # Increase stop loss by 50%
                'take_profit_factor': 1.5,    # Increase take profit by 50%
                'entry_confidence': 0.8       # Minimum confidence for entry
            },
            'low_volatility': {
                'leverage_factor': 0.9,       # Reduce leverage by 10%
                'position_size_factor': 0.9,  # Reduce position size by 10%
                'stop_loss_factor': 0.8,      # Decrease stop loss by 20%
                'take_profit_factor': 0.8,    # Decrease take profit by 20%
                'entry_confidence': 0.6       # Minimum confidence for entry
            },
            'uncertain': {
                'leverage_factor': 0.6,       # Reduce leverage by 40%
                'position_size_factor': 0.7,  # Reduce position size by 30%
                'stop_loss_factor': 1.3,      # Increase stop loss by 30%
                'take_profit_factor': 1.0,    # No change to take profit
                'entry_confidence': 0.75      # Minimum confidence for entry
            }
        }
        
        logger.info("Initialized market regime detector")
    
    def detect_regime(self, data: pd.DataFrame, trend_strength: float = None, 
                     volatility: float = None, trend_alignment: float = None) -> Dict[str, Union[str, float]]:
        """
        Detect the current market regime.
        
        Args:
            data: DataFrame with market data
            trend_strength: Pre-calculated trend strength (-1 to 1)
            volatility: Pre-calculated volatility (ATR as % of price)
            trend_alignment: Pre-calculated trend alignment (0 to 1)
            
        Returns:
            dict: Dictionary with regime information
        """
        # Calculate metrics if not provided
        if trend_strength is None or volatility is None:
            metrics = self._calculate_regime_metrics(data)
            trend_strength = metrics['trend_strength']
            volatility = metrics['volatility']
            trend_alignment = metrics.get('trend_alignment', 0.5)
        
        # Determine primary regime
        primary_regime = self._determine_primary_regime(trend_strength, volatility, trend_alignment)
        
        # Calculate regime scores
        regime_scores = self._calculate_regime_scores(trend_strength, volatility, trend_alignment)
        
        # Get parameter adjustments
        adjustments = self._get_parameter_adjustments(primary_regime, regime_scores)
        
        # Create result
        result = {
            'primary_regime': primary_regime,
            'regime_scores': regime_scores,
            'adjustments': adjustments,
            'metrics': {
                'trend_strength': trend_strength,
                'volatility': volatility,
                'trend_alignment': trend_alignment
            }
        }
        
        logger.info(f"Detected market regime: {primary_regime}")
        logger.info(f"Regime metrics - Trend strength: {trend_strength:.2f}, Volatility: {volatility:.2f}%, Alignment: {trend_alignment:.2f}")
        
        return result
    
    def _calculate_regime_metrics(self, data: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate metrics for regime detection.
        
        Args:
            data: DataFrame with market data
            
        Returns:
            dict: Dictionary with calculated metrics
        """
        if data.empty or len(data) < 20:
            return {
                'trend_strength': 0,
                'volatility': 1.0,
                'trend_alignment': 0.5
            }
        
        # Calculate trend strength using moving averages
        try:
            # Calculate moving averages
            if TALIB_AVAILABLE:
                data['sma_20'] = talib.SMA(data['close'].values, timeperiod=20)
                data['sma_50'] = talib.SMA(data['close'].values, timeperiod=50)
                data['ema_20'] = talib.EMA(data['close'].values, timeperiod=20)
            else:
                data['sma_20'] = data['close'].rolling(window=20).mean()
                data['sma_50'] = data['close'].rolling(window=50).mean()
                # Simple EMA calculation
                alpha = 2 / (20 + 1)
                data['ema_20'] = data['close'].ewm(alpha=alpha, adjust=False).mean()
            
            # Get latest values
            latest = data.iloc[-1]
            price = latest['close']
            sma_20 = latest['sma_20']
            sma_50 = latest['sma_50']
            ema_20 = latest['ema_20']
            
            # Calculate trend metrics
            ma_trend_score = 0
            trend_signals = 0
            
            # Price above/below moving averages
            if not np.isnan(sma_20):
                ma_trend_score += 1 if price > sma_20 else -1
                trend_signals += 1
            
            if not np.isnan(sma_50):
                ma_trend_score += 1 if price > sma_50 else -1
                trend_signals += 1
            
            # Moving average alignments
            if not np.isnan(sma_20) and not np.isnan(sma_50):
                ma_trend_score += 1 if sma_20 > sma_50 else -1
                trend_signals += 1
            
            # Normalize trend score to -1 to +1 range
            trend_strength = ma_trend_score / trend_signals if trend_signals > 0 else 0
            
            # Calculate directional movement
            data['returns'] = data['close'].pct_change()
            directional_movement = data['returns'].sum()
            normalized_directional = np.tanh(directional_movement * 100)  # Normalize to -1 to 1
            
            # Combine trend metrics
            trend_strength = (trend_strength + normalized_directional) / 2
            
        except Exception as e:
            logger.error(f"Error calculating trend metrics: {e}")
            trend_strength = 0
        
        # Calculate volatility using ATR
        try:
            if TALIB_AVAILABLE:
                atr = talib.ATR(
                    data['high'].values,
                    data['low'].values,
                    data['close'].values,
                    timeperiod=14
                )
                atr_value = atr[-1]
            else:
                # Calculate true range
                data['tr'] = np.maximum(
                    data['high'] - data['low'],
                    np.maximum(
                        abs(data['high'] - data['close'].shift(1)),
                        abs(data['low'] - data['close'].shift(1))
                    )
                )
                atr_value = data['tr'].rolling(window=14).mean().iloc[-1]
            
            # Normalize ATR as percentage of price
            volatility = (atr_value / price) * 100
            
        except Exception as e:
            logger.error(f"Error calculating volatility: {e}")
            volatility = 1.0  # Default to moderate volatility
        
        # Calculate trend alignment
        try:
            # Calculate price movement consistency
            returns = data['returns'].dropna().values
            positive_returns = sum(1 for r in returns if r > 0)
            negative_returns = sum(1 for r in returns if r < 0)
            total_returns = len(returns)
            
            if total_returns > 0:
                # If most returns are in the same direction, high alignment
                max_direction = max(positive_returns, negative_returns)
                trend_alignment = max_direction / total_returns
            else:
                trend_alignment = 0.5
                
        except Exception as e:
            logger.error(f"Error calculating trend alignment: {e}")
            trend_alignment = 0.5  # Default to moderate alignment
        
        return {
            'trend_strength': trend_strength,
            'volatility': volatility,
            'trend_alignment': trend_alignment
        }
    
    def _determine_primary_regime(self, trend_strength: float, volatility: float, trend_alignment: float) -> str:
        """
        SMART ENHANCEMENT: Determine the primary market regime with advanced logic.

        Args:
            trend_strength: Trend strength (-1 to 1)
            volatility: Volatility (ATR as % of price)
            trend_alignment: Trend alignment (0 to 1)

        Returns:
            str: Primary market regime
        """
        # SMART REGIME DETECTION WITH PRIORITY SCORING
        regime_scores = {}

        # 1. High Volatility Detection (Priority: Critical)
        if volatility >= self.volatility_threshold_high:
            regime_scores['high_volatility'] = 100 + (volatility - self.volatility_threshold_high) * 50

        # 2. Strong Trend Detection (Priority: High)
        if abs(trend_strength) >= self.trend_strength_threshold:
            trend_score = 80 + (abs(trend_strength) - self.trend_strength_threshold) * 40
            alignment_bonus = trend_alignment * 20  # Up to 20 bonus points for alignment
            regime_scores['strong_trend'] = trend_score + alignment_bonus

        # 3. Trending Market Detection (Priority: High)
        if abs(trend_strength) >= 0.4 and trend_alignment >= 0.6:
            trending_score = 70 + (abs(trend_strength) - 0.4) * 25 + (trend_alignment - 0.6) * 25
            regime_scores['trending'] = trending_score

        # 4. Low Volatility Detection (Priority: Medium)
        if volatility <= self.volatility_threshold_low:
            low_vol_score = 60 + (self.volatility_threshold_low - volatility) * 30
            # Bonus for stable trends in low volatility
            if trend_alignment >= 0.6:
                low_vol_score += trend_alignment * 15
            regime_scores['low_volatility'] = low_vol_score

        # 5. Range-Bound Detection (Priority: Medium)
        if abs(trend_strength) <= self.range_bound_threshold:
            range_score = 50 + (self.range_bound_threshold - abs(trend_strength)) * 40
            # Penalty for high volatility in range-bound markets
            if volatility > 1.0:
                range_score -= (volatility - 1.0) * 20
            regime_scores['range_bound'] = max(0, range_score)

        # 6. Weak Trend Detection (Priority: Low)
        if 0.2 <= abs(trend_strength) < 0.4 and trend_alignment >= 0.5:
            weak_trend_score = 40 + (abs(trend_strength) - 0.2) * 25 + (trend_alignment - 0.5) * 20
            regime_scores['weak_trend'] = weak_trend_score

        # 7. Consolidation Detection (Priority: Low)
        if volatility <= 1.0 and abs(trend_strength) <= 0.3 and trend_alignment <= 0.6:
            consolidation_score = 35 + (1.0 - volatility) * 15 + (0.6 - trend_alignment) * 10
            regime_scores['consolidation'] = consolidation_score

        # 8. Uncertain/Choppy Market (Default with penalties)
        uncertain_score = 30
        # Penalty for conflicting signals
        if len(regime_scores) == 0:
            uncertain_score += 20  # Boost if no other regime detected
        regime_scores['uncertain'] = uncertain_score

        # Select regime with highest score
        if regime_scores:
            best_regime = max(regime_scores.items(), key=lambda x: x[1])
            logger.info(f"Smart regime detection scores: {regime_scores}")
            logger.info(f"Selected regime: {best_regime[0]} (score: {best_regime[1]:.1f})")
            return best_regime[0]

        # Fallback to original logic if scoring fails
        logger.warning("Smart regime detection failed, using fallback logic")
        if volatility >= self.volatility_threshold_high:
            return 'high_volatility'
        elif volatility <= self.volatility_threshold_low:
            return 'low_volatility'
        elif abs(trend_strength) >= self.trend_strength_threshold and trend_alignment >= 0.7:
            return 'strong_trend'
        elif abs(trend_strength) >= 0.3 and trend_alignment >= 0.5:
            return 'weak_trend'
        elif abs(trend_strength) <= self.range_bound_threshold:
            return 'range_bound'
        else:
            return 'uncertain'
    
    def _calculate_regime_scores(self, trend_strength: float, volatility: float, trend_alignment: float) -> Dict[str, float]:
        """
        Calculate scores for each market regime.
        
        Args:
            trend_strength: Trend strength (-1 to 1)
            volatility: Volatility (ATR as % of price)
            trend_alignment: Trend alignment (0 to 1)
            
        Returns:
            dict: Dictionary with scores for each regime
        """
        # Initialize scores
        scores = {
            'strong_trend': 0,
            'weak_trend': 0,
            'range_bound': 0,
            'high_volatility': 0,
            'low_volatility': 0,
            'uncertain': 0
        }
        
        # Calculate strong trend score
        strong_trend_score = (abs(trend_strength) - 0.5) * 2 if abs(trend_strength) > 0.5 else 0
        strong_trend_score *= trend_alignment
        scores['strong_trend'] = max(0, min(1, strong_trend_score))
        
        # Calculate weak trend score
        weak_trend_score = 1 - abs(abs(trend_strength) - 0.4)
        weak_trend_score = max(0, min(1, weak_trend_score))
        scores['weak_trend'] = weak_trend_score * trend_alignment
        
        # Calculate range-bound score
        range_score = 1 - abs(trend_strength)
        scores['range_bound'] = range_score
        
        # Calculate volatility scores
        high_vol_score = (volatility - self.volatility_threshold_high) / self.volatility_threshold_high
        scores['high_volatility'] = max(0, min(1, high_vol_score))
        
        low_vol_score = 1 - (volatility / self.volatility_threshold_low)
        scores['low_volatility'] = max(0, min(1, low_vol_score))
        
        # Calculate uncertain score (inverse of other scores)
        other_scores_sum = sum(s for s in scores.values())
        scores['uncertain'] = max(0, min(1, 1 - (other_scores_sum / 5)))
        
        return scores
    
    def _get_parameter_adjustments(self, primary_regime: str, regime_scores: Dict[str, float]) -> Dict[str, float]:
        """
        Get parameter adjustments based on regime.
        
        Args:
            primary_regime: Primary market regime
            regime_scores: Scores for each regime
            
        Returns:
            dict: Dictionary with parameter adjustments
        """
        # Get base adjustments for primary regime
        base_adjustments = self.regime_adjustments[primary_regime].copy()
        
        # Calculate weighted adjustments based on all regime scores
        weighted_adjustments = {}
        for param in base_adjustments:
            weighted_value = 0
            for regime, score in regime_scores.items():
                regime_value = self.regime_adjustments[regime][param]
                weighted_value += regime_value * score
            
            # Normalize by sum of scores
            score_sum = sum(regime_scores.values())
            if score_sum > 0:
                weighted_adjustments[param] = weighted_value / score_sum
            else:
                weighted_adjustments[param] = base_adjustments[param]
        
        return weighted_adjustments
    
    def adjust_trading_parameters(self, base_params: Dict[str, float], regime_result: Dict[str, Union[str, float]]) -> Dict[str, float]:
        """
        Adjust trading parameters based on market regime.
        
        Args:
            base_params: Base trading parameters
            regime_result: Market regime detection result
            
        Returns:
            dict: Adjusted trading parameters
        """
        adjustments = regime_result['adjustments']
        
        # Apply adjustments to base parameters
        adjusted_params = {}
        
        # Adjust leverage
        if 'leverage' in base_params:
            adjusted_params['leverage'] = base_params['leverage'] * adjustments['leverage_factor']
        
        # Adjust position size
        if 'position_size' in base_params:
            adjusted_params['position_size'] = base_params['position_size'] * adjustments['position_size_factor']
        
        # Adjust stop loss
        if 'stop_loss_percentage' in base_params:
            adjusted_params['stop_loss_percentage'] = base_params['stop_loss_percentage'] * adjustments['stop_loss_factor']
        
        # Adjust take profit
        if 'take_profit_percentage' in base_params:
            adjusted_params['take_profit_percentage'] = base_params['take_profit_percentage'] * adjustments['take_profit_factor']
        
        # Add minimum entry confidence
        adjusted_params['min_entry_confidence'] = adjustments['entry_confidence']
        
        # Add regime information
        adjusted_params['market_regime'] = regime_result['primary_regime']
        adjusted_params['regime_volatility'] = regime_result['metrics']['volatility']
        adjusted_params['regime_trend_strength'] = regime_result['metrics']['trend_strength']
        
        logger.info(f"Adjusted trading parameters for {regime_result['primary_regime']} regime")
        
        return adjusted_params
