"""
Unit tests for the technical indicators functionality.
"""
import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Import the modules to test
from core.multi_timeframe import MultiTimeframeAnalyzer
from core.features import extract_features
from core.signal_scoring import SignalScorer

class TestIndicators(unittest.TestCase):
    """Test cases for technical indicators."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create sample OHLCV data
        self.sample_data = self._create_sample_data()
        
        # Initialize the MultiTimeframeAnalyzer
        self.mt_analyzer = MultiTimeframeAnalyzer(timeframes=['1m', '5m', '15m'])
        
        # Initialize the SignalScorer
        self.signal_scorer = SignalScorer()
    
    def _create_sample_data(self):
        """Create sample OHLCV data for testing."""
        # Create a date range (increased to 1000 to ensure enough data for all timeframes)
        dates = [datetime.now() - timedelta(minutes=i) for i in range(1000, 0, -1)]

        # Create a sine wave for prices
        t = np.linspace(0, 4*np.pi, 1000)
        close_prices = 100 + 10 * np.sin(t)
        
        # Create OHLCV data
        data = {
            'timestamp': dates,
            'open': close_prices - np.random.rand(1000),
            'high': close_prices + np.random.rand(1000) * 2,
            'low': close_prices - np.random.rand(1000) * 2,
            'close': close_prices,
            'volume': np.random.rand(1000) * 1000
        }
        
        # Create DataFrame
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        # Create multi-timeframe data
        multi_tf_data = {
            '1m': df.copy(),
            '5m': df.iloc[::5, :].copy(),  # Sample every 5th row for 5m data
            '15m': df.iloc[::15, :].copy()  # Sample every 15th row for 15m data
        }
        
        return multi_tf_data
    
    def test_calculate_indicators(self):
        """Test the calculate_indicators method."""
        # Calculate indicators
        indicators = self.mt_analyzer.calculate_indicators(self.sample_data)
        
        # Check that indicators were calculated for each timeframe
        self.assertEqual(len(indicators), 3)
        self.assertIn('1m', indicators)
        self.assertIn('5m', indicators)
        self.assertIn('15m', indicators)
        
        # Check that indicators were calculated correctly
        for tf, df in indicators.items():
            # Check that the DataFrame is not empty
            self.assertFalse(df.empty)
            
            # Check that the indicators were calculated
            self.assertIn('rsi', df.columns)
            self.assertIn('macd', df.columns)
            self.assertIn('macd_signal', df.columns)
            self.assertIn('macd_hist', df.columns)
            self.assertIn('sma_20', df.columns)
            self.assertIn('sma_50', df.columns)
            self.assertIn('ema_20', df.columns)
            self.assertIn('ema_50', df.columns)
            self.assertIn('atr', df.columns)
            
            # Check that the indicators have valid values
            self.assertFalse(df['rsi'].isnull().all())
            self.assertFalse(df['macd'].isnull().all())
            self.assertFalse(df['sma_20'].isnull().all())
            
            # Check that RSI is within valid range (0-100)
            self.assertTrue((df['rsi'].dropna() >= 0).all())
            self.assertTrue((df['rsi'].dropna() <= 100).all())
    
    def test_analyze_trend(self):
        """Test the analyze_trend method."""
        # Calculate indicators
        indicators = self.mt_analyzer.calculate_indicators(self.sample_data)
        
        # Analyze trend
        trend_analysis = self.mt_analyzer.analyze_trend(indicators)
        
        # Check that trend analysis was performed for each timeframe
        self.assertEqual(len(trend_analysis), 3)
        self.assertIn('1m', trend_analysis)
        self.assertIn('5m', trend_analysis)
        self.assertIn('15m', trend_analysis)
        
        # Check that trend analysis contains the expected fields
        for tf, analysis in trend_analysis.items():
            self.assertIn('trend_direction', analysis)
            self.assertIn('trend_score', analysis)
            self.assertIn('rsi', analysis)
            self.assertIn('rsi_signal', analysis)
            self.assertIn('macd_signal', analysis)
            self.assertIn('macd_hist_signal', analysis)
            
            # Check that trend direction is a valid value
            self.assertIn(analysis['trend_direction'], 
                         ['strong_bullish', 'bullish', 'neutral', 'bearish', 'strong_bearish'])
            
            # Check that trend score is within valid range (-1 to 1)
            self.assertTrue(analysis['trend_score'] >= -1)
            self.assertTrue(analysis['trend_score'] <= 1)
            
            # Check that RSI is within valid range (0-100)
            self.assertTrue(analysis['rsi'] >= 0)
            self.assertTrue(analysis['rsi'] <= 100)
            
            # Check that RSI signal is a valid value
            self.assertIn(analysis['rsi_signal'], ['overbought', 'bullish', 'neutral', 'bearish', 'oversold'])
            
            # Check that MACD signals are valid values
            self.assertIn(analysis['macd_signal'], ['bullish', 'neutral', 'bearish'])
            self.assertIn(analysis['macd_hist_signal'], ['bullish', 'neutral', 'bearish'])
    
    def test_calculate_trend_strength(self):
        """Test the calculate_trend_strength method."""
        # Calculate indicators
        indicators = self.mt_analyzer.calculate_indicators(self.sample_data)
        
        # Analyze trend
        trend_analysis = self.mt_analyzer.analyze_trend(indicators)
        
        # Calculate trend strength
        trend_strength = self.mt_analyzer.calculate_trend_strength(trend_analysis)
        
        # Check that trend strength contains the expected fields
        self.assertIn('trend_direction', trend_strength)
        self.assertIn('trend_strength', trend_strength)
        self.assertIn('trend_alignment', trend_strength)
        
        # Check that trend direction is a valid value
        self.assertIn(trend_strength['trend_direction'], 
                     ['strong_bullish', 'bullish', 'neutral', 'bearish', 'strong_bearish'])
        
        # Check that trend strength is within valid range (-1 to 1)
        self.assertTrue(trend_strength['trend_strength'] >= -1)
        self.assertTrue(trend_strength['trend_strength'] <= 1)
        
        # Check that trend alignment is within valid range (0 to 1)
        self.assertTrue(trend_strength['trend_alignment'] >= 0)
        self.assertTrue(trend_strength['trend_alignment'] <= 1)
    
    def test_extract_features(self):
        """Test the extract_features function."""
        # Extract features from the 1m data
        df = self.sample_data['1m'].copy()
        
        # Extract features
        extract_features(df)
        
        # Check that features were extracted
        self.assertIn('returns', df.columns)
        self.assertIn('log_returns', df.columns)
        self.assertIn('volume_ma', df.columns)
        self.assertIn('relative_volume', df.columns)
        self.assertIn('rsi', df.columns)
        self.assertIn('macd', df.columns)
        self.assertIn('macd_signal', df.columns)
        self.assertIn('macd_cross', df.columns)
        self.assertIn('atr', df.columns)
        self.assertIn('bollinger_high', df.columns)
        self.assertIn('bollinger_low', df.columns)
        self.assertIn('bb_width', df.columns)
        self.assertIn('sma_50', df.columns)
        self.assertIn('sma_200', df.columns)
        self.assertIn('sma_cross', df.columns)
    
    def test_signal_scorer(self):
        """Test the SignalScorer class."""
        # Extract features from the 1m data
        df = self.sample_data['1m'].copy()
        extract_features(df)
        
        # Calculate signal scores
        scores = self.signal_scorer.calculate_scores(df)
        
        # Check that scores were calculated
        self.assertIn('macd_score', scores)
        self.assertIn('orderbook_score', scores)
        self.assertIn('volume_score', scores)
        self.assertIn('price_action_score', scores)
        self.assertIn('total_score', scores)
        
        # Check that core scores are within valid range (-1 to 1)
        core_scores = ['macd_score', 'orderbook_score', 'volume_score', 'price_action_score', 'trend_score', 'total_score']
        for key in core_scores:
            if key in scores:
                score = scores[key]
                self.assertTrue(score >= -1, f"{key} score {score} is below -1")
                self.assertTrue(score <= 1, f"{key} score {score} is above 1")

        # Check that confidence is within valid range (0-100%)
        if 'confidence' in scores:
            confidence = scores['confidence']
            self.assertTrue(confidence >= 0, f"confidence {confidence} is below 0")
            self.assertTrue(confidence <= 100, f"confidence {confidence} is above 100")

        # Check that alignment is within valid range (0-100%)
        if 'alignment' in scores:
            alignment = scores['alignment']
            self.assertTrue(alignment >= 0, f"alignment {alignment} is below 0")
            self.assertTrue(alignment <= 100, f"alignment {alignment} is above 100")
        
        # Check that total score is calculated correctly
        expected_total = (
            scores['macd_score'] + 
            scores['orderbook_score'] + 
            scores['volume_score'] + 
            scores['price_action_score']
        )
        self.assertAlmostEqual(scores['total_score'], expected_total, places=6)

if __name__ == '__main__':
    unittest.main()
