2025-07-08 05:44:46,689 - config.autonomous_config - ERROR - Error loading configuration: TradingConfig.__init__() got an unexpected keyword argument 'auto_start_on_launch'
2025-07-08 06:24:03,630 - config.autonomous_config - ERROR - Error loading configuration: TradingConfig.__init__() got an unexpected keyword argument 'auto_start_on_launch'
2025-07-08 07:27:44,395 - config.autonomous_config - ERROR - Error loading configuration: TradingConfig.__init__() got an unexpected keyword argument 'auto_start_on_launch'
2025-07-08 11:07:52,468 - symbol_scanner - ERROR - Error fetching metrics for BTC/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=BTC-USDT
2025-07-08 11:08:09,569 - symbol_scanner - ERROR - Error fetching metrics for ETH/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=ETH-USDT
2025-07-08 11:08:19,598 - symbol_scanner - ERROR - Error fetching metrics for LTC/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=LTC-USDT
2025-07-08 11:08:29,627 - symbol_scanner - ERROR - Error fetching metrics for DOGE/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=DOGE-USDT
2025-07-08 11:08:29,708 - websocket - ERROR - [WinError 10054] An existing connection was forcibly closed by the remote host - goodbye
2025-07-08 11:08:39,642 - symbol_scanner - ERROR - Error fetching metrics for SHIB/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=SHIB-USDT
2025-07-08 11:08:49,656 - symbol_scanner - ERROR - Error fetching metrics for ICP/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=ICP-USDT
2025-07-08 11:08:59,666 - symbol_scanner - ERROR - Error fetching metrics for XRP/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=XRP-USDT
2025-07-08 11:09:09,678 - symbol_scanner - ERROR - Error fetching metrics for LINK/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=LINK-USDT
2025-07-08 11:12:21,101 - symbol_scanner - ERROR - Error fetching metrics for BTC/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=BTC-USDT
2025-07-08 11:12:31,122 - symbol_scanner - ERROR - Error fetching metrics for ETH/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=ETH-USDT
2025-07-08 11:12:41,142 - symbol_scanner - ERROR - Error fetching metrics for LTC/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=LTC-USDT
2025-07-08 11:12:51,162 - symbol_scanner - ERROR - Error fetching metrics for DOGE/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=DOGE-USDT
2025-07-08 11:13:01,179 - symbol_scanner - ERROR - Error fetching metrics for SHIB/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=SHIB-USDT
2025-07-08 11:13:11,200 - symbol_scanner - ERROR - Error fetching metrics for ICP/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=ICP-USDT
2025-07-08 11:13:21,225 - symbol_scanner - ERROR - Error fetching metrics for XRP/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=XRP-USDT
2025-07-08 11:13:31,233 - symbol_scanner - ERROR - Error fetching metrics for LINK/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=LINK-USDT
2025-07-08 11:16:19,598 - symbol_scanner - ERROR - Error fetching metrics for BTC/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=BTC-USDT
2025-07-08 11:16:29,612 - symbol_scanner - ERROR - Error fetching metrics for ETH/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=ETH-USDT
2025-07-08 11:16:39,634 - symbol_scanner - ERROR - Error fetching metrics for LTC/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=LTC-USDT
2025-07-08 11:16:49,660 - symbol_scanner - ERROR - Error fetching metrics for DOGE/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=DOGE-USDT
2025-07-08 11:16:59,685 - symbol_scanner - ERROR - Error fetching metrics for SHIB/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=SHIB-USDT
2025-07-08 11:17:09,714 - symbol_scanner - ERROR - Error fetching metrics for ICP/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=ICP-USDT
2025-07-08 11:17:19,738 - symbol_scanner - ERROR - Error fetching metrics for XRP/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=XRP-USDT
2025-07-08 11:17:29,751 - symbol_scanner - ERROR - Error fetching metrics for LINK/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=LINK-USDT
2025-07-08 11:27:43,619 - websocket - ERROR - [WinError 10054] An existing connection was forcibly closed by the remote host - goodbye
2025-07-08 11:27:54,633 - symbol_scanner - ERROR - Error fetching metrics for ETH/USDT:USDT: huobi GET https://api.hbdm.com/linear-swap-ex/market/detail/merged?contract_code=ETH-USDT
2025-07-08 12:03:24,673 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:03:25,926 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:03:27,230 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:03:28,531 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:03:31,039 - symbol_scanner - ERROR - Error scoring symbol ICP/USDT:USDT: 'affordability_score'
2025-07-08 12:03:32,227 - symbol_scanner - ERROR - Error scoring symbol XRP/USDT:USDT: 'affordability_score'
2025-07-08 12:03:33,456 - symbol_scanner - ERROR - Error scoring symbol LINK/USDT:USDT: 'affordability_score'
2025-07-08 12:03:36,473 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:03:37,752 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:03:39,115 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:03:40,481 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:03:43,035 - symbol_scanner - ERROR - Error scoring symbol ICP/USDT:USDT: 'affordability_score'
2025-07-08 12:03:44,394 - symbol_scanner - ERROR - Error scoring symbol XRP/USDT:USDT: 'affordability_score'
2025-07-08 12:03:45,721 - symbol_scanner - ERROR - Error scoring symbol LINK/USDT:USDT: 'affordability_score'
2025-07-08 12:03:49,449 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:03:51,225 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:03:52,525 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:03:53,810 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:03:56,290 - symbol_scanner - ERROR - Error scoring symbol ICP/USDT:USDT: 'affordability_score'
2025-07-08 12:03:57,561 - symbol_scanner - ERROR - Error scoring symbol XRP/USDT:USDT: 'affordability_score'
2025-07-08 12:03:58,998 - symbol_scanner - ERROR - Error scoring symbol LINK/USDT:USDT: 'affordability_score'
2025-07-08 12:04:03,786 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:04:05,025 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:04:06,236 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:04:07,462 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:04:09,967 - symbol_scanner - ERROR - Error scoring symbol ICP/USDT:USDT: 'affordability_score'
2025-07-08 12:04:11,216 - symbol_scanner - ERROR - Error scoring symbol XRP/USDT:USDT: 'affordability_score'
2025-07-08 12:04:12,504 - symbol_scanner - ERROR - Error scoring symbol LINK/USDT:USDT: 'affordability_score'
2025-07-08 12:04:18,948 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:04:20,175 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:04:21,403 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:04:22,608 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:04:25,220 - symbol_scanner - ERROR - Error scoring symbol ICP/USDT:USDT: 'affordability_score'
2025-07-08 12:04:26,472 - symbol_scanner - ERROR - Error scoring symbol XRP/USDT:USDT: 'affordability_score'
2025-07-08 12:04:27,656 - symbol_scanner - ERROR - Error scoring symbol LINK/USDT:USDT: 'affordability_score'
2025-07-08 12:04:33,589 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:04:34,861 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:04:36,001 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:04:37,147 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:04:39,664 - symbol_scanner - ERROR - Error scoring symbol ICP/USDT:USDT: 'affordability_score'
2025-07-08 12:04:40,790 - symbol_scanner - ERROR - Error scoring symbol XRP/USDT:USDT: 'affordability_score'
2025-07-08 12:04:41,915 - symbol_scanner - ERROR - Error scoring symbol LINK/USDT:USDT: 'affordability_score'
2025-07-08 12:04:48,426 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:04:49,606 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:04:50,718 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:04:51,812 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:04:54,064 - symbol_scanner - ERROR - Error scoring symbol ICP/USDT:USDT: 'affordability_score'
2025-07-08 12:04:55,198 - symbol_scanner - ERROR - Error scoring symbol XRP/USDT:USDT: 'affordability_score'
2025-07-08 12:04:56,327 - symbol_scanner - ERROR - Error scoring symbol LINK/USDT:USDT: 'affordability_score'
2025-07-08 12:05:03,467 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:05:04,595 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:05:05,761 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:05:52,100 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:05:52,103 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:05:53,506 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:05:53,507 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:05:54,851 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:05:54,852 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:05:56,146 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:05:56,148 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:05:58,566 - symbol_scanner - ERROR - Error scoring symbol ICP/USDT:USDT: 'affordability_score'
2025-07-08 12:05:58,567 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:05:59,878 - symbol_scanner - ERROR - Error scoring symbol XRP/USDT:USDT: 'affordability_score'
2025-07-08 12:05:59,879 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:01,149 - symbol_scanner - ERROR - Error scoring symbol LINK/USDT:USDT: 'affordability_score'
2025-07-08 12:06:01,150 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:05,860 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:06:05,861 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:07,214 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:06:07,216 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:08,397 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:06:08,398 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:09,547 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:06:09,548 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:11,991 - symbol_scanner - ERROR - Error scoring symbol ICP/USDT:USDT: 'affordability_score'
2025-07-08 12:06:11,992 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:13,199 - symbol_scanner - ERROR - Error scoring symbol XRP/USDT:USDT: 'affordability_score'
2025-07-08 12:06:13,200 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:14,507 - symbol_scanner - ERROR - Error scoring symbol LINK/USDT:USDT: 'affordability_score'
2025-07-08 12:06:14,508 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:16,919 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:06:16,920 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:18,103 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:06:18,104 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:19,516 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:06:19,516 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:20,731 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:06:20,732 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:23,237 - symbol_scanner - ERROR - Error scoring symbol ICP/USDT:USDT: 'affordability_score'
2025-07-08 12:06:23,238 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:24,472 - symbol_scanner - ERROR - Error scoring symbol XRP/USDT:USDT: 'affordability_score'
2025-07-08 12:06:24,474 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:25,654 - symbol_scanner - ERROR - Error scoring symbol LINK/USDT:USDT: 'affordability_score'
2025-07-08 12:06:25,654 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:32,298 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:06:32,299 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:34,041 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:06:34,042 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:35,419 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:06:35,420 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:36,721 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:06:36,722 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:38,990 - symbol_scanner - ERROR - Error scoring symbol ICP/USDT:USDT: 'affordability_score'
2025-07-08 12:06:38,992 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:40,187 - symbol_scanner - ERROR - Error scoring symbol XRP/USDT:USDT: 'affordability_score'
2025-07-08 12:06:40,207 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:41,535 - symbol_scanner - ERROR - Error scoring symbol LINK/USDT:USDT: 'affordability_score'
2025-07-08 12:06:41,540 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:52,305 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:06:52,306 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:53,444 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:06:53,445 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:54,554 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:06:54,554 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:55,688 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:06:55,689 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:57,910 - symbol_scanner - ERROR - Error scoring symbol ICP/USDT:USDT: 'affordability_score'
2025-07-08 12:06:57,911 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:06:59,022 - symbol_scanner - ERROR - Error scoring symbol XRP/USDT:USDT: 'affordability_score'
2025-07-08 12:06:59,024 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:07:00,125 - symbol_scanner - ERROR - Error scoring symbol LINK/USDT:USDT: 'affordability_score'
2025-07-08 12:07:00,127 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:07:07,312 - symbol_scanner - ERROR - Error scoring symbol BTC/USDT:USDT: 'affordability_score'
2025-07-08 12:07:07,313 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:07:08,466 - symbol_scanner - ERROR - Error scoring symbol ETH/USDT:USDT: 'affordability_score'
2025-07-08 12:07:08,467 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:07:09,594 - symbol_scanner - ERROR - Error scoring symbol LTC/USDT:USDT: 'affordability_score'
2025-07-08 12:07:09,594 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

2025-07-08 12:07:10,707 - symbol_scanner - ERROR - Error scoring symbol DOGE/USDT:USDT: 'affordability_score'
2025-07-08 12:07:10,708 - symbol_scanner - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\dev\Epinnox_v6\symbol_scanner.py", line 297, in score_symbol
    affordability_score * self.metrics_weights['affordability_score']
KeyError: 'affordability_score'

