"""
Visualization Module for Trading System
"""
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime
import os

# Set the style
sns.set(style="darkgrid")

def plot_price_with_decisions(df, decisions=None, save_path=None):
    """
    Plot price chart with trading decisions.
    
    Args:
        df: DataFrame with OHLCV data
        decisions: List of (timestamp, decision, explanation) tuples
        save_path: Path to save the plot (optional)
    """
    plt.figure(figsize=(14, 8))
    
    # Plot the price
    plt.plot(df['datetime'], df['close'], label='Close Price', color='blue', alpha=0.7)
    
    # Add VWAP if available
    if 'vwap' in df.columns:
        plt.plot(df['datetime'], df['vwap'], label='VWAP', color='purple', alpha=0.5, linestyle='--')
    
    # Add SMA if available
    if 'sma_50' in df.columns:
        plt.plot(df['datetime'], df['sma_50'], label='SMA 50', color='orange', alpha=0.5)
    if 'sma_200' in df.columns:
        plt.plot(df['datetime'], df['sma_200'], label='SMA 200', color='red', alpha=0.5)
    
    # Add Bollinger Bands if available
    if 'bollinger_high' in df.columns and 'bollinger_low' in df.columns:
        plt.plot(df['datetime'], df['bollinger_high'], label='Bollinger High', color='green', alpha=0.3)
        plt.plot(df['datetime'], df['bollinger_low'], label='Bollinger Low', color='green', alpha=0.3)
        plt.fill_between(df['datetime'], df['bollinger_high'], df['bollinger_low'], color='green', alpha=0.1)
    
    # Add trading decisions if available
    if decisions:
        for timestamp, decision, _ in decisions:
            if decision == 'LONG':
                plt.scatter(timestamp, df.loc[df['datetime'] == timestamp, 'close'].values[0], 
                           marker='^', color='green', s=100, label='LONG')
            elif decision == 'SHORT':
                plt.scatter(timestamp, df.loc[df['datetime'] == timestamp, 'close'].values[0], 
                           marker='v', color='red', s=100, label='SHORT')
    
    # Set labels and title
    plt.title('Price Chart with Trading Decisions', fontsize=16)
    plt.xlabel('Date', fontsize=12)
    plt.ylabel('Price', fontsize=12)
    
    # Add legend (only show each label once)
    handles, labels = plt.gca().get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    plt.legend(by_label.values(), by_label.keys(), loc='best')
    
    # Format the x-axis
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # Save the plot if a path is provided
    if save_path:
        plt.savefig(save_path)
    
    plt.show()

def plot_indicators(df, indicators=None, save_path=None):
    """
    Plot technical indicators.
    
    Args:
        df: DataFrame with OHLCV data and indicators
        indicators: List of indicators to plot (default: ['rsi', 'macd'])
        save_path: Path to save the plot (optional)
    """
    if indicators is None:
        indicators = ['rsi', 'macd']
    
    # Filter to only include available indicators
    indicators = [ind for ind in indicators if ind in df.columns]
    
    if not indicators:
        print("No indicators available to plot")
        return
    
    # Create subplots
    fig, axes = plt.subplots(len(indicators), 1, figsize=(14, 10), sharex=True)
    if len(indicators) == 1:
        axes = [axes]
    
    # Plot each indicator
    for i, indicator in enumerate(indicators):
        ax = axes[i]
        ax.plot(df['datetime'], df[indicator], label=indicator.upper())
        
        # Add special formatting for specific indicators
        if indicator == 'rsi':
            ax.axhline(y=70, color='r', linestyle='--', alpha=0.5)
            ax.axhline(y=30, color='g', linestyle='--', alpha=0.5)
            ax.set_ylim(0, 100)
        elif indicator == 'macd':
            if 'macd_signal' in df.columns:
                ax.plot(df['datetime'], df['macd_signal'], label='Signal', color='red', alpha=0.7)
            ax.axhline(y=0, color='k', linestyle='-', alpha=0.2)
        
        ax.set_title(f'{indicator.upper()}', fontsize=12)
        ax.legend(loc='best')
    
    # Format the x-axis
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # Save the plot if a path is provided
    if save_path:
        plt.savefig(save_path)
    
    plt.show()

def create_dashboard(df, decisions=None, save_dir=None):
    """
    Create a complete trading dashboard with multiple plots.
    
    Args:
        df: DataFrame with OHLCV data and indicators
        decisions: List of (timestamp, decision, explanation) tuples
        save_dir: Directory to save the plots (optional)
    """
    # Create save directory if provided
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)
    
    # 1. Price chart with decisions
    price_path = os.path.join(save_dir, 'price_chart.png') if save_dir else None
    plot_price_with_decisions(df, decisions, price_path)
    
    # 2. Momentum indicators
    momentum_indicators = ['rsi', 'macd']
    momentum_path = os.path.join(save_dir, 'momentum_indicators.png') if save_dir else None
    plot_indicators(df, momentum_indicators, momentum_path)
    
    # 3. Volume analysis
    if 'volume' in df.columns:
        plt.figure(figsize=(14, 6))
        plt.bar(df['datetime'], df['volume'], color='blue', alpha=0.6, label='Volume')
        
        if 'relative_volume' in df.columns:
            plt.plot(df['datetime'], df['relative_volume'] * df['volume'].mean(), 
                    color='red', label='Relative Volume')
        
        plt.title('Volume Analysis', fontsize=16)
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Volume', fontsize=12)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(os.path.join(save_dir, 'volume_analysis.png'))
        
        plt.show()
    
    # 4. Decision summary
    if decisions:
        decision_counts = {'LONG': 0, 'SHORT': 0, 'WAIT': 0}
        for _, decision, _ in decisions:
            if decision in decision_counts:
                decision_counts[decision] += 1
        
        plt.figure(figsize=(10, 6))
        plt.bar(decision_counts.keys(), decision_counts.values(), color=['green', 'red', 'gray'])
        plt.title('Decision Summary', fontsize=16)
        plt.ylabel('Count', fontsize=12)
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(os.path.join(save_dir, 'decision_summary.png'))
        
        plt.show()
