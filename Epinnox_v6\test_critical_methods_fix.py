#!/usr/bin/env python3
"""
Test script to verify critical methods are working properly
Tests the recently added missing methods and system health improvements
"""

import sys
import os
sys.path.append('.')

def test_critical_methods_availability():
    """Test that all critical methods are available and callable"""
    print("🧪 Testing critical methods availability...")
    
    try:
        # Import the main interface class
        from launch_epinnox import EpinnoxTradingInterface
        
        # Create a test instance (without GUI initialization)
        class MockInterface(EpinnoxTradingInterface):
            def __init__(self):
                # Initialize only the essential attributes for testing
                self.emergency_stop_active = False
                self.emergency_risk_mode = False
                
            def log_message(self, msg):
                print(f"LOG: {msg}")
        
        interface = MockInterface()
        
        # List of critical methods to test
        critical_methods = [
            'calculate_position_size',
            'validate_trade_instruction', 
            'execute_limit_buy_order',
            'execute_limit_sell_order',
            'fetch_enriched_market_data',
            'update_market_intelligence_display',
            'monitor_risk_thresholds'
        ]
        
        results = {}
        
        for method_name in critical_methods:
            try:
                # Check if method exists and is callable
                if hasattr(interface, method_name):
                    method = getattr(interface, method_name)
                    if callable(method):
                        results[method_name] = "✅ AVAILABLE"
                    else:
                        results[method_name] = "❌ NOT CALLABLE"
                else:
                    results[method_name] = "❌ MISSING"
                    
            except Exception as e:
                results[method_name] = f"❌ ERROR: {e}"
        
        # Print results
        print("\n📊 Critical Methods Availability Results:")
        all_available = True
        for method_name, status in results.items():
            print(f"   {method_name}: {status}")
            if "❌" in status:
                all_available = False
        
        if all_available:
            print("\n🎉 All critical methods are available and callable!")
            return True
        else:
            print("\n⚠️ Some critical methods are missing or not callable")
            return False
        
    except Exception as e:
        print(f"❌ Error testing critical methods: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trade_instruction_validation():
    """Test the validate_trade_instruction method"""
    print("\n🧪 Testing trade instruction validation...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        
        class MockInterface(EpinnoxTradingInterface):
            def __init__(self):
                self.emergency_stop_active = False
                self.emergency_risk_mode = False
                
            def log_message(self, msg):
                print(f"VALIDATION LOG: {msg}")
        
        interface = MockInterface()
        
        # Test cases
        test_cases = [
            # Valid instructions
            {
                'name': 'Valid BUY instruction',
                'instruction': {
                    'ACTION': 'BUY',
                    'QUANTITY': 100.0,
                    'LEVERAGE': 2,
                    'RISK_PCT': 2.0,
                    'confidence': 75.0
                },
                'expected': True
            },
            {
                'name': 'Valid SELL instruction',
                'instruction': {
                    'ACTION': 'SELL',
                    'QUANTITY': 50.0,
                    'LEVERAGE': 1,
                    'RISK_PCT': 1.5,
                    'confidence': 80.0
                },
                'expected': True
            },
            {
                'name': 'Valid WAIT instruction',
                'instruction': {
                    'ACTION': 'WAIT',
                    'QUANTITY': 0,
                    'LEVERAGE': 1
                },
                'expected': True
            },
            # Invalid instructions
            {
                'name': 'Missing ACTION',
                'instruction': {
                    'QUANTITY': 100.0,
                    'LEVERAGE': 2
                },
                'expected': False
            },
            {
                'name': 'Invalid ACTION',
                'instruction': {
                    'ACTION': 'INVALID',
                    'QUANTITY': 100.0,
                    'LEVERAGE': 2
                },
                'expected': False
            },
            {
                'name': 'Zero quantity',
                'instruction': {
                    'ACTION': 'BUY',
                    'QUANTITY': 0,
                    'LEVERAGE': 2
                },
                'expected': False
            },
            {
                'name': 'Invalid leverage',
                'instruction': {
                    'ACTION': 'BUY',
                    'QUANTITY': 100.0,
                    'LEVERAGE': 500
                },
                'expected': False
            }
        ]
        
        # Run test cases
        passed = 0
        failed = 0
        
        for test_case in test_cases:
            try:
                result = interface.validate_trade_instruction(test_case['instruction'])
                expected = test_case['expected']
                
                if result == expected:
                    print(f"   ✅ {test_case['name']}: PASS")
                    passed += 1
                else:
                    print(f"   ❌ {test_case['name']}: FAIL (expected {expected}, got {result})")
                    failed += 1
                    
            except Exception as e:
                print(f"   ❌ {test_case['name']}: ERROR - {e}")
                failed += 1
        
        print(f"\n📊 Validation Test Results: {passed} passed, {failed} failed")
        return failed == 0
        
    except Exception as e:
        print(f"❌ Error testing trade instruction validation: {e}")
        return False

def test_risk_metrics_parsing():
    """Test the risk metrics parsing fix"""
    print("\n🧪 Testing risk metrics parsing...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        
        class MockInterface(EpinnoxTradingInterface):
            def __init__(self):
                # Create mock labels with test data
                class MockLabel:
                    def __init__(self, text):
                        self._text = text
                    def text(self):
                        return self._text
                
                self.day_drawdown_label = MockLabel("Day Drawdown: 5.7%")
                self.cumulative_risk_label = MockLabel("Cumulative Risk: 11.2%")
                self.portfolio_risk_label = MockLabel("Portfolio Risk: 3.4%")
                
            def log_message(self, msg):
                print(f"RISK LOG: {msg}")
        
        interface = MockInterface()
        
        # Test risk metrics parsing
        risk_metrics = interface.get_current_risk_metrics()
        
        print(f"   Parsed risk metrics: {risk_metrics}")
        
        # Verify parsing worked correctly
        expected = {
            'day_drawdown': 5.7,
            'cumulative_risk': 11.2,
            'portfolio_risk': 3.4
        }
        
        success = True
        for key, expected_value in expected.items():
            actual_value = risk_metrics.get(key, 0.0)
            if abs(actual_value - expected_value) < 0.1:
                print(f"   ✅ {key}: {actual_value}% (expected {expected_value}%)")
            else:
                print(f"   ❌ {key}: {actual_value}% (expected {expected_value}%)")
                success = False
        
        if success:
            print("   🎉 Risk metrics parsing working correctly!")
        else:
            print("   ⚠️ Risk metrics parsing has issues")
        
        return success
        
    except Exception as e:
        print(f"❌ Error testing risk metrics parsing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all critical methods tests"""
    print("🚀 Running Critical Methods Fix Verification Tests")
    print("=" * 60)
    
    test1_result = test_critical_methods_availability()
    test2_result = test_trade_instruction_validation()
    test3_result = test_risk_metrics_parsing()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   Critical methods availability: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Trade instruction validation: {'✅ PASS' if test2_result else '❌ FAIL'}")
    print(f"   Risk metrics parsing: {'✅ PASS' if test3_result else '❌ FAIL'}")
    
    if test1_result and test2_result and test3_result:
        print("\n🎉 All critical methods tests PASSED!")
        print("The system health issues should now be resolved.")
        return True
    else:
        print("\n⚠️ Some tests failed - additional fixes may be needed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
