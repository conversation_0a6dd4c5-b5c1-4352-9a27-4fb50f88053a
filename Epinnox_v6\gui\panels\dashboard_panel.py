"""
Dashboard Panel for EPINNOX v6 GUI
Main dashboard interface for trading system monitoring
"""

try:
    from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                                QFrame, QGridLayout, QPushButton, QTextEdit)
    from PyQt5.QtCore import QTimer, pyqtSignal
    from PyQt5.QtGui import QFont, QPalette, QColor
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class DashboardPanel:
    """
    Main dashboard panel for trading system monitoring
    Falls back to console display if PyQt5 not available
    """
    
    def __init__(self, parent=None):
        self.parent = parent
        self.data = {}
        self.update_timer = None
        
        if PYQT_AVAILABLE:
            self.widget = DashboardPanelWidget(parent)
        else:
            logger.warning("PyQt5 not available. Dashboard will use console output.")
            self.widget = None
    
    def update_data(self, data: Dict[str, Any]):
        """Update dashboard with new data"""
        self.data = data
        
        if self.widget:
            self.widget.update_display(data)
        else:
            self._console_display(data)
    
    def _console_display(self, data: Dict[str, Any]):
        """Display data in console format"""
        print("\n" + "="*50)
        print("EPINNOX v6 DASHBOARD")
        print("="*50)
        
        for key, value in data.items():
            print(f"{key}: {value}")
        
        print("="*50)
    
    def start_auto_update(self, interval_ms: int = 5000):
        """Start automatic data updates"""
        if self.widget:
            self.widget.start_auto_update(interval_ms)
    
    def stop_auto_update(self):
        """Stop automatic data updates"""
        if self.widget:
            self.widget.stop_auto_update()

if PYQT_AVAILABLE:
    class DashboardPanelWidget(QWidget):
        """PyQt5 widget for dashboard panel"""
        
        data_updated = pyqtSignal(dict)
        
        def __init__(self, parent=None):
            super().__init__(parent)
            self.setup_ui()
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.request_data_update)
        
        def setup_ui(self):
            """Setup the user interface"""
            layout = QVBoxLayout(self)
            
            # Title
            title = QLabel("EPINNOX v6 Trading Dashboard")
            title.setFont(QFont("Arial", 16, QFont.Bold))
            layout.addWidget(title)
            
            # Metrics grid
            self.metrics_frame = QFrame()
            self.metrics_layout = QGridLayout(self.metrics_frame)
            layout.addWidget(self.metrics_frame)
            
            # Create metric labels
            self.metric_labels = {}
            metrics = [
                ("Balance", "balance"),
                ("Total P&L", "total_pnl"),
                ("Win Rate", "win_rate"),
                ("Total Trades", "total_trades"),
                ("Active Positions", "active_positions"),
                ("System Status", "status")
            ]
            
            for i, (display_name, key) in enumerate(metrics):
                row = i // 2
                col = (i % 2) * 2
                
                label = QLabel(f"{display_name}:")
                label.setFont(QFont("Arial", 10, QFont.Bold))
                self.metrics_layout.addWidget(label, row, col)
                
                value_label = QLabel("--")
                value_label.setFont(QFont("Arial", 10))
                self.metrics_layout.addWidget(value_label, row, col + 1)
                
                self.metric_labels[key] = value_label
            
            # Log area
            log_label = QLabel("Recent Activity:")
            log_label.setFont(QFont("Arial", 12, QFont.Bold))
            layout.addWidget(log_label)
            
            self.log_area = QTextEdit()
            self.log_area.setMaximumHeight(200)
            self.log_area.setReadOnly(True)
            layout.addWidget(self.log_area)
            
            # Control buttons
            button_layout = QHBoxLayout()
            
            self.refresh_button = QPushButton("Refresh")
            self.refresh_button.clicked.connect(self.request_data_update)
            button_layout.addWidget(self.refresh_button)
            
            self.clear_log_button = QPushButton("Clear Log")
            self.clear_log_button.clicked.connect(self.clear_log)
            button_layout.addWidget(self.clear_log_button)
            
            layout.addLayout(button_layout)
        
        def update_display(self, data: Dict[str, Any]):
            """Update the display with new data"""
            # Update metrics
            for key, label in self.metric_labels.items():
                if key in data:
                    value = data[key]
                    if isinstance(value, float):
                        if key in ['balance', 'total_pnl']:
                            label.setText(f"${value:.2f}")
                        elif key == 'win_rate':
                            label.setText(f"{value:.1%}")
                        else:
                            label.setText(f"{value:.2f}")
                    else:
                        label.setText(str(value))
            
            # Add to log if there's a message
            if 'message' in data:
                self.add_log_message(data['message'])
        
        def add_log_message(self, message: str):
            """Add a message to the log area"""
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.log_area.append(f"[{timestamp}] {message}")
        
        def clear_log(self):
            """Clear the log area"""
            self.log_area.clear()
        
        def request_data_update(self):
            """Request data update from parent"""
            self.data_updated.emit({})
        
        def start_auto_update(self, interval_ms: int):
            """Start automatic updates"""
            self.update_timer.start(interval_ms)
        
        def stop_auto_update(self):
            """Stop automatic updates"""
            self.update_timer.stop()

# Mock class for testing when PyQt5 is not available
class MockDashboardPanel:
    """Mock dashboard panel for testing"""
    
    def __init__(self, parent=None):
        self.data = {}
    
    def update_data(self, data):
        self.data = data
    
    def start_auto_update(self, interval_ms):
        pass
    
    def stop_auto_update(self):
        pass
