#!/usr/bin/env python3
"""
Real-Time Monitoring Dashboard for Epinnox v6
Provides real-time P&L tracking, performance metrics, system health indicators, and alerts
"""

import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import deque
import threading

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_pnl: float = 0.0
    daily_pnl: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    sharpe_ratio: float = 0.0
    profit_factor: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0

@dataclass
class SystemHealth:
    """System health indicators"""
    overall_status: str = "unknown"
    exchange_status: str = "unknown"
    trading_interface_status: str = "unknown"
    market_data_status: str = "unknown"
    llm_orchestrator_status: str = "unknown"
    risk_manager_status: str = "unknown"
    last_update: Optional[datetime] = None

@dataclass
class AlertData:
    """Alert data structure"""
    id: str
    level: str  # info, warning, error, critical
    component: str
    message: str
    timestamp: datetime
    acknowledged: bool = False

class MonitoringDashboard:
    """
    Real-time monitoring dashboard for autonomous trading system
    """
    
    def __init__(self, max_history_size: int = 1000):
        """Initialize monitoring dashboard"""
        self.max_history_size = max_history_size
        
        # Performance tracking
        self.performance_metrics = PerformanceMetrics()
        self.pnl_history = deque(maxlen=max_history_size)
        self.trade_history = deque(maxlen=max_history_size)
        
        # System health
        self.system_health = SystemHealth()
        self.health_history = deque(maxlen=100)
        
        # Alerts
        self.alerts = deque(maxlen=200)
        self.alert_counter = 0
        
        # Real-time data
        self.real_time_data = {
            'current_balance': 0.0,
            'daily_pnl_pct': 0.0,
            'open_positions': 0,
            'pending_orders': 0,
            'last_trade_time': None,
            'trading_active': False,
            'autonomous_mode': False
        }
        
        # Threading for real-time updates
        self.update_lock = threading.Lock()
        self.monitoring_active = False
        
        logger.info("📊 Monitoring Dashboard initialized")
    
    def update_trade_metrics(self, trade_data: Dict[str, Any]):
        """Update performance metrics with new trade data"""
        try:
            with self.update_lock:
                # Add to trade history
                trade_record = {
                    'timestamp': datetime.now(),
                    'symbol': trade_data.get('symbol', ''),
                    'side': trade_data.get('side', ''),
                    'quantity': trade_data.get('quantity', 0.0),
                    'price': trade_data.get('price', 0.0),
                    'pnl': trade_data.get('pnl', 0.0),
                    'success': trade_data.get('success', False)
                }
                self.trade_history.append(trade_record)
                
                # Update performance metrics
                if trade_record['success']:
                    self.performance_metrics.total_trades += 1
                    pnl = trade_record['pnl']
                    
                    if pnl > 0:
                        self.performance_metrics.winning_trades += 1
                        self.performance_metrics.avg_win = (
                            (self.performance_metrics.avg_win * (self.performance_metrics.winning_trades - 1) + pnl) /
                            self.performance_metrics.winning_trades
                        )
                        if pnl > self.performance_metrics.largest_win:
                            self.performance_metrics.largest_win = pnl
                    else:
                        self.performance_metrics.losing_trades += 1
                        self.performance_metrics.avg_loss = (
                            (self.performance_metrics.avg_loss * (self.performance_metrics.losing_trades - 1) + abs(pnl)) /
                            self.performance_metrics.losing_trades
                        )
                        if abs(pnl) > abs(self.performance_metrics.largest_loss):
                            self.performance_metrics.largest_loss = pnl
                    
                    # Update total P&L
                    self.performance_metrics.total_pnl += pnl
                    
                    # Calculate win rate
                    if self.performance_metrics.total_trades > 0:
                        self.performance_metrics.win_rate = (
                            self.performance_metrics.winning_trades / self.performance_metrics.total_trades * 100
                        )
                    
                    # Calculate profit factor
                    if self.performance_metrics.avg_loss > 0:
                        total_wins = self.performance_metrics.winning_trades * self.performance_metrics.avg_win
                        total_losses = self.performance_metrics.losing_trades * self.performance_metrics.avg_loss
                        if total_losses > 0:
                            self.performance_metrics.profit_factor = total_wins / total_losses
                
                # Update real-time data
                self.real_time_data['last_trade_time'] = datetime.now()
                
                logger.debug(f"📊 Trade metrics updated: {trade_record['symbol']} {trade_record['side']}")
                
        except Exception as e:
            logger.error(f"Error updating trade metrics: {e}")
    
    def update_pnl(self, current_balance: float, daily_pnl: float):
        """Update P&L tracking"""
        try:
            with self.update_lock:
                # Add to P&L history
                pnl_record = {
                    'timestamp': datetime.now(),
                    'balance': current_balance,
                    'daily_pnl': daily_pnl,
                    'total_pnl': self.performance_metrics.total_pnl
                }
                self.pnl_history.append(pnl_record)
                
                # Update daily P&L
                self.performance_metrics.daily_pnl = daily_pnl
                
                # Update max drawdown
                if daily_pnl < 0 and abs(daily_pnl) > abs(self.performance_metrics.max_drawdown):
                    self.performance_metrics.max_drawdown = daily_pnl
                
                # Update real-time data
                self.real_time_data['current_balance'] = current_balance
                self.real_time_data['daily_pnl_pct'] = daily_pnl
                
        except Exception as e:
            logger.error(f"Error updating P&L: {e}")
    
    def update_system_health(self, health_data: Dict[str, str]):
        """Update system health indicators"""
        try:
            with self.update_lock:
                # Update system health
                self.system_health.overall_status = health_data.get('overall_status', 'unknown')
                self.system_health.exchange_status = health_data.get('exchange_status', 'unknown')
                self.system_health.trading_interface_status = health_data.get('trading_interface_status', 'unknown')
                self.system_health.market_data_status = health_data.get('market_data_status', 'unknown')
                self.system_health.llm_orchestrator_status = health_data.get('llm_orchestrator_status', 'unknown')
                self.system_health.risk_manager_status = health_data.get('risk_manager_status', 'unknown')
                self.system_health.last_update = datetime.now()
                
                # Add to health history
                health_record = {
                    'timestamp': datetime.now(),
                    'health_data': health_data.copy()
                }
                self.health_history.append(health_record)
                
        except Exception as e:
            logger.error(f"Error updating system health: {e}")
    
    def add_alert(self, level: str, component: str, message: str) -> str:
        """Add a new alert"""
        try:
            with self.update_lock:
                self.alert_counter += 1
                alert_id = f"alert_{self.alert_counter}_{int(time.time())}"
                
                alert = AlertData(
                    id=alert_id,
                    level=level,
                    component=component,
                    message=message,
                    timestamp=datetime.now()
                )
                
                self.alerts.append(alert)
                
                logger.info(f"🚨 Alert added: [{level.upper()}] {component}: {message}")
                return alert_id
                
        except Exception as e:
            logger.error(f"Error adding alert: {e}")
            return ""
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert"""
        try:
            with self.update_lock:
                for alert in self.alerts:
                    if alert.id == alert_id:
                        alert.acknowledged = True
                        logger.info(f"✅ Alert acknowledged: {alert_id}")
                        return True
                return False
                
        except Exception as e:
            logger.error(f"Error acknowledging alert: {e}")
            return False
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get complete dashboard data"""
        try:
            with self.update_lock:
                return {
                    'performance_metrics': asdict(self.performance_metrics),
                    'system_health': asdict(self.system_health),
                    'real_time_data': self.real_time_data.copy(),
                    'recent_trades': list(self.trade_history)[-10:],  # Last 10 trades
                    'recent_pnl': list(self.pnl_history)[-50:],       # Last 50 P&L points
                    'active_alerts': [
                        asdict(alert) for alert in self.alerts 
                        if not alert.acknowledged and 
                        datetime.now() - alert.timestamp < timedelta(hours=24)
                    ],
                    'alert_summary': {
                        'total_alerts': len(self.alerts),
                        'unacknowledged': sum(1 for alert in self.alerts if not alert.acknowledged),
                        'critical': sum(1 for alert in self.alerts if alert.level == 'critical' and not alert.acknowledged),
                        'errors': sum(1 for alert in self.alerts if alert.level == 'error' and not alert.acknowledged)
                    }
                }
                
        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}")
            return {}
    
    def update_real_time_status(self, status_data: Dict[str, Any]):
        """Update real-time status indicators"""
        try:
            with self.update_lock:
                self.real_time_data.update(status_data)
                
        except Exception as e:
            logger.error(f"Error updating real-time status: {e}")
    
    def calculate_sharpe_ratio(self, risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio from P&L history"""
        try:
            if len(self.pnl_history) < 2:
                return 0.0
            
            # Get daily returns
            returns = []
            for i in range(1, len(self.pnl_history)):
                prev_balance = self.pnl_history[i-1]['balance']
                curr_balance = self.pnl_history[i]['balance']
                if prev_balance > 0:
                    daily_return = (curr_balance - prev_balance) / prev_balance
                    returns.append(daily_return)
            
            if not returns:
                return 0.0
            
            # Calculate Sharpe ratio
            avg_return = sum(returns) / len(returns)
            if len(returns) > 1:
                variance = sum((r - avg_return) ** 2 for r in returns) / (len(returns) - 1)
                std_dev = variance ** 0.5
                
                if std_dev > 0:
                    daily_risk_free = risk_free_rate / 365
                    sharpe = (avg_return - daily_risk_free) / std_dev
                    return sharpe * (365 ** 0.5)  # Annualized
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0
    
    def export_performance_report(self, filepath: str) -> bool:
        """Export performance report to file"""
        try:
            dashboard_data = self.get_dashboard_data()
            
            # Calculate additional metrics
            dashboard_data['performance_metrics']['sharpe_ratio'] = self.calculate_sharpe_ratio()
            
            # Add timestamp
            dashboard_data['report_timestamp'] = datetime.now().isoformat()
            
            with open(filepath, 'w') as f:
                json.dump(dashboard_data, f, indent=2, default=str)
            
            logger.info(f"📊 Performance report exported to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting performance report: {e}")
            return False
