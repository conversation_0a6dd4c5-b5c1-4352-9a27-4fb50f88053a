#!/usr/bin/env python3
"""
WebSocket Connection Manager for Epinnox v6
Handles WebSocket connections with retry logic
"""

import asyncio
import websockets
import logging
import json
import time

class WebSocketManager:
    """Manage WebSocket connections with automatic retry"""
    
    def __init__(self):
        self.connection = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        
    async def connect(self, uri):
        """Connect to WebSocket with retry logic"""
        while self.reconnect_attempts < self.max_reconnect_attempts:
            try:
                self.connection = await websockets.connect(uri)
                self.is_connected = True
                self.reconnect_attempts = 0
                print(f"WebSocket connected to {uri}")
                return True
                
            except Exception as e:
                self.reconnect_attempts += 1
                print(f"WebSocket connection failed (attempt {self.reconnect_attempts}): {e}")
                
                if self.reconnect_attempts < self.max_reconnect_attempts:
                    await asyncio.sleep(2 ** self.reconnect_attempts)  # Exponential backoff
        
        print("WebSocket connection failed after maximum attempts")
        return False
    
    async def send_message(self, message):
        """Send message through WebSocket"""
        if self.is_connected and self.connection:
            try:
                await self.connection.send(json.dumps(message))
                return True
            except Exception as e:
                print(f"Failed to send WebSocket message: {e}")
                self.is_connected = False
                return False
        return False
    
    async def receive_message(self):
        """Receive message from WebSocket"""
        if self.is_connected and self.connection:
            try:
                message = await self.connection.recv()
                return json.loads(message)
            except Exception as e:
                print(f"Failed to receive WebSocket message: {e}")
                self.is_connected = False
                return None
        return None
    
    def disconnect(self):
        """Disconnect WebSocket"""
        if self.connection:
            asyncio.create_task(self.connection.close())
            self.is_connected = False

# Usage example:
# ws_manager = WebSocketManager()
# await ws_manager.connect("wss://api.htx.com/ws")
