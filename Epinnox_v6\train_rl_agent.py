#!/usr/bin/env python3
"""
Reinforcement Learning Training Pipeline for Epinnox Trading System
Trains RL agents for autonomous trading decisions
"""

import os
import sys
import argparse
import logging
import json
from datetime import datetime
from typing import Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ml.rl_agent import TradingRLAgent
from ml.trading_env import TradingEnvironment
from data.exchange import ExchangeDataFetcher
from monitoring.performance_tracker import PerformanceTracker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RLTrainingPipeline:
    """Complete RL training pipeline for trading agents"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.data_fetcher = None
        self.environment = None
        self.agent = None
        self.performance_tracker = None
        
        # Training parameters
        self.total_timesteps = config.get('total_timesteps', 100000)
        self.model_type = config.get('model_type', 'PPO')
        self.save_path = config.get('save_path', 'models/rl_trading_agent')
        self.validation_episodes = config.get('validation_episodes', 100)
        
        logger.info("RL Training Pipeline initialized")
    
    def setup_environment(self):
        """Setup the trading environment for training"""
        logger.info("Setting up trading environment...")
        
        try:
            # Initialize data fetcher
            self.data_fetcher = ExchangeDataFetcher()
            
            # Create trading environment
            env_config = self.config.get('environment', {})
            self.environment = TradingEnvironment(
                data_fetcher=self.data_fetcher,
                initial_balance=env_config.get('initial_balance', 10000.0),
                max_steps=env_config.get('max_steps', 1000)
            )
            
            logger.info("✅ Trading environment setup complete")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup environment: {e}")
            raise
    
    def setup_agent(self):
        """Setup the RL agent for training"""
        logger.info(f"Setting up {self.model_type} agent...")
        
        try:
            if self.environment is None:
                raise ValueError("Environment must be setup before agent")
            
            # Create RL agent
            self.agent = TradingRLAgent(
                env=self.environment,
                model_type=self.model_type
            )
            
            logger.info(f"✅ {self.model_type} agent setup complete")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup agent: {e}")
            raise
    
    def setup_monitoring(self):
        """Setup performance monitoring"""
        logger.info("Setting up performance monitoring...")
        
        try:
            self.performance_tracker = PerformanceTracker()
            logger.info("✅ Performance monitoring setup complete")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup monitoring: {e}")
            raise
    
    def train_agent(self):
        """Train the RL agent"""
        logger.info(f"Starting RL agent training for {self.total_timesteps} timesteps...")
        
        try:
            if self.agent is None:
                raise ValueError("Agent must be setup before training")
            
            # Start training
            training_history = self.agent.train(
                total_timesteps=self.total_timesteps,
                save_path=self.save_path
            )
            
            logger.info("✅ RL agent training completed successfully")
            return training_history
            
        except Exception as e:
            logger.error(f"❌ Training failed: {e}")
            raise
    
    def validate_agent(self):
        """Validate the trained agent"""
        logger.info(f"Validating trained agent over {self.validation_episodes} episodes...")
        
        try:
            if self.agent is None:
                raise ValueError("Agent must be trained before validation")
            
            validation_results = []
            
            for episode in range(self.validation_episodes):
                obs = self.environment.reset()
                episode_reward = 0
                done = False
                steps = 0
                
                while not done and steps < 1000:  # Max steps per episode
                    action, _ = self.agent.predict(obs, deterministic=True)
                    obs, reward, done, info = self.environment.step(action)
                    episode_reward += reward
                    steps += 1
                
                validation_results.append({
                    'episode': episode + 1,
                    'reward': episode_reward,
                    'steps': steps,
                    'final_balance': info.get('balance', 0)
                })
                
                if (episode + 1) % 10 == 0:
                    avg_reward = sum(r['reward'] for r in validation_results[-10:]) / 10
                    logger.info(f"Episode {episode + 1}/{self.validation_episodes}, "
                              f"Avg Reward (last 10): {avg_reward:.2f}")
            
            # Calculate validation metrics
            avg_reward = sum(r['reward'] for r in validation_results) / len(validation_results)
            avg_steps = sum(r['steps'] for r in validation_results) / len(validation_results)
            avg_balance = sum(r['final_balance'] for r in validation_results) / len(validation_results)
            
            validation_summary = {
                'episodes': self.validation_episodes,
                'average_reward': avg_reward,
                'average_steps': avg_steps,
                'average_final_balance': avg_balance,
                'success_rate': len([r for r in validation_results if r['reward'] > 0]) / len(validation_results)
            }
            
            logger.info("✅ Validation completed")
            logger.info(f"Average Reward: {avg_reward:.2f}")
            logger.info(f"Average Steps: {avg_steps:.1f}")
            logger.info(f"Average Final Balance: ${avg_balance:.2f}")
            logger.info(f"Success Rate: {validation_summary['success_rate']:.1%}")
            
            return validation_summary
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            raise
    
    def save_training_results(self, training_history, validation_summary):
        """Save training results and metadata"""
        logger.info("Saving training results...")
        
        try:
            results = {
                'timestamp': datetime.now().isoformat(),
                'config': self.config,
                'training_history': training_history,
                'validation_summary': validation_summary,
                'model_path': self.save_path
            }
            
            results_path = f"{self.save_path}_results.json"
            os.makedirs(os.path.dirname(results_path), exist_ok=True)
            
            with open(results_path, 'w') as f:
                json.dump(results, f, indent=2)
            
            logger.info(f"✅ Training results saved to {results_path}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save results: {e}")
    
    def run_full_pipeline(self):
        """Run the complete training pipeline"""
        logger.info("🚀 Starting RL Training Pipeline")
        logger.info("=" * 60)
        
        try:
            # Setup components
            self.setup_environment()
            self.setup_agent()
            self.setup_monitoring()
            
            # Train agent
            training_history = self.train_agent()
            
            # Validate agent
            validation_summary = self.validate_agent()
            
            # Save results
            self.save_training_results(training_history, validation_summary)
            
            logger.info("=" * 60)
            logger.info("🎉 RL Training Pipeline completed successfully!")
            
            return {
                'training_history': training_history,
                'validation_summary': validation_summary
            }
            
        except Exception as e:
            logger.error(f"❌ Pipeline failed: {e}")
            raise

def create_default_config():
    """Create default training configuration"""
    return {
        'total_timesteps': 50000,
        'model_type': 'PPO',
        'save_path': 'models/rl_trading_agent',
        'validation_episodes': 50,
        'environment': {
            'initial_balance': 10000.0,
            'max_steps': 1000
        }
    }

def main():
    """Main training script entry point"""
    parser = argparse.ArgumentParser(description='RL Trading Agent Training Pipeline')
    
    parser.add_argument('--timesteps', type=int, default=50000,
                       help='Total training timesteps (default: 50000)')
    parser.add_argument('--model-type', choices=['PPO', 'SAC'], default='PPO',
                       help='RL model type (default: PPO)')
    parser.add_argument('--save-path', default='models/rl_trading_agent',
                       help='Model save path (default: models/rl_trading_agent)')
    parser.add_argument('--validation-episodes', type=int, default=50,
                       help='Validation episodes (default: 50)')
    parser.add_argument('--config', help='JSON config file path')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Load or create configuration
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config = json.load(f)
    else:
        config = create_default_config()
        
        # Override with command line arguments
        config['total_timesteps'] = args.timesteps
        config['model_type'] = args.model_type
        config['save_path'] = args.save_path
        config['validation_episodes'] = args.validation_episodes
    
    # Create and run pipeline
    pipeline = RLTrainingPipeline(config)
    
    try:
        results = pipeline.run_full_pipeline()
        logger.info("Training pipeline completed successfully!")
        return 0
    except Exception as e:
        logger.error(f"Training pipeline failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
