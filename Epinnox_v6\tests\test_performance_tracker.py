"""
Unit tests for PerformanceTracker
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from monitoring.performance_tracker import PerformanceTracker

class TestPerformanceTracker:
    
    @pytest.fixture
    def tracker(self, temp_db_path):
        """Create performance tracker instance for testing"""
        return PerformanceTracker(db_path=temp_db_path)
    
    def test_init_database(self, tracker):
        """Test database initialization"""
        # Database should be created and tables should exist
        import sqlite3
        conn = sqlite3.connect(tracker.db_path)
        cursor = conn.cursor()
        
        # Check if tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        assert 'trades' in tables
        assert 'performance_metrics' in tables
        
        conn.close()
    
    def test_record_trade(self, tracker, sample_trade_data):
        """Test trade recording"""
        tracker.record_trade(sample_trade_data)
        
        # Verify trade was recorded
        import sqlite3
        conn = sqlite3.connect(tracker.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM trades")
        count = cursor.fetchone()[0]
        assert count == 1
        
        cursor.execute("SELECT * FROM trades")
        trade = cursor.fetchone()
        assert trade[2] == 'BTC/USDT'  # symbol
        assert trade[3] == 'LONG'      # decision
        assert trade[4] == 75.0        # confidence
        
        conn.close()
    
    def test_calculate_daily_metrics_empty(self, tracker):
        """Test daily metrics calculation with no trades"""
        metrics = tracker.calculate_daily_metrics()
        
        assert metrics['total_trades'] == 0
        assert metrics['date'] == datetime.now().date()
    
    def test_calculate_daily_metrics_with_trades(self, tracker):
        """Test daily metrics calculation with sample trades"""
        # Add some sample trades
        trades = [
            {
                'timestamp': datetime.now(),
                'symbol': 'BTC/USDT',
                'decision': 'LONG',
                'confidence': 75.0,
                'pnl_usd': 100.0,
                'pnl_pct': 2.0,
                'duration_minutes': 30.0,
                'execution_status': 'FILLED'
            },
            {
                'timestamp': datetime.now(),
                'symbol': 'ETH/USDT',
                'decision': 'SHORT',
                'confidence': 80.0,
                'pnl_usd': -50.0,
                'pnl_pct': -1.0,
                'duration_minutes': 45.0,
                'execution_status': 'FILLED'
            },
            {
                'timestamp': datetime.now(),
                'symbol': 'ADA/USDT',
                'decision': 'LONG',
                'confidence': 70.0,
                'pnl_usd': 75.0,
                'pnl_pct': 1.5,
                'duration_minutes': 25.0,
                'execution_status': 'FILLED'
            }
        ]
        
        for trade in trades:
            tracker.record_trade(trade)
        
        metrics = tracker.calculate_daily_metrics()
        
        assert metrics['total_trades'] == 3
        assert metrics['winning_trades'] == 2
        assert metrics['losing_trades'] == 1
        assert metrics['win_rate'] == 2/3
        assert metrics['total_pnl'] == 125.0
        assert abs(metrics['avg_trade_duration'] - 33.33) < 0.1
    
    def test_calculate_sharpe_ratio(self, tracker):
        """Test Sharpe ratio calculation"""
        returns = pd.Series([0.02, -0.01, 0.03, 0.01, -0.005])
        sharpe = tracker.calculate_sharpe_ratio(returns)
        
        assert isinstance(sharpe, float)
        assert sharpe > 0  # Should be positive for this sample
    
    def test_calculate_sharpe_ratio_empty(self, tracker):
        """Test Sharpe ratio with insufficient data"""
        returns = pd.Series([0.02])
        sharpe = tracker.calculate_sharpe_ratio(returns)
        
        assert sharpe == 0.0
    
    def test_calculate_max_drawdown(self, tracker):
        """Test maximum drawdown calculation"""
        pnl_series = pd.Series([100, -50, 25, -75, 50])
        max_dd = tracker.calculate_max_drawdown(pnl_series)
        
        assert max_dd < 0  # Drawdown should be negative
        assert isinstance(max_dd, float)
    
    def test_calculate_profit_factor(self, tracker):
        """Test profit factor calculation"""
        pnl_series = pd.Series([100, -50, 75, -25, 50])
        pf = tracker.calculate_profit_factor(pnl_series)
        
        assert pf > 0  # Should be positive
        assert pf == 225 / 75  # (100+75+50) / (50+25)
    
    def test_calculate_profit_factor_no_losses(self, tracker):
        """Test profit factor with no losses"""
        pnl_series = pd.Series([100, 50, 75])
        pf = tracker.calculate_profit_factor(pnl_series)
        
        assert pf == float('inf')
    
    def test_get_model_performance_empty(self, tracker):
        """Test model performance with no data"""
        performance = tracker.get_model_performance(days=30)
        
        assert performance == {}
    
    def test_get_model_performance_with_data(self, tracker):
        """Test model performance calculation"""
        # Add trades with different confidence levels
        trades = [
            {'confidence': 85.0, 'pnl_usd': 100.0, 'timestamp': datetime.now()},
            {'confidence': 65.0, 'pnl_usd': -30.0, 'timestamp': datetime.now()},
            {'confidence': 90.0, 'pnl_usd': 150.0, 'timestamp': datetime.now()},
            {'confidence': 55.0, 'pnl_usd': -20.0, 'timestamp': datetime.now()}
        ]
        
        for trade in trades:
            trade.update({
                'symbol': 'BTC/USDT',
                'decision': 'LONG',
                'execution_status': 'FILLED'
            })
            tracker.record_trade(trade)
        
        performance = tracker.get_model_performance(days=1)
        
        assert 'overall' in performance
        assert performance['overall']['total_trades'] == 4
        assert performance['overall']['win_rate'] == 0.5
        assert performance['overall']['total_pnl'] == 200.0
    
    def test_get_recent_performance_summary(self, tracker):
        """Test recent performance summary"""
        # Add a recent trade
        trade_data = {
            'timestamp': datetime.now(),
            'symbol': 'BTC/USDT',
            'decision': 'LONG',
            'confidence': 75.0,
            'pnl_usd': 100.0,
            'execution_status': 'FILLED'
        }
        tracker.record_trade(trade_data)
        
        summary = tracker.get_recent_performance_summary(hours=24)
        
        assert summary['total_trades'] == 1
        assert summary['win_rate'] == 1.0
        assert summary['total_pnl'] == 100.0
        assert summary['best_trade'] == 100.0
        assert summary['worst_trade'] == 100.0
    
    def test_get_recent_performance_summary_empty(self, tracker):
        """Test recent performance summary with no data"""
        summary = tracker.get_recent_performance_summary(hours=24)
        
        assert summary['total_trades'] == 0
        assert summary['win_rate'] == 0
        assert summary['total_pnl'] == 0
        assert summary['avg_pnl_per_trade'] == 0
    
    def test_save_daily_metrics(self, tracker):
        """Test saving daily metrics to database"""
        metrics = {
            'date': datetime.now().date(),
            'total_trades': 5,
            'winning_trades': 3,
            'losing_trades': 2,
            'win_rate': 0.6,
            'total_pnl': 150.0,
            'sharpe_ratio': 1.5,
            'max_drawdown': -50.0,
            'profit_factor': 2.0,
            'avg_trade_duration': 35.0
        }
        
        tracker.save_daily_metrics(metrics)
        
        # Verify metrics were saved
        import sqlite3
        conn = sqlite3.connect(tracker.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM performance_metrics")
        count = cursor.fetchone()[0]
        assert count == 1
        
        cursor.execute("SELECT * FROM performance_metrics")
        saved_metrics = cursor.fetchone()
        assert saved_metrics[2] == 5    # total_trades
        assert saved_metrics[5] == 0.6  # win_rate
        assert saved_metrics[6] == 150.0 # total_pnl
        
        conn.close()
