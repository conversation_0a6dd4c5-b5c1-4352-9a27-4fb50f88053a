#!/usr/bin/env python3
"""
Multi-Agent Simulation CLI for EPINNOX v6
Run parallel trading agents with different strategies
"""

import argparse
import sys
import os
import asyncio
from datetime import datetime, timedelta
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from simulation.multi_agent_simulator import MultiAgentSimulator, AgentConfig, AgentType

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def run_simulation(args):
    """Run the multi-agent simulation"""
    
    # Create simulator
    simulator = MultiAgentSimulator(initial_balance=args.initial_balance)
    
    # Add agents based on configuration
    if args.preset:
        # Use preset agent configurations
        if args.preset == 'diverse':
            simulator.create_default_agents()
        elif args.preset == 'momentum':
            # All momentum agents
            for i, symbol in enumerate(['BTC/USDT', 'ETH/USDT', 'ADA/USDT']):
                config = AgentConfig(
                    agent_id=f"momentum_{symbol.replace('/', '_').lower()}",
                    agent_type=AgentType.MOMENTUM,
                    symbols=[symbol],
                    allocation=1.0 / len(['BTC/USDT', 'ETH/USDT', 'ADA/USDT']),
                    parameters={"lookback": 20, "threshold": 0.02}
                )
                simulator.add_agent(config)
        elif args.preset == 'mixed':
            # Mix of strategies
            configs = [
                AgentConfig("momentum_btc", AgentType.MOMENTUM, ["BTC/USDT"], 0.4, {"lookback": 20}),
                AgentConfig("mean_rev_eth", AgentType.MEAN_REVERSION, ["ETH/USDT"], 0.3, {"lookback": 50}),
                AgentConfig("breakout_multi", AgentType.BREAKOUT, ["BTC/USDT", "ETH/USDT"], 0.3, {"lookback": 20})
            ]
            for config in configs:
                simulator.add_agent(config)
    else:
        # Custom agent configuration
        if args.agents:
            for agent_spec in args.agents:
                # Parse agent specification: type:symbols:allocation
                parts = agent_spec.split(':')
                if len(parts) != 3:
                    logger.error(f"Invalid agent specification: {agent_spec}")
                    continue
                
                agent_type_str, symbols_str, allocation_str = parts
                
                try:
                    agent_type = AgentType(agent_type_str)
                    symbols = symbols_str.split(',')
                    allocation = float(allocation_str)
                    
                    config = AgentConfig(
                        agent_id=f"{agent_type_str}_{len(simulator.agents)}",
                        agent_type=agent_type,
                        symbols=symbols,
                        allocation=allocation,
                        parameters={}
                    )
                    
                    simulator.add_agent(config)
                    
                except Exception as e:
                    logger.error(f"Error parsing agent {agent_spec}: {e}")
        else:
            # Default agents
            simulator.create_default_agents()
    
    # Calculate date range
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=args.days)).strftime('%Y-%m-%d')
    
    # Display simulation info
    print(f"\n{'='*60}")
    print(f"EPINNOX v6 MULTI-AGENT SIMULATION")
    print(f"{'='*60}")
    print(f"Initial Balance: ${args.initial_balance:,.2f}")
    print(f"Simulation Period: {start_date} to {end_date} ({args.days} days)")
    print(f"Number of Agents: {len(simulator.agents)}")
    print(f"Symbols: {args.symbols}")
    
    print(f"\n📊 Agent Configuration:")
    for agent in simulator.agents:
        print(f"  • {agent.config.agent_id}: {agent.config.agent_type.value}")
        print(f"    Symbols: {', '.join(agent.config.symbols)}")
        print(f"    Allocation: {agent.config.allocation:.1%}")
        print(f"    Balance: ${agent.balance:,.2f}")
    
    print(f"{'='*60}\n")
    
    try:
        # Run simulation
        logger.info("🚀 Starting multi-agent simulation...")
        results = await simulator.run_simulation(start_date, end_date, args.symbols)
        
        # Display results
        display_results(simulator, results)
        
        # Save results
        results_file = simulator.save_results()
        logger.info(f"📄 Results saved to {results_file}")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Simulation failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

def display_results(simulator, results):
    """Display simulation results"""
    
    analysis = simulator.analyze_results()
    
    print(f"\n{'='*60}")
    print(f"SIMULATION RESULTS")
    print(f"{'='*60}")
    
    # Portfolio summary
    portfolio = analysis['portfolio_summary']
    print(f"Portfolio Performance:")
    print(f"  Initial Balance: ${portfolio['initial_balance']:,.2f}")
    print(f"  Final Balance:   ${portfolio['final_balance']:,.2f}")
    print(f"  Total Return:    {portfolio['total_return']:.2%}")
    print(f"  Number of Agents: {portfolio['num_agents']}")
    
    # Agent performance
    print(f"\n📊 Agent Performance Ranking:")
    print("-" * 60)
    
    for i, agent in enumerate(analysis['agent_performance']):
        rank_emoji = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else "📈"
        print(f"{rank_emoji} {i+1}. {agent['agent_id']}")
        print(f"    Return: {agent['total_return']:.2%}")
        print(f"    Win Rate: {agent['win_rate']:.1%}")
        print(f"    Trades: {agent['total_trades']}")
        print(f"    Final Balance: ${agent['final_balance']:,.2f}")
        print()
    
    # Best vs Worst
    if analysis['best_agent'] and analysis['worst_agent']:
        print(f"🏆 Best Performer: {analysis['best_agent']['agent_id']} ({analysis['best_agent']['total_return']:.2%})")
        print(f"📉 Worst Performer: {analysis['worst_agent']['agent_id']} ({analysis['worst_agent']['total_return']:.2%})")
    
    print(f"{'='*60}\n")

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description='EPINNOX v6 Multi-Agent Simulation')
    
    # Basic parameters
    parser.add_argument('--initial-balance', type=float, default=100000.0,
                       help='Initial portfolio balance (default: 100000)')
    parser.add_argument('--days', type=int, default=30,
                       help='Number of days to simulate (default: 30)')
    parser.add_argument('--symbols', type=str, nargs='+', 
                       default=['BTC/USDT', 'ETH/USDT', 'ADA/USDT'],
                       help='Trading symbols (default: BTC/USDT ETH/USDT ADA/USDT)')
    
    # Agent configuration
    parser.add_argument('--preset', type=str, 
                       choices=['diverse', 'momentum', 'mixed'],
                       help='Use preset agent configuration')
    parser.add_argument('--agents', type=str, nargs='+',
                       help='Custom agents (format: type:symbols:allocation)')
    
    # Output options
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Quiet mode')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    elif args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate agent specifications
    if args.agents:
        valid_types = [t.value for t in AgentType]
        for agent_spec in args.agents:
            parts = agent_spec.split(':')
            if len(parts) != 3:
                print(f"❌ Invalid agent format: {agent_spec}")
                print(f"   Expected: type:symbols:allocation")
                print(f"   Example: momentum:BTC/USDT,ETH/USDT:0.5")
                return 1
            
            agent_type, symbols, allocation = parts
            if agent_type not in valid_types:
                print(f"❌ Invalid agent type: {agent_type}")
                print(f"   Valid types: {', '.join(valid_types)}")
                return 1
            
            try:
                float(allocation)
            except ValueError:
                print(f"❌ Invalid allocation: {allocation} (must be a number)")
                return 1
    
    # Run simulation
    try:
        result = asyncio.run(run_simulation(args))
        sys.exit(result)
    except KeyboardInterrupt:
        logger.info("🛑 Simulation interrupted by user")
        sys.exit(1)

if __name__ == "__main__":
    main()
