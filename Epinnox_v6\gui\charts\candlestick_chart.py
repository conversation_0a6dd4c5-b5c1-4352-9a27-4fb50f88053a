"""
Candlestick Chart Implementation for PyQtGraph
Professional candlestick rendering with Matrix theme integration
"""

import pyqtgraph as pg
import numpy as np
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor
from PyQt5.QtCore import QRectF
from typing import List

from ..matrix_theme import MatrixTheme


class CandlestickChart(pg.GraphicsObject):
    """
    Custom candlestick chart implementation for PyQtGraph
    Renders OHLC data as professional candlestick bars
    """
    
    def __init__(self):
        super().__init__()
        
        # Data storage
        self.timestamps = []
        self.opens = []
        self.highs = []
        self.lows = []
        self.closes = []
        
        # Appearance settings
        self.candle_width = 0.8  # Relative to time interval
        self.wick_width = 1
        
        # Colors (Matrix theme)
        self.bull_color = QColor(MatrixTheme.GREEN)  # Green for bullish candles
        self.bear_color = QColor(MatrixTheme.RED)    # Red for bearish candles
        self.wick_color = QColor(MatrixTheme.GREEN)
        
        # Bounding rectangle cache
        self._bounding_rect = None
        
    def set_data(self, timestamps: List[float], opens: List[float], 
                 highs: List[float], lows: List[float], closes: List[float]):
        """Set OHLC data for the candlestick chart"""
        try:
            # Validate data
            if not all(len(arr) == len(timestamps) for arr in [opens, highs, lows, closes]):
                raise ValueError("All data arrays must have the same length")
            
            self.timestamps = np.array(timestamps)
            self.opens = np.array(opens)
            self.highs = np.array(highs)
            self.lows = np.array(lows)
            self.closes = np.array(closes)
            
            # Calculate candle width based on time interval
            if len(timestamps) > 1:
                time_interval = timestamps[1] - timestamps[0]
                self.candle_width = time_interval * 0.8
            
            # Clear bounding rect cache
            self._bounding_rect = None
            
            # Trigger redraw
            self.prepareGeometryChange()
            self.update()
            
        except Exception as e:
            print(f"Error setting candlestick data: {e}")
    
    def boundingRect(self):
        """Return the bounding rectangle of the chart"""
        if self._bounding_rect is None and len(self.timestamps) > 0:
            # Calculate bounds
            x_min = float(np.min(self.timestamps))
            x_max = float(np.max(self.timestamps))
            y_min = float(np.min(self.lows))
            y_max = float(np.max(self.highs))
            
            # Add some padding
            x_padding = (x_max - x_min) * 0.05
            y_padding = (y_max - y_min) * 0.05
            
            self._bounding_rect = QRectF(
                x_min - x_padding, y_min - y_padding,
                (x_max - x_min) + 2 * x_padding,
                (y_max - y_min) + 2 * y_padding
            )
        
        return self._bounding_rect or QRectF(0, 0, 1, 1)
    
    def paint(self, painter: QPainter, option, widget):
        """Paint the candlestick chart"""
        try:
            if len(self.timestamps) == 0:
                return
            
            # Set up painter
            painter.setRenderHint(QPainter.Antialiasing, True)
            
            # Draw each candlestick
            for i in range(len(self.timestamps)):
                self._draw_candlestick(painter, i)
                
        except Exception as e:
            print(f"Error painting candlesticks: {e}")
    
    def _draw_candlestick(self, painter: QPainter, index: int):
        """Draw a single candlestick"""
        try:
            timestamp = self.timestamps[index]
            open_price = self.opens[index]
            high_price = self.highs[index]
            low_price = self.lows[index]
            close_price = self.closes[index]
            
            # Determine if bullish or bearish
            is_bullish = close_price >= open_price
            
            # Choose colors
            body_color = self.bull_color if is_bullish else self.bear_color
            wick_color = self.wick_color
            
            # Calculate positions
            half_width = self.candle_width / 2
            x_center = timestamp
            x_left = x_center - half_width
            x_right = x_center + half_width
            
            # Body coordinates
            body_top = max(open_price, close_price)
            body_bottom = min(open_price, close_price)
            
            # Draw upper wick (high to body top)
            if high_price > body_top:
                wick_pen = QPen(wick_color, self.wick_width)
                painter.setPen(wick_pen)
                painter.drawLine(
                    int(x_center), int(body_top),
                    int(x_center), int(high_price)
                )
            
            # Draw lower wick (body bottom to low)
            if low_price < body_bottom:
                wick_pen = QPen(wick_color, self.wick_width)
                painter.setPen(wick_pen)
                painter.drawLine(
                    int(x_center), int(body_bottom),
                    int(x_center), int(low_price)
                )
            
            # Draw body
            if is_bullish:
                # Bullish candle - hollow (outline only)
                body_pen = QPen(body_color, 1)
                painter.setPen(body_pen)
                painter.setBrush(QBrush())  # No fill
                
                body_rect = QRectF(
                    x_left, body_bottom,
                    self.candle_width, body_top - body_bottom
                )
                painter.drawRect(body_rect)
            else:
                # Bearish candle - filled
                body_pen = QPen(body_color, 1)
                body_brush = QBrush(body_color)
                painter.setPen(body_pen)
                painter.setBrush(body_brush)
                
                body_rect = QRectF(
                    x_left, body_bottom,
                    self.candle_width, body_top - body_bottom
                )
                painter.drawRect(body_rect)
            
            # Handle doji (open == close)
            if abs(open_price - close_price) < 1e-8:
                # Draw horizontal line for doji
                doji_pen = QPen(wick_color, 2)
                painter.setPen(doji_pen)
                painter.drawLine(
                    int(x_left), int(open_price),
                    int(x_right), int(close_price)
                )
                
        except Exception as e:
            print(f"Error drawing candlestick at index {index}: {e}")
    
    def get_candle_at_position(self, x_pos: float) -> int:
        """Get the index of the candle at the given x position"""
        try:
            if len(self.timestamps) == 0:
                return -1
            
            # Find closest timestamp
            distances = np.abs(self.timestamps - x_pos)
            closest_index = np.argmin(distances)
            
            # Check if within candle width
            if distances[closest_index] <= self.candle_width / 2:
                return int(closest_index)
            
            return -1
            
        except Exception as e:
            print(f"Error getting candle at position: {e}")
            return -1
    
    def get_ohlc_at_index(self, index: int) -> dict:
        """Get OHLC data at the given index"""
        try:
            if 0 <= index < len(self.timestamps):
                return {
                    "timestamp": self.timestamps[index],
                    "open": self.opens[index],
                    "high": self.highs[index],
                    "low": self.lows[index],
                    "close": self.closes[index]
                }
            return {}
        except Exception as e:
            print(f"Error getting OHLC at index {index}: {e}")
            return {}
    
    def set_colors(self, bull_color: str, bear_color: str, wick_color: str = None):
        """Set custom colors for the candlesticks"""
        try:
            self.bull_color = QColor(bull_color)
            self.bear_color = QColor(bear_color)
            if wick_color:
                self.wick_color = QColor(wick_color)
            
            # Trigger redraw
            self.update()
            
        except Exception as e:
            print(f"Error setting candlestick colors: {e}")
    
    def set_candle_width(self, width: float):
        """Set the relative width of candles (0.0 to 1.0)"""
        try:
            self.candle_width = max(0.1, min(1.0, width))
            
            # Recalculate actual width based on time interval
            if len(self.timestamps) > 1:
                time_interval = self.timestamps[1] - self.timestamps[0]
                self.candle_width = time_interval * self.candle_width
            
            # Trigger redraw
            self.update()
            
        except Exception as e:
            print(f"Error setting candle width: {e}")
    
    def clear(self):
        """Clear all data"""
        self.timestamps = []
        self.opens = []
        self.highs = []
        self.lows = []
        self.closes = []
        self._bounding_rect = None
        
        self.prepareGeometryChange()
        self.update()


class SimpleCandlestickItem(pg.GraphicsObject):
    """
    Simplified candlestick implementation using PyQtGraph primitives
    Fallback for when custom painting is not available
    """
    
    def __init__(self):
        super().__init__()
        self.data = []
        
    def set_data(self, timestamps, opens, highs, lows, closes):
        """Set OHLC data using simple line plots"""
        try:
            self.clear()
            
            for i, (ts, o, h, l, c) in enumerate(zip(timestamps, opens, highs, lows, closes)):
                color = MatrixTheme.GREEN if c >= o else MatrixTheme.RED
                
                # High-low line (wick)
                wick = pg.PlotDataItem([ts, ts], [l, h], pen=pg.mkPen(color, width=1))
                self.data.append(wick)
                
                # Body rectangle (simplified as thick line)
                body_width = 2
                body = pg.PlotDataItem([ts, ts], [o, c], pen=pg.mkPen(color, width=body_width))
                self.data.append(body)
                
        except Exception as e:
            print(f"Error setting simple candlestick data: {e}")
    
    def boundingRect(self):
        """Return bounding rectangle"""
        return QRectF(0, 0, 1, 1)
    
    def paint(self, painter, option, widget):
        """Paint method (not used for this implementation)"""
        pass
    
    def clear(self):
        """Clear all data"""
        for item in self.data:
            if hasattr(item, 'clear'):
                item.clear()
        self.data = []
