#!/usr/bin/env python3
"""
Parameter Search CLI for EPINNOX v6
Automated hyperparameter optimization for trading strategies
"""

import argparse
import sys
import os
import asyncio
from datetime import datetime, timedelta
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from optimization.hyperparameter_optimizer import HyperparameterOptimizer, ParameterSpace
from tests.backtesting.backtest_runner import BacktestRunner

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_base_config(args):
    """Create base configuration for optimization"""
    return {
        'initial_balance': args.initial_balance,
        'start_date': (datetime.now() - timedelta(days=args.days)).strftime('%Y-%m-%d'),
        'end_date': datetime.now().strftime('%Y-%m-%d'),
        'symbols': args.symbols,
        'timeframe': '1m',
        'max_positions': 5,
        'slippage': 0.001,
        'commission': 0.001,
        'min_confidence': 0.65
    }

def create_custom_search_space(args):
    """Create custom search space based on CLI arguments"""
    search_space = {}
    
    # Risk parameters
    if args.optimize_risk:
        search_space.update({
            'max_portfolio_risk': ParameterSpace(
                name='max_portfolio_risk',
                values=[0.10, 0.15, 0.20, 0.25, 0.30]
            ),
            'max_position_size': ParameterSpace(
                name='max_position_size', 
                values=[0.05, 0.08, 0.10, 0.12, 0.15]
            ),
            'max_leverage': ParameterSpace(
                name='max_leverage',
                values=[1.0, 2.0, 3.0, 5.0, 8.0]
            )
        })
    
    # Trading parameters
    if args.optimize_trading:
        search_space.update({
            'min_confidence': ParameterSpace(
                name='min_confidence',
                values=[0.55, 0.60, 0.65, 0.70, 0.75, 0.80]
            ),
            'slippage': ParameterSpace(
                name='slippage',
                values=[0.0005, 0.001, 0.0015, 0.002]
            ),
            'commission': ParameterSpace(
                name='commission',
                values=[0.0005, 0.001, 0.0015, 0.002]
            )
        })
    
    # Signal weights
    if args.optimize_signals:
        search_space.update({
            'rsi_weight': ParameterSpace(
                name='rsi_weight',
                values=[0.1, 0.2, 0.3, 0.4, 0.5]
            ),
            'macd_weight': ParameterSpace(
                name='macd_weight',
                values=[0.1, 0.2, 0.3, 0.4, 0.5]
            ),
            'momentum_weight': ParameterSpace(
                name='momentum_weight',
                values=[0.1, 0.2, 0.3, 0.4, 0.5]
            )
        })
    
    # RL parameters
    if args.optimize_rl:
        search_space.update({
            'rl_learning_rate': ParameterSpace(
                name='rl_learning_rate',
                values=[1e-5, 3e-5, 1e-4, 3e-4, 1e-3]
            ),
            'rl_batch_size': ParameterSpace(
                name='rl_batch_size',
                values=[32, 64, 128, 256]
            )
        })
    
    return search_space

async def run_optimization(args):
    """Run the optimization process"""
    
    # Create base configuration
    base_config = create_base_config(args)
    
    # Initialize optimizer
    optimizer = HyperparameterOptimizer(BacktestRunner, base_config)
    
    # Create search space
    if args.custom_space:
        search_space = create_custom_search_space(args)
        if not search_space:
            logger.error("No optimization categories selected. Use --optimize-* flags.")
            return 1
    else:
        search_space = optimizer.define_search_space()
    
    # Display optimization info
    print(f"\n{'='*60}")
    print(f"EPINNOX v6 HYPERPARAMETER OPTIMIZATION")
    print(f"{'='*60}")
    print(f"Optimization Method: {args.method}")
    print(f"Symbols: {', '.join(args.symbols)}")
    print(f"Date Range: {base_config['start_date']} to {base_config['end_date']}")
    print(f"Initial Balance: ${args.initial_balance:,.2f}")
    print(f"Parameters to optimize: {len(search_space)}")
    print(f"Max Combinations: {args.max_combinations}")
    if args.method == 'grid':
        print(f"Parallel Jobs: {args.parallel_jobs}")
    print(f"{'='*60}\n")
    
    try:
        # Run optimization
        if args.method == 'grid':
            results = await optimizer.run_grid_search(
                search_space=search_space,
                max_combinations=args.max_combinations,
                parallel_jobs=args.parallel_jobs
            )
        elif args.method == 'random':
            results = optimizer.run_random_search(
                search_space=search_space,
                n_iterations=args.max_combinations
            )
        else:
            logger.error(f"Unknown optimization method: {args.method}")
            return 1
        
        # Analyze and display results
        analysis = optimizer.analyze_results()
        display_results(analysis)
        
        # Save results
        results_file = optimizer.save_results()
        
        # Save best parameters separately
        if optimizer.best_parameters:
            best_params_file = f"best_parameters_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            import json
            os.makedirs('optimization_results', exist_ok=True)
            with open(os.path.join('optimization_results', best_params_file), 'w') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'optimization_score': optimizer.best_score,
                    'parameters': optimizer.best_parameters,
                    'base_config': base_config
                }, f, indent=2)
            
            logger.info(f"📋 Best parameters saved to optimization_results/{best_params_file}")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Optimization failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

def display_results(analysis):
    """Display optimization results"""
    
    print(f"\n{'='*60}")
    print(f"OPTIMIZATION RESULTS")
    print(f"{'='*60}")
    
    if not analysis:
        print("No results to display.")
        return
    
    print(f"Total Runs: {analysis['total_runs']}")
    print(f"Best Score: {analysis['best_score']:.4f}")
    print(f"Score Statistics:")
    print(f"  Mean: {analysis['score_statistics']['mean']:.4f}")
    print(f"  Std:  {analysis['score_statistics']['std']:.4f}")
    print(f"  Min:  {analysis['score_statistics']['min']:.4f}")
    print(f"  Max:  {analysis['score_statistics']['max']:.4f}")
    
    print(f"\n📊 TOP 5 PARAMETER COMBINATIONS:")
    print("-" * 60)
    
    for result in analysis['top_10_results'][:5]:
        print(f"Rank {result['rank']}: Score {result['score']:.4f}")
        print(f"  Return: {result['metrics'].get('total_return', 0):.2%}")
        print(f"  Sharpe: {result['metrics'].get('sharpe_ratio', 0):.2f}")
        print(f"  Win Rate: {result['metrics'].get('win_rate', 0):.1%}")
        print(f"  Max DD: {result['metrics'].get('max_drawdown', 0):.1%}")
        print(f"  Parameters: {result['parameters']}")
        print()
    
    if analysis.get('parameter_importance'):
        print(f"🎯 PARAMETER IMPORTANCE (correlation with score):")
        print("-" * 60)
        for param, importance in list(analysis['parameter_importance'].items())[:10]:
            print(f"  {param}: {importance:.3f}")
    
    print(f"{'='*60}\n")

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description='EPINNOX v6 Hyperparameter Optimization')
    
    # Basic parameters
    parser.add_argument('--method', type=str, choices=['grid', 'random'], default='grid',
                       help='Optimization method (default: grid)')
    parser.add_argument('--symbols', type=str, nargs='+', default=['BTC/USDT'],
                       help='Trading symbols (default: BTC/USDT)')
    parser.add_argument('--days', type=int, default=30,
                       help='Number of days for backtesting (default: 30)')
    parser.add_argument('--initial-balance', type=float, default=10000.0,
                       help='Initial balance (default: 10000)')
    
    # Optimization parameters
    parser.add_argument('--max-combinations', type=int, default=100,
                       help='Maximum parameter combinations to test (default: 100)')
    parser.add_argument('--parallel-jobs', type=int, default=4,
                       help='Number of parallel jobs for grid search (default: 4)')
    
    # Parameter categories
    parser.add_argument('--custom-space', action='store_true',
                       help='Use custom search space (requires --optimize-* flags)')
    parser.add_argument('--optimize-risk', action='store_true',
                       help='Optimize risk management parameters')
    parser.add_argument('--optimize-trading', action='store_true',
                       help='Optimize trading parameters')
    parser.add_argument('--optimize-signals', action='store_true',
                       help='Optimize signal weights')
    parser.add_argument('--optimize-rl', action='store_true',
                       help='Optimize RL parameters')
    
    # Output options
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Quiet mode')
    
    # Presets
    parser.add_argument('--preset', type=str, choices=['quick', 'thorough', 'risk-focused'],
                       help='Use optimization preset')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    elif args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Apply presets
    if args.preset == 'quick':
        args.max_combinations = 25
        args.days = 7
        args.custom_space = True
        args.optimize_trading = True
    elif args.preset == 'thorough':
        args.max_combinations = 500
        args.days = 60
        args.parallel_jobs = 8
    elif args.preset == 'risk-focused':
        args.max_combinations = 100
        args.days = 30
        args.custom_space = True
        args.optimize_risk = True
        args.optimize_trading = True
    
    # Run optimization
    try:
        result = asyncio.run(run_optimization(args))
        sys.exit(result)
    except KeyboardInterrupt:
        logger.info("🛑 Optimization interrupted by user")
        sys.exit(1)

if __name__ == "__main__":
    main()
