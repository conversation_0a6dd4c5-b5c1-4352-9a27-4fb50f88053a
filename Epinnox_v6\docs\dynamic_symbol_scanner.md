# Dynamic Symbol Scanner

## Overview

The Dynamic Symbol Scanner is an intelligent module that automatically evaluates and selects the best trading symbols for scalping based on real-time market metrics. It continuously monitors multiple symbols and ranks them according to configurable criteria optimized for high-frequency trading strategies.

## Features

### 🤖 **Automatic Symbol Selection**
- Continuously scans multiple trading pairs
- Automatically switches to the best performing symbol
- Real-time metric evaluation every 5 seconds
- Seamless integration with the main trading interface

### 📊 **Comprehensive Metrics Analysis**
- **Spread Analysis**: Evaluates bid-ask spreads for optimal entry/exit costs
- **Tick ATR**: Measures price volatility for scalping opportunities
- **Order Flow**: Analyzes buy/sell pressure and market imbalance
- **Orderbook Depth**: Assesses liquidity for smooth execution
- **24h Volume**: Considers trading volume for market activity

### ⚙️ **Configurable Scoring System**
- Weighted scoring algorithm with customizable parameters
- Dynamic normalization based on rolling market data
- Optimized default weights for scalping strategies
- Real-time score updates and ranking

### 🎯 **Performance Optimization**
- Intelligent caching to reduce API calls
- Rate limiting to prevent exchange throttling
- Efficient data structures for fast processing
- Minimal impact on system performance

## How It Works

### 1. **Metric Collection**
For each symbol, the scanner collects:
```python
# Market Data
- Current price and 24h volume
- Top 5 bid/ask levels from orderbook
- Last 50-100 recent trades

# Calculated Metrics
- Spread percentage: (ask - bid) / price * 100
- Tick ATR: Average price movement over recent trades
- Flow imbalance: Buy vs sell volume percentage
- Orderbook depth: Sum of top 5 bid/ask volumes
```

### 2. **Scoring Algorithm**
Each metric is normalized and weighted:
```python
Default Weights:
- Spread Score: 25% (lower spread = better)
- Tick ATR Score: 20% (higher volatility = more opportunities)
- Flow Score: 15% (balanced flow = stable conditions)
- Depth Score: 20% (higher depth = better execution)
- Volume Score: 20% (higher volume = more liquidity)

Final Score = Σ(normalized_metric × weight) × 100
```

### 3. **Symbol Selection**
- Ranks all symbols by composite score
- Selects the highest-scoring symbol
- Updates the trading interface automatically
- Logs symbol changes for monitoring

## GUI Integration

### **Symbol Selection Panel**
The scanner integrates seamlessly into the existing Symbol Selection panel:

```
┌─ Symbol Selection ─────────────────┐
│ Trading Symbol: [DOGE/USDT:USDT ▼] │
│ ☑ Use Live Data                    │
│ ☑ Auto Refresh (60s)               │
│ ☑ 🤖 Auto-Select Best Symbol       │
│ Scanner: BTC/USDT:USDT (87.3)      │
└────────────────────────────────────┘
```

### **Controls**
- **Checkbox**: Enable/disable automatic symbol selection
- **Status Label**: Shows current symbol and score
- **Real-time Updates**: Visual feedback on symbol changes

### **User Experience**
- **Seamless Operation**: No interruption to trading workflow
- **Visual Feedback**: Status updates and notifications
- **Manual Override**: Can disable scanner and select manually
- **Performance Monitoring**: Real-time score display

## Configuration

### **Default Symbols**
```python
DEFAULT_SYMBOLS = [
    'BTC/USDT:USDT',   # Bitcoin futures
    'ETH/USDT:USDT',   # Ethereum futures
    'DOGE/USDT:USDT',  # Dogecoin futures
    'ADA/USDT:USDT',   # Cardano futures
    'SOL/USDT:USDT',   # Solana futures
    'MATIC/USDT:USDT', # Polygon futures
    'AVAX/USDT:USDT',  # Avalanche futures
    'DOT/USDT:USDT'    # Polkadot futures
]
```

### **Metrics Weights**
```python
DEFAULT_WEIGHTS = {
    'spread_score': 0.25,    # Prioritize low spreads
    'tick_atr_score': 0.20,  # Value volatility for scalping
    'flow_score': 0.15,      # Prefer balanced markets
    'depth_score': 0.20,     # Ensure good liquidity
    'volume_score': 0.20     # High volume markets
}
```

### **Customization**
You can customize the scanner by modifying the weights:
```python
# Example: Prioritize low spreads and high volume
custom_weights = {
    'spread_score': 0.4,     # 40% weight on spreads
    'volume_score': 0.3,     # 30% weight on volume
    'depth_score': 0.2,      # 20% weight on depth
    'tick_atr_score': 0.1,   # 10% weight on volatility
    'flow_score': 0.0        # Ignore flow imbalance
}

scanner.update_weights(custom_weights)
```

## Usage

### **Enabling the Scanner**
1. Check the "🤖 Auto-Select Best Symbol" checkbox
2. Scanner starts automatically with 5-second intervals
3. Status label shows "Scanner: Active"
4. Best symbol is selected and displayed

### **Monitoring Performance**
- **Status Label**: Shows current symbol and score
- **Console Logs**: Symbol changes and scores
- **Status Bar**: Notifications when symbols change

### **Manual Control**
- Uncheck the scanner to disable automatic selection
- Manually select symbols from the dropdown
- Scanner can be re-enabled at any time

## API Reference

### **SymbolScanner Class**
```python
class SymbolScanner:
    def __init__(self, market_api, symbols, metrics_weights=None)
    def fetch_metrics(self, symbol) -> SymbolMetrics
    def score_symbol(self, metrics) -> float
    def find_best(self, n=1) -> List[str]
    def update_weights(self, new_weights)
    def get_scan_summary() -> dict
```

### **Key Methods**
- `find_best(n)`: Returns top n symbols by score
- `get_symbol_metrics(symbol)`: Get cached metrics for symbol
- `update_weights(weights)`: Update scoring weights
- `add_symbol(symbol)`: Add symbol to scan list
- `remove_symbol(symbol)`: Remove symbol from scan list

## Performance Considerations

### **Optimization Features**
- **Caching**: Results cached for 5 seconds to reduce API calls
- **Rate Limiting**: Prevents exchange API throttling
- **Efficient Processing**: Minimal CPU and memory usage
- **Background Operation**: Non-blocking GUI updates

### **Resource Usage**
- **API Calls**: ~1-2 calls per symbol per scan
- **Memory**: <10MB for typical symbol lists
- **CPU**: <1% during scanning operations
- **Network**: Minimal bandwidth usage

## Troubleshooting

### **Common Issues**

**Scanner Not Available**
```
⚠️ Symbol scanner not available (missing dependencies)
```
- Ensure `symbol_scanner.py` is in the project directory
- Check that the exchange API is properly initialized

**No Symbols Found**
```
⚠️ Scanner found no suitable symbols
```
- Verify exchange connectivity
- Check that symbols are active and tradeable
- Review symbol list configuration

**Performance Issues**
```
Scanner: Error (timeout)
```
- Reduce scan frequency in timer settings
- Check network connectivity
- Verify exchange API limits

### **Debug Information**
Enable debug logging to see detailed scanner operation:
```python
import logging
logging.getLogger('symbol_scanner').setLevel(logging.DEBUG)
```

## Integration Examples

### **Basic Integration**
```python
# Initialize scanner
scanner = SymbolScannerConfig.create_scanner(exchange_api)

# Find best symbol
best_symbols = scanner.find_best(n=1)
current_symbol = best_symbols[0] if best_symbols else None

# Update trading interface
if current_symbol:
    self.symbol_combo.setCurrentText(current_symbol)
```

### **Custom Configuration**
```python
# Custom symbols and weights
custom_symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'DOGE/USDT:USDT']
custom_weights = {'spread_score': 0.5, 'volume_score': 0.5}

scanner = SymbolScanner(
    market_api=exchange_api,
    symbols=custom_symbols,
    metrics_weights=custom_weights
)
```

### **Timer Integration**
```python
# Setup timer for automatic scanning
self.scanner_timer = QTimer()
self.scanner_timer.timeout.connect(self.on_scan_tick)
self.scanner_timer.start(5000)  # 5 second intervals

def on_scan_tick(self):
    best_symbols = self.scanner.find_best(n=1)
    if best_symbols and best_symbols[0] != self.current_symbol:
        self.update_symbol(best_symbols[0])
```

## Future Enhancements

### **Planned Features**
- **Machine Learning**: ML-based scoring optimization
- **Historical Analysis**: Backtesting scanner performance
- **Multi-Exchange**: Support for multiple exchanges
- **Advanced Metrics**: Additional technical indicators
- **User Profiles**: Saved scanner configurations

### **Performance Improvements**
- **WebSocket Integration**: Real-time data streaming
- **Parallel Processing**: Concurrent symbol analysis
- **Predictive Caching**: Anticipate data needs
- **Smart Filtering**: Pre-filter symbols by criteria

## Conclusion

The Dynamic Symbol Scanner provides intelligent, automated symbol selection for optimal scalping performance. By continuously monitoring market conditions and automatically selecting the best trading opportunities, it enhances trading efficiency while reducing manual oversight requirements.

The scanner integrates seamlessly with the existing Epinnox trading interface, providing a powerful tool for automated market analysis and symbol optimization.
