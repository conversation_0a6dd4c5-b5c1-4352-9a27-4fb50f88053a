2025-06-29 22:51:18,929 - main - INFO - Epinnox v6 starting up...
2025-06-29 22:51:18,942 - core.performance_monitor - INFO - Performance monitoring started
2025-06-29 22:51:18,943 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-29 22:51:18,943 - main - INFO - Performance monitoring initialized
2025-06-29 22:51:18,954 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-29 22:51:18,954 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-29 22:51:18,955 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-29 22:51:23,325 - __main__ - INFO -    ✅ Main Interface: Import successful
2025-06-29 22:51:23,326 - __main__ - INFO - 🧪 Testing Critical Dependencies...
2025-06-29 22:51:23,327 - __main__ - INFO -    ✅ PyQt5 GUI Framework: Available
2025-06-29 22:51:23,327 - __main__ - INFO -    ✅ NumPy: Available
2025-06-29 22:51:23,327 - __main__ - INFO -    ✅ Pandas: Available
2025-06-29 22:51:23,328 - __main__ - INFO -    ✅ HTTP Requests: Available
2025-06-29 22:51:23,339 - __main__ - INFO -    ✅ WebSocket Client: Available
2025-06-29 22:51:23,340 - __main__ - INFO -    ✅ CCXT Exchange Library: Available
2025-06-29 22:51:23,340 - __main__ - INFO - 🧪 Testing Configuration Files...
2025-06-29 22:51:23,347 - __main__ - INFO -    ✅ Autonomous Deployment Config: Valid
2025-06-29 22:51:23,439 - __main__ - ERROR -    ❌ API Credentials: Invalid - Credentials file credentials.yaml not found
2025-06-29 22:51:23,451 - __main__ - INFO -    ✅ Trading Configuration: Valid
2025-06-29 22:51:23,452 - __main__ - WARNING -    ⚠️ Risk Management Config: File not found
2025-06-29 22:51:23,452 - __main__ - INFO - 🧪 Running Interface Operations...
2025-06-29 22:51:23,695 - test_complete_interface_operations - INFO - 🚀 Starting Complete Interface Operations Testing...
2025-06-29 22:56:15,217 - main - INFO - Epinnox v6 starting up...
2025-06-29 22:56:15,230 - core.performance_monitor - INFO - Performance monitoring started
2025-06-29 22:56:15,230 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-29 22:56:15,230 - main - INFO - Performance monitoring initialized
2025-06-29 22:56:15,240 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-29 22:56:15,240 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-29 22:56:15,242 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-29 22:56:18,913 - __main__ - INFO -    ✅ Main Interface: Import successful
2025-06-29 22:56:18,914 - __main__ - INFO - 🧪 Testing Critical Dependencies...
2025-06-29 22:56:18,914 - __main__ - INFO -    ✅ PyQt5 GUI Framework: Available
2025-06-29 22:56:18,914 - __main__ - INFO -    ✅ NumPy: Available
2025-06-29 22:56:18,915 - __main__ - INFO -    ✅ Pandas: Available
2025-06-29 22:56:18,915 - __main__ - INFO -    ✅ HTTP Requests: Available
2025-06-29 22:56:18,926 - __main__ - INFO -    ✅ WebSocket Client: Available
2025-06-29 22:56:18,927 - __main__ - INFO -    ✅ CCXT Exchange Library: Available
2025-06-29 22:56:18,927 - __main__ - INFO - 🧪 Testing Configuration Files...
2025-06-29 22:56:18,934 - __main__ - INFO -    ✅ Autonomous Deployment Config: Valid
2025-06-29 22:56:18,936 - __main__ - ERROR -    ❌ API Credentials: Invalid - Credentials file credentials.yaml not found
2025-06-29 22:56:18,940 - __main__ - INFO -    ✅ Trading Configuration: Valid
2025-06-29 22:56:18,941 - __main__ - WARNING -    ⚠️ Risk Management Config: File not found
2025-06-29 22:56:18,944 - test_interface_functionality_direct - INFO - 🚀 Starting Direct Interface Functionality Testing...
2025-06-29 22:56:18,944 - test_interface_functionality_direct - INFO - 🧪 Testing Interface Button Operations...
2025-06-29 22:56:18,945 - test_interface_functionality_direct - INFO -    ✅ Menu: Save Layout: OPERATIONAL
2025-06-29 22:56:18,945 - test_interface_functionality_direct - INFO -    ✅ Menu: Load Layout: OPERATIONAL
2025-06-29 22:56:18,945 - test_interface_functionality_direct - INFO -    ✅ Menu: Reset Layout: OPERATIONAL
2025-06-29 22:56:18,946 - test_interface_functionality_direct - INFO -    ✅ Menu: Model Settings: OPERATIONAL
2025-06-29 22:56:18,946 - test_interface_functionality_direct - INFO -    ✅ Menu: Preferences: OPERATIONAL
2025-06-29 22:56:18,946 - test_interface_functionality_direct - INFO -    ✅ Menu: About: OPERATIONAL
2025-06-29 22:56:18,947 - test_interface_functionality_direct - INFO -    ✅ LLM: Toggle Orchestrator: OPERATIONAL
2025-06-29 22:56:18,947 - test_interface_functionality_direct - INFO -    ✅ LLM: Emergency Stop: OPERATIONAL
2025-06-29 22:56:18,948 - test_interface_functionality_direct - INFO -    ✅ LLM: Run Analysis Cycle: OPERATIONAL
2025-06-29 22:56:18,948 - test_interface_functionality_direct - INFO -    ✅ Trading: Place Limit Buy: OPERATIONAL
2025-06-29 22:56:18,948 - test_interface_functionality_direct - INFO -    ✅ Trading: Place Market Buy: OPERATIONAL
2025-06-29 22:56:18,949 - test_interface_functionality_direct - INFO -    ✅ Trading: Place Limit Sell: OPERATIONAL
2025-06-29 22:56:18,949 - test_interface_functionality_direct - INFO -    ✅ Trading: Place Market Sell: OPERATIONAL
2025-06-29 22:56:18,949 - test_interface_functionality_direct - INFO -    ✅ Trading: Cancel All Orders: OPERATIONAL
2025-06-29 22:56:18,949 - test_interface_functionality_direct - INFO -    ✅ Trading: Close All Positions: OPERATIONAL
2025-06-29 22:56:18,949 - test_interface_functionality_direct - INFO -    ✅ Data: Refresh Positions: OPERATIONAL
2025-06-29 22:56:18,949 - test_interface_functionality_direct - INFO -    ✅ Data: Refresh Orders: OPERATIONAL
2025-06-29 22:56:18,951 - test_interface_functionality_direct - INFO -    ✅ Data: Refresh Balance: OPERATIONAL
2025-06-29 22:56:18,951 - test_interface_functionality_direct - INFO -    ✅ Data: Refresh Market Data: OPERATIONAL
2025-06-29 22:56:18,951 - test_interface_functionality_direct - INFO -    ✅ Analysis: Analyze Symbol: OPERATIONAL
2025-06-29 22:56:18,952 - test_interface_functionality_direct - INFO -    ✅ Analysis: Stop Analysis: OPERATIONAL
2025-06-29 22:56:18,952 - test_interface_functionality_direct - INFO -    ✅ Price: Fill Bid Price: OPERATIONAL
2025-06-29 22:56:18,952 - test_interface_functionality_direct - INFO -    ✅ Price: Fill Ask Price: OPERATIONAL
2025-06-29 22:56:18,953 - test_interface_functionality_direct - INFO - 🧪 Testing Interface Input Controls...
2025-06-29 22:56:18,953 - test_interface_functionality_direct - INFO -    ✅ Input: Symbol Selection: OPERATIONAL
2025-06-29 22:56:18,953 - test_interface_functionality_direct - INFO -    ✅ Input: Order Quantity: OPERATIONAL
2025-06-29 22:56:18,954 - test_interface_functionality_direct - INFO -    ✅ Input: Order Price: OPERATIONAL
2025-06-29 22:56:18,954 - test_interface_functionality_direct - INFO -    ✅ Input: Leverage Setting: OPERATIONAL
2025-06-29 22:56:18,954 - test_interface_functionality_direct - INFO -    ✅ Input: Live Data Toggle: OPERATIONAL
2025-06-29 22:56:18,954 - test_interface_functionality_direct - INFO -    ✅ Input: Auto Refresh Toggle: OPERATIONAL
2025-06-29 22:56:18,955 - test_interface_functionality_direct - INFO -    ✅ Input: Auto Trading Enable: OPERATIONAL
2025-06-29 22:56:18,955 - test_interface_functionality_direct - INFO -    ✅ Input: Trading Symbols: OPERATIONAL
2025-06-29 22:56:18,955 - test_interface_functionality_direct - INFO -    ✅ Input: Trading Interval: OPERATIONAL
2025-06-29 22:56:18,956 - test_interface_functionality_direct - INFO - 🧪 Testing Interface Status Displays...
2025-06-29 22:56:18,957 - test_interface_functionality_direct - INFO -    ✅ Status: System Status: OPERATIONAL
2025-06-29 22:56:18,957 - test_interface_functionality_direct - INFO -    ✅ Status: Balance Display: OPERATIONAL
2025-06-29 22:56:18,957 - test_interface_functionality_direct - INFO -    ✅ Status: Mode Display: OPERATIONAL
2025-06-29 22:56:18,958 - test_interface_functionality_direct - INFO -    ✅ Status: Time Display: OPERATIONAL
2025-06-29 22:56:18,958 - test_interface_functionality_direct - INFO -    ✅ Status: Orchestrator Status: OPERATIONAL
2025-06-29 22:56:18,958 - test_interface_functionality_direct - INFO -    ✅ Status: Current Bid: OPERATIONAL
2025-06-29 22:56:18,958 - test_interface_functionality_direct - INFO -    ✅ Status: Current Ask: OPERATIONAL
2025-06-29 22:56:18,959 - test_interface_functionality_direct - INFO -    ✅ Status: Spread Display: OPERATIONAL
2025-06-29 22:56:18,959 - test_interface_functionality_direct - INFO -    ✅ Status: Portfolio Value: OPERATIONAL
2025-06-29 22:56:18,959 - test_interface_functionality_direct - INFO -    ✅ Status: Portfolio Risk: OPERATIONAL
2025-06-29 22:56:18,960 - test_interface_functionality_direct - INFO -    ✅ Status: Position PnL: OPERATIONAL
2025-06-29 22:56:18,960 - test_interface_functionality_direct - INFO - 🧪 Testing Interface Data Tables...
2025-06-29 22:56:18,960 - test_interface_functionality_direct - INFO -    ✅ Table: Positions Table: OPERATIONAL
2025-06-29 22:56:18,960 - test_interface_functionality_direct - INFO -    ✅ Table: Orders Table: OPERATIONAL
2025-06-29 22:56:18,961 - test_interface_functionality_direct - INFO -    ✅ Table: Trades Table: OPERATIONAL
2025-06-29 22:56:18,961 - test_interface_functionality_direct - INFO -    ✅ Table: Scanner Results: OPERATIONAL
2025-06-29 22:56:18,961 - test_interface_functionality_direct - INFO -    ✅ Table: Strategy Queue: OPERATIONAL
2025-06-29 22:56:18,962 - test_interface_functionality_direct - INFO -    ✅ Table: Trading Log: OPERATIONAL
2025-06-29 22:56:18,962 - test_interface_functionality_direct - INFO -    ✅ Table: System Log: OPERATIONAL
2025-06-29 22:56:18,962 - test_interface_functionality_direct - INFO -    ✅ Table: Orchestrator Log: OPERATIONAL
2025-06-29 22:56:18,963 - test_interface_functionality_direct - INFO - 🧪 Testing Interface Workflow Operations...
2025-06-29 22:56:18,963 - test_interface_functionality_direct - INFO -    ✅ Workflow: Symbol Analysis: OPERATIONAL
2025-06-29 22:56:18,963 - test_interface_functionality_direct - INFO -    ✅ Workflow: Order Placement: OPERATIONAL
2025-06-29 22:56:18,964 - test_interface_functionality_direct - INFO -    ✅ Workflow: Position Management: OPERATIONAL
2025-06-29 22:56:18,964 - test_interface_functionality_direct - INFO -    ✅ Workflow: Risk Management: OPERATIONAL
2025-06-29 22:56:18,964 - test_interface_functionality_direct - INFO -    ✅ Workflow: LLM Analysis: OPERATIONAL
2025-06-29 22:56:18,965 - test_interface_functionality_direct - INFO -    ✅ Workflow: Decision Making: OPERATIONAL
2025-06-29 22:56:18,965 - test_interface_functionality_direct - INFO -    ✅ Workflow: Trade Execution: OPERATIONAL
2025-06-29 22:56:18,965 - test_interface_functionality_direct - INFO -    ✅ Workflow: Data Refresh: OPERATIONAL
2025-06-29 22:56:18,966 - test_interface_functionality_direct - INFO -    ✅ Workflow: Status Updates: OPERATIONAL
2025-06-29 22:56:18,966 - test_interface_functionality_direct - INFO -    ✅ Workflow: Log Management: OPERATIONAL
2025-06-29 22:56:19,029 - test_gui_components_pyqt - INFO - 🚀 Starting PyQt GUI Components Testing...
2025-06-29 22:56:19,029 - test_gui_components_pyqt - INFO - 🧪 Creating Test Widgets...
2025-06-29 22:56:19,610 - test_gui_components_pyqt - INFO -    ✅ Created 14 test widgets
2025-06-29 22:56:19,611 - test_gui_components_pyqt - INFO - 🧪 Testing Button Functionality...
2025-06-29 22:56:19,612 - test_gui_components_pyqt - INFO -    ✅ Start Trading Button: PASSED
2025-06-29 22:56:19,612 - test_gui_components_pyqt - INFO -    ✅ Stop Trading Button: PASSED
2025-06-29 22:56:19,613 - test_gui_components_pyqt - INFO -    ✅ Analyze Symbol Button: PASSED
2025-06-29 22:56:19,613 - test_gui_components_pyqt - INFO -    ✅ Emergency Stop Button: PASSED
2025-06-29 22:56:19,613 - test_gui_components_pyqt - INFO - 🧪 Testing Input Controls Functionality...
2025-06-29 22:56:19,614 - test_gui_components_pyqt - INFO -    ✅ Symbol Selection Combo: PASSED
2025-06-29 22:56:19,614 - test_gui_components_pyqt - INFO -    ✅ Quantity Spinbox: PASSED
2025-06-29 22:56:19,615 - test_gui_components_pyqt - INFO -    ✅ Leverage Spinbox: PASSED
2025-06-29 22:56:19,615 - test_gui_components_pyqt - INFO -    ✅ Live Data Checkbox: PASSED
2025-06-29 22:56:19,615 - test_gui_components_pyqt - INFO -    ✅ Auto Refresh Checkbox: PASSED
2025-06-29 22:56:19,616 - test_gui_components_pyqt - INFO -    ✅ Symbol Input Field: PASSED
2025-06-29 22:56:19,616 - test_gui_components_pyqt - INFO - 🧪 Testing Label Updates Functionality...
2025-06-29 22:56:19,617 - test_gui_components_pyqt - ERROR -    ❌ Status Label: FAILED - Label status_label is not visible
2025-06-29 22:56:19,617 - test_gui_components_pyqt - ERROR -    ❌ Balance Label: FAILED - Label balance_label is not visible
2025-06-29 22:56:19,618 - test_gui_components_pyqt - ERROR -    ❌ PnL Label: FAILED - Label pnl_label is not visible
2025-06-29 22:56:19,622 - test_gui_components_pyqt - INFO - 🧪 Testing Table Functionality...
2025-06-29 22:56:19,622 - test_gui_components_pyqt - INFO -    ✅ Positions Table Functionality: PASSED
2025-06-29 22:56:19,623 - test_gui_components_pyqt - INFO - 🧪 Testing Timer Functionality...
2025-06-29 22:56:19,749 - test_gui_components_pyqt - INFO -    ✅ QTimer Functionality: PASSED
2025-06-29 22:56:19,750 - __main__ - INFO - 🧪 Running Interface Operations...
2025-06-29 22:56:19,891 - test_complete_interface_operations - INFO - 🚀 Starting Complete Interface Operations Testing...
2025-06-29 22:56:19,892 - test_complete_interface_operations - INFO - 🧪 Testing Menu Bar Operations...
2025-06-29 22:56:19,893 - test_complete_interface_operations - INFO -    ✅ Save Layout: PASSED
2025-06-29 22:56:19,893 - test_complete_interface_operations - INFO -    ✅ Load Layout: PASSED
2025-06-29 22:56:19,894 - test_complete_interface_operations - INFO -    ✅ Reset Layout: PASSED
2025-06-29 22:56:19,894 - test_complete_interface_operations - INFO -    ✅ Model Settings: PASSED
2025-06-29 22:56:19,895 - test_complete_interface_operations - INFO -    ✅ Preferences: PASSED
2025-06-29 22:56:19,895 - test_complete_interface_operations - INFO -    ✅ About: PASSED
2025-06-29 22:56:19,896 - test_complete_interface_operations - INFO - 🧪 Testing LLM Orchestrator Controls...
2025-06-29 22:56:19,896 - test_complete_interface_operations - INFO -    ✅ Toggle Orchestrator: PASSED
2025-06-29 22:56:19,897 - test_complete_interface_operations - INFO -    ✅ Emergency Stop: PASSED
2025-06-29 22:56:19,897 - test_complete_interface_operations - INFO -    ✅ Run Analysis Cycle: PASSED
2025-06-29 22:56:19,898 - test_complete_interface_operations - INFO - 🧪 Testing Trading Controls...
2025-06-29 22:56:19,898 - test_complete_interface_operations - INFO -    ✅ Limit Buy Order: PASSED
2025-06-29 22:56:19,899 - test_complete_interface_operations - INFO -    ✅ Market Buy Order: PASSED
2025-06-29 22:56:19,899 - test_complete_interface_operations - INFO -    ✅ Limit Sell Order: PASSED
2025-06-29 22:56:19,900 - test_complete_interface_operations - INFO -    ✅ Market Sell Order: PASSED
2025-06-29 22:56:19,900 - test_complete_interface_operations - INFO -    ✅ Limit Close Position: PASSED
2025-06-29 22:56:19,900 - test_complete_interface_operations - INFO -    ✅ Market Close Position: PASSED
2025-06-29 22:56:19,901 - test_complete_interface_operations - INFO -    ✅ Cancel All Orders: PASSED
2025-06-29 22:56:19,901 - test_complete_interface_operations - INFO -    ✅ Close All Positions: PASSED
2025-06-29 22:56:19,902 - test_complete_interface_operations - INFO -    ✅ Fill Bid Price: PASSED
2025-06-29 22:56:19,903 - test_complete_interface_operations - INFO -    ✅ Fill Ask Price: PASSED
2025-06-29 22:56:19,903 - test_complete_interface_operations - INFO - 🧪 Testing Data Refresh Controls...
2025-06-29 22:56:19,903 - test_complete_interface_operations - INFO -    ✅ Refresh Positions: PASSED
2025-06-29 22:56:19,904 - test_complete_interface_operations - INFO -    ✅ Refresh Orders: PASSED
2025-06-29 22:56:19,904 - test_complete_interface_operations - INFO -    ✅ Refresh Balance: PASSED
2025-06-29 22:56:19,905 - test_complete_interface_operations - INFO -    ✅ Refresh Market Data: PASSED
2025-06-29 22:56:19,905 - test_complete_interface_operations - INFO -    ✅ Refresh Portfolio: PASSED
2025-06-29 22:56:19,906 - test_complete_interface_operations - INFO - 🧪 Testing Input Controls...
2025-06-29 22:56:19,906 - test_complete_interface_operations - INFO -    ✅ Symbol Selection: PASSED
2025-06-29 22:56:19,907 - test_complete_interface_operations - ERROR -    ❌ Position Size: FAILED - 
2025-06-29 22:56:19,907 - test_complete_interface_operations - INFO -    ✅ Leverage: PASSED
2025-06-29 22:56:19,908 - test_complete_interface_operations - INFO -    ✅ Auto Refresh: PASSED
2025-06-29 22:56:19,908 - test_complete_interface_operations - INFO -    ✅ Live Data: PASSED
2025-06-29 22:56:19,908 - test_complete_interface_operations - INFO -    ✅ Symbol Input: PASSED
2025-06-29 22:56:19,909 - test_complete_interface_operations - INFO - 🧪 Testing Status Displays...
2025-06-29 22:56:19,909 - test_complete_interface_operations - INFO -    ✅ System Status: PASSED
2025-06-29 22:56:19,910 - test_complete_interface_operations - INFO -    ✅ Balance Display: PASSED
2025-06-29 22:56:19,910 - test_complete_interface_operations - INFO -    ✅ Mode Display: PASSED
2025-06-29 22:56:19,910 - test_complete_interface_operations - INFO -    ✅ Orchestrator Status: PASSED
2025-06-29 22:56:19,911 - test_complete_interface_operations - INFO -    ✅ Time Display: PASSED
2025-06-29 22:56:19,911 - test_complete_interface_operations - INFO - 🧪 Testing Button Click Responsiveness...
2025-06-29 22:56:19,911 - test_complete_interface_operations - ERROR -    ❌ Start Trading Button: FAILED - 
2025-06-29 22:56:19,912 - test_complete_interface_operations - ERROR -    ❌ Stop Trading Button: FAILED - 
2025-06-29 22:56:19,912 - test_complete_interface_operations - ERROR -    ❌ Analyze Symbol Button: FAILED - 
2025-06-29 22:56:19,912 - test_complete_interface_operations - ERROR -    ❌ Refresh Button: FAILED - 
2025-06-29 22:56:19,913 - test_complete_interface_operations - ERROR -    ❌ Emergency Stop Button: FAILED - 
2025-06-29 22:56:19,914 - test_complete_interface_operations - INFO - 🧪 Testing Timer Operations...
2025-06-29 22:56:19,914 - test_complete_interface_operations - INFO -    ✅ Auto Refresh Timer: PASSED
2025-06-29 22:56:19,915 - test_complete_interface_operations - INFO -    ✅ Orchestrator Analysis Timer: PASSED
2025-06-29 22:56:19,915 - test_complete_interface_operations - INFO -    ✅ Time Update Timer: PASSED
2025-06-29 22:56:19,915 - test_complete_interface_operations - INFO -    ✅ Balance Update Timer: PASSED
2025-06-29 22:56:19,916 - test_complete_interface_operations - INFO - 🧪 Testing Complete Interface Integration...
2025-06-29 22:56:19,916 - test_complete_interface_operations - INFO -    ✅ Initialize Interface: EXECUTED
2025-06-29 22:56:19,916 - test_complete_interface_operations - INFO -    ✅ Load Market Data: EXECUTED
2025-06-29 22:56:19,917 - test_complete_interface_operations - INFO -    ✅ Update Balance: EXECUTED
2025-06-29 22:56:19,917 - test_complete_interface_operations - INFO -    ✅ Enable Orchestrator: EXECUTED
2025-06-29 22:56:19,917 - test_complete_interface_operations - INFO -    ✅ Execute Analysis: EXECUTED
2025-06-29 22:56:19,917 - test_complete_interface_operations - INFO -    ✅ Refresh Portfolio: EXECUTED
2025-06-29 22:56:19,918 - test_complete_interface_operations - INFO -    ✅ Complete Interface Integration: PASSED
2025-06-29 22:56:19,919 - __main__ - ERROR -    ❌ Interface Operations: FAILED
2025-06-29 22:56:40,198 - main - INFO - Epinnox v6 starting up...
2025-06-29 22:56:40,210 - core.performance_monitor - INFO - Performance monitoring started
2025-06-29 22:56:40,211 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-29 22:56:40,211 - main - INFO - Performance monitoring initialized
2025-06-29 22:56:40,220 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-29 22:56:40,221 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-29 22:56:40,222 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-29 22:56:43,908 - __main__ - INFO -    ✅ Main Interface: Import successful
2025-06-29 22:56:43,909 - __main__ - INFO - 🧪 Testing Critical Dependencies...
2025-06-29 22:56:43,909 - __main__ - INFO -    ✅ PyQt5 GUI Framework: Available
2025-06-29 22:56:43,909 - __main__ - INFO -    ✅ NumPy: Available
2025-06-29 22:56:43,910 - __main__ - INFO -    ✅ Pandas: Available
2025-06-29 22:56:43,910 - __main__ - INFO -    ✅ HTTP Requests: Available
2025-06-29 22:56:43,921 - __main__ - INFO -    ✅ WebSocket Client: Available
2025-06-29 22:56:43,922 - __main__ - INFO -    ✅ CCXT Exchange Library: Available
2025-06-29 22:56:43,922 - __main__ - INFO - 🧪 Testing Configuration Files...
2025-06-29 22:56:43,928 - __main__ - INFO -    ✅ Autonomous Deployment Config: Valid
2025-06-29 22:56:43,929 - __main__ - ERROR -    ❌ API Credentials: Invalid - Credentials file credentials.yaml not found
2025-06-29 22:56:43,933 - __main__ - INFO -    ✅ Trading Configuration: Valid
2025-06-29 22:56:43,934 - __main__ - WARNING -    ⚠️ Risk Management Config: File not found
2025-06-29 22:56:43,935 - test_interface_functionality_direct - INFO - 🚀 Starting Direct Interface Functionality Testing...
2025-06-29 22:56:43,935 - test_interface_functionality_direct - INFO - 🧪 Testing Interface Button Operations...
2025-06-29 22:56:43,936 - test_interface_functionality_direct - INFO -    ✅ Menu: Save Layout: OPERATIONAL
2025-06-29 22:56:43,936 - test_interface_functionality_direct - INFO -    ✅ Menu: Load Layout: OPERATIONAL
2025-06-29 22:56:43,936 - test_interface_functionality_direct - INFO -    ✅ Menu: Reset Layout: OPERATIONAL
2025-06-29 22:56:43,936 - test_interface_functionality_direct - INFO -    ✅ Menu: Model Settings: OPERATIONAL
2025-06-29 22:56:43,937 - test_interface_functionality_direct - INFO -    ✅ Menu: Preferences: OPERATIONAL
2025-06-29 22:56:43,937 - test_interface_functionality_direct - INFO -    ✅ Menu: About: OPERATIONAL
2025-06-29 22:56:43,937 - test_interface_functionality_direct - INFO -    ✅ LLM: Toggle Orchestrator: OPERATIONAL
2025-06-29 22:56:43,938 - test_interface_functionality_direct - INFO -    ✅ LLM: Emergency Stop: OPERATIONAL
2025-06-29 22:56:43,938 - test_interface_functionality_direct - INFO -    ✅ LLM: Run Analysis Cycle: OPERATIONAL
2025-06-29 22:56:43,938 - test_interface_functionality_direct - INFO -    ✅ Trading: Place Limit Buy: OPERATIONAL
2025-06-29 22:56:43,939 - test_interface_functionality_direct - INFO -    ✅ Trading: Place Market Buy: OPERATIONAL
2025-06-29 22:56:43,939 - test_interface_functionality_direct - INFO -    ✅ Trading: Place Limit Sell: OPERATIONAL
2025-06-29 22:56:43,939 - test_interface_functionality_direct - INFO -    ✅ Trading: Place Market Sell: OPERATIONAL
2025-06-29 22:56:43,940 - test_interface_functionality_direct - INFO -    ✅ Trading: Cancel All Orders: OPERATIONAL
2025-06-29 22:56:43,940 - test_interface_functionality_direct - INFO -    ✅ Trading: Close All Positions: OPERATIONAL
2025-06-29 22:56:43,940 - test_interface_functionality_direct - INFO -    ✅ Data: Refresh Positions: OPERATIONAL
2025-06-29 22:56:43,940 - test_interface_functionality_direct - INFO -    ✅ Data: Refresh Orders: OPERATIONAL
2025-06-29 22:56:43,941 - test_interface_functionality_direct - INFO -    ✅ Data: Refresh Balance: OPERATIONAL
2025-06-29 22:56:43,941 - test_interface_functionality_direct - INFO -    ✅ Data: Refresh Market Data: OPERATIONAL
2025-06-29 22:56:43,941 - test_interface_functionality_direct - INFO -    ✅ Analysis: Analyze Symbol: OPERATIONAL
2025-06-29 22:56:43,942 - test_interface_functionality_direct - INFO -    ✅ Analysis: Stop Analysis: OPERATIONAL
2025-06-29 22:56:43,942 - test_interface_functionality_direct - INFO -    ✅ Price: Fill Bid Price: OPERATIONAL
2025-06-29 22:56:43,942 - test_interface_functionality_direct - INFO -    ✅ Price: Fill Ask Price: OPERATIONAL
2025-06-29 22:56:43,942 - test_interface_functionality_direct - INFO - 🧪 Testing Interface Input Controls...
2025-06-29 22:56:43,943 - test_interface_functionality_direct - INFO -    ✅ Input: Symbol Selection: OPERATIONAL
2025-06-29 22:56:43,943 - test_interface_functionality_direct - INFO -    ✅ Input: Order Quantity: OPERATIONAL
2025-06-29 22:56:43,943 - test_interface_functionality_direct - INFO -    ✅ Input: Order Price: OPERATIONAL
2025-06-29 22:56:43,944 - test_interface_functionality_direct - INFO -    ✅ Input: Leverage Setting: OPERATIONAL
2025-06-29 22:56:43,944 - test_interface_functionality_direct - INFO -    ✅ Input: Live Data Toggle: OPERATIONAL
2025-06-29 22:56:43,944 - test_interface_functionality_direct - INFO -    ✅ Input: Auto Refresh Toggle: OPERATIONAL
2025-06-29 22:56:43,944 - test_interface_functionality_direct - INFO -    ✅ Input: Auto Trading Enable: OPERATIONAL
2025-06-29 22:56:43,945 - test_interface_functionality_direct - INFO -    ✅ Input: Trading Symbols: OPERATIONAL
2025-06-29 22:56:43,945 - test_interface_functionality_direct - INFO -    ✅ Input: Trading Interval: OPERATIONAL
2025-06-29 22:56:43,945 - test_interface_functionality_direct - INFO - 🧪 Testing Interface Status Displays...
2025-06-29 22:56:43,946 - test_interface_functionality_direct - INFO -    ✅ Status: System Status: OPERATIONAL
2025-06-29 22:56:43,946 - test_interface_functionality_direct - INFO -    ✅ Status: Balance Display: OPERATIONAL
2025-06-29 22:56:43,946 - test_interface_functionality_direct - INFO -    ✅ Status: Mode Display: OPERATIONAL
2025-06-29 22:56:43,947 - test_interface_functionality_direct - INFO -    ✅ Status: Time Display: OPERATIONAL
2025-06-29 22:56:43,947 - test_interface_functionality_direct - INFO -    ✅ Status: Orchestrator Status: OPERATIONAL
2025-06-29 22:56:43,947 - test_interface_functionality_direct - INFO -    ✅ Status: Current Bid: OPERATIONAL
2025-06-29 22:56:43,947 - test_interface_functionality_direct - INFO -    ✅ Status: Current Ask: OPERATIONAL
2025-06-29 22:56:43,947 - test_interface_functionality_direct - INFO -    ✅ Status: Spread Display: OPERATIONAL
2025-06-29 22:56:43,949 - test_interface_functionality_direct - INFO -    ✅ Status: Portfolio Value: OPERATIONAL
2025-06-29 22:56:43,949 - test_interface_functionality_direct - INFO -    ✅ Status: Portfolio Risk: OPERATIONAL
2025-06-29 22:56:43,949 - test_interface_functionality_direct - INFO -    ✅ Status: Position PnL: OPERATIONAL
2025-06-29 22:56:43,949 - test_interface_functionality_direct - INFO - 🧪 Testing Interface Data Tables...
2025-06-29 22:56:43,949 - test_interface_functionality_direct - INFO -    ✅ Table: Positions Table: OPERATIONAL
2025-06-29 22:56:43,950 - test_interface_functionality_direct - INFO -    ✅ Table: Orders Table: OPERATIONAL
2025-06-29 22:56:43,950 - test_interface_functionality_direct - INFO -    ✅ Table: Trades Table: OPERATIONAL
2025-06-29 22:56:43,950 - test_interface_functionality_direct - INFO -    ✅ Table: Scanner Results: OPERATIONAL
2025-06-29 22:56:43,950 - test_interface_functionality_direct - INFO -    ✅ Table: Strategy Queue: OPERATIONAL
2025-06-29 22:56:43,951 - test_interface_functionality_direct - INFO -    ✅ Table: Trading Log: OPERATIONAL
2025-06-29 22:56:43,951 - test_interface_functionality_direct - INFO -    ✅ Table: System Log: OPERATIONAL
2025-06-29 22:56:43,951 - test_interface_functionality_direct - INFO -    ✅ Table: Orchestrator Log: OPERATIONAL
2025-06-29 22:56:43,952 - test_interface_functionality_direct - INFO - 🧪 Testing Interface Workflow Operations...
2025-06-29 22:56:43,952 - test_interface_functionality_direct - INFO -    ✅ Workflow: Symbol Analysis: OPERATIONAL
2025-06-29 22:56:43,952 - test_interface_functionality_direct - INFO -    ✅ Workflow: Order Placement: OPERATIONAL
2025-06-29 22:56:43,952 - test_interface_functionality_direct - INFO -    ✅ Workflow: Position Management: OPERATIONAL
2025-06-29 22:56:43,953 - test_interface_functionality_direct - INFO -    ✅ Workflow: Risk Management: OPERATIONAL
2025-06-29 22:56:43,953 - test_interface_functionality_direct - INFO -    ✅ Workflow: LLM Analysis: OPERATIONAL
2025-06-29 22:56:43,953 - test_interface_functionality_direct - INFO -    ✅ Workflow: Decision Making: OPERATIONAL
2025-06-29 22:56:43,954 - test_interface_functionality_direct - INFO -    ✅ Workflow: Trade Execution: OPERATIONAL
2025-06-29 22:56:43,954 - test_interface_functionality_direct - INFO -    ✅ Workflow: Data Refresh: OPERATIONAL
2025-06-29 22:56:43,954 - test_interface_functionality_direct - INFO -    ✅ Workflow: Status Updates: OPERATIONAL
2025-06-29 22:56:43,954 - test_interface_functionality_direct - INFO -    ✅ Workflow: Log Management: OPERATIONAL
2025-06-29 22:56:44,011 - test_gui_components_pyqt - INFO - 🚀 Starting PyQt GUI Components Testing...
2025-06-29 22:56:44,011 - test_gui_components_pyqt - INFO - 🧪 Creating Test Widgets...
2025-06-29 22:56:44,467 - test_gui_components_pyqt - INFO -    ✅ Created 14 test widgets
2025-06-29 22:56:44,467 - test_gui_components_pyqt - INFO - 🧪 Testing Button Functionality...
2025-06-29 22:56:44,469 - test_gui_components_pyqt - INFO -    ✅ Start Trading Button: PASSED
2025-06-29 22:56:44,469 - test_gui_components_pyqt - INFO -    ✅ Stop Trading Button: PASSED
2025-06-29 22:56:44,470 - test_gui_components_pyqt - INFO -    ✅ Analyze Symbol Button: PASSED
2025-06-29 22:56:44,470 - test_gui_components_pyqt - INFO -    ✅ Emergency Stop Button: PASSED
2025-06-29 22:56:44,470 - test_gui_components_pyqt - INFO - 🧪 Testing Input Controls Functionality...
2025-06-29 22:56:44,470 - test_gui_components_pyqt - INFO -    ✅ Symbol Selection Combo: PASSED
2025-06-29 22:56:44,471 - test_gui_components_pyqt - INFO -    ✅ Quantity Spinbox: PASSED
2025-06-29 22:56:44,471 - test_gui_components_pyqt - INFO -    ✅ Leverage Spinbox: PASSED
2025-06-29 22:56:44,472 - test_gui_components_pyqt - INFO -    ✅ Live Data Checkbox: PASSED
2025-06-29 22:56:44,472 - test_gui_components_pyqt - INFO -    ✅ Auto Refresh Checkbox: PASSED
2025-06-29 22:56:44,473 - test_gui_components_pyqt - INFO -    ✅ Symbol Input Field: PASSED
2025-06-29 22:56:44,473 - test_gui_components_pyqt - INFO - 🧪 Testing Label Updates Functionality...
2025-06-29 22:56:44,474 - test_gui_components_pyqt - ERROR -    ❌ Status Label: FAILED - Label status_label is not visible
2025-06-29 22:56:44,474 - test_gui_components_pyqt - ERROR -    ❌ Balance Label: FAILED - Label balance_label is not visible
2025-06-29 22:56:44,475 - test_gui_components_pyqt - ERROR -    ❌ PnL Label: FAILED - Label pnl_label is not visible
2025-06-29 22:56:44,476 - test_gui_components_pyqt - INFO - 🧪 Testing Table Functionality...
2025-06-29 22:56:44,476 - test_gui_components_pyqt - INFO -    ✅ Positions Table Functionality: PASSED
2025-06-29 22:56:44,477 - test_gui_components_pyqt - INFO - 🧪 Testing Timer Functionality...
2025-06-29 22:56:44,590 - test_gui_components_pyqt - INFO -    ✅ QTimer Functionality: PASSED
2025-06-29 22:56:44,592 - __main__ - INFO - 🧪 Running Interface Operations...
2025-06-29 22:56:44,726 - test_complete_interface_operations - INFO - 🚀 Starting Complete Interface Operations Testing...
2025-06-29 22:56:44,727 - test_complete_interface_operations - INFO - 🧪 Testing Menu Bar Operations...
2025-06-29 22:56:44,728 - test_complete_interface_operations - INFO -    ✅ Save Layout: PASSED
2025-06-29 22:56:44,728 - test_complete_interface_operations - INFO -    ✅ Load Layout: PASSED
2025-06-29 22:56:44,729 - test_complete_interface_operations - INFO -    ✅ Reset Layout: PASSED
2025-06-29 22:56:44,729 - test_complete_interface_operations - INFO -    ✅ Model Settings: PASSED
2025-06-29 22:56:44,730 - test_complete_interface_operations - INFO -    ✅ Preferences: PASSED
2025-06-29 22:56:44,730 - test_complete_interface_operations - INFO -    ✅ About: PASSED
2025-06-29 22:56:44,730 - test_complete_interface_operations - INFO - 🧪 Testing LLM Orchestrator Controls...
2025-06-29 22:56:44,731 - test_complete_interface_operations - INFO -    ✅ Toggle Orchestrator: PASSED
2025-06-29 22:56:44,732 - test_complete_interface_operations - INFO -    ✅ Emergency Stop: PASSED
2025-06-29 22:56:44,732 - test_complete_interface_operations - INFO -    ✅ Run Analysis Cycle: PASSED
2025-06-29 22:56:44,732 - test_complete_interface_operations - INFO - 🧪 Testing Trading Controls...
2025-06-29 22:56:44,733 - test_complete_interface_operations - INFO -    ✅ Limit Buy Order: PASSED
2025-06-29 22:56:44,734 - test_complete_interface_operations - INFO -    ✅ Market Buy Order: PASSED
2025-06-29 22:56:44,734 - test_complete_interface_operations - INFO -    ✅ Limit Sell Order: PASSED
2025-06-29 22:56:44,735 - test_complete_interface_operations - INFO -    ✅ Market Sell Order: PASSED
2025-06-29 22:56:44,735 - test_complete_interface_operations - INFO -    ✅ Limit Close Position: PASSED
2025-06-29 22:56:44,735 - test_complete_interface_operations - INFO -    ✅ Market Close Position: PASSED
2025-06-29 22:56:44,736 - test_complete_interface_operations - INFO -    ✅ Cancel All Orders: PASSED
2025-06-29 22:56:44,736 - test_complete_interface_operations - INFO -    ✅ Close All Positions: PASSED
2025-06-29 22:56:44,736 - test_complete_interface_operations - INFO -    ✅ Fill Bid Price: PASSED
2025-06-29 22:56:44,737 - test_complete_interface_operations - INFO -    ✅ Fill Ask Price: PASSED
2025-06-29 22:56:44,738 - test_complete_interface_operations - INFO - 🧪 Testing Data Refresh Controls...
2025-06-29 22:56:44,738 - test_complete_interface_operations - INFO -    ✅ Refresh Positions: PASSED
2025-06-29 22:56:44,738 - test_complete_interface_operations - INFO -    ✅ Refresh Orders: PASSED
2025-06-29 22:56:44,739 - test_complete_interface_operations - INFO -    ✅ Refresh Balance: PASSED
2025-06-29 22:56:44,739 - test_complete_interface_operations - INFO -    ✅ Refresh Market Data: PASSED
2025-06-29 22:56:44,739 - test_complete_interface_operations - INFO -    ✅ Refresh Portfolio: PASSED
2025-06-29 22:56:44,740 - test_complete_interface_operations - INFO - 🧪 Testing Input Controls...
2025-06-29 22:56:44,740 - test_complete_interface_operations - INFO -    ✅ Symbol Selection: PASSED
2025-06-29 22:56:44,740 - test_complete_interface_operations - ERROR -    ❌ Position Size: FAILED - 
2025-06-29 22:56:44,741 - test_complete_interface_operations - INFO -    ✅ Leverage: PASSED
2025-06-29 22:56:44,741 - test_complete_interface_operations - INFO -    ✅ Auto Refresh: PASSED
2025-06-29 22:56:44,742 - test_complete_interface_operations - INFO -    ✅ Live Data: PASSED
2025-06-29 22:56:44,742 - test_complete_interface_operations - INFO -    ✅ Symbol Input: PASSED
2025-06-29 22:56:44,742 - test_complete_interface_operations - INFO - 🧪 Testing Status Displays...
2025-06-29 22:56:44,743 - test_complete_interface_operations - INFO -    ✅ System Status: PASSED
2025-06-29 22:56:44,743 - test_complete_interface_operations - INFO -    ✅ Balance Display: PASSED
2025-06-29 22:56:44,743 - test_complete_interface_operations - INFO -    ✅ Mode Display: PASSED
2025-06-29 22:56:44,744 - test_complete_interface_operations - INFO -    ✅ Orchestrator Status: PASSED
2025-06-29 22:56:44,744 - test_complete_interface_operations - INFO -    ✅ Time Display: PASSED
2025-06-29 22:56:44,744 - test_complete_interface_operations - INFO - 🧪 Testing Button Click Responsiveness...
2025-06-29 22:56:44,745 - test_complete_interface_operations - ERROR -    ❌ Start Trading Button: FAILED - 
2025-06-29 22:56:44,745 - test_complete_interface_operations - ERROR -    ❌ Stop Trading Button: FAILED - 
2025-06-29 22:56:44,745 - test_complete_interface_operations - ERROR -    ❌ Analyze Symbol Button: FAILED - 
2025-06-29 22:56:44,746 - test_complete_interface_operations - ERROR -    ❌ Refresh Button: FAILED - 
2025-06-29 22:56:44,747 - test_complete_interface_operations - ERROR -    ❌ Emergency Stop Button: FAILED - 
2025-06-29 22:56:44,747 - test_complete_interface_operations - INFO - 🧪 Testing Timer Operations...
2025-06-29 22:56:44,747 - test_complete_interface_operations - INFO -    ✅ Auto Refresh Timer: PASSED
2025-06-29 22:56:44,748 - test_complete_interface_operations - INFO -    ✅ Orchestrator Analysis Timer: PASSED
2025-06-29 22:56:44,748 - test_complete_interface_operations - INFO -    ✅ Time Update Timer: PASSED
2025-06-29 22:56:44,748 - test_complete_interface_operations - INFO -    ✅ Balance Update Timer: PASSED
2025-06-29 22:56:44,749 - test_complete_interface_operations - INFO - 🧪 Testing Complete Interface Integration...
2025-06-29 22:56:44,749 - test_complete_interface_operations - INFO -    ✅ Initialize Interface: EXECUTED
2025-06-29 22:56:44,749 - test_complete_interface_operations - INFO -    ✅ Load Market Data: EXECUTED
2025-06-29 22:56:44,750 - test_complete_interface_operations - INFO -    ✅ Update Balance: EXECUTED
2025-06-29 22:56:44,750 - test_complete_interface_operations - INFO -    ✅ Enable Orchestrator: EXECUTED
2025-06-29 22:56:44,750 - test_complete_interface_operations - INFO -    ✅ Execute Analysis: EXECUTED
2025-06-29 22:56:44,750 - test_complete_interface_operations - INFO -    ✅ Refresh Portfolio: EXECUTED
2025-06-29 22:56:44,751 - test_complete_interface_operations - INFO -    ✅ Complete Interface Integration: PASSED
2025-06-29 22:56:44,751 - __main__ - ERROR -    ❌ Interface Operations: FAILED
