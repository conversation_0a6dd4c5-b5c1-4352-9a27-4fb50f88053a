{"timestamp": "2025-07-06T22:59:13.901921", "analysis_results": {"main_logs": {"file": "epinnox_20250706.log", "startup_count": 18, "llm_requests": 10, "trading_decisions": 4, "order_executions": 0, "autonomous_activity": 12}, "error_logs": {"file": "epinnox_errors_20250706.log", "total_errors": 2, "error_categories": {"WebSocket Errors": 2}, "recent_errors": ["2025-07-06 20:05:12,466 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x000002336A04D640>>: wrapped C/C++ object of type WebSocketClient has been deleted", "2025-07-06 20:05:12,466 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x000002336A04D640>>: wrapped C/C++ object of type WebSocketClient has been deleted"]}, "deployment": {"file": "deployment_record_autonomous_20250630_225918.json", "type": "autonomous_live", "status": "prepared", "initial_balance": 50.0, "safety_checks_passed": true, "failed_checks": []}, "system_logs": {"file": "complete_system_check_20250706_224410.log", "validation_status": "issues"}}, "critical_issues": ["Excessive restarts detected: 18 startups", "Trading decisions made but no order executions found", "WebSocket Errors: 2 occurrences", "Memory management issues detected (wrapped C/C++ object deleted)", "Autonomous trading log is empty - no autonomous activity recorded", "Deployment prepared but not executed - autonomous trading not started", "CRITICAL: Trading decisions made but no orders executed", "CRITICAL: System prepared for autonomous trading but not executing", "CRITICAL: No autonomous trading activity recorded", "WARNING: Excessive system restarts detected", "WARNING: WebSocket connection issues (2 errors)"], "warnings": [], "recommendations": ["Investigate system stability issues", "Check for memory leaks or resource exhaustion", "Review error logs for crash causes", "Check order execution system and exchange connectivity", "Verify trading permissions and API credentials", "Review risk management settings that might block orders", "Start autonomous trading execution after preparation", "Check if autonomous trading checkbox is enabled in GUI", "Verify autonomous trading loop is running", "Enable autonomous trading mode in the application", "Check autonomous trading configuration settings", "Verify LLM integration is working properly", "Check network connectivity and firewall settings", "Restart WebSocket connections", "Verify exchange API endpoints are accessible"]}