"""
Main Application for Trading System
This module connects all components and runs the trading system.
"""
import logging
import argparse
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import textwrap
from pathlib import Path
import yaml
import os
import sys
import asyncio
import concurrent.futures

# Initialize GPU environment at startup
try:
    from startup import setup_environment
    gpu_config = setup_environment()
    if gpu_config['gpu_enabled']:
        print(f"GPU acceleration enabled: {gpu_config['device_info']['device_name']}\n")
    else:
        print("GPU acceleration not available. Using CPU only.\n")
except Exception as e:
    print(f"Error setting up GPU environment: {e}\n")

from config.config import config, DEFAULT_SYMBOL, DEFAULT_DELAY
from trading.trading_system_interface import TradingSystemInterface
from trading.simulation_interface import SimulationInterface
from core.multi_timeframe import MultiTimeframeAnalyzer
from core.market_regime import MarketRegimeDetector
from core.adaptive_risk import AdaptiveRiskManager
from core.signal_scoring import SignalScorer
from core.prompt import build_prompt, parse_llm_response
from llama.runner import <PERSON>lamaRunner
from ml.models import MLModelManager
from ml.position_sizing import SmartPositionSizer
from core.signal_hierarchy import IntelligentSignalHierarchy, SignalInput
from core.leverage_manager import DynamicLeverageManager
from data.exchange import ExchangeDataFetcher
from core.features import extract_features, analyze_tick_data, combine_features

# Import exchange initialization
from me2_stable import initialize_exchange
from symbol_scanner import SymbolScannerConfig
from execution.autonomous_executor import AutonomousTradeExecutor
from ml.rl_agent import TradingRLAgent
from ml.trading_env import TradingEnvironment
from core.autonomous_controller import AutonomousController

# Add NLP imports after existing imports
try:
    from nlp.sentiment_analyzer import SentimentAnalyzer
    from nlp.news_scraper import NewsScraperManager, NewsSource
    from nlp.social_monitor import SocialMediaMonitor, SocialPlatform
    from nlp.market_sentiment import MarketSentimentAggregator
    from nlp.nlp_features import NLPFeatureExtractor
    NLP_AVAILABLE = True
    print("[OK] NLP sentiment analysis modules loaded successfully")
except ImportError as e:
    print(f"[WARN] NLP modules not available: {e}")
    print("   Install NLP requirements: pip install -r nlp/requirements_nlp.txt")
    NLP_AVAILABLE = False

# Configure logging
class WrapperFormatter(logging.Formatter):
    def __init__(self, fmt=None, datefmt=None, style='%', width=100):
        super().__init__(fmt, datefmt, style)
        self.width = width

    def format(self, record):
        # Format the message using the parent formatter
        formatted = super().format(record)

        # Wrap the message
        lines = formatted.split('\n')
        wrapped_lines = []

        for line in lines:
            # Find the position of the message part (after the timestamp, level, etc.)
            parts = line.split(' - ', 3)
            if len(parts) >= 4:
                prefix = ' - '.join(parts[:3]) + ' - '
                message = parts[3]

                # Wrap the message part
                wrapped_message = textwrap.fill(message,
                                              width=self.width,
                                              initial_indent='',
                                              subsequent_indent=' ' * len(prefix))

                # Reconstruct the line
                wrapped_lines.append(prefix + wrapped_message)
            else:
                wrapped_lines.append(line)

        return '\n'.join(wrapped_lines)

# Create formatter
formatter = WrapperFormatter(
    fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    width=100
)

# Configure logging - prevent duplicate handlers
root_logger = logging.getLogger()
root_logger.setLevel(getattr(logging, config.system.log_level))

# Clear any existing handlers to prevent duplicates
if root_logger.handlers:
    root_logger.handlers.clear()

# Add file handler
file_handler = logging.FileHandler(config.system.log_file)
file_handler.setFormatter(formatter)
root_logger.addHandler(file_handler)

# Add console handler
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)
root_logger.addHandler(console_handler)

# Get logger for this module
logger = logging.getLogger(__name__)

# Removed fetch_market_data function - we use ccxt exclusively now

def simulate_tick_data(ohlcv_df, num_ticks=100):
    """
    Simulate tick data based on OHLCV data.
    In a real system, this would be replaced with actual tick data.

    Args:
        ohlcv_df: OHLCV DataFrame
        num_ticks: Number of ticks to simulate

    Returns:
        DataFrame: Simulated tick data
    """
    if ohlcv_df is None or ohlcv_df.empty:
        return pd.DataFrame()

    # Get the last candle
    last_candle = ohlcv_df.iloc[-1]

    # Create a range of prices between the high and low
    price_range = pd.Series(
        [last_candle['low'] + (last_candle['high'] - last_candle['low']) * i / num_ticks
         for i in range(num_ticks)]
    )

    # Add some randomness to the prices
    price_range = price_range + (price_range * 0.001 * (pd.Series(np.random.randn(num_ticks))))

    # Create a DataFrame with the simulated ticks
    ticks = pd.DataFrame({
        'timestamp': [last_candle['datetime'] + timedelta(seconds=i) for i in range(num_ticks)],
        'price': price_range,
        'volume': [last_candle['volume'] / num_ticks * (1 + 0.5 * np.random.randn()) for _ in range(num_ticks)]
    })

    return ticks

def fetch_exchange_data(symbol, use_exchange=False, use_live_data=False, timeframes=None):
    """
    Fetch data from the exchange.

    Args:
        symbol: Trading symbol (e.g., 'DOGE/USDT')
        use_exchange: Whether to use the exchange data fetcher
        use_live_data: Whether to use live trades data instead of historical OHLCV
        timeframes: List of timeframes to fetch (e.g., ['1m', '5m', '15m'])

    Returns:
        tuple: (ohlcv_df, exchange_features, multi_timeframe_data, fetcher)
    """
    if not use_exchange:
        return None, {}, {}, None

    try:
        # Initialize exchange data fetcher
        fetcher = ExchangeDataFetcher(exchange_id='htx')

        # Determine spot and futures symbols
        if '/' in symbol:
            # Check if it's already a futures symbol
            if ':' in symbol:
                # It's a futures symbol, extract the spot symbol
                spot_symbol = symbol.split(':')[0]
                futures_symbol = symbol
                logger.info(f"Using futures symbol: {futures_symbol} and spot symbol: {spot_symbol}")
            else:
                # It's a spot symbol, construct the futures symbol
                spot_symbol = symbol
                base, quote = symbol.split('/')
                futures_symbol = f"{base}/{quote}:{quote}"
                logger.info(f"Using spot symbol: {spot_symbol} and futures symbol: {futures_symbol}")
        else:
            # Default to DOGE/USDT if symbol format is not recognized
            spot_symbol = 'DOGE/USDT'
            futures_symbol = 'DOGE/USDT:USDT'
            logger.warning(f"Symbol format not recognized: {symbol}. Using default: {spot_symbol}")

        # Ensure we don't have duplicate quote in futures symbol
        if futures_symbol.endswith(':USDT:USDT'):
            futures_symbol = futures_symbol.replace(':USDT:USDT', ':USDT')
            logger.info(f"Corrected futures symbol format to: {futures_symbol}")

        # Fetch combined data
        data_type = "live trades" if use_live_data else "historical OHLCV"
        logger.info(f"Fetching {data_type} data for {spot_symbol} and {futures_symbol}")

        # SMART ENHANCEMENT: Increase historical data depth for better analysis
        # Use 500 candles for more reliable indicators and pattern recognition
        data_limit = 500  # Increased from 100 for better signal quality

        if use_live_data:
            combined_data = fetcher.fetch_combined_data(
                spot_symbol,
                futures_symbol,
                timeframe='1m',
                limit=data_limit,
                ob_limit=5,
                trades_limit=100,
                include_trades=True
            )
        else:
            combined_data = fetcher.fetch_combined_data(
                spot_symbol,
                futures_symbol,
                timeframe='1m',
                limit=data_limit,
                ob_limit=5
            )

        # Extract features
        exchange_features = fetcher.extract_features_from_combined_data(combined_data)

        # Create OHLCV DataFrame from spot data
        ohlcv_df = None
        if combined_data['spot_ohlcv']:
            ohlcv_df = pd.DataFrame(combined_data['spot_ohlcv'])
            logger.info(f"Fetched {len(ohlcv_df)} rows of exchange data for {spot_symbol}")
        else:
            logger.warning(f"No OHLCV data fetched from exchange for {spot_symbol}")

        # SMART ENHANCEMENT: Fetch multi-timeframe data with optimized limits
        multi_timeframe_data = {}
        if timeframes:
            # Initialize multi-timeframe analyzer
            mt_analyzer = MultiTimeframeAnalyzer(timeframes=timeframes)

            # Use different limits for different timeframes for optimal analysis
            timeframe_limits = {
                '1m': 500,   # 8+ hours of 1-minute data for short-term patterns
                '5m': 300,   # 25+ hours of 5-minute data for intraday trends
                '15m': 200,  # 50+ hours of 15-minute data for swing analysis
                '1h': 168,   # 1 week of hourly data for trend confirmation
                '4h': 84,    # 2 weeks of 4-hour data for major trends
                '1d': 30     # 1 month of daily data for long-term context
            }

            # Fetch enhanced multi-timeframe data
            multi_timeframe_data = {}
            for tf in timeframes:
                limit = timeframe_limits.get(tf, 300)  # Default to 300 if timeframe not specified
                try:
                    tf_data = fetcher.fetch_ohlcv(spot_symbol, timeframe=tf, limit=limit, market_type='spot')
                    if tf_data is not None and not tf_data.empty:
                        multi_timeframe_data[tf] = tf_data
                        logger.info(f"Enhanced fetch: {len(tf_data)} {tf} candles for improved analysis")
                    else:
                        logger.warning(f"No data received for {tf} timeframe")
                except Exception as e:
                    logger.error(f"Error fetching enhanced {tf} data: {e}")
                    # Fallback to original method
                    try:
                        fallback_data = mt_analyzer.fetch_multi_timeframe_data(fetcher, spot_symbol)
                        if tf in fallback_data:
                            multi_timeframe_data[tf] = fallback_data[tf]
                            logger.info(f"Fallback: Using original method for {tf}")
                    except Exception as fallback_error:
                        logger.error(f"Fallback also failed for {tf}: {fallback_error}")

            logger.info(f"Enhanced multi-timeframe data: {len(multi_timeframe_data)} timeframes with improved depth: {list(multi_timeframe_data.keys())}")

        return ohlcv_df, exchange_features, multi_timeframe_data, fetcher

    except Exception as e:
        logger.exception(f"Error fetching exchange data: {e}")
        return None, {}, {}, None

def calculate_ml_consensus_boost(ml_predictions: dict, signal_decision: str) -> float:
    """
    Calculate confidence boost based on ML model consensus

    Args:
        ml_predictions: Dictionary of ML model predictions
        signal_decision: Decision from signal scoring

    Returns:
        float: Confidence boost percentage (0-15%)
    """
    try:
        if not ml_predictions or not isinstance(ml_predictions, dict):
            return 0.0

        # Count models that agree with signal decision
        agreeing_models = 0
        total_models = 0
        total_confidence = 0

        for model_name, prediction in ml_predictions.items():
            if model_name == 'ensemble' or not isinstance(prediction, dict):
                continue

            model_direction = prediction.get('direction', 'WAIT')
            model_confidence = prediction.get('direction_confidence', 0)

            total_models += 1
            total_confidence += model_confidence

            if model_direction == signal_decision:
                agreeing_models += 1

        if total_models == 0:
            return 0.0

        # Calculate consensus ratio
        consensus_ratio = agreeing_models / total_models
        avg_confidence = total_confidence / total_models

        # Calculate boost based on consensus and average confidence
        # Maximum boost: 15% for 100% consensus with high confidence
        base_boost = consensus_ratio * 10  # Up to 10% for consensus
        confidence_boost = avg_confidence * 5  # Up to 5% for high confidence

        total_boost = base_boost + confidence_boost

        logger.info(f"ML consensus: {agreeing_models}/{total_models} models agree, "
                   f"avg confidence: {avg_confidence:.3f}, boost: {total_boost:.1f}%")

        return min(15.0, total_boost)  # Cap at 15%

    except Exception as e:
        logger.error(f"Error calculating ML consensus boost: {e}")
        return 0.0

def build_enhanced_prompt_with_ml(features, signal_scores=None, market_regime=None,
                                 multi_timeframe_analysis=None, ml_predictions=None,
                                 position_sizing=None, nlp_sentiment=None):
    """
    Build enhanced prompt including ML predictions, position sizing, and NLP sentiment
    """
    try:
        # Start with base prompt
        base_prompt = build_prompt(features, signal_scores=signal_scores,
                                 market_regime=market_regime,
                                 multi_timeframe_analysis=multi_timeframe_analysis)

        # Add ML predictions section
        ml_section = ""
        if ml_predictions:
            ml_section = "\n🤖 MACHINE LEARNING PREDICTIONS:\n"

            for model_name, prediction in ml_predictions.items():
                if isinstance(prediction, dict):
                    direction = prediction.get('direction', 'WAIT')
                    confidence = prediction.get('direction_confidence', 0)
                    magnitude = prediction.get('magnitude', 0)

                    if model_name == 'ensemble':
                        ml_section += f"[TARGET] ENSEMBLE PREDICTION: {direction} (confidence: {confidence:.3f}, magnitude: {magnitude:.4f})\n"
                        if 'model_votes' in prediction:
                            votes = prediction['model_votes']
                            ml_section += f"   Model votes: {votes}\n"
                    else:
                        model_emoji = {"svm": "🔬", "random_forest": "🌲", "lstm": "🧠"}.get(model_name, "🤖")
                        ml_section += f"{model_emoji} {model_name.upper()}: {direction} (conf: {confidence:.3f}, mag: {magnitude:.4f})\n"

        # Add NLP sentiment analysis section
        nlp_section = ""
        if nlp_sentiment:
            nlp_section = "\n📰 NLP SENTIMENT ANALYSIS:\n"
            
            # Overall market sentiment
            overall_sentiment = nlp_sentiment.get('overall_sentiment', 'neutral')
            sentiment_score = nlp_sentiment.get('sentiment_score', 0.0)
            confidence = nlp_sentiment.get('confidence', 0.0)
            
            nlp_section += f"[TARGET] MARKET SENTIMENT: {overall_sentiment.upper()} (score: {sentiment_score:.3f}, confidence: {confidence:.2%})\n"
            
            # News sentiment
            news_sentiment = nlp_sentiment.get('news_sentiment', 0.0)
            if abs(news_sentiment) > 0.01:
                news_emoji = "📈" if news_sentiment > 0 else "📉" if news_sentiment < 0 else "📰"
                nlp_section += f"{news_emoji} NEWS SENTIMENT: {news_sentiment:.3f}\n"
            
            # Social sentiment
            social_sentiment = nlp_sentiment.get('social_sentiment', 0.0)
            if abs(social_sentiment) > 0.01:
                social_emoji = "[UP]" if social_sentiment > 0 else "[DOWN]" if social_sentiment < 0 else "[NEUTRAL]"
                nlp_section += f"{social_emoji} SOCIAL SENTIMENT: {social_sentiment:.3f}\n"
            
            # Sentiment sources
            sources_count = nlp_sentiment.get('sources_count', {})
            if sources_count:
                source_details = []
                for source, count in sources_count.items():
                    if count > 0:
                        source_details.append(f"{source}: {count}")
                if source_details:
                    nlp_section += f"[DATA] DATA SOURCES: {', '.join(source_details)}\n"
            
            # NLP features if available
            nlp_features = nlp_sentiment.get('nlp_features')
            if nlp_features:
                nlp_section += f"[MOMENTUM] SENTIMENT MOMENTUM: {nlp_features.get('sentiment_momentum', 0):.3f}\n"
                nlp_section += f"[NEWS] NEWS VOLUME: {nlp_features.get('news_volume_score', 0):.3f}\n"
                nlp_section += f"🗣️ SOCIAL BUZZ: {nlp_features.get('social_buzz_score', 0):.3f}\n"
                nlp_section += f"😱😤 FEAR-GREED INDEX: {nlp_features.get('fear_greed_index', 0.5):.3f}\n"
                
                viral_potential = nlp_features.get('viral_potential', 0)
                if viral_potential > 0.3:
                    nlp_section += f"🔥 VIRAL POTENTIAL: {viral_potential:.3f} (HIGH)\n"
                
                controversy = nlp_features.get('controversy_score', 0)
                if controversy > 0.3:
                    nlp_section += f"⚠️ CONTROVERSY LEVEL: {controversy:.3f}\n"
            
            # Sentiment reasoning
            reasoning = nlp_sentiment.get('reasoning', '')
            if reasoning:
                nlp_section += f"💭 SENTIMENT REASONING: {reasoning[:150]}...\n"

        # Add position sizing section
        sizing_section = ""
        if position_sizing:
            sizing_section = "\n💰 SMART POSITION SIZING:\n"
            sizing_section += f"- Optimal size: {position_sizing.get('optimal_position_size', 0):.2f} units\n"
            sizing_section += f"- Position value: ${position_sizing.get('position_usd', 0):.2f}\n"
            sizing_section += f"- Liquidity score: {position_sizing.get('liquidity_score', 0):.3f}\n"
            sizing_section += f"- Market impact: {position_sizing.get('market_impact_estimate', 0):.3f}%\n"

            execution = position_sizing.get('execution_strategy', {})
            if execution.get('type') == 'split_order':
                sizing_section += f"- Execution: Split into {execution.get('splits', 1)} orders\n"

            recommendations = position_sizing.get('recommendations', [])
            if recommendations:
                sizing_section += "- Recommendations:\n"
                for rec in recommendations:
                    sizing_section += f"  {rec}\n"

        # Combine all sections
        enhanced_prompt = base_prompt + ml_section + nlp_section + sizing_section

        # Add enhanced decision guidance
        enhanced_prompt += """
[TARGET] ENHANCED DECISION FRAMEWORK:
Consider the following in your analysis:
1. ML Model Consensus: Do multiple models agree on direction?
2. Confidence Alignment: Are signal scores and ML predictions aligned?
3. NLP Sentiment Convergence: Do news and social sentiment support the technical signals?
4. Market Sentiment Context: Is the overall market sentiment favorable for this decision?
5. Position Sizing Constraints: Are there liquidity or market impact concerns?
6. Risk-Reward Optimization: Does the setup justify the position size?

SENTIMENT INTEGRATION GUIDELINES:
- Strong positive sentiment (>0.5) + bullish technicals = High confidence LONG
- Strong negative sentiment (<-0.5) + bearish technicals = High confidence SHORT
- Conflicting sentiment vs technicals = Reduce position size or WAIT
- High controversy/uncertainty = Apply extra caution
- Viral potential + positive sentiment = Potential momentum continuation

Make your decision based on the convergence of traditional indicators, ML predictions, market sentiment, and microstructure analysis.
"""

        return enhanced_prompt

    except Exception as e:
        logger.error(f"Error building enhanced prompt with NLP: {e}")
        # Fallback to basic prompt
        return build_prompt(features, signal_scores=signal_scores,
                          market_regime=market_regime,
                          multi_timeframe_analysis=multi_timeframe_analysis)

def run_trading_system(symbol, use_live_data=False):
    """
    Run the trading system for a given symbol using ccxt exchange data.

    Args:
        symbol: Trading symbol (e.g., 'DOGE/USDT')
        use_live_data: Whether to use live trades data instead of historical OHLCV

    Returns:
        tuple: (decision, explanation, parsed_response)
        parsed_response is a dictionary containing:
            - decision: LONG, SHORT, or WAIT
            - explanation: Explanation for the decision
            - confidence: Confidence level (0-100%)
            - take_profit: Take profit percentage (optional)
            - stop_loss: Stop loss percentage (optional)
            - market_regime: Detected market regime
            - multi_timeframe_analysis: Analysis across multiple timeframes
            - position_scaling: Position scaling strategy
    """
    # Define timeframes for multi-timeframe analysis
    timeframes = ['1m', '5m', '15m']

    # 1. Fetch data from the exchange (ccxt only - no yfinance fallback)
    exchange_df, exchange_features, multi_timeframe_data, exchange_fetcher = fetch_exchange_data(
        symbol, use_exchange=True, use_live_data=use_live_data, timeframes=timeframes
    )

    # 2. Use exchange data exclusively
    if exchange_df is None:
        logger.error(f"Failed to fetch exchange data for {symbol}")
        return "ERROR", "Failed to fetch exchange data", {"decision": "ERROR", "explanation": "Failed to fetch exchange data"}

    ohlcv_df = exchange_df
    logger.info(f"Processing {symbol} data: {len(ohlcv_df)} OHLCV rows, {len(exchange_features)} exchange features")

    # 3. Simulate tick data (in a real system, this would be actual tick data)
    tick_df = simulate_tick_data(ohlcv_df)

    # 4. Extract features from OHLCV data
    ohlcv_features = extract_features(ohlcv_df)

    # 5. Extract features from tick data
    tick_features = analyze_tick_data(tick_df)

    # 6. Combine features
    combined_features = combine_features(ohlcv_features, tick_features)

    # 7. Add exchange features if available
    if exchange_features:
        # Add exchange features that don't conflict with existing features
        for k, v in exchange_features.items():
            if k not in combined_features:
                combined_features[k] = v

    # 8. Perform multi-timeframe analysis
    multi_timeframe_analysis = {}
    trend_strength = 0
    trend_direction = "neutral"
    trend_alignment = 0
    volatility = 1.0

    if multi_timeframe_data and len(multi_timeframe_data) > 0:
        try:
            # Initialize multi-timeframe analyzer
            mt_analyzer = MultiTimeframeAnalyzer(timeframes=timeframes)

            # Calculate indicators for each timeframe
            indicators = mt_analyzer.calculate_indicators(multi_timeframe_data)

            # Analyze trend for each timeframe
            trend_analysis = mt_analyzer.analyze_trend(indicators)

            # Calculate overall trend strength
            trend_metrics = mt_analyzer.calculate_trend_strength(trend_analysis)

            # Store results
            multi_timeframe_analysis = {
                'trend_metrics': trend_metrics,
                'timeframe_analysis': trend_analysis
            }

            # Extract key metrics for market regime detection
            trend_strength = trend_metrics['trend_strength']
            trend_direction = trend_metrics['trend_direction']
            trend_alignment = trend_metrics['trend_alignment']
            volatility = trend_metrics['volatility']

            logger.info(f"Multi-timeframe analysis: {trend_direction} trend, strength: {trend_strength:.2f}, alignment: {trend_alignment:.2f}")
        except Exception as e:
            logger.warning(f"Error in multi-timeframe analysis: {e}. Proceeding with single timeframe.")

    # 9. Detect market regime
    market_regime = {}
    try:
        # Initialize market regime detector
        regime_detector = MarketRegimeDetector()

        # Detect market regime
        regime_result = regime_detector.detect_regime(
            ohlcv_df, trend_strength=trend_strength,
            volatility=volatility, trend_alignment=trend_alignment
        )

        # Store results
        market_regime = regime_result

        logger.info(f"Market regime: {regime_result['primary_regime']} (leverage: {regime_result['adjustments']['leverage_factor']:.2f})")
    except Exception as e:
        logger.warning(f"Error detecting market regime: {e}. Proceeding without regime detection.")

    # 10. MACHINE LEARNING PREDICTIONS
    ml_predictions = {}
    try:
        # Initialize ML model manager
        ml_manager = MLModelManager()

        # Try to load existing models
        models_loaded = ml_manager.load_models()

        # If no models exist or insufficient data, train new models
        if not models_loaded or len(ohlcv_df) >= 200:
            logger.info("Training ML models with current data...")
            training_scores = ml_manager.train_models(ohlcv_df)
            logger.info(f"ML model training scores: {training_scores}")

        # Make predictions with all models
        ml_predictions = ml_manager.predict(ohlcv_df)

        if ml_predictions:
            logger.info("ML Predictions:")
            for model_name, prediction in ml_predictions.items():
                if isinstance(prediction, dict):
                    direction = prediction.get('direction', 'WAIT')
                    confidence = prediction.get('direction_confidence', 0)
                    logger.info(f"  {model_name}: {direction} (confidence: {confidence:.3f})")

    except Exception as e:
        logger.warning(f"ML prediction error: {e}. Continuing without ML predictions.")

    # 10.5. NLP SENTIMENT ANALYSIS
    nlp_sentiment_result = None
    try:
        if NLP_AVAILABLE:
            logger.info("Running NLP sentiment analysis...")
            
            # Initialize NLP components
            sentiment_analyzer = SentimentAnalyzer(use_transformer=False)  # Start with lightweight version
            news_scraper = NewsScraperManager(
                sources=[NewsSource.COINDESK, NewsSource.COINTELEGRAPH, NewsSource.CRYPTONEWS],
                max_articles_per_source=20
            )
            social_monitor = SocialMediaMonitor(platforms=[SocialPlatform.REDDIT])
            
            # Initialize market sentiment aggregator
            sentiment_aggregator = MarketSentimentAggregator(
                sentiment_analyzer=sentiment_analyzer,
                news_scraper=news_scraper,
                social_monitor=social_monitor
            )
            
            # Scrape recent news (last 24 hours)
            logger.info("Fetching recent news articles...")
            async def fetch_news():
                async with news_scraper:
                    articles = await news_scraper.scrape_all_sources()
                    # Filter for recent articles (last 24 hours)
                    recent_articles = news_scraper.filter_articles_by_time(articles, hours=24)
                    # Filter for symbol-relevant articles
                    symbol_articles = news_scraper.filter_articles_by_symbols(
                        recent_articles, [symbol.split('/')[0] if '/' in symbol else symbol, 'BTC', 'CRYPTO']
                    )
                    return symbol_articles
            
            # Get news articles
            try:
                import asyncio
                if asyncio.get_event_loop().is_running():
                    # If already in an event loop, create a new thread
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, fetch_news())
                        news_articles = future.result(timeout=30)
                else:
                    news_articles = asyncio.run(fetch_news())
                
                logger.info(f"Fetched {len(news_articles)} relevant news articles")
            except Exception as news_error:
                logger.warning(f"Error fetching news: {news_error}. Continuing without news data.")
                news_articles = []
            
            # Fetch social media sentiment (Reddit)
            logger.info("Fetching social media sentiment...")
            social_posts = []
            try:
                async def fetch_social():
                    return await social_monitor.monitor_reddit(
                        symbols=[symbol.split('/')[0] if '/' in symbol else symbol], 
                        max_posts_per_subreddit=15
                    )
                
                if asyncio.get_event_loop().is_running():
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, fetch_social())
                        social_posts = future.result(timeout=30)
                else:
                    social_posts = asyncio.run(fetch_social())
                
                logger.info(f"Fetched {len(social_posts)} relevant social media posts")
            except Exception as social_error:
                logger.warning(f"Error fetching social media: {social_error}. Continuing without social data.")
                social_posts = []
            
            # Aggregate market sentiment
            logger.info("Aggregating market sentiment...")
            async def aggregate_sentiment():
                return await sentiment_aggregator.aggregate_market_sentiment(
                    symbol=symbol,
                    ohlcv_data=ohlcv_df,
                    news_articles=news_articles,
                    social_posts=social_posts
                )
            
            try:
                if asyncio.get_event_loop().is_running():
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, aggregate_sentiment())
                        sentiment_signal = future.result(timeout=20)
                else:
                    sentiment_signal = asyncio.run(aggregate_sentiment())
                
                # Convert sentiment signal to dict for prompt integration
                nlp_sentiment_result = {
                    'overall_sentiment': sentiment_signal.overall_sentiment.value,
                    'sentiment_score': sentiment_signal.sentiment_score,
                    'confidence': sentiment_signal.confidence,
                    'news_sentiment': sentiment_signal.news_sentiment,
                    'social_sentiment': sentiment_signal.social_sentiment,
                    'volume_sentiment': sentiment_signal.volume_sentiment,
                    'technical_sentiment': sentiment_signal.technical_sentiment,
                    'sources_count': sentiment_signal.sources_count,
                    'reasoning': sentiment_signal.reasoning,
                    'symbol': sentiment_signal.symbol
                }
                
                # Extract NLP features
                try:
                    nlp_extractor = NLPFeatureExtractor()
                    
                    # Combine all text data for feature extraction
                    all_sentiment_scores = []
                    if news_articles:
                        for article in news_articles:
                            content = f"{article.title} {article.content}"
                            sentiment = sentiment_analyzer.analyze_sentiment(content)
                            all_sentiment_scores.append(sentiment)
                    
                    if social_posts:
                        for post in social_posts:
                            sentiment = sentiment_analyzer.analyze_sentiment(post.content)
                            all_sentiment_scores.append(sentiment)
                    
                    if all_sentiment_scores:
                        nlp_features = nlp_extractor.extract_features(
                            sentiment_scores=all_sentiment_scores,
                            news_articles=news_articles,
                            social_posts=social_posts,
                            symbol=symbol
                        )
                        
                        nlp_sentiment_result['nlp_features'] = {
                            'sentiment_momentum': nlp_features.sentiment_momentum,
                            'news_volume_score': nlp_features.news_volume_score,
                            'social_buzz_score': nlp_features.social_buzz_score,
                            'fear_greed_index': nlp_features.fear_greed_index,
                            'viral_potential': nlp_features.viral_potential,
                            'controversy_score': nlp_features.controversy_score,
                            'urgency_score': nlp_features.urgency_score,
                            'confidence_score': nlp_features.confidence_score
                        }
                
                except Exception as features_error:
                    logger.warning(f"Error extracting NLP features: {features_error}")
                
                logger.info(f"NLP Sentiment Analysis complete:")
                logger.info(f"  Overall Sentiment: {sentiment_signal.overall_sentiment.value}")
                logger.info(f"  Confidence: {sentiment_signal.confidence:.2%}")
                logger.info(f"  Sentiment Score: {sentiment_signal.sentiment_score:.3f}")
                logger.info(f"  News Sentiment: {sentiment_signal.news_sentiment:.3f}")
                logger.info(f"  Social Sentiment: {sentiment_signal.social_sentiment:.3f}")
                logger.info(f"  Sources: {dict(sentiment_signal.sources_count)}")
                
            except Exception as agg_error:
                logger.warning(f"Error aggregating sentiment: {agg_error}. Using basic sentiment.")
                nlp_sentiment_result = None
                
        else:
            logger.info("NLP modules not available. Install with: pip install -r nlp/requirements_nlp.txt")
            
    except Exception as e:
        logger.warning(f"NLP sentiment analysis error: {e}. Continuing without NLP analysis.")
        nlp_sentiment_result = None

    # 11. Calculate signal scores with longer timeframe confirmation
    signal_scores = None
    try:
        # Initialize signal scorer with smoothing and longer timeframe confirmation
        scorer = SignalScorer(smoothing_periods=3, use_longer_timeframe=True)

        # Calculate scores with longer timeframe data if available
        signal_scores = scorer.calculate_scores(ohlcv_df, None)  # We're using our own multi-timeframe analysis now

        # SMART ENHANCEMENT: Enhanced decision criteria with confidence boosting
        decision_from_score, confidence_from_score, alignment = scorer.get_decision(signal_scores)

        # Calculate enhanced confidence based on multiple factors
        enhanced_confidence = confidence_from_score
        confidence_factors = []

        # Factor 1: Multi-timeframe alignment boost
        if multi_timeframe_analysis and trend_alignment > 0.6:
            alignment_boost = min(20, trend_alignment * 30)  # Up to 20% boost for strong alignment
            enhanced_confidence += alignment_boost
            confidence_factors.append(f"MTF alignment: +{alignment_boost:.1f}%")

        # Factor 2: Trend strength boost
        if trend_strength > 0.7:
            strength_boost = min(15, (trend_strength - 0.7) * 50)  # Up to 15% boost for strong trends
            enhanced_confidence += strength_boost
            confidence_factors.append(f"Trend strength: +{strength_boost:.1f}%")

        # Factor 3: Signal score magnitude boost
        total_score = abs(signal_scores.get('total_score', 0))
        if total_score > 0.05:
            score_boost = min(10, (total_score - 0.05) * 100)  # Up to 10% boost for strong signals
            enhanced_confidence += score_boost
            confidence_factors.append(f"Signal magnitude: +{score_boost:.1f}%")

        # Factor 4: Market regime consistency boost
        if market_regime and market_regime.get('primary_regime') in ['trending', 'high_volatility']:
            regime_boost = 8  # 8% boost for favorable market conditions
            enhanced_confidence += regime_boost
            confidence_factors.append(f"Market regime: +{regime_boost}%")

        # Factor 5: ML Model Consensus Boost
        if ml_predictions:
            ml_boost = calculate_ml_consensus_boost(ml_predictions, decision_from_score)
            if ml_boost > 0:
                enhanced_confidence += ml_boost
                confidence_factors.append(f"ML consensus: +{ml_boost:.1f}%")

        # Cap enhanced confidence at 95% to maintain realism
        enhanced_confidence = min(95, enhanced_confidence)

        # Update signal scores with enhanced metrics
        signal_scores['original_confidence'] = confidence_from_score
        signal_scores['enhanced_confidence'] = enhanced_confidence
        signal_scores['confidence_factors'] = confidence_factors

        # Enhance signal scores with multi-timeframe analysis
        if multi_timeframe_analysis:
            signal_scores['trend_strength'] = trend_strength
            signal_scores['trend_direction'] = trend_direction
            signal_scores['trend_alignment'] = trend_alignment
            signal_scores['multi_timeframe_confidence'] = (trend_alignment + 0.5) * 100  # Convert to percentage

        # Smart decision override based on enhanced confidence
        if enhanced_confidence >= 70 and decision_from_score == 'WAIT':
            # Override WAIT decision if we have high confidence from other factors
            if signal_scores.get('total_score', 0) > 0.02:
                decision_from_score = 'LONG'
                logger.info(f"Smart override: WAIT -> LONG due to enhanced confidence ({enhanced_confidence:.1f}%)")
            elif signal_scores.get('total_score', 0) < -0.02:
                decision_from_score = 'SHORT'
                logger.info(f"Smart override: WAIT -> SHORT due to enhanced confidence ({enhanced_confidence:.1f}%)")

        logger.info(f"Enhanced signal analysis: {decision_from_score} (confidence: {confidence_from_score:.1f}% -> {enhanced_confidence:.1f}%, total score: {signal_scores.get('total_score', 0):.3f})")
        if confidence_factors:
            logger.info(f"Confidence boosts: {', '.join(confidence_factors)}")
    except ImportError:
        logger.warning("Signal scoring module not available. Proceeding without signal scores.")
    except Exception as e:
        logger.warning(f"Error calculating signal scores: {e}. Proceeding without signal scores.")

    # 16. Build enhanced prompt for LLaMA with ML predictions and NLP sentiment
    prompt = build_enhanced_prompt_with_ml(
        combined_features,
        signal_scores=signal_scores,
        market_regime=market_regime.get('primary_regime', 'unknown'),
        multi_timeframe_analysis=multi_timeframe_analysis,
        ml_predictions=ml_predictions,
        position_sizing=leverage_position_result if 'leverage_position_result' in locals() else {},
        nlp_sentiment=nlp_sentiment_result
    )

    # 12. Run LLaMA inference
    llama = LlamaRunner()
    response = llama.run_inference(prompt)

    # 13. Parse the response with enhanced confidence handling
    parsed_response = parse_llm_response(response)
    decision = parsed_response.get('decision', 'WAIT')
    explanation = parsed_response.get('explanation', 'No explanation provided')
    confidence = parsed_response.get('confidence')
    take_profit = parsed_response.get('take_profit')
    stop_loss = parsed_response.get('stop_loss')

    # CONFIDENCE FIX: Use enhanced confidence if LLM confidence is missing
    if confidence is None and 'enhanced_confidence' in locals():
        confidence = enhanced_confidence
        logger.info(f"Using enhanced confidence: {confidence:.1f}%")
    elif confidence is None:
        confidence = confidence_from_score if 'confidence_from_score' in locals() else 50.0
        logger.info(f"Using fallback confidence: {confidence:.1f}%")

    logger.info(f"LLM decision: {decision} (confidence: {confidence:.1f}%)")

    # 13.5. INTELLIGENT SIGNAL HIERARCHY RESOLUTION
    try:
        # Initialize signal hierarchy
        signal_hierarchy = IntelligentSignalHierarchy()

        # Prepare signals from different sources
        signals = []

        # ML ensemble signal
        if ml_predictions and 'ensemble' in ml_predictions:
            ensemble = ml_predictions['ensemble']
            signals.append(SignalInput(
                source='ml_ensemble',
                decision=ensemble.get('direction', 'WAIT'),
                confidence=ensemble.get('confidence', 0.33),
                weight=0.30,  # Reduced slightly to accommodate NLP
                reasoning=f"ML ensemble prediction with {ensemble.get('individual_predictions', 0)} models"
            ))

        # Technical signals
        if signal_scores:
            tech_decision = decision_from_score if 'decision_from_score' in locals() else 'WAIT'
            tech_confidence = enhanced_confidence / 100 if 'enhanced_confidence' in locals() else 0.5
            signals.append(SignalInput(
                source='technical_signals',
                decision=tech_decision,
                confidence=tech_confidence,
                weight=0.25,
                reasoning=f"Technical analysis with score: {signal_scores.get('total_score', 0):.3f}"
            ))

        # Multi-timeframe signal
        if multi_timeframe_analysis:
            mtf_decision = 'LONG' if trend_direction == 'strong_bullish' else ('SHORT' if 'bearish' in trend_direction else 'WAIT')
            mtf_confidence = trend_strength if 'trend_strength' in locals() else 0.5
            signals.append(SignalInput(
                source='multi_timeframe',
                decision=mtf_decision,
                confidence=mtf_confidence,
                weight=0.15,  # Reduced slightly
                reasoning=f"Multi-timeframe trend: {trend_direction}, strength: {trend_strength:.2f}"
            ))

        # Market regime signal
        if market_regime:
            regime_decision = 'LONG' if market_regime.get('primary_regime') in ['strong_trend', 'trending'] else 'WAIT'
            regime_confidence = 0.7 if market_regime.get('primary_regime') == 'strong_trend' else 0.5
            signals.append(SignalInput(
                source='market_regime',
                decision=regime_decision,
                confidence=regime_confidence,
                weight=0.10,  # Reduced to accommodate NLP
                reasoning=f"Market regime: {market_regime.get('primary_regime', 'unknown')}"
            ))

        # NLP sentiment signal
        if nlp_sentiment_result:
            # Convert sentiment to trading decision
            sentiment_level = nlp_sentiment_result.get('overall_sentiment', 'neutral')
            sentiment_score = nlp_sentiment_result.get('sentiment_score', 0.0)
            sentiment_confidence = nlp_sentiment_result.get('confidence', 0.5)
            
            nlp_decision = 'WAIT'
            if sentiment_level in ['very_bullish', 'bullish'] and sentiment_score > 0.2:
                nlp_decision = 'LONG'
            elif sentiment_level in ['very_bearish', 'bearish'] and sentiment_score < -0.2:
                nlp_decision = 'SHORT'
            elif abs(sentiment_score) > 0.4:  # Strong sentiment override
                nlp_decision = 'LONG' if sentiment_score > 0 else 'SHORT'
            
            signals.append(SignalInput(
                source='nlp_sentiment',
                decision=nlp_decision,
                confidence=sentiment_confidence,
                weight=0.15,  # Significant weight for sentiment
                reasoning=f"NLP sentiment: {sentiment_level} (score: {sentiment_score:.3f}, sources: {sum(nlp_sentiment_result.get('sources_count', {}).values())})"
            ))

        # LLM signal (lowest weight, can be boosted)
        signals.append(SignalInput(
            source='llm_analysis',
            decision=decision,
            confidence=confidence / 100 if confidence else 0.5,
            weight=0.05,
            reasoning=explanation[:100] + "..." if len(explanation) > 100 else explanation
        ))

        # Resolve signals using intelligent hierarchy
        hierarchy_result = signal_hierarchy.resolve_signals(signals)

        # Update decision based on hierarchy resolution
        if hierarchy_result:
            original_decision = decision
            original_confidence = confidence

            decision = hierarchy_result['decision']
            confidence = hierarchy_result['confidence'] * 100  # Convert back to percentage
            explanation = hierarchy_result['reasoning']

            logger.info(f"Signal hierarchy resolution: {original_decision} -> {decision} "
                       f"(confidence: {original_confidence:.1f}% -> {confidence:.1f}%)")
            logger.info(f"Hierarchy reasoning: {hierarchy_result['reasoning']}")

            # Add hierarchy analysis to parsed response
            parsed_response['signal_hierarchy'] = hierarchy_result

    except Exception as e:
        logger.warning(f"Error in signal hierarchy resolution: {e}. Using original decision.")

    # 14. DYNAMIC LEVERAGE & SMART POSITION SIZING
    leverage_position_result = None
    try:
        # Initialize smart position sizer with leverage capabilities
        position_sizer = SmartPositionSizer(conservative_mode=False, base_balance=50.0)

        # Prepare market data for position sizing
        market_data_for_sizing = {
            'order_book': exchange_features.get('order_book', {}),
            'volume_24h': exchange_features.get('volume_24h', 0),
            'current_price': ohlcv_df['close'].iloc[-1] if not ohlcv_df.empty else 0,
            'recent_trades': exchange_features.get('recent_trades', [])
        }

        # Calculate current volatility for leverage adjustment
        if len(ohlcv_df) >= 20:
            returns = ohlcv_df['close'].pct_change().dropna()
            current_volatility = returns.rolling(20).std().iloc[-1] if len(returns) >= 20 else 0.02
        else:
            current_volatility = 0.02  # Default 2% volatility

        # Get final confidence and decision
        final_confidence = enhanced_confidence / 100 if 'enhanced_confidence' in locals() else 0.7
        final_decision = decision if 'decision' in locals() else 'WAIT'

        # Calculate leverage-aware position size
        leverage_position_result = position_sizer.calculate_leverage_aware_position_size(
            exchange=exchange_fetcher.exchange if exchange_fetcher else None,
            symbol=symbol,
            market_data=market_data_for_sizing,
            signal_direction=final_decision,
            confidence=final_confidence,
            market_regime=market_regime.get('primary_regime', 'uncertain'),
            volatility=current_volatility
        )

        # Log comprehensive leverage and position information
        logger.info(f"LEVERAGE ANALYSIS for {symbol}:")
        logger.info(f"  Max Available: {leverage_position_result.max_available_leverage:.1f}x")
        logger.info(f"  Recommended: {leverage_position_result.recommended_leverage:.1f}x")
        logger.info(f"  Effective: {leverage_position_result.effective_leverage:.1f}x")
        logger.info(f"  Position: {leverage_position_result.position_units:.2f} units (${leverage_position_result.position_usd:.2f})")
        logger.info(f"  Risk per trade: ${leverage_position_result.risk_per_trade_usd:.2f}")
        logger.info(f"  Stop Loss: ${leverage_position_result.stop_loss_price:.4f}")
        logger.info(f"  Take Profit: ${leverage_position_result.take_profit_price:.4f}")

        # Log leverage reasoning
        logger.info(f"Leverage reasoning: {leverage_position_result.leverage_reasoning}")

        # Log any risk warnings
        for warning in leverage_position_result.risk_warnings:
            logger.warning(f"Risk warning: {warning}")

    except Exception as e:
        logger.warning(f"Error calculating leverage-aware position sizing: {e}. Using fallback.")

    # 15. Calculate adaptive risk parameters
    try:
        # Initialize adaptive risk manager
        risk_manager = AdaptiveRiskManager()

        # Get current price
        current_price = ohlcv_df['close'].iloc[-1] if not ohlcv_df.empty else 0

        # Calculate volatility-based stops
        if decision in ['LONG', 'SHORT']:
            adaptive_stops = risk_manager.calculate_volatility_based_stops(
                ohlcv_df, current_price, decision,
                confidence=confidence/100 if confidence else 0.7
            )

            # Override take profit and stop loss if not provided by LLM
            if not take_profit:
                take_profit = adaptive_stops['take_profit_pct']
                parsed_response['take_profit'] = take_profit

            if not stop_loss:
                stop_loss = adaptive_stops['stop_loss_pct']
                parsed_response['stop_loss'] = stop_loss

            # Calculate position scaling strategy
            position_scaling = risk_manager.calculate_position_scaling(
                1.0,  # Initial position size (normalized)
                100.0,  # Available balance (placeholder)
                confidence/100 if confidence else 0.7
            )

            parsed_response['position_scaling'] = position_scaling

            # Add adaptive risk parameters to response
            parsed_response['adaptive_stops'] = adaptive_stops

            logger.info(f"Risk management: TP: {take_profit:.2f}%, SL: {stop_loss:.2f}%, Scaling: {position_scaling['scaling_stages']} stages")
    except Exception as e:
        logger.warning(f"Error calculating adaptive risk parameters: {e}. Using standard parameters.")

    # 15. Log the final decision (avoid emoji for Windows compatibility)
    logger.info(f"FINAL DECISION for {symbol}: {decision} (confidence: {confidence}%)")
    logger.info(f"Reasoning: {explanation[:100]}..." if len(explanation) > 100 else f"Reasoning: {explanation}")

    # 17. Add additional data to the parsed response
    if signal_scores:
        parsed_response['signal_scores'] = signal_scores

    parsed_response['market_regime'] = market_regime.get('primary_regime', 'unknown')
    parsed_response['multi_timeframe_analysis'] = multi_timeframe_analysis

    # Add ML predictions and position sizing to response
    if ml_predictions:
        parsed_response['ml_predictions'] = ml_predictions

        # Add ensemble prediction summary
        if 'ensemble' in ml_predictions:
            ensemble = ml_predictions['ensemble']
            parsed_response['ml_ensemble'] = {
                'direction': ensemble.get('direction', 'WAIT'),
                'confidence': ensemble.get('confidence', 0.33),
                'model_count': ensemble.get('individual_predictions', 0)
            }

    # Add NLP sentiment analysis results
    if nlp_sentiment_result:
        parsed_response['nlp_sentiment'] = {
            'overall_sentiment': nlp_sentiment_result.get('overall_sentiment', 'neutral'),
            'sentiment_score': nlp_sentiment_result.get('sentiment_score', 0.0),
            'confidence': nlp_sentiment_result.get('confidence', 0.0),
            'news_sentiment': nlp_sentiment_result.get('news_sentiment', 0.0),
            'social_sentiment': nlp_sentiment_result.get('social_sentiment', 0.0),
            'sources_count': nlp_sentiment_result.get('sources_count', {}),
            'reasoning': nlp_sentiment_result.get('reasoning', ''),
            'nlp_features': nlp_sentiment_result.get('nlp_features', {})
        }

    if 'leverage_position_result' in locals() and leverage_position_result:
        parsed_response['leverage_position_sizing'] = {
            'max_leverage': leverage_position_result.max_available_leverage,
            'recommended_leverage': leverage_position_result.recommended_leverage,
            'effective_leverage': leverage_position_result.effective_leverage,
            'position_units': leverage_position_result.position_units,
            'position_usd': leverage_position_result.position_usd,
            'risk_per_trade_usd': leverage_position_result.risk_per_trade_usd,
            'stop_loss_price': leverage_position_result.stop_loss_price,
            'take_profit_price': leverage_position_result.take_profit_price,
            'leverage_reasoning': leverage_position_result.leverage_reasoning,
            'risk_warnings': leverage_position_result.risk_warnings
        }

    return decision, explanation, parsed_response

def get_dynamic_symbol_selection(current_symbol=None, market_api=None):
    """
    Dynamically select the best trading symbol based on real-time metrics
    
    Args:
        current_symbol: Currently selected symbol (optional)
        market_api: Exchange API instance for fetching data
        
    Returns:
        str: Best symbol to trade
    """
    try:
        logger.info("[SCAN] Running dynamic symbol selection...")
        
        # Initialize symbol scanner with default configuration
        if market_api is None:
            # Use the global exchange if available
            import me2_stable
            market_api = me2_stable.exchange if me2_stable.exchange else None
            
        if market_api is None:
            logger.warning("No market API available for symbol scanning, using default symbol")
            return current_symbol or DEFAULT_SYMBOL
            
        # Create scanner with optimized configuration for scalping
        scanner = SymbolScannerConfig.create_scanner(
            market_api,
            mode='scalping',  # Optimized for high-frequency trading
            update_interval=5.0  # Update every 5 seconds
        )
        
        # Find the best symbol
        best_symbols = scanner.find_best(n=3)  # Get top 3 symbols
        
        if not best_symbols:
            logger.warning("No symbols found by scanner, using default")
            return current_symbol or DEFAULT_SYMBOL
            
        selected_symbol = best_symbols[0]
        
        # Log symbol selection details
        logger.info(f"[RESULTS] Dynamic Symbol Selection Results:")
        for i, symbol in enumerate(best_symbols[:3], 1):
            try:
                metrics = scanner.fetch_metrics(symbol)
                if metrics:
                    score = scanner.score_symbol(metrics)
                    logger.info(f"  {i}. {symbol}: Score {score:.2f} "
                              f"(Spread: {metrics.spread:.4f}, "
                              f"Volume: {metrics.volume_24h:.0f}, "
                              f"ATR: {metrics.tick_atr:.4f})")
            except Exception as e:
                logger.debug(f"Error getting metrics for {symbol}: {e}")
        
        # Only switch if the new symbol is significantly better or current symbol is None
        if current_symbol and current_symbol in best_symbols[:2]:
            # If current symbol is still in top 2, keep it to avoid excessive switching
            logger.info(f"✅ Keeping current symbol {current_symbol} (still in top performers)")
            return current_symbol
        else:
            logger.info(f"[SWITCH] Changing to new optimal symbol: {selected_symbol}")
            return selected_symbol
            
    except Exception as e:
        logger.error(f"Error in dynamic symbol selection: {e}")
        return current_symbol or DEFAULT_SYMBOL

def run_trading_system_with_dynamic_selection(use_live_data=False, enable_dynamic_selection=True):
    """
    Enhanced trading system with dynamic symbol selection
    
    Args:
        use_live_data: Whether to use live trades data
        enable_dynamic_selection: Whether to use dynamic symbol selection
        
    Returns:
        tuple: (decision, explanation, parsed_response, selected_symbol)
    """
    import me2_stable
    
    # Initialize exchange if not already done
    if me2_stable.exchange is None:
        try:
            me2_stable.exchange = initialize_exchange()
        except Exception as e:
            logger.error(f"Failed to initialize exchange: {e}")
            return "ERROR", "Exchange initialization failed", {"decision": "ERROR"}, DEFAULT_SYMBOL
    
    # Dynamic symbol selection
    selected_symbol = DEFAULT_SYMBOL
    if enable_dynamic_selection:
        try:
            selected_symbol = get_dynamic_symbol_selection(
                current_symbol=None,  # Let it select the best one
                market_api=me2_stable.exchange
            )
            logger.info(f"[TARGET] Selected symbol for trading: {selected_symbol}")
        except Exception as e:
            logger.warning(f"Dynamic selection failed, using default: {e}")
            selected_symbol = DEFAULT_SYMBOL
    
    # Run the trading system with the selected symbol
    decision, explanation, parsed_response = run_trading_system(
        selected_symbol, 
        use_live_data=use_live_data
    )
    
    # Add symbol selection info to response
    parsed_response['selected_symbol'] = selected_symbol
    parsed_response['dynamic_selection_enabled'] = enable_dynamic_selection
    
    return decision, explanation, parsed_response, selected_symbol

def run_continuous_trading_with_dynamic_selection(delay=DEFAULT_DELAY, use_live_data=False):
    """
    Run continuous trading with dynamic symbol selection
    
    Args:
        delay: Delay between trading cycles in seconds
        use_live_data: Whether to use live data
    """
    logger.info("[START] Starting continuous trading with dynamic symbol selection...")
    
    current_symbol = None
    symbol_change_count = 0
    last_symbol_change = time.time()
    
    try:
        while True:
            cycle_start = time.time()
            
            # Run trading system with dynamic selection
            decision, explanation, parsed_response, selected_symbol = run_trading_system_with_dynamic_selection(
                use_live_data=use_live_data,
                enable_dynamic_selection=True
            )
            
            # Track symbol changes
            if current_symbol != selected_symbol:
                current_symbol = selected_symbol
                symbol_change_count += 1
                last_symbol_change = time.time()
                logger.info(f"[SYMBOL] Changed to {selected_symbol} (change #{symbol_change_count})")
            
            # Enhanced output display with symbol info
            print(f"\n{'='*70}")
            print(f">>> EPINNOX v6 DYNAMIC TRADING SYSTEM")
            print(f"{'='*70}")
            print(f"Selected Symbol: {selected_symbol}")
            print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Decision: {decision}")
            print(f"Symbol Changes: {symbol_change_count}")
            
            # Show symbol selection metrics if available
            if 'dynamic_selection_enabled' in parsed_response and parsed_response['dynamic_selection_enabled']:
                print(f"[AUTO] Dynamic Selection: ACTIVE")
                
                # Try to get current symbol metrics
                try:
                    import me2_stable
                    scanner = SymbolScannerConfig.create_scanner(me2_stable.exchange, mode='scalping')
                    metrics = scanner.fetch_metrics(selected_symbol)
                    if metrics:
                        score = scanner.score_symbol(metrics)
                        print(f"Symbol Score: {score:.2f}")
                        print(f"Spread: {metrics.spread:.4f} | Volume 24h: {metrics.volume_24h:.0f}")
                        print(f"Tick ATR: {metrics.tick_atr:.4f} | Depth Score: {metrics.depth_score:.2f}")
                except Exception as e:
                    logger.debug(f"Could not fetch current symbol metrics: {e}")
            
            # Show standard trading results
            if 'signal_hierarchy' in parsed_response:
                hierarchy = parsed_response['signal_hierarchy']
                print(f"Signal Hierarchy: {hierarchy['decision']} (Risk: {hierarchy.get('risk_level', 'N/A')})")
            
            if 'ml_predictions' in parsed_response:
                ml_preds = parsed_response['ml_predictions']
                ml_count = len([p for p in ml_preds.values() if isinstance(p, dict)])
                print(f"ML Models: {ml_count} models active")
                
                if 'ensemble' in ml_preds:
                    ensemble = ml_preds['ensemble']
                    print(f"ML Ensemble: {ensemble.get('direction', 'WAIT')} (confidence: {ensemble.get('confidence', 0):.1%})")
            
            # Show NLP sentiment if available
            if 'nlp_sentiment' in parsed_response:
                nlp = parsed_response['nlp_sentiment']
                print(f"NLP Sentiment: {nlp.get('overall_sentiment', 'neutral')} (score: {nlp.get('sentiment_score', 0):.3f})")
            
            # Show leverage and position sizing
            if 'leverage_position_sizing' in parsed_response:
                lev_pos = parsed_response['leverage_position_sizing']
                print(f"Recommended Leverage: {lev_pos.get('recommended_leverage', 0):.1f}x")
                print(f"Position Size: {lev_pos.get('position_units', 0):.2f} units (${lev_pos.get('position_usd', 0):.2f})")
            
            print(f"Explanation: {explanation}")
            print(f"{'='*70}\n")
            
            # Calculate sleep time to maintain consistent cycle timing
            cycle_time = time.time() - cycle_start
            sleep_time = max(0, delay - cycle_time)
            
            if sleep_time > 0:
                time.sleep(sleep_time)
            else:
                logger.warning(f"Trading cycle took {cycle_time:.1f}s, longer than {delay}s delay")
                
    except KeyboardInterrupt:
        logger.info("[STOP] Continuous trading stopped by user")
        print(f"\n[STATS] Session Summary:")
        print(f"Total Symbol Changes: {symbol_change_count}")
        print(f"Final Symbol: {current_symbol}")
        print(f"Session Duration: {time.time() - cycle_start:.0f} seconds")

async def run_autonomous_trading_with_dynamic_selection(delay=DEFAULT_DELAY, use_live_data=False):
    """Run fully autonomous continuous trading"""
    logger.info("[START] Starting AUTONOMOUS trading with dynamic symbol selection...")

    # Initialize autonomous executor
    import me2_stable
    if me2_stable.exchange is None:
        me2_stable.exchange = initialize_exchange()

    autonomous_executor = AutonomousTradeExecutor(
        exchange=me2_stable.exchange,
        risk_manager=AdaptiveRiskManager(),
        min_confidence=0.65  # Only trade with 65%+ confidence
    )

    trade_count = 0
    successful_trades = 0

    try:
        while True:
            cycle_start = time.time()

            # Get trading decision
            decision, explanation, parsed_response, selected_symbol = run_trading_system_with_dynamic_selection(
                use_live_data=use_live_data,
                enable_dynamic_selection=True
            )

            # AUTONOMOUS EXECUTION
            execution_result = await autonomous_executor.execute_trading_decision({
                'decision': decision,
                'confidence': parsed_response.get('confidence', 0),
                'selected_symbol': selected_symbol,
                'leverage_position_sizing': parsed_response.get('leverage_position_sizing', {}),
                'explanation': explanation
            })

            # Track results
            if execution_result['status'] in ['FILLED', 'PARTIALLY_FILLED']:
                successful_trades += 1
                logger.info(f"[TRADE] Executed {execution_result['side']} {execution_result['amount']} {execution_result['symbol']} @ ${execution_result['price']:.4f}")

            trade_count += 1

            # Enhanced autonomous output
            print(f"\n{'='*70}")
            print(f">>> AUTONOMOUS TRADING SYSTEM - Cycle #{trade_count}")
            print(f"{'='*70}")
            print(f"Symbol: {selected_symbol}")
            print(f"Decision: {decision}")
            print(f"Execution: {execution_result['status']}")
            print(f"Success Rate: {successful_trades}/{trade_count} ({successful_trades/trade_count*100:.1f}%)")

            if execution_result['status'] == 'FILLED':
                print(f"[EXECUTED] {execution_result['side'].upper()} {execution_result['amount']} @ ${execution_result['price']:.4f}")
            elif execution_result['status'] == 'RISK_REJECTED':
                print(f"[BLOCKED] Risk Management: {execution_result['reason']}")
            elif execution_result['status'] == 'SKIPPED':
                print(f"[WAIT] {execution_result['reason']}")

            print(f"{'='*70}\n")

            # Adaptive delay based on market conditions
            if execution_result['status'] == 'FILLED':
                time.sleep(delay * 2)  # Wait longer after executing a trade
            else:
                time.sleep(delay)

    except KeyboardInterrupt:
        print(f"\n[AUTONOMOUS STATS] Session Summary:")
        print(f"Total Cycles: {trade_count}")
        print(f"Successful Trades: {successful_trades}")
        print(f"Success Rate: {successful_trades/trade_count*100:.1f}%" if trade_count > 0 else "0%")

async def run_rl_autonomous_trading(delay=60, training_steps=10000):
    """
    Run fully autonomous RL-based trading
    """
    logger.info("[RL] Starting RL-based autonomous trading...")

    # Initialize exchange
    import me2_stable
    if me2_stable.exchange is None:
        me2_stable.exchange = initialize_exchange()

    # Create trading environment
    from data.exchange import ExchangeDataFetcher
    data_fetcher = ExchangeDataFetcher(exchange_id='htx')
    trading_env = TradingEnvironment(data_fetcher, initial_balance=1000.0)

    # Initialize RL agent
    rl_agent = TradingRLAgent(trading_env, model_type='PPO')

    # Try to load existing model, otherwise train
    if not rl_agent.load_model():
        logger.info("[RL] No existing model found. Training new RL agent...")
        training_history = rl_agent.train(total_timesteps=training_steps)
        logger.info(f"[RL] Training completed with {len(training_history)} episodes")

    # Initialize execution system
    autonomous_executor = AutonomousTradeExecutor(
        exchange=me2_stable.exchange,
        risk_manager=AdaptiveRiskManager(),
        min_confidence=0.5  # RL agent has its own confidence mechanism
    )

    cycle_count = 0
    successful_trades = 0

    try:
        while True:
            cycle_start = time.time()

            # Get current market observation
            market_obs = trading_env._get_observation()

            # Get RL agent decision
            rl_action = rl_agent.predict(market_obs, deterministic=True)

            # Convert RL action to trading decision
            direction = int(rl_action[0])
            position_size = float(rl_action[1])
            leverage = float(rl_action[2])

            # Map to trading system format
            if direction == 0:
                decision = 'WAIT'
                confidence = 50
            elif direction == 1:
                decision = 'LONG'
                confidence = min(95, 60 + position_size * 35)  # Higher confidence for larger positions
            else:
                decision = 'SHORT'
                confidence = min(95, 60 + position_size * 35)

            # Get symbol selection (still use dynamic selection)
            selected_symbol = get_dynamic_symbol_selection(market_api=me2_stable.exchange)

            # Create decision data for executor
            decision_data = {
                'decision': decision,
                'confidence': confidence,
                'selected_symbol': selected_symbol,
                'leverage_position_sizing': {
                    'position_units': position_size * 100,  # Convert to units
                    'position_usd': position_size * 1000,   # Assume $1000 balance
                    'effective_leverage': leverage,
                    'recommended_leverage': leverage,
                    'max_leverage': 10.0,
                    'stop_loss_price': 0.0,  # RL agent manages its own stops
                    'take_profit_price': 0.0
                }
            }

            # Execute RL decision
            execution_result = await autonomous_executor.execute_trading_decision(decision_data)

            # Provide feedback to RL environment (for online learning)
            if execution_result['status'] == 'FILLED':
                successful_trades += 1
                # Here you could implement online RL updates

            cycle_count += 1

            # Display RL autonomous trading status
            print(f"\n{'='*70}")
            print(f">>> RL AUTONOMOUS TRADING - Cycle #{cycle_count}")
            print(f"{'='*70}")
            print(f"Symbol: {selected_symbol}")
            print(f"RL Action: Direction={direction}, Size={position_size:.2f}, Leverage={leverage:.1f}x")
            print(f"Decision: {decision} (confidence: {confidence}%)")
            print(f"Execution: {execution_result['status']}")
            print(f"Success Rate: {successful_trades}/{cycle_count} ({successful_trades/cycle_count*100:.1f}%)")
            print(f"{'='*70}\n")

            time.sleep(delay)

    except KeyboardInterrupt:
        print(f"\n[RL STATS] RL Autonomous Trading Session Summary:")
        print(f"Total Cycles: {cycle_count}")
        print(f"Successful Trades: {successful_trades}")
        print(f"RL Success Rate: {successful_trades/cycle_count*100:.1f}%" if cycle_count > 0 else "0%")

async def run_master_autonomous_trading(config: dict = None):
    """
    Run the master autonomous trading controller
    """
    if config is None:
        config = {
            'initial_balance': 1000.0,
            'max_positions': 5,
            'min_confidence': 0.65,
            'use_rl': False,
            'base_delay': 60
        }

    logger.info("[MASTER] Starting master autonomous trading controller...")

    # Initialize exchange
    import me2_stable
    if me2_stable.exchange is None:
        me2_stable.exchange = initialize_exchange()

    # Create and initialize autonomous controller
    controller = AutonomousController(me2_stable.exchange, config)
    await controller.initialize()

    # Run autonomous trading
    await controller.run_autonomous_trading()

def main():
    """
    Enhanced main function with dynamic symbol selection support
    """
    parser = argparse.ArgumentParser(description='Run the trading system with dynamic symbol selection')
    parser.add_argument('--symbol', type=str, default=None, help='Specific trading symbol (overrides dynamic selection)')
    parser.add_argument('--continuous', action='store_true', help='Run continuously with dynamic selection')
    parser.add_argument('--autonomous', action='store_true', help='Run autonomous trading (actually executes trades)')
    parser.add_argument('--rl', action='store_true', help='Use reinforcement learning agent for decisions')
    parser.add_argument('--master', action='store_true', help='Use master autonomous controller (full integration)')
    parser.add_argument('--delay', type=int, default=DEFAULT_DELAY, help='Delay between runs in seconds')
    parser.add_argument('--live', action='store_true', help='Use live trades data instead of historical OHLCV')
    parser.add_argument('--no-dynamic', action='store_true', help='Disable dynamic symbol selection')

    args = parser.parse_args()

    # Enhanced logging for dynamic selection
    logger.info(f"[START] Starting Epinnox v6 Trading System")
    logger.info(f"Dynamic Selection: {'DISABLED' if args.no_dynamic else 'ENABLED'}")
    
    if args.continuous:
        if args.autonomous:
            if args.master:
                # Run master autonomous controller (full integration)
                logger.info(f"Running MASTER AUTONOMOUS trading with {args.delay}s delay")
                config = {
                    'initial_balance': 1000.0,
                    'max_positions': 5,
                    'min_confidence': 0.65,
                    'use_rl': args.rl,
                    'base_delay': args.delay
                }
                import asyncio
                asyncio.run(run_master_autonomous_trading(config))
            elif args.rl:
                # Run RL-based autonomous trading
                logger.info(f"Running RL AUTONOMOUS trading with {args.delay}s delay")
                import asyncio
                asyncio.run(run_rl_autonomous_trading(
                    delay=args.delay,
                    training_steps=10000
                ))
            else:
                # Run standard autonomous trading (actually executes trades)
                logger.info(f"Running AUTONOMOUS trading with {args.delay}s delay")
                import asyncio
                asyncio.run(run_autonomous_trading_with_dynamic_selection(
                    delay=args.delay,
                    use_live_data=args.live
                ))
        else:
            # Run continuous trading with dynamic selection (simulation only)
            logger.info(f"Running continuously with {args.delay}s delay")
            run_continuous_trading_with_dynamic_selection(
                delay=args.delay,
                use_live_data=args.live
            )
    else:
        # Single run mode
        if args.symbol:
            # Use specific symbol (disable dynamic selection)
            logger.info(f"Using specified symbol: {args.symbol}")
            decision, explanation, parsed_response = run_trading_system(
                args.symbol,
                use_live_data=args.live
            )
            selected_symbol = args.symbol
        else:
            # Use dynamic selection for single run
            enable_dynamic = not args.no_dynamic
            decision, explanation, parsed_response, selected_symbol = run_trading_system_with_dynamic_selection(
                use_live_data=args.live,
                enable_dynamic_selection=enable_dynamic
            )
        
        # Enhanced single-run output
        print(f"\n{'='*70}")
        print(f">>> EPINNOX v6 TRADING SYSTEM")
        print(f"{'='*70}")
        print(f"Selected Symbol: {selected_symbol}")
        print(f"Dynamic Selection: {'ENABLED' if not args.no_dynamic and not args.symbol else 'DISABLED'}")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Decision: {decision}")
        
        # Show optimization features
        if 'signal_hierarchy' in parsed_response:
            hierarchy = parsed_response['signal_hierarchy']
            print(f"Signal Hierarchy: {hierarchy['decision']} (Risk: {hierarchy.get('risk_level', 'N/A')})")

        if 'ml_predictions' in parsed_response:
            ml_preds = parsed_response['ml_predictions']
            ml_count = len([p for p in ml_preds.values() if isinstance(p, dict)])
            print(f"ML Models: {ml_count} models active")

            if 'ensemble' in ml_preds:
                ensemble = ml_preds['ensemble']
                print(f"ML Ensemble: {ensemble.get('direction', 'WAIT')} (confidence: {ensemble.get('confidence', 0):.1%})")

        # Show NLP features if available
        if 'nlp_sentiment' in parsed_response:
            nlp = parsed_response['nlp_sentiment']
            print(f"NLP Sentiment: {nlp.get('overall_sentiment', 'neutral')}")
            
            if 'nlp_features' in nlp:
                nlp_features = nlp['nlp_features']
                buzz_score = nlp_features.get('social_buzz_score', 0)
                viral_score = nlp_features.get('viral_potential', 0)
                if buzz_score > 0.3 or viral_score > 0.3:
                    print(f"NLP Features: Buzz={buzz_score:.2f}, Viral={viral_score:.2f}")

        if 'leverage_position_sizing' in parsed_response:
            lev_pos = parsed_response['leverage_position_sizing']
            print(f"Max Available Leverage: {lev_pos.get('max_leverage', 0):.1f}x")
            print(f"Recommended Leverage: {lev_pos.get('recommended_leverage', 0):.1f}x")
            print(f"Effective Leverage: {lev_pos.get('effective_leverage', 0):.1f}x")
            print(f"Position Size: {lev_pos.get('position_units', 0):.2f} units (${lev_pos.get('position_usd', 0):.2f})")
            print(f"Risk Per Trade: ${lev_pos.get('risk_per_trade_usd', 0):.2f}")
            print(f"Stop Loss: ${lev_pos.get('stop_loss_price', 0):.4f}")
            print(f"Take Profit: ${lev_pos.get('take_profit_price', 0):.4f}")

        print(f"Explanation: {explanation}")
        print(f"{'='*70}\n")

if __name__ == "__main__":
    main()

