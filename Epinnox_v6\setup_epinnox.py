#!/usr/bin/env python3
"""
Complete EPINNOX v6 Setup Script
One-click setup for all dependencies and features
"""

import subprocess
import sys
import os
import platform
import argparse
from pathlib import Path

def print_banner():
    """Print setup banner"""
    print("""
🚀 EPINNOX v6 Complete Setup
═══════════════════════════════════════════════════════════════
Autonomous Trading System - Dependency Installation & Configuration
═══════════════════════════════════════════════════════════════
    """)

def run_command(command: str, description: str = "", ignore_errors: bool = False):
    """Run a command with proper error handling"""
    print(f"🔄 {description}")
    try:
        result = subprocess.run(command, shell=True, check=not ignore_errors, 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {description} - Success")
            return True
        else:
            print(f"❌ {description} - Failed")
            if result.stderr:
                print(f"   Error: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - Timeout")
        return False
    except Exception as e:
        print(f"💥 {description} - Exception: {e}")
        return False

def install_core_packages():
    """Install core Python packages"""
    print("\n📦 Installing Core Packages...")
    
    # Upgrade pip first
    run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip")
    
    # Core packages
    core_packages = [
        "pandas>=1.5.0",
        "numpy>=1.21.0",
        "scipy>=1.9.0", 
        "requests>=2.28.0",
        "aiohttp>=3.8.0",
        "websockets>=10.0",
        "python-dateutil>=2.8.0",
        "pytz>=2022.1"
    ]
    
    for package in core_packages:
        run_command(f"{sys.executable} -m pip install {package}", 
                   f"Installing {package.split('>=')[0]}", ignore_errors=True)

def install_ml_packages():
    """Install ML and RL packages"""
    print("\n🧠 Installing ML/RL Packages...")
    
    # PyTorch (CPU version by default)
    run_command(f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu",
               "Installing PyTorch (CPU)", ignore_errors=True)
    
    # RL packages
    rl_packages = [
        "stable-baselines3[extra]",
        "gymnasium", 
        "gym",
        "scikit-learn>=1.1.0"
    ]
    
    for package in rl_packages:
        run_command(f"{sys.executable} -m pip install {package}",
                   f"Installing {package.split('[')[0]}", ignore_errors=True)
    
    # TensorFlow (optional)
    run_command(f"{sys.executable} -m pip install tensorflow",
               "Installing TensorFlow", ignore_errors=True)

def install_ta_packages():
    """Install technical analysis packages"""
    print("\n📈 Installing Technical Analysis Packages...")
    
    # Try TA-Lib first
    ta_success = run_command(f"{sys.executable} -m pip install TA-Lib",
                            "Installing TA-Lib", ignore_errors=True)
    
    if not ta_success:
        print("⚠️ TA-Lib installation failed - using alternatives")
        # Install alternative TA libraries
        run_command(f"{sys.executable} -m pip install ta pandas-ta",
                   "Installing alternative TA libraries", ignore_errors=True)

def install_gui_packages():
    """Install GUI and visualization packages"""
    print("\n🖼️ Installing GUI & Visualization Packages...")
    
    gui_packages = [
        "PyQt5",
        "pyqtgraph",
        "streamlit>=1.25.0",
        "plotly>=5.15.0",
        "matplotlib>=3.5.0",
        "seaborn>=0.11.0"
    ]
    
    for package in gui_packages:
        run_command(f"{sys.executable} -m pip install {package}",
                   f"Installing {package.split('>=')[0]}", ignore_errors=True)

def install_testing_packages():
    """Install testing packages"""
    print("\n🧪 Installing Testing Packages...")
    
    test_packages = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-mock>=3.10.0",
        "pytest-cov>=4.0.0",
        "pytest-timeout>=2.1.0"
    ]
    
    for package in test_packages:
        run_command(f"{sys.executable} -m pip install {package}",
                   f"Installing {package.split('>=')[0]}", ignore_errors=True)

def install_optional_packages():
    """Install optional performance packages"""
    print("\n⚡ Installing Optional Packages...")
    
    optional_packages = [
        "orjson",  # Faster JSON
        "numba",   # JIT compilation
        "loguru",  # Better logging
        "psutil",  # System monitoring
        "python-dotenv",  # Environment variables
        "black",   # Code formatting
        "isort"    # Import sorting
    ]
    
    for package in optional_packages:
        run_command(f"{sys.executable} -m pip install {package}",
                   f"Installing {package}", ignore_errors=True)

def create_missing_directories():
    """Create missing directories and files"""
    print("\n📁 Creating Missing Directories...")
    
    directories = [
        "data",
        "logs", 
        "models",
        "optimization_results",
        "ui",
        "ui/dialogs",
        "autotune"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    # Create missing __init__.py files
    init_files = [
        "ui/__init__.py",
        "ui/dialogs/__init__.py",
        "autotune/__init__.py"
    ]
    
    for init_file in init_files:
        if not Path(init_file).exists():
            with open(init_file, 'w') as f:
                f.write("# Auto-generated __init__.py\n")
            print(f"✅ Created: {init_file}")

def run_dependency_check():
    """Run dependency check"""
    print("\n🔍 Running Dependency Check...")
    
    if Path("check_dependencies.py").exists():
        run_command(f"{sys.executable} check_dependencies.py", 
                   "Running dependency check", ignore_errors=True)
    else:
        print("⚠️ check_dependencies.py not found")

def run_tests():
    """Run basic tests"""
    print("\n🧪 Running Basic Tests...")
    
    if Path("run_tests.py").exists():
        run_command(f"{sys.executable} run_tests.py --quick", 
                   "Running quick tests", ignore_errors=True)
    else:
        print("⚠️ run_tests.py not found")

def display_next_steps():
    """Display next steps for the user"""
    print("""
🎉 EPINNOX v6 Setup Complete!
═══════════════════════════════════════════════════════════════

📋 Next Steps:

1. 🔍 Check Dependencies:
   python check_dependencies.py

2. 🧪 Run Tests:
   python run_tests.py --verbose

3. 📊 Start Dashboard:
   python start_dashboard.py

4. 🔧 Parameter Optimization:
   python run_param_search.py --preset quick

5. 🤖 Multi-Agent Simulation:
   python run_multi_agent_sim.py --preset diverse --days 7

6. 🧪 Stress Testing:
   python run_stress_test.py flash_crash --intensity 1.5

7. 📈 Paper Trading:
   python start_paper_trading.py --duration 60 --balance 5000

8. 🚀 Main Trading System:
   python main.py --help

═══════════════════════════════════════════════════════════════

🔧 Optional GPU Setup:
   python setup_gpu.py

📚 Documentation:
   - README.md
   - ADVANCED_TESTING_GUIDE.md

💡 Troubleshooting:
   - Check logs/ directory for error logs
   - Run dependency check if issues occur
   - Use --help flag on any script for options

═══════════════════════════════════════════════════════════════
    """)

def main():
    """Main setup process"""
    parser = argparse.ArgumentParser(description='EPINNOX v6 Complete Setup')
    parser.add_argument('--minimal', action='store_true', 
                       help='Install only core dependencies')
    parser.add_argument('--no-tests', action='store_true',
                       help='Skip running tests after installation')
    parser.add_argument('--gpu', action='store_true',
                       help='Also setup GPU acceleration')
    
    args = parser.parse_args()
    
    print_banner()
    
    try:
        # Core installation
        install_core_packages()
        install_ml_packages()
        
        if not args.minimal:
            install_ta_packages()
            install_gui_packages()
            install_testing_packages()
            install_optional_packages()
        
        # Create missing directories and files
        create_missing_directories()
        
        # Run dependency check
        run_dependency_check()
        
        # Run tests unless skipped
        if not args.no_tests:
            run_tests()
        
        # GPU setup if requested
        if args.gpu:
            if Path("setup_gpu.py").exists():
                run_command(f"{sys.executable} setup_gpu.py", 
                           "Setting up GPU acceleration", ignore_errors=True)
            else:
                print("⚠️ setup_gpu.py not found")
        
        # Display next steps
        display_next_steps()
        
        print("✅ Setup completed successfully!")
        return 0
        
    except KeyboardInterrupt:
        print("\n🛑 Setup interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Setup failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
