#!/usr/bin/env python3
"""
Production Deployment Script for Epinnox v6
Automated deployment with validation, backup, and rollback capabilities
"""

import os
import sys
import json
import shutil
import logging
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.production_config import ProductionConfigManager, Environment

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deployment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DeploymentManager:
    """
    Automated deployment manager for Epinnox v6
    """
    
    def __init__(self, target_environment: Environment):
        """Initialize deployment manager"""
        self.target_environment = target_environment
        self.project_root = Path(__file__).parent.parent
        self.backup_dir = self.project_root / "backups"
        self.config_manager = ProductionConfigManager()
        
        # Deployment metadata
        self.deployment_id = f"deploy_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.deployment_log = []
        
        logger.info(f"🚀 Deployment Manager initialized for {target_environment.value}")
    
    def deploy(self) -> bool:
        """Execute complete deployment process"""
        try:
            logger.info(f"🚀 Starting deployment to {self.target_environment.value}")
            
            # Pre-deployment checks
            if not self._pre_deployment_checks():
                logger.error("❌ Pre-deployment checks failed")
                return False
            
            # Create backup
            if not self._create_backup():
                logger.error("❌ Backup creation failed")
                return False
            
            # Validate configuration
            if not self._validate_configuration():
                logger.error("❌ Configuration validation failed")
                return False
            
            # Install dependencies
            if not self._install_dependencies():
                logger.error("❌ Dependency installation failed")
                return False
            
            # Run tests
            if not self._run_tests():
                logger.error("❌ Tests failed")
                return False
            
            # Deploy configuration
            if not self._deploy_configuration():
                logger.error("❌ Configuration deployment failed")
                return False
            
            # Start services
            if not self._start_services():
                logger.error("❌ Service startup failed")
                return False
            
            # Post-deployment validation
            if not self._post_deployment_validation():
                logger.error("❌ Post-deployment validation failed")
                return False
            
            # Generate deployment report
            self._generate_deployment_report()
            
            logger.info("✅ Deployment completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Deployment failed: {e}")
            self._rollback()
            return False
    
    def _pre_deployment_checks(self) -> bool:
        """Perform pre-deployment checks"""
        try:
            logger.info("🔍 Running pre-deployment checks...")
            
            # Check Python version
            if sys.version_info < (3, 8):
                logger.error("Python 3.8+ required")
                return False
            
            # Check required files exist
            required_files = [
                "launch_epinnox.py",
                "core/risk_management_system.py",
                "core/error_handling_system.py",
                "core/monitoring_dashboard.py",
                "config/production_config.py"
            ]
            
            for file_path in required_files:
                if not (self.project_root / file_path).exists():
                    logger.error(f"Required file missing: {file_path}")
                    return False
            
            # Check disk space (minimum 1GB)
            free_space = shutil.disk_usage(self.project_root).free
            if free_space < 1024 * 1024 * 1024:  # 1GB
                logger.error("Insufficient disk space (minimum 1GB required)")
                return False
            
            # Check credentials file exists for production
            if self.target_environment == Environment.PRODUCTION:
                credentials_file = self.project_root / "credentials.py"
                if not credentials_file.exists():
                    logger.error("Production credentials file missing")
                    return False
            
            logger.info("✅ Pre-deployment checks passed")
            return True
            
        except Exception as e:
            logger.error(f"Error in pre-deployment checks: {e}")
            return False
    
    def _create_backup(self) -> bool:
        """Create backup of current deployment"""
        try:
            logger.info("💾 Creating deployment backup...")
            
            # Create backup directory
            backup_path = self.backup_dir / self.deployment_id
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # Backup critical files
            backup_items = [
                "launch_epinnox.py",
                "config/",
                "core/",
                "credentials.py"
            ]
            
            for item in backup_items:
                source = self.project_root / item
                if source.exists():
                    if source.is_file():
                        shutil.copy2(source, backup_path / item)
                    else:
                        shutil.copytree(source, backup_path / item, dirs_exist_ok=True)
            
            # Create backup metadata
            backup_metadata = {
                "deployment_id": self.deployment_id,
                "timestamp": datetime.now().isoformat(),
                "environment": self.target_environment.value,
                "backup_items": backup_items
            }
            
            with open(backup_path / "backup_metadata.json", 'w') as f:
                json.dump(backup_metadata, f, indent=2)
            
            logger.info(f"✅ Backup created: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return False
    
    def _validate_configuration(self) -> bool:
        """Validate deployment configuration"""
        try:
            logger.info("🔧 Validating configuration...")
            
            # Load target environment configuration
            config = self.config_manager.load_config(self.target_environment)
            
            # Validate configuration
            is_valid, errors = self.config_manager.validate_config(config)
            
            if not is_valid:
                logger.error("Configuration validation failed:")
                for error in errors:
                    logger.error(f"  - {error}")
                return False
            
            # Environment-specific validations
            if self.target_environment == Environment.PRODUCTION:
                if config.api_settings.enable_testnet:
                    logger.error("Production cannot use testnet")
                    return False
                
                if not config.risk_settings.enable_emergency_stops:
                    logger.error("Production must have emergency stops enabled")
                    return False
            
            logger.info("✅ Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Error validating configuration: {e}")
            return False
    
    def _install_dependencies(self) -> bool:
        """Install required dependencies"""
        try:
            logger.info("📦 Installing dependencies...")
            
            # Check if requirements.txt exists
            requirements_file = self.project_root / "requirements.txt"
            if requirements_file.exists():
                # Install using pip
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
                ], capture_output=True, text=True)
                
                if result.returncode != 0:
                    logger.error(f"Dependency installation failed: {result.stderr}")
                    return False
            
            logger.info("✅ Dependencies installed")
            return True
            
        except Exception as e:
            logger.error(f"Error installing dependencies: {e}")
            return False
    
    def _run_tests(self) -> bool:
        """Run test suite"""
        try:
            logger.info("🧪 Running test suite...")
            
            # Run real interface tests
            test_files = [
                "tests/test_real_interface_operations.py",
                "tests/test_integration_workflow.py"
            ]
            
            for test_file in test_files:
                test_path = self.project_root / test_file
                if test_path.exists():
                    result = subprocess.run([
                        sys.executable, "-m", "pytest", str(test_path), "-v"
                    ], capture_output=True, text=True, cwd=self.project_root)
                    
                    if result.returncode != 0:
                        logger.error(f"Test failed: {test_file}")
                        logger.error(result.stdout)
                        logger.error(result.stderr)
                        return False
                    
                    logger.info(f"✅ Test passed: {test_file}")
            
            logger.info("✅ All tests passed")
            return True
            
        except Exception as e:
            logger.error(f"Error running tests: {e}")
            return False
    
    def _deploy_configuration(self) -> bool:
        """Deploy configuration for target environment"""
        try:
            logger.info("🔧 Deploying configuration...")
            
            # Load and save configuration for target environment
            config = self.config_manager.load_config(self.target_environment)
            config.deployment_timestamp = datetime.now().isoformat()
            
            self.config_manager.save_config(config)
            
            logger.info(f"✅ Configuration deployed for {self.target_environment.value}")
            return True
            
        except Exception as e:
            logger.error(f"Error deploying configuration: {e}")
            return False
    
    def _start_services(self) -> bool:
        """Start required services"""
        try:
            logger.info("🚀 Starting services...")
            
            # For now, just validate that the main application can be imported
            try:
                from launch_epinnox import EpinnoxTradingInterface
                logger.info("✅ Main application validated")
            except ImportError as e:
                logger.error(f"Failed to import main application: {e}")
                return False
            
            logger.info("✅ Services ready")
            return True
            
        except Exception as e:
            logger.error(f"Error starting services: {e}")
            return False
    
    def _post_deployment_validation(self) -> bool:
        """Validate deployment after completion"""
        try:
            logger.info("✅ Running post-deployment validation...")
            
            # Validate configuration is loaded correctly
            config = self.config_manager.get_current_config()
            if not config or config.environment != self.target_environment:
                logger.error("Configuration not loaded correctly")
                return False
            
            # Validate critical components can be imported
            critical_modules = [
                "core.risk_management_system",
                "core.error_handling_system", 
                "core.monitoring_dashboard"
            ]
            
            for module_name in critical_modules:
                try:
                    __import__(module_name)
                    logger.info(f"✅ Module validated: {module_name}")
                except ImportError as e:
                    logger.error(f"Failed to import {module_name}: {e}")
                    return False
            
            logger.info("✅ Post-deployment validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Error in post-deployment validation: {e}")
            return False
    
    def _rollback(self) -> bool:
        """Rollback deployment in case of failure"""
        try:
            logger.warning("🔄 Rolling back deployment...")
            
            backup_path = self.backup_dir / self.deployment_id
            if not backup_path.exists():
                logger.error("Backup not found for rollback")
                return False
            
            # Restore backed up files
            backup_metadata_file = backup_path / "backup_metadata.json"
            if backup_metadata_file.exists():
                with open(backup_metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                for item in metadata.get("backup_items", []):
                    source = backup_path / item
                    target = self.project_root / item
                    
                    if source.exists():
                        if source.is_file():
                            shutil.copy2(source, target)
                        else:
                            if target.exists():
                                shutil.rmtree(target)
                            shutil.copytree(source, target)
            
            logger.info("✅ Rollback completed")
            return True
            
        except Exception as e:
            logger.error(f"Error during rollback: {e}")
            return False
    
    def _generate_deployment_report(self):
        """Generate deployment report"""
        try:
            report = {
                "deployment_id": self.deployment_id,
                "environment": self.target_environment.value,
                "timestamp": datetime.now().isoformat(),
                "status": "success",
                "configuration_summary": self.config_manager.get_environment_summary()
            }
            
            report_file = self.project_root / f"deployment_report_{self.deployment_id}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"📊 Deployment report generated: {report_file}")
            
        except Exception as e:
            logger.error(f"Error generating deployment report: {e}")

def main():
    """Main deployment function"""
    if len(sys.argv) != 2:
        print("Usage: python deploy_production.py <environment>")
        print("Environments: development, testing, staging, production")
        sys.exit(1)
    
    env_name = sys.argv[1].lower()
    try:
        environment = Environment(env_name)
    except ValueError:
        print(f"Invalid environment: {env_name}")
        print("Valid environments: development, testing, staging, production")
        sys.exit(1)
    
    # Create deployment manager and deploy
    deployment_manager = DeploymentManager(environment)
    success = deployment_manager.deploy()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
