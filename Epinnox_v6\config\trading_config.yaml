# Epinnox Trading System Configuration

# Exchange credentials
credentials:
  exchange: 'htx'  # HTX (formerly Huobi)
  apiKey: 'tycf4rw2-72d300ec-fb900970-27ef8'
  secret: 'b4d92e15-523563a0-72a16ad9-9a275'

# Trading parameters
trading:
  # Default parameters
  default:
    market_type: 'future'  # 'spot' or 'future'
    leverage: 75            # Default leverage if not specified for an asset
    risk_percentage: 2.0   # Percentage of available balance to risk per trade
    max_position_size: 500.0  # Maximum position size in USDT
    stop_loss_percentage: 2.0  # Default stop loss percentage
    take_profit_percentage: 0.35  # Default take profit percentage
    trailing_stop: false    # Whether to use trailing stop
    trailing_stop_percentage: 1.0  # Trailing stop percentage
    
  # Asset-specific parameters
  assets:
    'DOGE/USDT:USDT':  # Dogecoin futures
      leverage: 75
      risk_percentage: 1.5
      max_position_size: 500.0
      stop_loss_percentage: 3.0
      take_profit_percentage: 6.0
      
    'BTC/USDT:USDT':   # Bitcoin futures
      leverage: 50
      risk_percentage: 1.0
      max_position_size: 2000.0
      stop_loss_percentage: 2.0
      take_profit_percentage: 5.0
      
    'ETH/USDT:USDT':   # Ethereum futures
      leverage: 50
      risk_percentage: 1.2
      max_position_size: 1500.0
      stop_loss_percentage: 2.5
      take_profit_percentage: 5.5
      
    'SOL/USDT:USDT':   # Solana futures
      leverage: 20
      risk_percentage: 1.5
      max_position_size: 800.0
      
    'SHIB/USDT:USDT':  # Shiba Inu futures
      leverage: 10
      risk_percentage: 1.0
      max_position_size: 300.0
      
    'XRP/USDT:USDT':   # Ripple futures
      leverage: 20
      risk_percentage: 1.5
      max_position_size: 700.0
      
    'ADA/USDT:USDT':   # Cardano futures
      leverage: 20
      risk_percentage: 1.5
      max_position_size: 600.0
      
    'AVAX/USDT:USDT':  # Avalanche futures
      leverage: 20
      risk_percentage: 1.5
      max_position_size: 700.0
      
    'MATIC/USDT:USDT': # Polygon futures
      leverage: 20
      risk_percentage: 1.5
      max_position_size: 500.0
      
    'DOT/USDT:USDT':   # Polkadot futures
      leverage: 20
      risk_percentage: 1.5
      max_position_size: 600.0

# Performance tracking
performance:
  db_path: 'performance.db'
  save_dir: 'performance_charts'
  update_interval: 10  # Update metrics every N trades
  visualization_interval: 20  # Create visualizations every N trades

# System settings
system:
  log_level: 'INFO'  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  log_file: 'trading_system.log'
  continuous_mode: true
  delay: 60  # Seconds between trading cycles in continuous mode
  use_live_data: true  # Use live trades data instead of OHLCV
  max_memory_usage: 8000  # Maximum memory usage in MB
  
# LLaMA model settings
llama:
  use_mock: false  # Set to false to use real LLaMA models
  model_path: 'models/Llama-4-Scout-17B-16E-Instruct.gguf'
  guard_path: 'models/Llama-Guard-3-8B.gguf'
  bin_path: 'llama.cpp/build/bin/main'
  context_size: 4096
  temperature: 0.7
  top_p: 0.9
  max_tokens: 1024
