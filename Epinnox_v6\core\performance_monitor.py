"""
Performance Monitoring Module for Epinnox v6
Provides real-time performance monitoring and optimization for trading operations
"""

import time
import psutil
import threading
import logging
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta
from collections import deque
from dataclasses import dataclass
from PyQt5.QtCore import QObject, QTimer, pyqtSignal

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Container for performance metrics"""
    timestamp: float
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    disk_io_read: int
    disk_io_write: int
    network_sent: int
    network_recv: int
    active_threads: int
    open_files: int

@dataclass
class OperationMetrics:
    """Container for operation-specific metrics"""
    operation_name: str
    start_time: float
    end_time: float
    duration: float
    success: bool
    error_message: Optional[str] = None

class PerformanceMonitor(QObject):
    """
    Real-time performance monitoring for trading operations
    """
    
    # Signals for UI updates
    metrics_updated = pyqtSignal(dict)  # Performance metrics
    operation_completed = pyqtSignal(dict)  # Operation metrics
    alert_triggered = pyqtSignal(str, str)  # Alert type, message
    
    def __init__(self, monitoring_interval: int = 5, history_size: int = 1000):
        super().__init__()
        
        self.monitoring_interval = monitoring_interval
        self.history_size = history_size
        
        # Performance history
        self.metrics_history = deque(maxlen=history_size)
        self.operation_history = deque(maxlen=history_size)
        
        # Current metrics
        self.current_metrics = None
        self.process = psutil.Process()
        
        # Performance thresholds
        self.thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'operation_duration': 30.0,  # seconds
            'error_rate': 0.1  # 10%
        }
        
        # Operation tracking
        self.active_operations = {}
        self.operation_stats = {}
        
        # Monitoring timer
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._collect_metrics)
        
        # Start monitoring
        self.start_monitoring()
        
        logger.info(f"Performance monitor initialized with {monitoring_interval}s interval")
    
    def start_monitoring(self):
        """Start performance monitoring"""
        self.monitor_timer.start(self.monitoring_interval * 1000)
        logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitor_timer.stop()
        logger.info("Performance monitoring stopped")
    
    def _collect_metrics(self):
        """Collect system performance metrics"""
        try:
            # CPU and memory
            cpu_percent = self.process.cpu_percent()
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            memory_percent = self.process.memory_percent()
            
            # I/O statistics
            try:
                io_counters = self.process.io_counters()
                disk_io_read = io_counters.read_bytes
                disk_io_write = io_counters.write_bytes
            except (AttributeError, psutil.AccessDenied):
                disk_io_read = disk_io_write = 0
            
            # Network statistics (system-wide)
            try:
                net_io = psutil.net_io_counters()
                network_sent = net_io.bytes_sent
                network_recv = net_io.bytes_recv
            except (AttributeError, psutil.AccessDenied):
                network_sent = network_recv = 0
            
            # Process information
            active_threads = self.process.num_threads()
            try:
                open_files = len(self.process.open_files())
            except (AttributeError, psutil.AccessDenied):
                open_files = 0
            
            # Create metrics object
            metrics = PerformanceMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_mb=memory_mb,
                memory_percent=memory_percent,
                disk_io_read=disk_io_read,
                disk_io_write=disk_io_write,
                network_sent=network_sent,
                network_recv=network_recv,
                active_threads=active_threads,
                open_files=open_files
            )
            
            # Store metrics
            self.metrics_history.append(metrics)
            self.current_metrics = metrics
            
            # Check thresholds
            self._check_thresholds(metrics)
            
            # Emit signal for UI updates
            metrics_dict = {
                'timestamp': metrics.timestamp,
                'cpu_percent': metrics.cpu_percent,
                'memory_mb': metrics.memory_mb,
                'memory_percent': metrics.memory_percent,
                'active_threads': metrics.active_threads,
                'open_files': metrics.open_files
            }
            self.metrics_updated.emit(metrics_dict)
            
        except Exception as e:
            logger.error(f"Error collecting performance metrics: {e}")
    
    def _check_thresholds(self, metrics: PerformanceMetrics):
        """Check if metrics exceed thresholds and trigger alerts"""
        if metrics.cpu_percent > self.thresholds['cpu_percent']:
            self.alert_triggered.emit(
                'performance',
                f"High CPU usage: {metrics.cpu_percent:.1f}%"
            )
        
        if metrics.memory_percent > self.thresholds['memory_percent']:
            self.alert_triggered.emit(
                'performance',
                f"High memory usage: {metrics.memory_percent:.1f}%"
            )
    
    def start_operation(self, operation_name: str) -> str:
        """Start tracking an operation"""
        operation_id = f"{operation_name}_{int(time.time() * 1000)}"
        self.active_operations[operation_id] = {
            'name': operation_name,
            'start_time': time.time()
        }
        return operation_id
    
    def end_operation(self, operation_id: str, success: bool = True, error_message: str = None):
        """End tracking an operation"""
        if operation_id not in self.active_operations:
            logger.warning(f"Operation {operation_id} not found in active operations")
            return
        
        operation = self.active_operations.pop(operation_id)
        end_time = time.time()
        duration = end_time - operation['start_time']
        
        # Create operation metrics
        op_metrics = OperationMetrics(
            operation_name=operation['name'],
            start_time=operation['start_time'],
            end_time=end_time,
            duration=duration,
            success=success,
            error_message=error_message
        )
        
        # Store metrics
        self.operation_history.append(op_metrics)
        
        # Update operation statistics
        op_name = operation['name']
        if op_name not in self.operation_stats:
            self.operation_stats[op_name] = {
                'total_count': 0,
                'success_count': 0,
                'total_duration': 0,
                'avg_duration': 0,
                'max_duration': 0,
                'min_duration': float('inf')
            }
        
        stats = self.operation_stats[op_name]
        stats['total_count'] += 1
        if success:
            stats['success_count'] += 1
        stats['total_duration'] += duration
        stats['avg_duration'] = stats['total_duration'] / stats['total_count']
        stats['max_duration'] = max(stats['max_duration'], duration)
        stats['min_duration'] = min(stats['min_duration'], duration)
        
        # Check duration threshold
        if duration > self.thresholds['operation_duration']:
            self.alert_triggered.emit(
                'performance',
                f"Slow operation: {op_name} took {duration:.2f}s"
            )
        
        # Emit signal for UI updates
        op_dict = {
            'operation_name': op_metrics.operation_name,
            'duration': op_metrics.duration,
            'success': op_metrics.success,
            'error_message': op_metrics.error_message
        }
        self.operation_completed.emit(op_dict)
        
        logger.debug(f"Operation {op_name} completed in {duration:.3f}s (success: {success})")
    
    def get_performance_summary(self) -> Dict:
        """Get performance summary statistics"""
        if not self.metrics_history:
            return {}
        
        # Calculate averages over last 10 minutes
        cutoff_time = time.time() - 600  # 10 minutes
        recent_metrics = [m for m in self.metrics_history if m.timestamp > cutoff_time]
        
        if not recent_metrics:
            recent_metrics = list(self.metrics_history)[-10:]  # Last 10 samples
        
        if not recent_metrics:
            return {}
        
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_mb for m in recent_metrics) / len(recent_metrics)
        avg_memory_pct = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        
        # Operation statistics
        total_operations = len(self.operation_history)
        successful_operations = sum(1 for op in self.operation_history if op.success)
        success_rate = successful_operations / total_operations if total_operations > 0 else 0
        
        return {
            'avg_cpu_percent': avg_cpu,
            'avg_memory_mb': avg_memory,
            'avg_memory_percent': avg_memory_pct,
            'total_operations': total_operations,
            'success_rate': success_rate,
            'operation_stats': self.operation_stats.copy(),
            'active_operations': len(self.active_operations)
        }
    
    def optimize_performance(self):
        """Perform automatic performance optimizations"""
        try:
            # Memory cleanup
            import gc
            gc.collect()
            
            # Clear old metrics if history is getting large
            if len(self.metrics_history) > self.history_size * 0.9:
                # Keep only recent 50% of history
                keep_count = self.history_size // 2
                self.metrics_history = deque(
                    list(self.metrics_history)[-keep_count:],
                    maxlen=self.history_size
                )
            
            # Clear old operation history
            if len(self.operation_history) > self.history_size * 0.9:
                keep_count = self.history_size // 2
                self.operation_history = deque(
                    list(self.operation_history)[-keep_count:],
                    maxlen=self.history_size
                )
            
            logger.info("Performance optimization completed")
            
        except Exception as e:
            logger.error(f"Error during performance optimization: {e}")
    
    def set_threshold(self, metric: str, value: float):
        """Set performance threshold"""
        if metric in self.thresholds:
            self.thresholds[metric] = value
            logger.info(f"Updated threshold {metric} to {value}")
        else:
            logger.warning(f"Unknown threshold metric: {metric}")

# Decorator for automatic operation tracking
def monitor_operation(operation_name: str):
    """Decorator to automatically monitor operation performance"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Get monitor instance (assumes it's available globally or as class attribute)
            monitor = getattr(args[0], 'performance_monitor', None) if args else None
            
            if monitor and isinstance(monitor, PerformanceMonitor):
                op_id = monitor.start_operation(operation_name)
                try:
                    result = func(*args, **kwargs)
                    monitor.end_operation(op_id, success=True)
                    return result
                except Exception as e:
                    monitor.end_operation(op_id, success=False, error_message=str(e))
                    raise
            else:
                return func(*args, **kwargs)
        
        return wrapper
    return decorator

# Global performance monitor instance
performance_monitor = None

def get_performance_monitor() -> Optional[PerformanceMonitor]:
    """Get global performance monitor instance"""
    return performance_monitor

def initialize_performance_monitor(**kwargs) -> PerformanceMonitor:
    """Initialize global performance monitor"""
    global performance_monitor
    performance_monitor = PerformanceMonitor(**kwargs)
    return performance_monitor
