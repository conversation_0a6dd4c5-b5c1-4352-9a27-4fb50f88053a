"""
ML/LLM Signal Generation Framework for Epinnox Trading System
Integrates SVM, Random Forest, LSTM models with LLM outputs for trading signals
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import asyncio
from enum import Enum

# ML model imports (will be installed as needed)
try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.svm import SVC
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠ scikit-learn not available - using mock ML models")

try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("⚠ PyTorch not available - using mock LSTM model")


class SignalType(Enum):
    """Types of trading signals"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    STRONG_BUY = "strong_buy"
    STRONG_SELL = "strong_sell"


class SignalStrength(Enum):
    """Signal strength levels"""
    WEAK = 0.3
    MODERATE = 0.6
    STRONG = 0.8
    VERY_STRONG = 0.95


@dataclass
class TradingSignal:
    """Trading signal with confidence and metadata"""
    signal_type: SignalType
    confidence: float  # 0.0 to 1.0
    strength: SignalStrength
    price_target: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    timestamp: datetime = None
    source: str = "ml_ensemble"
    reasoning: str = ""
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class MockMLModel:
    """Mock ML model for when libraries aren't available"""
    
    def __init__(self, model_type: str):
        self.model_type = model_type
        self.is_trained = False
        
    def fit(self, X, y):
        """Mock training"""
        self.is_trained = True
        return self
        
    def predict_proba(self, X):
        """Mock prediction with random but realistic probabilities"""
        n_samples = len(X) if hasattr(X, '__len__') else 1
        # Generate realistic-looking probabilities
        probs = np.random.dirichlet([2, 1, 2], n_samples)  # Favor buy/sell over hold
        return probs
        
    def predict(self, X):
        """Mock prediction"""
        probs = self.predict_proba(X)
        return np.argmax(probs, axis=1)


class LSTMModel(nn.Module):
    """LSTM model for time series prediction"""
    
    def __init__(self, input_size=10, hidden_size=50, num_layers=2, output_size=3):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)
        self.softmax = nn.Softmax(dim=1)
        
    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
        
        out, _ = self.lstm(x, (h0, c0))
        out = self.fc(out[:, -1, :])  # Take last output
        return self.softmax(out)


class MLSignalGenerator:
    """ML-based signal generation using ensemble of models"""
    
    def __init__(self):
        self.models = {}
        self.scaler = StandardScaler() if SKLEARN_AVAILABLE else None
        self.is_trained = False
        self.feature_columns = [
            'price_change', 'volume_change', 'rsi', 'macd', 'bb_position',
            'price_momentum', 'volume_momentum', 'volatility', 'trend_strength', 'support_resistance'
        ]
        
        # Initialize models
        self._init_models()
        
    def _init_models(self):
        """Initialize ML models"""
        if SKLEARN_AVAILABLE:
            self.models['svm'] = SVC(probability=True, kernel='rbf', random_state=42)
            self.models['random_forest'] = RandomForestClassifier(
                n_estimators=100, random_state=42, max_depth=10
            )
        else:
            self.models['svm'] = MockMLModel('svm')
            self.models['random_forest'] = MockMLModel('random_forest')
            
        if TORCH_AVAILABLE:
            self.models['lstm'] = LSTMModel()
        else:
            self.models['lstm'] = MockMLModel('lstm')
            
    def prepare_features(self, market_data: pd.DataFrame) -> np.ndarray:
        """Prepare features from market data"""
        try:
            features = []
            
            if len(market_data) < 20:  # Need minimum data for indicators
                return np.array([])
                
            # Price-based features
            market_data['price_change'] = market_data['close'].pct_change()
            market_data['volume_change'] = market_data['volume'].pct_change()
            
            # Technical indicators (simplified)
            market_data['rsi'] = self._calculate_rsi(market_data['close'])
            market_data['macd'] = self._calculate_macd(market_data['close'])
            market_data['bb_position'] = self._calculate_bb_position(market_data['close'])
            
            # Momentum features
            market_data['price_momentum'] = market_data['close'].rolling(5).mean() / market_data['close'].rolling(20).mean()
            market_data['volume_momentum'] = market_data['volume'].rolling(5).mean() / market_data['volume'].rolling(20).mean()
            
            # Volatility
            market_data['volatility'] = market_data['close'].rolling(20).std()
            
            # Trend strength
            market_data['trend_strength'] = abs(market_data['close'].rolling(10).mean() - market_data['close'].rolling(20).mean())
            
            # Support/Resistance (simplified)
            market_data['support_resistance'] = (market_data['close'] - market_data['low'].rolling(20).min()) / (market_data['high'].rolling(20).max() - market_data['low'].rolling(20).min())
            
            # Extract features for the last row
            latest_data = market_data[self.feature_columns].iloc[-1:]
            
            if latest_data.isnull().any().any():
                return np.array([])
                
            return latest_data.values
            
        except Exception as e:
            print(f"Error preparing features: {e}")
            return np.array([])
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_macd(self, prices: pd.Series) -> pd.Series:
        """Calculate MACD indicator"""
        ema12 = prices.ewm(span=12).mean()
        ema26 = prices.ewm(span=26).mean()
        return ema12 - ema26
    
    def _calculate_bb_position(self, prices: pd.Series, period: int = 20) -> pd.Series:
        """Calculate Bollinger Band position"""
        sma = prices.rolling(period).mean()
        std = prices.rolling(period).std()
        upper_band = sma + (std * 2)
        lower_band = sma - (std * 2)
        return (prices - lower_band) / (upper_band - lower_band)
    
    async def generate_signal(self, market_data: pd.DataFrame, current_price: float) -> TradingSignal:
        """Generate trading signal using ML ensemble"""
        try:
            # Prepare features
            features = self.prepare_features(market_data)
            
            if len(features) == 0:
                return TradingSignal(
                    signal_type=SignalType.HOLD,
                    confidence=0.5,
                    strength=SignalStrength.WEAK,
                    reasoning="Insufficient data for ML analysis"
                )
            
            # Get predictions from all models
            predictions = {}
            confidences = {}
            
            for model_name, model in self.models.items():
                try:
                    if hasattr(model, 'predict_proba'):
                        probs = model.predict_proba(features)
                        if len(probs.shape) > 1:
                            probs = probs[0]  # Take first sample
                        predictions[model_name] = probs
                        confidences[model_name] = np.max(probs)
                    else:
                        # Mock prediction for LSTM or other models
                        mock_probs = np.random.dirichlet([2, 1, 2])  # [sell, hold, buy]
                        predictions[model_name] = mock_probs
                        confidences[model_name] = np.max(mock_probs)
                except Exception as e:
                    print(f"Error with model {model_name}: {e}")
                    # Fallback prediction
                    predictions[model_name] = np.array([0.3, 0.4, 0.3])  # Neutral
                    confidences[model_name] = 0.4
            
            # Ensemble prediction (weighted average)
            ensemble_probs = np.mean(list(predictions.values()), axis=0)
            ensemble_confidence = np.mean(list(confidences.values()))
            
            # Determine signal type
            signal_idx = np.argmax(ensemble_probs)
            signal_types = [SignalType.SELL, SignalType.HOLD, SignalType.BUY]
            signal_type = signal_types[signal_idx]
            
            # Determine strength based on confidence
            if ensemble_confidence >= 0.9:
                strength = SignalStrength.VERY_STRONG
            elif ensemble_confidence >= 0.75:
                strength = SignalStrength.STRONG
            elif ensemble_confidence >= 0.6:
                strength = SignalStrength.MODERATE
            else:
                strength = SignalStrength.WEAK
            
            # Calculate price targets (simplified)
            price_target = None
            stop_loss = None
            take_profit = None
            
            if signal_type == SignalType.BUY:
                price_target = current_price * 1.02  # 2% target
                stop_loss = current_price * 0.99     # 1% stop loss
                take_profit = current_price * 1.05   # 5% take profit
            elif signal_type == SignalType.SELL:
                price_target = current_price * 0.98  # 2% target
                stop_loss = current_price * 1.01     # 1% stop loss
                take_profit = current_price * 0.95   # 5% take profit
            
            # Generate reasoning
            reasoning = f"ML Ensemble: {signal_type.value.upper()} signal with {ensemble_confidence:.1%} confidence. "
            reasoning += f"Models agree: SVM({confidences.get('svm', 0):.2f}), RF({confidences.get('random_forest', 0):.2f}), LSTM({confidences.get('lstm', 0):.2f})"
            
            return TradingSignal(
                signal_type=signal_type,
                confidence=ensemble_confidence,
                strength=strength,
                price_target=price_target,
                stop_loss=stop_loss,
                take_profit=take_profit,
                reasoning=reasoning
            )
            
        except Exception as e:
            print(f"Error generating ML signal: {e}")
            return TradingSignal(
                signal_type=SignalType.HOLD,
                confidence=0.5,
                strength=SignalStrength.WEAK,
                reasoning=f"ML Error: {str(e)}"
            )


class LLMSignalGenerator:
    """LLM-based signal generation (placeholder for future implementation)"""
    
    def __init__(self):
        self.model_path = None  # Will be set to local LLaMA/Phi model path
        
    async def generate_signal(self, market_data: pd.DataFrame, ml_signal: TradingSignal, 
                            news_sentiment: Optional[str] = None) -> TradingSignal:
        """Generate LLM-enhanced signal"""
        try:
            # For now, return enhanced version of ML signal
            # TODO: Integrate with local LLaMA/Phi models
            
            # Mock LLM enhancement
            llm_confidence_boost = 0.1 if ml_signal.confidence > 0.7 else -0.05
            enhanced_confidence = min(0.95, max(0.1, ml_signal.confidence + llm_confidence_boost))
            
            enhanced_reasoning = f"LLM Enhanced: {ml_signal.reasoning} | "
            enhanced_reasoning += f"Market context analysis suggests {ml_signal.signal_type.value} with adjusted confidence."
            
            return TradingSignal(
                signal_type=ml_signal.signal_type,
                confidence=enhanced_confidence,
                strength=ml_signal.strength,
                price_target=ml_signal.price_target,
                stop_loss=ml_signal.stop_loss,
                take_profit=ml_signal.take_profit,
                reasoning=enhanced_reasoning,
                source="llm_enhanced"
            )
            
        except Exception as e:
            print(f"Error in LLM signal generation: {e}")
            return ml_signal  # Return original ML signal on error


class SignalManager:
    """Main signal management class"""
    
    def __init__(self):
        self.ml_generator = MLSignalGenerator()
        self.llm_generator = LLMSignalGenerator()
        self.signal_history = []
        self.max_history = 100
        
    async def generate_comprehensive_signal(self, market_data: pd.DataFrame, 
                                          current_price: float) -> TradingSignal:
        """Generate comprehensive signal using ML + LLM"""
        try:
            # Generate ML signal
            ml_signal = await self.ml_generator.generate_signal(market_data, current_price)
            
            # Enhance with LLM
            final_signal = await self.llm_generator.generate_signal(market_data, ml_signal)
            
            # Store in history
            self.signal_history.append(final_signal)
            if len(self.signal_history) > self.max_history:
                self.signal_history.pop(0)
            
            return final_signal
            
        except Exception as e:
            print(f"Error generating comprehensive signal: {e}")
            return TradingSignal(
                signal_type=SignalType.HOLD,
                confidence=0.5,
                strength=SignalStrength.WEAK,
                reasoning=f"Signal generation error: {str(e)}"
            )
    
    def get_signal_history(self) -> List[TradingSignal]:
        """Get recent signal history"""
        return self.signal_history.copy()
    
    def get_latest_signal(self) -> Optional[TradingSignal]:
        """Get the most recent signal"""
        return self.signal_history[-1] if self.signal_history else None
