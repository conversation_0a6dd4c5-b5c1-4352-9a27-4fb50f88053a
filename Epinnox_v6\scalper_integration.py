
# Elite Futures Scalper Integration for Epinnox v6
# Add this to your LLM orchestrator or trading interface

from elite_futures_scalper import EliteFuturesScalper

class ScalperIntegration:
    def __init__(self):
        self.scalper = EliteFuturesScalper()
    
    def get_scalping_decision(self, market_data):
        """Get aggressive scalping decision from Elite Futures Scalper"""
        return self.scalper.analyze_market_and_decide(market_data)
    
    def format_for_llm(self, market_data):
        """Format market data for LLM with scalper prompt"""
        decision = self.get_scalping_decision(market_data)
        
        prompt = f"""
You are an aggressive high-frequency scalping AI for cryptocurrency futures trading.
Your primary directive is to execute rapid, profitable trades with minimal hesitation.

Current Market Analysis:
- Symbol: {market_data.get('symbol', 'BTC/USDT:USDT')}
- Price: ${market_data.get('current_price', 0)}
- Volume: {market_data.get('volume', 0)}
- Account Balance: ${market_data.get('account_balance', 1000)}

Elite Scalper Recommendation: {json.dumps(decision, indent=2)}

Respond with the exact JSON format only. No additional commentary.
"""
        return prompt

# Usage in existing system:
# scalper_integration = ScalperIntegration()
# decision = scalper_integration.get_scalping_decision(market_data)
