#!/usr/bin/env python3
"""
Pre-Deployment Validation for Live Trading
Comprehensive validation before starting live autonomous trading
"""

import asyncio
import logging
import os
import sys
import yaml
import ccxt
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LiveTradingValidator:
    """
    Comprehensive validator for live trading deployment
    """
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = None
        self.exchange = None
        self.validation_results = {}
        
    async def run_validation(self):
        """Run comprehensive pre-deployment validation"""
        logger.info("🔍 Starting Live Trading Pre-Deployment Validation")
        logger.info("="*60)
        
        # Load configuration
        if not await self._load_configuration():
            return False
        
        # Validate API credentials
        if not await self._validate_api_credentials():
            return False
        
        # Validate account access
        if not await self._validate_account_access():
            return False
        
        # Validate trading permissions
        if not await self._validate_trading_permissions():
            return False
        
        # Validate account balance
        if not await self._validate_account_balance():
            return False
        
        # Validate risk parameters
        if not await self._validate_risk_parameters():
            return False
        
        # Validate safety systems
        if not await self._validate_safety_systems():
            return False
        
        # Validate system components
        if not await self._validate_system_components():
            return False

        # Validate symbol scanner if dynamic selection is enabled
        if not await self._validate_symbol_scanner():
            return False

        # Generate final report
        return self._generate_final_report()
    
    async def _load_configuration(self):
        """Load and validate configuration file"""
        try:
            logger.info("📋 Loading configuration...")
            
            if not Path(self.config_path).exists():
                logger.error(f"❌ Configuration file not found: {self.config_path}")
                return False
            
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            
            # Validate required fields
            required_fields = [
                'trading_mode', 'max_daily_loss',
                'max_position_size', 'max_leverage', 'exchange'
            ]

            # Check for either static symbols or dynamic symbol selection
            has_symbols = 'symbols' in self.config
            has_dynamic = self.config.get('dynamic_symbol_selection', {}).get('enabled', False)
            has_symbol_pool = 'symbol_pool' in self.config

            if not has_symbols and not (has_dynamic and has_symbol_pool):
                logger.error("❌ Must have either 'symbols' or dynamic symbol selection with 'symbol_pool'")
                return False

            missing_fields = [field for field in required_fields if field not in self.config]
            if missing_fields:
                logger.error(f"❌ Missing required configuration fields: {missing_fields}")
                return False
            
            # Validate trading mode
            if self.config['trading_mode'] != 'live':
                logger.error(f"❌ Trading mode must be 'live', got: {self.config['trading_mode']}")
                return False
            
            logger.info("✅ Configuration loaded and validated")
            return True
            
        except Exception as e:
            logger.error(f"❌ Configuration loading failed: {e}")
            return False
    
    async def _validate_api_credentials(self):
        """Validate exchange API credentials"""
        try:
            logger.info("🔑 Validating API credentials...")

            # Try to load credentials from credentials.yaml file
            credentials_file = Path("credentials.yaml")
            if credentials_file.exists():
                logger.info("📂 Loading credentials from credentials.yaml...")
                with open(credentials_file, 'r') as f:
                    import yaml
                    creds = yaml.safe_load(f)

                # Extract HTX credentials
                htx_creds = creds.get('htx', {})
                api_key = htx_creds.get('apiKey')
                secret_key = htx_creds.get('secret')
                passphrase = htx_creds.get('password', '')

                if not api_key or not secret_key:
                    logger.error("❌ HTX credentials not found in credentials.yaml")
                    return False

                logger.info("✅ Credentials loaded from credentials.yaml")

            else:
                # Fallback to environment variables
                logger.info("📂 Loading credentials from environment variables...")
                api_key = os.getenv('HTX_API_KEY')
                secret_key = os.getenv('HTX_SECRET_KEY')
                passphrase = os.getenv('HTX_PASSPHRASE', '')

                if not api_key or not secret_key:
                    logger.error("❌ Missing API credentials")
                    logger.error("Please ensure credentials are in credentials.yaml or environment variables")
                    return False

            # Initialize exchange
            exchange_config = {
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': False,  # Live trading
                'enableRateLimit': True,
            }

            # Add passphrase if available
            if passphrase:
                exchange_config['password'] = passphrase

            self.exchange = ccxt.htx(exchange_config)

            logger.info("✅ API credentials configured")
            return True

        except Exception as e:
            logger.error(f"❌ API credential validation failed: {e}")
            return False
    
    async def _validate_account_access(self):
        """Validate exchange account access"""
        try:
            logger.info("🏦 Validating account access...")
            
            # Test API connection
            await asyncio.sleep(0.1)  # Rate limiting
            balance = self.exchange.fetch_balance()
            
            if not balance:
                logger.error("❌ Unable to fetch account balance")
                return False
            
            logger.info("✅ Account access validated")
            logger.info(f"📊 Account currencies available: {list(balance.keys())}")
            return True
            
        except ccxt.AuthenticationError as e:
            logger.error(f"❌ Authentication failed: {e}")
            logger.error("Please check your API credentials")
            return False
        except ccxt.PermissionDenied as e:
            logger.error(f"❌ Permission denied: {e}")
            logger.error("Please check API key permissions")
            return False
        except Exception as e:
            logger.error(f"❌ Account access validation failed: {e}")
            return False
    
    async def _validate_trading_permissions(self):
        """Validate trading permissions"""
        try:
            logger.info("🔐 Validating trading permissions...")
            
            # Check if futures trading is available
            if hasattr(self.exchange, 'has') and self.exchange.has.get('future'):
                logger.info("✅ Futures trading supported")
            else:
                logger.warning("⚠️  Futures trading support unclear")
            
            # Test market data access for configured symbols
            symbols_to_test = []

            # Check if dynamic symbol selection is enabled
            if self.config.get('dynamic_symbol_selection', {}).get('enabled', False):
                logger.info("✅ Dynamic symbol selection enabled")
                symbols_to_test = self.config.get('symbol_pool', [])
                logger.info(f"📊 Testing symbol pool: {len(symbols_to_test)} symbols")
            else:
                symbols_to_test = self.config.get('symbols', [])
                logger.info(f"📊 Testing static symbols: {len(symbols_to_test)} symbols")

            # Test market data access
            successful_symbols = 0
            for symbol in symbols_to_test[:5]:  # Test first 5 symbols to avoid rate limits
                try:
                    ticker = self.exchange.fetch_ticker(symbol)
                    logger.info(f"✅ Market data access for {symbol}: ${ticker['last']:.2f}")
                    successful_symbols += 1
                except Exception as e:
                    logger.warning(f"⚠️  Cannot access market data for {symbol}: {e}")

            if successful_symbols == 0:
                logger.error("❌ No symbols accessible for trading")
                return False

            logger.info(f"✅ {successful_symbols}/{len(symbols_to_test[:5])} symbols accessible")
            
            logger.info("✅ Trading permissions validated")
            return True
            
        except Exception as e:
            logger.error(f"❌ Trading permissions validation failed: {e}")
            return False
    
    async def _validate_account_balance(self):
        """Validate account balance and margin for futures trading"""
        try:
            logger.info("💰 Validating futures account balance...")

            # First check spot balance
            logger.info("📊 Checking spot account balance:")
            spot_balance = self.exchange.fetch_balance()

            # Check futures balance specifically for USDT-M contracts
            logger.info("🔮 Checking futures account balance:")
            try:
                # For HTX, we need to check the futures account
                futures_balance = self.exchange.fetch_balance({'type': 'swap'})
                logger.info("✅ Futures balance retrieved successfully")

                # Check USDT in futures account
                futures_usdt = futures_balance.get('USDT', {})
                futures_usdt_free = futures_usdt.get('free', 0)
                futures_usdt_total = futures_usdt.get('total', 0)

                logger.info(f"🔮 Futures USDT Balance - Free: ${futures_usdt_free:.2f}, Total: ${futures_usdt_total:.2f}")

                # Also check for any other currencies in futures account
                logger.info("📊 Futures account currencies:")
                for currency, bal in futures_balance.items():
                    if currency not in ['info', 'free', 'used', 'total'] and isinstance(bal, dict):
                        free_amount = bal.get('free', 0)
                        total_amount = bal.get('total', 0)
                        if total_amount > 0:
                            logger.info(f"   {currency}: Free: {free_amount:.6f}, Total: {total_amount:.6f}")

                # Use futures balance for validation
                usdt_free = futures_usdt_free
                usdt_total = futures_usdt_total

            except Exception as e:
                logger.warning(f"⚠️  Could not fetch futures balance directly: {e}")
                logger.info("📊 Falling back to spot balance check...")

                # Fallback to spot balance
                usdt_balance = spot_balance.get('USDT', {})
                usdt_free = usdt_balance.get('free', 0)
                usdt_total = usdt_balance.get('total', 0)

                logger.info(f"💵 Spot USDT Balance - Free: ${usdt_free:.2f}, Total: ${usdt_total:.2f}")

            logger.info(f"💰 Trading Balance (USDT): ${usdt_free:.2f} available")

            # Check if we have sufficient balance for futures trading
            min_required = 10.0  # Reduced minimum for validation
            sufficient_balance = False

            if usdt_free >= min_required:
                sufficient_balance = True
                logger.info(f"✅ Sufficient USDT balance for futures trading: ${usdt_free:.2f}")
            else:
                logger.warning(f"⚠️  Low USDT balance for futures trading: ${usdt_free:.2f}")

            if not sufficient_balance:
                logger.warning(f"⚠️  Insufficient balance for futures trading")
                logger.warning("For USDT-M futures, you need USDT as collateral")

                # Ask user if they want to continue anyway
                print(f"\n⚠️  Futures account balance appears insufficient:")
                print(f"USDT Available: ${usdt_free:.2f}")
                print(f"Minimum Required: ${min_required:.2f}")
                print("Note: For USDT-M futures trading, USDT is required as collateral")

                response = input("\nDo you want to continue anyway? (yes/no): ").strip().lower()
                if response != 'yes':
                    logger.error("❌ User chose not to continue with insufficient balance")
                    return False

                logger.warning("⚠️  Continuing with low balance as requested by user")

            logger.info("✅ Account balance validation completed")
            return True

        except Exception as e:
            logger.error(f"❌ Account balance validation failed: {e}")
            return False
    
    async def _validate_risk_parameters(self):
        """Validate risk management parameters"""
        try:
            logger.info("⚖️  Validating risk parameters...")
            
            # Extract risk parameters
            max_daily_loss = abs(self.config['max_daily_loss'])
            max_position_size = self.config['max_position_size']
            max_leverage = self.config['max_leverage']
            max_positions = self.config['max_open_positions']
            min_balance = self.config['min_account_balance']
            
            # Validate parameter ranges
            if max_daily_loss > 25.0:  # More than $25 loss
                logger.error(f"❌ Daily loss limit too high for small account: ${max_daily_loss:.2f}")
                return False
            
            if max_position_size > 20.0:  # More than $20 per position
                logger.error(f"❌ Position size too large for small account: ${max_position_size:.2f}")
                return False
            
            if max_leverage > 5.0:  # More than 5x leverage
                logger.error(f"❌ Leverage too high for conservative trading: {max_leverage}x")
                return False
            
            if max_positions > 3:  # More than 3 positions
                logger.error(f"❌ Too many concurrent positions for small account: {max_positions}")
                return False
            
            # Log risk parameters
            logger.info(f"📊 Risk Parameters:")
            logger.info(f"   Max Daily Loss: ${max_daily_loss:.2f}")
            logger.info(f"   Max Position Size: ${max_position_size:.2f}")
            logger.info(f"   Max Leverage: {max_leverage}x")
            logger.info(f"   Max Positions: {max_positions}")
            logger.info(f"   Min Balance: ${min_balance:.2f}")
            
            logger.info("✅ Risk parameters validated")
            return True
            
        except Exception as e:
            logger.error(f"❌ Risk parameter validation failed: {e}")
            return False
    
    async def _validate_safety_systems(self):
        """Validate safety and monitoring systems"""
        try:
            logger.info("🛡️ Validating safety systems...")
            
            # Test safety monitor initialization
            from core.safety_monitor import SafetyMonitor
            safety_monitor = SafetyMonitor(self.config)
            
            # Test emergency procedures configuration
            emergency_config = self.config.get('emergency', {})
            required_emergency_features = [
                'auto_stop_on_daily_loss',
                'auto_stop_on_low_balance', 
                'emergency_close_all_positions'
            ]
            
            for feature in required_emergency_features:
                if not emergency_config.get(feature, False):
                    logger.error(f"❌ Emergency feature not enabled: {feature}")
                    return False
            
            logger.info("✅ Safety systems validated")
            return True
            
        except Exception as e:
            logger.error(f"❌ Safety system validation failed: {e}")
            return False
    
    async def _validate_system_components(self):
        """Validate system components"""
        try:
            logger.info("🔧 Validating system components...")
            
            # Test orchestrator initialization
            from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator
            orchestrator = AutonomousTradingOrchestrator(self.config)
            
            # Test LLM analyzer
            from core.enhanced_llm_analyzer import EnhancedLLMAnalyzer
            llm_analyzer = EnhancedLLMAnalyzer(self.config)
            
            # Test simulation executor (for testing)
            from execution.simulation_executor import SimulationExecutor
            sim_executor = SimulationExecutor()
            
            logger.info("✅ System components validated")
            return True

        except Exception as e:
            logger.error(f"❌ System component validation failed: {e}")
            return False

    async def _validate_symbol_scanner(self):
        """Validate symbol scanner for dynamic selection"""
        try:
            # Check if dynamic symbol selection is enabled
            dynamic_config = self.config.get('dynamic_symbol_selection', {})
            if not dynamic_config.get('enabled', False):
                logger.info("📊 Dynamic symbol selection disabled - skipping scanner validation")
                return True

            logger.info("🔍 Validating symbol scanner...")

            # Test symbol scanner initialization
            from symbol_scanner import SymbolScannerConfig

            symbol_pool = self.config.get('symbol_pool', [])
            scanner_config = self.config.get('symbol_scanner', {})

            if not symbol_pool:
                logger.error("❌ No symbol pool configured for dynamic selection")
                return False

            # Create scanner
            scanner = SymbolScannerConfig.create_scanner(
                market_api=self.exchange,
                symbols=symbol_pool[:5],  # Test with first 5 symbols
                weights=scanner_config.get('metrics_weights'),
                mode=dynamic_config.get('mode', 'scalping')
            )

            # Test scanner functionality
            logger.info("🧪 Testing symbol scanner...")
            best_symbols = scanner.find_best(n=3)

            if best_symbols:
                logger.info(f"✅ Symbol scanner working - Best symbols: {best_symbols}")

                # Validate scanner configuration
                filters = scanner_config.get('filters', {})
                logger.info(f"📋 Scanner filters: {len(filters)} criteria configured")

                # Log scanner settings
                weights = scanner_config.get('metrics_weights', {})
                logger.info(f"⚖️  Metrics weights: {weights}")

                return True
            else:
                logger.error("❌ Symbol scanner returned no results")
                return False

        except Exception as e:
            logger.error(f"❌ Symbol scanner validation failed: {e}")
            return False
    
    def _generate_final_report(self):
        """Generate final validation report"""
        logger.info("\n" + "="*60)
        logger.info("📊 LIVE TRADING PRE-DEPLOYMENT VALIDATION REPORT")
        logger.info("="*60)
        
        logger.info("✅ Configuration: VALIDATED")
        logger.info("✅ API Credentials: VALIDATED")
        logger.info("✅ Account Access: VALIDATED")
        logger.info("✅ Trading Permissions: VALIDATED")
        logger.info("✅ Account Balance: VALIDATED")
        logger.info("✅ Risk Parameters: VALIDATED")
        logger.info("✅ Safety Systems: VALIDATED")
        logger.info("✅ System Components: VALIDATED")

        # Check if dynamic symbol selection is enabled
        if self.config.get('dynamic_symbol_selection', {}).get('enabled', False):
            logger.info("✅ Dynamic Symbol Scanner: VALIDATED")
            symbol_pool = self.config.get('symbol_pool', [])
            logger.info(f"📊 Symbol Pool: {len(symbol_pool)} USDT-M futures pairs")
        else:
            logger.info("📊 Static Symbol Configuration: VALIDATED")
        
        logger.info("\n🎯 DEPLOYMENT STATUS: READY FOR LIVE TRADING")
        logger.info("="*60)
        
        # Final safety reminder
        logger.warning("\n🚨 LIVE TRADING SAFETY REMINDER:")
        logger.warning("• This will trade with REAL MONEY")
        logger.warning("• Monitor the system continuously")
        logger.warning("• Emergency stop procedures are active")
        logger.warning("• Risk limits are enforced")
        logger.warning("• Start with close monitoring")
        
        return True

async def main():
    """Main validation function"""
    config_path = "config/live_production.yaml"
    
    if not Path(config_path).exists():
        logger.error(f"❌ Configuration file not found: {config_path}")
        sys.exit(1)
    
    validator = LiveTradingValidator(config_path)
    success = await validator.run_validation()
    
    if success:
        logger.info("\n✅ PRE-DEPLOYMENT VALIDATION PASSED")
        logger.info("System is ready for live trading deployment")
    else:
        logger.error("\n❌ PRE-DEPLOYMENT VALIDATION FAILED")
        logger.error("Please fix the issues before deploying")
        sys.exit(1)

if __name__ == '__main__':
    asyncio.run(main())
