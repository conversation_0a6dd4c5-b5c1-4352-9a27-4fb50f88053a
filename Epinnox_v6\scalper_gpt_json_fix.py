
def fix_scalper_gpt_response(response_text: str) -> Dict[str, Any]:
    """Fix ScalperGPT JSON parsing issues"""
    try:
        # Clean up common JSON issues
        cleaned_text = response_text.strip()
        
        # Remove markdown code blocks if present
        if cleaned_text.startswith('```'):
            lines = cleaned_text.split('\n')
            cleaned_text = '\n'.join(lines[1:-1])
        
        # Fix common field name inconsistencies
        cleaned_text = cleaned_text.replace('"ACTION":', '"action":')
        cleaned_text = cleaned_text.replace('"QUANTITY":', '"quantity":')
        cleaned_text = cleaned_text.replace('"LEVERAGE":', '"leverage":')
        cleaned_text = cleaned_text.replace('"STOP_LOSS":', '"stop_loss":')
        cleaned_text = cleaned_text.replace('"TAKE_PROFIT":', '"take_profit":')
        cleaned_text = cleaned_text.replace('"RISK_PCT":', '"risk_pct":')
        
        # Parse JSON
        parsed_data = json.loads(cleaned_text)
        
        # Validate and fix required fields
        if 'action' not in parsed_data:
            parsed_data['action'] = 'WAIT'
        
        if 'quantity' not in parsed_data or parsed_data['quantity'] <= 0:
            # Calculate default quantity based on balance
            balance = 1000.0  # Default balance
            risk_pct = parsed_data.get('risk_pct', 2.0) / 100.0
            price = parsed_data.get('price', 50000.0)
            parsed_data['quantity'] = (balance * risk_pct) / price
        
        # Ensure numeric fields are properly typed
        numeric_fields = ['quantity', 'leverage', 'stop_loss', 'take_profit', 'risk_pct']
        for field in numeric_fields:
            if field in parsed_data and parsed_data[field] is not None:
                try:
                    parsed_data[field] = float(parsed_data[field])
                except (ValueError, TypeError):
                    parsed_data[field] = 0.0
        
        return parsed_data
        
    except json.JSONDecodeError as e:
        # Return safe default response
        return {
            'action': 'WAIT',
            'quantity': 0.0,
            'leverage': 1.0,
            'stop_loss': 0.0,
            'take_profit': 0.0,
            'risk_pct': 1.0,
            'error': f'JSON parsing failed: {e}'
        }
    except Exception as e:
        return {
            'action': 'WAIT',
            'quantity': 0.0,
            'error': f'Response processing failed: {e}'
        }
