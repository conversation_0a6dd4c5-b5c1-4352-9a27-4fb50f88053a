"""
Live Trading Tab for Epinnox v6 Trading System
Real-time trading analysis and signal hierarchy display
"""

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
except ImportError:
    print("PyQt5 not installed. Install with: pip install PyQt5")
    raise

from .base_tab import BaseTab
from .matrix_theme import MatrixTheme
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class LiveTradingTab(BaseTab):
    """Live Trading tab with real-time system integration"""
    
    # Signals
    analysis_requested = pyqtSignal(str, bool)  # symbol, use_live_data
    analysis_stopped = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_data = {}
        
    def setup_ui(self):
        """Setup the Live Trading tab UI"""
        layout = QHBoxLayout(self)
        
        # Left panel - Controls and Symbol Selection
        left_panel = self.create_left_panel()
        left_panel.setMaximumWidth(350)
        
        # Right panel - Detailed Analysis
        right_panel = self.create_right_panel()
        
        layout.addWidget(left_panel)
        layout.addWidget(right_panel, 2)
    
    def create_left_panel(self):
        """Create the left control panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Symbol selection group
        symbol_group = self.create_symbol_selection_group()
        
        # Current Analysis Results
        results_group = self.create_analysis_results_group()
        
        # ML Models Status
        ml_group = self.create_ml_status_group()
        
        # Leverage Analysis
        leverage_group = self.create_leverage_analysis_group()
        
        layout.addWidget(symbol_group)
        layout.addWidget(results_group)
        layout.addWidget(ml_group)
        layout.addWidget(leverage_group)
        layout.addStretch()
        
        return panel
    
    def create_symbol_selection_group(self):
        """Create symbol selection controls"""
        group = self.create_matrix_group_box("Symbol Selection")
        layout = QVBoxLayout(group)
        
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems([
            "DOGE/USDT", "DOGE/USDT:USDT", "BTC/USDT", "BTC/USDT:USDT",
            "ETH/USDT", "ETH/USDT:USDT", "ADA/USDT", "SOL/USDT"
        ])
        self.symbol_combo.setCurrentText("DOGE/USDT")
        
        self.live_data_checkbox = QCheckBox("Use Live Data")
        self.auto_refresh_checkbox = QCheckBox("Auto Refresh (30s)")
        self.auto_refresh_checkbox.setChecked(True)
        
        self.analyze_button = self.create_matrix_button("ANALYZE SYMBOL", self.analyze_symbol)
        self.stop_button = self.create_matrix_button("STOP ANALYSIS", self.stop_analysis, False)
        
        layout.addWidget(QLabel("Trading Symbol:"))
        layout.addWidget(self.symbol_combo)
        layout.addWidget(self.live_data_checkbox)
        layout.addWidget(self.auto_refresh_checkbox)
        layout.addWidget(self.analyze_button)
        layout.addWidget(self.stop_button)
        
        return group
    
    def create_analysis_results_group(self):
        """Create current analysis results display"""
        group = self.create_matrix_group_box("Current Analysis")
        layout = QVBoxLayout(group)
        
        self.decision_label = self.create_matrix_label("Decision: WAITING...", MatrixTheme.FONT_SIZE_LARGE)
        self.confidence_label = QLabel("Confidence: --")
        self.timestamp_label = QLabel("Last Update: --")
        
        layout.addWidget(self.decision_label)
        layout.addWidget(self.confidence_label)
        layout.addWidget(self.timestamp_label)
        
        return group
    
    def create_ml_status_group(self):
        """Create ML models status table"""
        group = self.create_matrix_group_box("ML Models Status")
        layout = QVBoxLayout(group)
        
        self.ml_status_table = QTableWidget(3, 3)
        self.ml_status_table.setHorizontalHeaderLabels(["Model", "Decision", "Confidence"])
        self.ml_status_table.setMaximumHeight(120)
        self.apply_matrix_table_styling(self.ml_status_table)
        
        layout.addWidget(self.ml_status_table)
        return group
    
    def create_leverage_analysis_group(self):
        """Create leverage analysis display"""
        group = self.create_matrix_group_box("Leverage Analysis")
        layout = QVBoxLayout(group)
        
        self.max_leverage_label = QLabel("Max Available: --")
        self.recommended_leverage_label = QLabel("Recommended: --")
        self.effective_leverage_label = QLabel("Effective: --")
        self.position_size_label = QLabel("Position Size: --")
        self.risk_label = QLabel("Risk per Trade: --")
        
        layout.addWidget(self.max_leverage_label)
        layout.addWidget(self.recommended_leverage_label)
        layout.addWidget(self.effective_leverage_label)
        layout.addWidget(self.position_size_label)
        layout.addWidget(self.risk_label)
        
        return group
    
    def create_right_panel(self):
        """Create the right analysis panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Signal Hierarchy Display
        hierarchy_group = self.create_signal_hierarchy_group()
        
        # Market Analysis
        market_group = self.create_market_analysis_group()
        
        # Analysis Log
        log_group = self.create_analysis_log_group()
        
        # Risk Warnings
        warnings_group = self.create_warnings_group()
        
        layout.addWidget(hierarchy_group)
        layout.addWidget(market_group)
        layout.addWidget(log_group)
        layout.addWidget(warnings_group)
        
        return panel
    
    def create_signal_hierarchy_group(self):
        """Create signal hierarchy analysis table"""
        group = self.create_matrix_group_box("Signal Hierarchy Analysis")
        layout = QVBoxLayout(group)
        
        self.hierarchy_table = QTableWidget(5, 4)
        self.hierarchy_table.setHorizontalHeaderLabels(["Source", "Decision", "Confidence", "Weight"])
        self.hierarchy_table.setMaximumHeight(180)
        self.apply_matrix_table_styling(self.hierarchy_table)
        
        layout.addWidget(self.hierarchy_table)
        return group
    
    def create_market_analysis_group(self):
        """Create market analysis display"""
        group = self.create_matrix_group_box("Market Analysis")
        layout = QVBoxLayout(group)
        
        self.market_regime_label = QLabel("Market Regime: --")
        self.trend_strength_label = QLabel("Trend Strength: --")
        self.volatility_label = QLabel("Volatility: --")
        self.liquidity_label = QLabel("Liquidity Score: --")
        
        layout.addWidget(self.market_regime_label)
        layout.addWidget(self.trend_strength_label)
        layout.addWidget(self.volatility_label)
        layout.addWidget(self.liquidity_label)
        
        return group
    
    def create_analysis_log_group(self):
        """Create analysis log display"""
        group = self.create_matrix_group_box("Analysis Log")
        layout = QVBoxLayout(group)
        
        self.analysis_log = QTextEdit()
        self.analysis_log.setMaximumHeight(200)
        self.analysis_log.setReadOnly(True)
        
        layout.addWidget(self.analysis_log)
        return group
    
    def create_warnings_group(self):
        """Create risk warnings display"""
        group = self.create_matrix_group_box("Risk Warnings")
        layout = QVBoxLayout(group)
        
        self.warnings_list = QTextEdit()
        self.warnings_list.setMaximumHeight(100)
        self.warnings_list.setReadOnly(True)
        self.warnings_list.setStyleSheet(f"color: {MatrixTheme.YELLOW};")
        
        layout.addWidget(self.warnings_list)
        return group
    
    def analyze_symbol(self):
        """Start symbol analysis"""
        symbol = self.symbol_combo.currentText()
        use_live_data = self.live_data_checkbox.isChecked()
        
        self.analyze_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        
        self.analysis_requested.emit(symbol, use_live_data)
        self.log_message(f"Analysis requested for {symbol}")
    
    def stop_analysis(self):
        """Stop current analysis"""
        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        self.analysis_stopped.emit()
        self.log_message("Analysis stopped by user")
    
    def update_analysis_display(self, result: dict):
        """Update the display with analysis results"""
        try:
            self.current_data = result
            data = result.get('data', {})
            
            # Update basic decision info
            decision = result.get('decision', 'UNKNOWN')
            self.decision_label.setText(f"Decision: {decision}")
            
            # Color code the decision
            if decision == 'LONG':
                color = MatrixTheme.GREEN
            elif decision == 'SHORT':
                color = MatrixTheme.RED
            else:
                color = MatrixTheme.YELLOW
            
            self.decision_label.setStyleSheet(f"color: {color}; font-size: {MatrixTheme.FONT_SIZE_LARGE}px; font-weight: bold;")
            
            # Update other displays
            self.update_confidence_display(data)
            self.update_ml_models_table(data)
            self.update_leverage_display(data)
            self.update_hierarchy_table(data)
            self.update_market_analysis(data)
            self.update_warnings_display(data)
            
            self.timestamp_label.setText(f"Last Update: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            self.log_message(f"Error updating display: {e}", logging.ERROR)
    
    def update_confidence_display(self, data: dict):
        """Update confidence display"""
        confidence = "Unknown"
        if 'signal_hierarchy' in data:
            confidence = f"{data['signal_hierarchy'].get('confidence', 0) * 100:.1f}%"
        self.confidence_label.setText(f"Confidence: {confidence}")
    
    def update_ml_models_table(self, data: dict):
        """Update ML models status table"""
        # Implementation will be added in the next step
        pass
    
    def update_leverage_display(self, data: dict):
        """Update leverage analysis display"""
        # Implementation will be added in the next step
        pass
    
    def update_hierarchy_table(self, data: dict):
        """Update signal hierarchy table with weights"""
        # Implementation will be added in the next step
        pass
    
    def update_market_analysis(self, data: dict):
        """Update market analysis display"""
        # Implementation will be added in the next step
        pass
    
    def update_warnings_display(self, data: dict):
        """Update risk warnings display"""
        # Implementation will be added in the next step
        pass
