"""
Social Media Monitoring Module for Trading Systems
This module monitors social media platforms for cryptocurrency sentiment and trends.
"""

import logging
import asyncio
import aiohttp
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import re
import json
import hashlib
import time
from urllib.parse import quote_plus
import requests

logger = logging.getLogger(__name__)

class SocialPlatform(Enum):
    """Supported social media platforms"""
    TWITTER = "twitter"
    REDDIT = "reddit"
    TELEGRAM = "telegram"
    DISCORD = "discord"
    YOUTUBE = "youtube"

@dataclass
class SocialPost:
    """Social media post data structure"""
    id: str
    platform: SocialPlatform
    content: str
    author: str
    published_at: datetime
    engagement_score: float  # likes, retweets, comments combined
    follower_count: Optional[int]
    symbols_mentioned: List[str]
    hashtags: List[str]
    mentions: List[str]
    url: Optional[str]
    sentiment_score: Optional[float]
    influence_score: float
    post_hash: str
    scraped_at: datetime

class RedditAPI:
    """Reddit API integration for cryptocurrency subreddits"""
    
    def __init__(self):
        self.base_url = "https://www.reddit.com/r"
        self.headers = {
            'User-Agent': 'EpinnoxTrading/1.0 (by /u/CryptoTrader)'
        }
        
        # Crypto-related subreddits
        self.crypto_subreddits = [
            'CryptoCurrency', 'Bitcoin', 'ethereum', 'dogecoin', 'SatoshiStreetBets',
            'CryptoMarkets', 'altcoin', 'CryptoMoonShots', 'CryptoCurrencyTrading',
            'BitcoinMarkets', 'ethtrader', 'CardanoTrading'
        ]
    
    async def fetch_subreddit_posts(self, subreddit: str, sort: str = 'hot', limit: int = 25) -> List[Dict[str, Any]]:
        """
        Fetch posts from a subreddit
        
        Args:
            subreddit: Subreddit name
            sort: Sort method ('hot', 'new', 'top')
            limit: Number of posts to fetch
            
        Returns:
            List of post data
        """
        try:
            url = f"{self.base_url}/{subreddit}/{sort}.json?limit={limit}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status != 200:
                        logger.warning(f"Failed to fetch {subreddit}: status {response.status}")
                        return []
                    
                    data = await response.json()
                    posts = []
                    
                    for post_data in data.get('data', {}).get('children', []):
                        post = post_data.get('data', {})
                        
                        # Skip if deleted or removed
                        if post.get('removed_by_category') or post.get('selftext') == '[deleted]':
                            continue
                        
                        posts.append({
                            'id': post.get('id', ''),
                            'title': post.get('title', ''),
                            'selftext': post.get('selftext', ''),
                            'author': post.get('author', ''),
                            'created_utc': post.get('created_utc', 0),
                            'score': post.get('score', 0),
                            'num_comments': post.get('num_comments', 0),
                            'upvote_ratio': post.get('upvote_ratio', 0.5),
                            'url': f"https://reddit.com{post.get('permalink', '')}",
                            'subreddit': subreddit
                        })
                    
                    return posts
                    
        except Exception as e:
            logger.error(f"Error fetching Reddit posts from {subreddit}: {e}")
            return []

class TwitterAPI:
    """Twitter/X API integration (placeholder - requires proper API credentials)"""
    
    def __init__(self, api_key: str = None, api_secret: str = None, bearer_token: str = None):
        """
        Initialize Twitter API client
        
        Note: This is a placeholder implementation. 
        For production use, you'll need proper Twitter API credentials.
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.bearer_token = bearer_token
        self.base_url = "https://api.twitter.com/2"
        
        # Crypto influencers and keywords to monitor
        self.crypto_keywords = [
            'bitcoin', 'btc', 'ethereum', 'eth', 'dogecoin', 'doge', 'crypto', 'cryptocurrency',
            'altcoin', 'defi', 'nft', 'blockchain', 'trading', 'hodl', 'bullish', 'bearish'
        ]
        
        self.crypto_influencers = [
            'elonmusk', 'michael_saylor', 'APompliano', 'VitalikButerin',
            'satoshi_nakomoto', 'CoinbaseWallet', 'binance', 'cz_binance'
        ]
    
    async def search_tweets(self, query: str, max_results: int = 100) -> List[Dict[str, Any]]:
        """
        Search for tweets (placeholder implementation)
        
        Args:
            query: Search query
            max_results: Maximum number of results
            
        Returns:
            List of tweet data
        """
        # This is a placeholder implementation
        # In production, you would use the actual Twitter API v2
        logger.warning("Twitter API integration is a placeholder. Requires proper API credentials.")
        
        # Mock data for demonstration
        mock_tweets = [
            {
                'id': '1234567890',
                'text': f'Sample tweet about {query} - this is bullish! 🚀',
                'author_username': 'crypto_trader',
                'created_at': datetime.now().isoformat(),
                'public_metrics': {
                    'retweet_count': 50,
                    'like_count': 200,
                    'reply_count': 25,
                    'quote_count': 10
                }
            }
        ]
        
        return mock_tweets
    
    async def get_user_timeline(self, username: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """Get user timeline (placeholder implementation)"""
        logger.warning("Twitter user timeline is a placeholder implementation")
        return []

class SocialMediaMonitor:
    """Main social media monitoring class"""
    
    def __init__(self, platforms: List[SocialPlatform] = None):
        """
        Initialize social media monitor
        
        Args:
            platforms: List of platforms to monitor
        """
        self.platforms = platforms or [SocialPlatform.REDDIT, SocialPlatform.TWITTER]
        self.reddit_api = RedditAPI()
        self.twitter_api = TwitterAPI()  # Would need credentials for production
        
        self.crypto_symbols = ['BTC', 'ETH', 'DOGE', 'ADA', 'SOL', 'MATIC', 'DOT', 'LINK', 'UNI', 'LTC']
        
        # Pattern for extracting crypto symbols
        self.symbol_patterns = {
            'btc': r'\b(?:bitcoin|btc|\$btc)\b',
            'eth': r'\b(?:ethereum|eth|\$eth)\b',
            'doge': r'\b(?:dogecoin|doge|\$doge)\b',
            'ada': r'\b(?:cardano|ada|\$ada)\b',
            'sol': r'\b(?:solana|sol|\$sol)\b',
            'matic': r'\b(?:polygon|matic|\$matic)\b',
            'dot': r'\b(?:polkadot|dot|\$dot)\b',
            'link': r'\b(?:chainlink|link|\$link)\b',
            'uni': r'\b(?:uniswap|uni|\$uni)\b',
            'ltc': r'\b(?:litecoin|ltc|\$ltc)\b'
        }
    
    def extract_hashtags(self, text: str) -> List[str]:
        """Extract hashtags from text"""
        return re.findall(r'#(\w+)', text, re.IGNORECASE)
    
    def extract_mentions(self, text: str) -> List[str]:
        """Extract mentions from text"""
        return re.findall(r'@(\w+)', text, re.IGNORECASE)
    
    def extract_symbols(self, text: str) -> List[str]:
        """Extract cryptocurrency symbols from text"""
        text_lower = text.lower()
        found_symbols = []
        
        for symbol, pattern in self.symbol_patterns.items():
            if re.search(pattern, text_lower, re.IGNORECASE):
                found_symbols.append(symbol.upper())
        
        return list(set(found_symbols))
    
    def calculate_engagement_score(self, platform: SocialPlatform, metrics: Dict[str, Any]) -> float:
        """
        Calculate engagement score based on platform-specific metrics
        
        Args:
            platform: Social media platform
            metrics: Platform-specific metrics
            
        Returns:
            Normalized engagement score (0.0 to 1.0)
        """
        if platform == SocialPlatform.REDDIT:
            score = metrics.get('score', 0)
            comments = metrics.get('num_comments', 0)
            upvote_ratio = metrics.get('upvote_ratio', 0.5)
            
            # Normalize Reddit engagement
            engagement = (score * upvote_ratio) + (comments * 2)
            return min(1.0, engagement / 1000.0)  # Normalize to 0-1
            
        elif platform == SocialPlatform.TWITTER:
            likes = metrics.get('like_count', 0)
            retweets = metrics.get('retweet_count', 0)
            replies = metrics.get('reply_count', 0)
            quotes = metrics.get('quote_count', 0)
            
            # Weighted Twitter engagement
            engagement = likes + (retweets * 3) + (replies * 2) + (quotes * 2)
            return min(1.0, engagement / 10000.0)  # Normalize to 0-1
        
        return 0.0
    
    def calculate_influence_score(self, platform: SocialPlatform, author: str, 
                                follower_count: Optional[int], engagement_score: float) -> float:
        """
        Calculate influence score based on author metrics
        
        Args:
            platform: Social media platform
            author: Author username
            follower_count: Number of followers (if available)
            engagement_score: Engagement score for the post
            
        Returns:
            Influence score (0.0 to 1.0)
        """
        base_score = engagement_score
        
        # Boost for follower count
        if follower_count:
            if follower_count > 1000000:  # 1M+ followers
                base_score += 0.3
            elif follower_count > 100000:  # 100K+ followers
                base_score += 0.2
            elif follower_count > 10000:   # 10K+ followers
                base_score += 0.1
        
        # Boost for known crypto influencers
        known_influencers = ['elonmusk', 'michael_saylor', 'APompliano', 'VitalikButerin']
        if author.lower() in [inf.lower() for inf in known_influencers]:
            base_score += 0.4
        
        return min(1.0, base_score)
    
    def get_post_hash(self, platform: SocialPlatform, post_id: str, content: str) -> str:
        """Generate unique hash for post to avoid duplicates"""
        content_hash = f"{platform.value}{post_id}{content}"
        return hashlib.md5(content_hash.encode()).hexdigest()
    
    async def monitor_reddit(self, symbols: List[str] = None, max_posts_per_subreddit: int = 25) -> List[SocialPost]:
        """
        Monitor Reddit for cryptocurrency discussions
        
        Args:
            symbols: List of symbols to filter for
            max_posts_per_subreddit: Maximum posts to fetch per subreddit
            
        Returns:
            List of SocialPost objects
        """
        if symbols is None:
            symbols = self.crypto_symbols
        
        all_posts = []
        
        for subreddit in self.reddit_api.crypto_subreddits[:5]:  # Limit to avoid rate limits
            try:
                posts_data = await self.reddit_api.fetch_subreddit_posts(
                    subreddit, sort='hot', limit=max_posts_per_subreddit
                )
                
                for post_data in posts_data:
                    # Combine title and content
                    content = f"{post_data['title']} {post_data['selftext']}"
                    
                    # Extract symbols
                    symbols_mentioned = self.extract_symbols(content)
                    
                    # Skip if no relevant symbols
                    if symbols and not any(symbol.upper() in symbols_mentioned for symbol in symbols):
                        continue
                    
                    # Calculate metrics
                    engagement_score = self.calculate_engagement_score(
                        SocialPlatform.REDDIT, 
                        {
                            'score': post_data['score'],
                            'num_comments': post_data['num_comments'],
                            'upvote_ratio': post_data['upvote_ratio']
                        }
                    )
                    
                    influence_score = self.calculate_influence_score(
                        SocialPlatform.REDDIT,
                        post_data['author'],
                        None,  # Reddit doesn't provide follower count easily
                        engagement_score
                    )
                    
                    # Create SocialPost object
                    post = SocialPost(
                        id=post_data['id'],
                        platform=SocialPlatform.REDDIT,
                        content=content[:1000],  # Limit content length
                        author=post_data['author'],
                        published_at=datetime.fromtimestamp(post_data['created_utc']),
                        engagement_score=engagement_score,
                        follower_count=None,
                        symbols_mentioned=symbols_mentioned,
                        hashtags=self.extract_hashtags(content),
                        mentions=self.extract_mentions(content),
                        url=post_data['url'],
                        sentiment_score=None,  # Will be filled by sentiment analyzer
                        influence_score=influence_score,
                        post_hash=self.get_post_hash(SocialPlatform.REDDIT, post_data['id'], content),
                        scraped_at=datetime.now()
                    )
                    
                    all_posts.append(post)
                
                # Small delay between subreddits
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error monitoring Reddit subreddit {subreddit}: {e}")
        
        logger.info(f"Monitored Reddit: found {len(all_posts)} relevant posts")
        return all_posts
    
    async def monitor_twitter(self, symbols: List[str] = None, max_tweets: int = 100) -> List[SocialPost]:
        """
        Monitor Twitter for cryptocurrency discussions (placeholder)
        
        Args:
            symbols: List of symbols to filter for
            max_tweets: Maximum tweets to fetch
            
        Returns:
            List of SocialPost objects
        """
        logger.warning("Twitter monitoring is a placeholder - requires API credentials")
        
        # This would require proper Twitter API implementation
        # For now, return empty list
        return []
    
    async def monitor_all_platforms(self, symbols: List[str] = None) -> List[SocialPost]:
        """
        Monitor all configured social media platforms
        
        Args:
            symbols: List of symbols to filter for
            
        Returns:
            List of all SocialPost objects
        """
        all_posts = []
        
        # Monitor Reddit
        if SocialPlatform.REDDIT in self.platforms:
            reddit_posts = await self.monitor_reddit(symbols)
            all_posts.extend(reddit_posts)
        
        # Monitor Twitter (placeholder)
        if SocialPlatform.TWITTER in self.platforms:
            twitter_posts = await self.monitor_twitter(symbols)
            all_posts.extend(twitter_posts)
        
        # Sort by influence score and published time
        all_posts.sort(key=lambda x: (x.influence_score, x.published_at), reverse=True)
        
        logger.info(f"Total social media posts monitored: {len(all_posts)}")
        return all_posts
    
    def filter_posts_by_engagement(self, posts: List[SocialPost], min_engagement: float = 0.1) -> List[SocialPost]:
        """Filter posts by minimum engagement score"""
        return [post for post in posts if post.engagement_score >= min_engagement]
    
    def filter_posts_by_influence(self, posts: List[SocialPost], min_influence: float = 0.2) -> List[SocialPost]:
        """Filter posts by minimum influence score"""
        return [post for post in posts if post.influence_score >= min_influence]
    
    def filter_posts_by_time(self, posts: List[SocialPost], hours: int = 24) -> List[SocialPost]:
        """Filter posts by publication time"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [post for post in posts if post.published_at >= cutoff_time]
    
    def get_social_summary(self, posts: List[SocialPost]) -> Dict[str, Any]:
        """
        Generate summary statistics for social media posts
        
        Args:
            posts: List of posts to summarize
            
        Returns:
            Dictionary with summary statistics
        """
        if not posts:
            return {
                'total_posts': 0,
                'platforms': {},
                'symbols': {},
                'avg_engagement': 0.0,
                'avg_influence': 0.0,
                'time_range': None
            }
        
        # Platform distribution
        platform_counts = {}
        for post in posts:
            platform = post.platform.value
            platform_counts[platform] = platform_counts.get(platform, 0) + 1
        
        # Symbol distribution
        symbol_counts = {}
        for post in posts:
            for symbol in post.symbols_mentioned:
                symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
        
        # Time range
        published_times = [post.published_at for post in posts]
        time_range = {
            'earliest': min(published_times),
            'latest': max(published_times)
        } if published_times else None
        
        # Average scores
        avg_engagement = np.mean([post.engagement_score for post in posts])
        avg_influence = np.mean([post.influence_score for post in posts])
        
        return {
            'total_posts': len(posts),
            'platforms': platform_counts,
            'symbols': dict(sorted(symbol_counts.items(), key=lambda x: x[1], reverse=True)),
            'avg_engagement': avg_engagement,
            'avg_influence': avg_influence,
            'time_range': time_range,
            'high_engagement_count': sum(1 for p in posts if p.engagement_score > 0.5),
            'high_influence_count': sum(1 for p in posts if p.influence_score > 0.5),
            'recent_count': sum(1 for p in posts if (datetime.now() - p.published_at).total_seconds() < 3600)
        }

# Example usage
async def main():
    """Example usage of the social media monitor"""
    
    # Initialize monitor
    monitor = SocialMediaMonitor(platforms=[SocialPlatform.REDDIT])
    
    # Monitor for specific symbols
    symbols = ['DOGE', 'BTC', 'ETH']
    posts = await monitor.monitor_all_platforms(symbols)
    
    print(f"Found {len(posts)} social media posts")
    
    # Filter high engagement posts
    high_engagement = monitor.filter_posts_by_engagement(posts, min_engagement=0.2)
    print(f"High engagement posts: {len(high_engagement)}")
    
    # Filter recent posts
    recent_posts = monitor.filter_posts_by_time(posts, hours=24)
    print(f"Recent posts (24h): {len(recent_posts)}")
    
    # Get summary
    summary = monitor.get_social_summary(posts)
    print(f"Social media summary: {summary}")
    
    # Print top posts
    print("\nTop Social Media Posts:")
    for i, post in enumerate(posts[:5]):
        print(f"{i+1}. {post.content[:100]}...")
        print(f"   Platform: {post.platform.value}")
        print(f"   Author: {post.author}")
        print(f"   Engagement: {post.engagement_score:.2f}")
        print(f"   Influence: {post.influence_score:.2f}")
        print(f"   Symbols: {post.symbols_mentioned}")
        print()

if __name__ == "__main__":
    asyncio.run(main())
