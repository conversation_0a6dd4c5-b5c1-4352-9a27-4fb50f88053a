#!/usr/bin/env python3
"""
Test the autonomous configuration fix
"""

try:
    print("Testing autonomous configuration fix...")
    
    # Test 1: Import the config manager
    from config.autonomous_config import config_manager
    print("✅ Configuration manager imported successfully")
    
    # Test 2: Get all configuration
    config = config_manager.get_all_config()
    print(f"✅ Config loaded: {list(config.keys()) if config else 'None'}")
    
    # Test 3: Check trading config specifically
    if config and 'trading' in config:
        trading_config = config['trading']
        print(f"✅ Trading config keys: {list(trading_config.keys())}")
        print(f"✅ autonomous_mode setting: {trading_config.get('autonomous_mode', 'NOT FOUND')}")
    else:
        print("⚠️ Trading config not found in main config")
    
    # Test 4: Try loading from YAML directly
    try:
        import yaml
        with open('configs/autonomous_trading.yaml', 'r') as f:
            yaml_config = yaml.safe_load(f)
        
        if 'trading' in yaml_config and 'autonomous_mode' in yaml_config['trading']:
            print(f"✅ YAML autonomous_mode: {yaml_config['trading']['autonomous_mode']}")
            
            # Test creating TradingConfig with autonomous_mode
            from config.autonomous_config import TradingConfig
            trading_data = yaml_config['trading']
            print(f"✅ YAML trading keys: {list(trading_data.keys())}")
            
            try:
                trading_config_obj = TradingConfig(**trading_data)
                print(f"✅ TradingConfig object created successfully!")
                print(f"✅ Object autonomous_mode: {trading_config_obj.autonomous_mode}")
            except Exception as e:
                print(f"❌ TradingConfig creation failed: {e}")
        else:
            print("⚠️ autonomous_mode not found in YAML")
            
    except Exception as e:
        print(f"❌ YAML test failed: {e}")

except Exception as e:
    print(f"❌ Main test failed: {e}")
    import traceback
    traceback.print_exc()

print("\nTest completed.")
