#!/usr/bin/env python3
"""
Comprehensive test script to verify all ScalperGPT fixes
Tests widget initialization, analysis engine, performance optimization, and error handling
"""

import sys
import os
import time
sys.path.append('.')

def test_scalper_widget_initialization():
    """Test ScalperGPT widget initialization"""
    print("🧪 Testing ScalperGPT widget initialization...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        
        class MockInterface(EpinnoxTradingInterface):
            def __init__(self):
                # Initialize minimal required attributes
                self.emergency_stop_active = False
                self.emergency_risk_mode = False
                
                # Mock symbol combo
                class MockCombo:
                    def currentText(self):
                        return 'BTC/USDT:USDT'
                
                self.symbol_combo = MockCombo()
                
                # Initialize ScalperGPT widgets
                self.setup_scalper_gpt_widgets()
                
            def log_message(self, msg):
                print(f"WIDGET LOG: {msg}")
        
        interface = MockInterface()
        
        # Test that all ScalperGPT widgets are created
        required_widgets = [
            'scalper_spread_label', 'spread_pct_label', 'tick_atr_label',
            'trade_flow_label', 'volume_momentum_label', 'data_latency_label',
            'scalper_spread_quality_label', 'scalping_suitability_label',
            'scalper_volatility_state_label', 'scalper_order_flow_label',
            'scalper_decision_quality_label', 'scalper_total_trades_label'
        ]
        
        missing_widgets = []
        for widget_name in required_widgets:
            if not hasattr(interface, widget_name):
                missing_widgets.append(widget_name)
        
        if not missing_widgets:
            print("   ✅ All ScalperGPT widgets initialized successfully")
            print(f"   ✅ Widget count: {len(required_widgets)}")
            return True
        else:
            print(f"   ❌ Missing widgets: {missing_widgets}")
            return False
        
    except Exception as e:
        print(f"   ❌ Error testing widget initialization: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_analysis_engine():
    """Test enhanced ScalperGPT analysis engine"""
    print("\n🧪 Testing enhanced analysis engine...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        
        class MockInterface(EpinnoxTradingInterface):
            def __init__(self):
                self.emergency_stop_active = False
                self.emergency_risk_mode = False
                
                class MockCombo:
                    def currentText(self):
                        return 'BTC/USDT:USDT'
                
                self.symbol_combo = MockCombo()
                
            def log_message(self, msg):
                print(f"ANALYSIS LOG: {msg}")
        
        interface = MockInterface()
        
        # Test enhanced volatility clustering
        market_data = {
            'tick_atr': 0.002,
            'volume_momentum': 25.0,
            'spread_pct': 0.3
        }
        
        clustering = interface.detect_volatility_clustering(market_data)
        print(f"   ✅ Volatility clustering: {clustering}")
        
        # Test dynamic thresholds
        atr_threshold = interface.get_dynamic_atr_threshold('BTC/USDT:USDT')
        volume_threshold = interface.get_dynamic_volume_threshold('BTC/USDT:USDT')
        print(f"   ✅ Dynamic thresholds - ATR: {atr_threshold}, Volume: {volume_threshold}")
        
        # Test enhanced order flow analysis
        top_5_bids = [(50000, 1.5), (49995, 2.0), (49990, 1.8), (49985, 2.2), (49980, 1.9)]
        top_5_asks = [(50005, 1.2), (50010, 1.8), (50015, 2.1), (50020, 1.6), (50025, 2.0)]
        
        flow_strength = interface.calculate_order_flow_strength(top_5_bids, top_5_asks)
        print(f"   ✅ Order flow strength: {flow_strength}")
        
        # Test weighted order book levels
        weighted_levels = interface.calculate_weighted_order_book_levels(top_5_bids, top_5_asks)
        print(f"   ✅ Weighted order book levels: {weighted_levels:.3f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing analysis engine: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_optimization():
    """Test performance optimization and memory management"""
    print("\n🧪 Testing performance optimization...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        
        class MockInterface(EpinnoxTradingInterface):
            def __init__(self):
                self.emergency_stop_active = False
                self.emergency_risk_mode = False
                
                # Initialize performance systems
                self.setup_memory_management()
                self.setup_unified_update_scheduler()
                
            def log_message(self, msg):
                print(f"PERF LOG: {msg}")
        
        interface = MockInterface()
        
        # Test memory management initialization
        if hasattr(interface, 'scalper_memory_stats'):
            print("   ✅ Memory management initialized")
            print(f"   ✅ Memory stats: {interface.scalper_memory_stats}")
        else:
            print("   ❌ Memory management not initialized")
            return False
        
        # Test unified scheduler
        if hasattr(interface, 'unified_scheduler'):
            print("   ✅ Unified scheduler initialized")
            print(f"   ✅ Scheduler config: {interface.unified_scheduler}")
        else:
            print("   ❌ Unified scheduler not initialized")
            return False
        
        # Test cleanup functionality
        interface.cleanup_stale_analysis_data()
        print("   ✅ Cleanup functionality working")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing performance optimization: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_error_handling():
    """Test enhanced error handling and data validation"""
    print("\n🧪 Testing enhanced error handling...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        
        class MockInterface(EpinnoxTradingInterface):
            def __init__(self):
                self.emergency_stop_active = False
                self.emergency_risk_mode = False
                
                class MockCombo:
                    def currentText(self):
                        return 'BTC/USDT:USDT'
                
                self.symbol_combo = MockCombo()
                
            def log_message(self, msg):
                print(f"ERROR LOG: {msg}")
        
        interface = MockInterface()
        
        # Test safe data validation
        test_cases = [
            ({'value': 50000.0}, 'value', 50000.0),
            ({'value': None}, 'value', 0.0),
            ({'value': 'invalid'}, 'value', 0.0),
            ({'value': float('inf')}, 'value', 0.0),
            ({'value': float('nan')}, 'value', 0.0),
        ]
        
        for data, field, expected in test_cases:
            result = interface.validate_market_data_safely(data, field, 0.0)
            if result == expected:
                print(f"   ✅ Validation test passed: {data} -> {result}")
            else:
                print(f"   ❌ Validation test failed: {data} -> {result} (expected {expected})")
                return False
        
        # Test safe division
        division_tests = [
            (100, 2, 50.0),
            (100, 0, 0.0),  # Division by zero
            (100, None, 0.0),  # None denominator
            (None, 2, 0.0),  # None numerator
        ]
        
        for num, den, expected in division_tests:
            result = interface.safe_division(num, den, 0.0)
            if result == expected:
                print(f"   ✅ Division test passed: {num}/{den} -> {result}")
            else:
                print(f"   ❌ Division test failed: {num}/{den} -> {result} (expected {expected})")
                return False
        
        # Test robust market data fetching
        emergency_data = interface.get_emergency_fallback_data('BTC/USDT:USDT')
        if emergency_data and emergency_data.get('data_quality') == 'EMERGENCY':
            print("   ✅ Emergency fallback data generation working")
        else:
            print("   ❌ Emergency fallback data generation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing error handling: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_validation_comprehensive():
    """Test comprehensive data validation"""
    print("\n🧪 Testing comprehensive data validation...")
    
    try:
        from launch_epinnox import EpinnoxTradingInterface
        
        class MockInterface(EpinnoxTradingInterface):
            def __init__(self):
                self.emergency_stop_active = False
                self.emergency_risk_mode = False
                
            def log_message(self, msg):
                print(f"VALIDATION LOG: {msg}")
        
        interface = MockInterface()
        
        # Test valid market data
        valid_data = {
            'best_bid': 50000.0,
            'best_ask': 50005.0,
            'spread': 5.0,
            'spread_pct': 0.01
        }
        
        is_valid = interface.enhanced_data_validation(valid_data)
        if is_valid:
            print("   ✅ Valid data validation passed")
        else:
            print("   ❌ Valid data validation failed")
            return False
        
        # Test invalid market data
        invalid_data = {
            'best_bid': 0.0,  # Invalid bid
            'best_ask': 50005.0,
            'spread': 5.0
        }
        
        is_valid = interface.enhanced_data_validation(invalid_data)
        if not is_valid:
            print("   ✅ Invalid data validation correctly rejected")
        else:
            print("   ❌ Invalid data validation incorrectly accepted")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing data validation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all ScalperGPT fix verification tests"""
    print("🚀 Running ScalperGPT Fixes Verification Tests")
    print("=" * 60)
    
    test1_result = test_scalper_widget_initialization()
    test2_result = test_enhanced_analysis_engine()
    test3_result = test_performance_optimization()
    test4_result = test_enhanced_error_handling()
    test5_result = test_data_validation_comprehensive()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   Widget initialization: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Enhanced analysis engine: {'✅ PASS' if test2_result else '❌ FAIL'}")
    print(f"   Performance optimization: {'✅ PASS' if test3_result else '❌ FAIL'}")
    print(f"   Enhanced error handling: {'✅ PASS' if test4_result else '❌ FAIL'}")
    print(f"   Data validation: {'✅ PASS' if test5_result else '❌ FAIL'}")
    
    all_passed = all([test1_result, test2_result, test3_result, test4_result, test5_result])
    
    if all_passed:
        print("\n🎉 All ScalperGPT fixes tests PASSED!")
        print("Expected improvements:")
        print("  • All ScalperGPT widgets properly initialized")
        print("  • Enhanced volatility clustering with dynamic thresholds")
        print("  • Robust order flow analysis with multiple fallbacks")
        print("  • Memory management and performance optimization")
        print("  • Comprehensive error handling and data validation")
        print("  • System should achieve 100% health with no widget errors")
        return True
    else:
        print("\n⚠️ Some ScalperGPT tests failed - additional fixes may be needed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
