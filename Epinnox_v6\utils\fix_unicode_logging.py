#!/usr/bin/env python3
"""
Fix Unicode encoding issues in logging by setting up proper console encoding
"""

import sys
import os
import logging
import codecs

def setup_unicode_console():
    """Setup console for proper Unicode handling on Windows"""
    
    # For Windows, try to set UTF-8 encoding
    if sys.platform == 'win32':
        try:
            # Try to reconfigure stdout/stderr for UTF-8
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
            
            # Set console code page to UTF-8 if possible
            try:
                import subprocess
                subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
            except Exception:
                pass
                
        except Exception as e:
            print(f"Warning: Could not set UTF-8 encoding: {e}")
            return False
    
    return True

def create_safe_logging_config():
    """Create a logging configuration that handles Unicode safely"""
    
    # Emoji to ASCII replacements
    emoji_map = {
        '🔍': '[SEARCH]',
        '📋': '[CONFIG]', 
        '✅': '[OK]',
        '🔑': '[KEY]',
        '📂': '[FILE]',
        '🏦': '[BANK]',
        '📊': '[DATA]',
        '🎯': '[TARGET]',
        '🚨': '[ALERT]',
        '🛡️': '[SHIELD]',
        '🤖': '[BOT]',
        '🚀': '[ROCKET]',
        '📈': '[CHART]',
        '💰': '[MONEY]',
        '⚠️': '[WARN]',
        '❌': '[ERROR]',
        '🔐': '[LOCK]',
        '🔮': '[FUTURE]',
        '💵': '[CASH]',
        '⚖️': '[SCALE]',
        '🔧': '[TOOL]',
        '🧪': '[TEST]',
    }
    
    class SafeFormatter(logging.Formatter):
        """Formatter that replaces emojis with ASCII equivalents"""
        
        def format(self, record):
            # Get the formatted message
            formatted = super().format(record)
            
            # Replace emojis with ASCII
            for emoji, replacement in emoji_map.items():
                formatted = formatted.replace(emoji, replacement)
            
            return formatted
    
    # Configure logging with safe formatter
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('safe_trading.log', encoding='utf-8')
        ]
    )
    
    # Apply safe formatter to all handlers
    safe_formatter = SafeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    for handler in logging.getLogger().handlers:
        if isinstance(handler, logging.StreamHandler):
            handler.setFormatter(safe_formatter)

def main():
    """Main function to test Unicode handling"""
    
    print("Setting up Unicode console...")
    unicode_ok = setup_unicode_console()
    
    print("Creating safe logging configuration...")
    create_safe_logging_config()
    
    # Test logging with emojis
    logger = logging.getLogger(__name__)
    
    logger.info("🔍 Testing Unicode logging...")
    logger.info("✅ Configuration loaded successfully")
    logger.warning("⚠️ This is a warning with emoji")
    logger.error("❌ This is an error with emoji")
    
    print(f"Unicode console setup: {'OK' if unicode_ok else 'FAILED'}")
    print("Safe logging test completed!")

if __name__ == '__main__':
    main()
