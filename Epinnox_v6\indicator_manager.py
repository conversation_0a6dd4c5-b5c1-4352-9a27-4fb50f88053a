"""
Indicator Manager for EPINNOX Trading Platform
Provides a dialog for managing technical indicators and their settings
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QCheckBox,
    QComboBox, QSpinBox, QDoubleSpinBox, QPushButton,
    QTabWidget, QWidget, QFormLayout, QGroupBox, QScrollArea,
    QSizePolicy, QSlider, QColorDialog, QTableWidget, QTableWidgetItem,
    QHeaderView
)
from PySide6.QtCore import Qt, Signal, QSettings
from PySide6.QtGui import QColor, QFont

class IndicatorSettingsDialog(QDialog):
    """Dialog for managing indicator settings"""

    # Signal emitted when settings are applied
    settings_applied = Signal(dict)

    def __init__(self, parent=None, current_settings=None):
        super().__init__(parent)
        self.setWindowTitle("Indicator Settings")
        self.resize(600, 500)
        self.setModal(True)

        # Default settings if none provided
        self.current_settings = current_settings or {
            "Moving Average": {
                "enabled": True,
                "type": "SMA",
                "period": 20,
                "color": "#ffffff",
                "width": 1.0,
                "style": "Dash"
            },
            "Bollinger Bands": {
                "enabled": False,
                "period": 20,
                "std_dev": 2.0,
                "color": "#8a2be2",
                "width": 1.0,
                "fill": True,
                "fill_alpha": 0.1
            },
            "ATR-EMA Bands": {
                "enabled": False,
                "ema_period": 20,
                "atr_period": 14,
                "multipliers": [1, 2, 3],
                "ema_color": "#ffff00",
                "upper_band_color": "#ff4444",
                "lower_band_color": "#44ff44",
                "width": 1.0,
                "fill": True,
                "fill_alpha": 0.05
            },
            "RSI": {
                "enabled": False,
                "period": 14,
                "overbought": 70,
                "oversold": 30,
                "color": "#ffaa00",
                "width": 1.0
            },
            "MACD": {
                "enabled": False,
                "fast_period": 12,
                "slow_period": 26,
                "signal_period": 9,
                "macd_color": "#00aaff",
                "signal_color": "#ff00aa",
                "histogram_color": "#00ff00",
                "width": 1.0
            },
            "Stochastic": {
                "enabled": False,
                "k_period": 14,
                "d_period": 3,
                "slowing": 3,
                "overbought": 80,
                "oversold": 20,
                "k_color": "#00ffff",
                "d_color": "#ffff00",
                "width": 1.0
            }
        }

        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI"""
        layout = QVBoxLayout(self)

        # Create tabs for different indicator categories
        self.tabs = QTabWidget()

        # Trend indicators tab
        trend_tab = QWidget()
        trend_layout = QVBoxLayout(trend_tab)

        # Moving Average settings
        ma_group = self.create_ma_settings()
        trend_layout.addWidget(ma_group)

        # Bollinger Bands settings
        bb_group = self.create_bb_settings()
        trend_layout.addWidget(bb_group)

        # ATR-EMA Bands settings
        atr_ema_group = self.create_atr_ema_bands_settings()
        trend_layout.addWidget(atr_ema_group)

        trend_layout.addStretch()
        self.tabs.addTab(trend_tab, "Trend")

        # Momentum indicators tab
        momentum_tab = QWidget()
        momentum_layout = QVBoxLayout(momentum_tab)

        # RSI settings
        rsi_group = self.create_rsi_settings()
        momentum_layout.addWidget(rsi_group)

        # MACD settings
        macd_group = self.create_macd_settings()
        momentum_layout.addWidget(macd_group)

        # Stochastic settings
        stoch_group = self.create_stochastic_settings()
        momentum_layout.addWidget(stoch_group)

        momentum_layout.addStretch()
        self.tabs.addTab(momentum_tab, "Momentum")

        # Add tabs to main layout
        layout.addWidget(self.tabs)

        # Buttons
        button_layout = QHBoxLayout()
        self.apply_btn = QPushButton("Apply")
        self.apply_btn.clicked.connect(self.apply_settings)
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

    def create_ma_settings(self):
        """Create Moving Average settings group"""
        group = QGroupBox("Moving Average")
        layout = QFormLayout(group)

        # Enable checkbox
        self.ma_enabled = QCheckBox()
        self.ma_enabled.setChecked(self.current_settings["Moving Average"]["enabled"])
        layout.addRow("Enable:", self.ma_enabled)

        # MA Type
        self.ma_type = QComboBox()
        self.ma_type.addItems(["SMA", "EMA", "WMA"])
        self.ma_type.setCurrentText(self.current_settings["Moving Average"]["type"])
        layout.addRow("Type:", self.ma_type)

        # Period
        self.ma_period = QSpinBox()
        self.ma_period.setRange(2, 200)
        self.ma_period.setValue(self.current_settings["Moving Average"]["period"])
        layout.addRow("Period:", self.ma_period)

        # Color
        self.ma_color_btn = QPushButton()
        self.ma_color_btn.setStyleSheet(f"background-color: {self.current_settings['Moving Average']['color']}")
        self.ma_color_btn.clicked.connect(lambda: self.choose_color(self.ma_color_btn))
        layout.addRow("Color:", self.ma_color_btn)

        # Line style
        self.ma_style = QComboBox()
        self.ma_style.addItems(["Solid", "Dash", "Dot"])
        self.ma_style.setCurrentText(self.current_settings["Moving Average"]["style"])
        layout.addRow("Style:", self.ma_style)

        return group

    def create_bb_settings(self):
        """Create Bollinger Bands settings group"""
        group = QGroupBox("Bollinger Bands")
        layout = QFormLayout(group)

        # Enable checkbox
        self.bb_enabled = QCheckBox()
        self.bb_enabled.setChecked(self.current_settings["Bollinger Bands"]["enabled"])
        layout.addRow("Enable:", self.bb_enabled)

        # Period
        self.bb_period = QSpinBox()
        self.bb_period.setRange(2, 100)
        self.bb_period.setValue(self.current_settings["Bollinger Bands"]["period"])
        layout.addRow("Period:", self.bb_period)

        # Standard Deviation
        self.bb_std = QDoubleSpinBox()
        self.bb_std.setRange(0.1, 5.0)
        self.bb_std.setSingleStep(0.1)
        self.bb_std.setValue(self.current_settings["Bollinger Bands"]["std_dev"])
        layout.addRow("Std Dev:", self.bb_std)

        # Color
        self.bb_color_btn = QPushButton()
        self.bb_color_btn.setStyleSheet(f"background-color: {self.current_settings['Bollinger Bands']['color']}")
        self.bb_color_btn.clicked.connect(lambda: self.choose_color(self.bb_color_btn))
        layout.addRow("Color:", self.bb_color_btn)

        # Fill between bands
        self.bb_fill = QCheckBox()
        self.bb_fill.setChecked(self.current_settings["Bollinger Bands"]["fill"])
        layout.addRow("Fill:", self.bb_fill)

        return group

    def create_atr_ema_bands_settings(self):
        """Create ATR-EMA Bands settings group"""
        group = QGroupBox("ATR-EMA Bands")
        layout = QFormLayout(group)

        # Enable checkbox
        self.atr_ema_enabled = QCheckBox()
        self.atr_ema_enabled.setChecked(self.current_settings["ATR-EMA Bands"]["enabled"])
        layout.addRow("Enable:", self.atr_ema_enabled)

        # EMA Period
        self.atr_ema_period = QSpinBox()
        self.atr_ema_period.setRange(2, 100)
        self.atr_ema_period.setValue(self.current_settings["ATR-EMA Bands"]["ema_period"])
        layout.addRow("EMA Period:", self.atr_ema_period)

        # ATR Period
        self.atr_period = QSpinBox()
        self.atr_period.setRange(2, 100)
        self.atr_period.setValue(self.current_settings["ATR-EMA Bands"]["atr_period"])
        layout.addRow("ATR Period:", self.atr_period)

        # EMA Color
        self.atr_ema_color_btn = QPushButton()
        self.atr_ema_color_btn.setStyleSheet(f"background-color: {self.current_settings['ATR-EMA Bands']['ema_color']}")
        self.atr_ema_color_btn.clicked.connect(lambda: self.choose_color(self.atr_ema_color_btn))
        layout.addRow("EMA Color:", self.atr_ema_color_btn)

        # Upper Band Color
        self.atr_upper_color_btn = QPushButton()
        self.atr_upper_color_btn.setStyleSheet(f"background-color: {self.current_settings['ATR-EMA Bands']['upper_band_color']}")
        self.atr_upper_color_btn.clicked.connect(lambda: self.choose_color(self.atr_upper_color_btn))
        layout.addRow("Upper Band Color:", self.atr_upper_color_btn)

        # Lower Band Color
        self.atr_lower_color_btn = QPushButton()
        self.atr_lower_color_btn.setStyleSheet(f"background-color: {self.current_settings['ATR-EMA Bands']['lower_band_color']}")
        self.atr_lower_color_btn.clicked.connect(lambda: self.choose_color(self.atr_lower_color_btn))
        layout.addRow("Lower Band Color:", self.atr_lower_color_btn)

        # Fill between bands
        self.atr_ema_fill = QCheckBox()
        self.atr_ema_fill.setChecked(self.current_settings["ATR-EMA Bands"]["fill"])
        layout.addRow("Fill:", self.atr_ema_fill)

        return group

    def create_rsi_settings(self):
        """Create RSI settings group"""
        group = QGroupBox("Relative Strength Index (RSI)")
        layout = QFormLayout(group)

        # Enable checkbox
        self.rsi_enabled = QCheckBox()
        self.rsi_enabled.setChecked(self.current_settings["RSI"]["enabled"])
        layout.addRow("Enable:", self.rsi_enabled)

        # Period
        self.rsi_period = QSpinBox()
        self.rsi_period.setRange(2, 50)
        self.rsi_period.setValue(self.current_settings["RSI"]["period"])
        layout.addRow("Period:", self.rsi_period)

        # Overbought level
        self.rsi_overbought = QSpinBox()
        self.rsi_overbought.setRange(50, 100)
        self.rsi_overbought.setValue(self.current_settings["RSI"]["overbought"])
        layout.addRow("Overbought:", self.rsi_overbought)

        # Oversold level
        self.rsi_oversold = QSpinBox()
        self.rsi_oversold.setRange(0, 50)
        self.rsi_oversold.setValue(self.current_settings["RSI"]["oversold"])
        layout.addRow("Oversold:", self.rsi_oversold)

        # Color
        self.rsi_color_btn = QPushButton()
        self.rsi_color_btn.setStyleSheet(f"background-color: {self.current_settings['RSI']['color']}")
        self.rsi_color_btn.clicked.connect(lambda: self.choose_color(self.rsi_color_btn))
        layout.addRow("Color:", self.rsi_color_btn)

        return group

    def create_macd_settings(self):
        """Create MACD settings group"""
        group = QGroupBox("MACD")
        layout = QFormLayout(group)

        # Enable checkbox
        self.macd_enabled = QCheckBox()
        self.macd_enabled.setChecked(self.current_settings["MACD"]["enabled"])
        layout.addRow("Enable:", self.macd_enabled)

        # Fast period
        self.macd_fast = QSpinBox()
        self.macd_fast.setRange(2, 50)
        self.macd_fast.setValue(self.current_settings["MACD"]["fast_period"])
        layout.addRow("Fast Period:", self.macd_fast)

        # Slow period
        self.macd_slow = QSpinBox()
        self.macd_slow.setRange(5, 100)
        self.macd_slow.setValue(self.current_settings["MACD"]["slow_period"])
        layout.addRow("Slow Period:", self.macd_slow)

        # Signal period
        self.macd_signal = QSpinBox()
        self.macd_signal.setRange(2, 50)
        self.macd_signal.setValue(self.current_settings["MACD"]["signal_period"])
        layout.addRow("Signal Period:", self.macd_signal)

        # MACD Line Color
        self.macd_color_btn = QPushButton()
        self.macd_color_btn.setStyleSheet(f"background-color: {self.current_settings['MACD']['macd_color']}")
        self.macd_color_btn.clicked.connect(lambda: self.choose_color(self.macd_color_btn))
        layout.addRow("MACD Color:", self.macd_color_btn)

        # Signal Line Color
        self.signal_color_btn = QPushButton()
        self.signal_color_btn.setStyleSheet(f"background-color: {self.current_settings['MACD']['signal_color']}")
        self.signal_color_btn.clicked.connect(lambda: self.choose_color(self.signal_color_btn))
        layout.addRow("Signal Color:", self.signal_color_btn)

        return group

    def create_stochastic_settings(self):
        """Create Stochastic settings group"""
        group = QGroupBox("Stochastic Oscillator")
        layout = QFormLayout(group)

        # Enable checkbox
        self.stoch_enabled = QCheckBox()
        self.stoch_enabled.setChecked(self.current_settings["Stochastic"]["enabled"])
        layout.addRow("Enable:", self.stoch_enabled)

        # K Period
        self.stoch_k = QSpinBox()
        self.stoch_k.setRange(1, 50)
        self.stoch_k.setValue(self.current_settings["Stochastic"]["k_period"])
        layout.addRow("K Period:", self.stoch_k)

        # D Period
        self.stoch_d = QSpinBox()
        self.stoch_d.setRange(1, 50)
        self.stoch_d.setValue(self.current_settings["Stochastic"]["d_period"])
        layout.addRow("D Period:", self.stoch_d)

        # Slowing
        self.stoch_slowing = QSpinBox()
        self.stoch_slowing.setRange(1, 10)
        self.stoch_slowing.setValue(self.current_settings["Stochastic"]["slowing"])
        layout.addRow("Slowing:", self.stoch_slowing)

        # Overbought level
        self.stoch_overbought = QSpinBox()
        self.stoch_overbought.setRange(50, 100)
        self.stoch_overbought.setValue(self.current_settings["Stochastic"]["overbought"])
        layout.addRow("Overbought:", self.stoch_overbought)

        # Oversold level
        self.stoch_oversold = QSpinBox()
        self.stoch_oversold.setRange(0, 50)
        self.stoch_oversold.setValue(self.current_settings["Stochastic"]["oversold"])
        layout.addRow("Oversold:", self.stoch_oversold)

        # K Line Color
        self.stoch_k_color_btn = QPushButton()
        self.stoch_k_color_btn.setStyleSheet(f"background-color: {self.current_settings['Stochastic']['k_color']}")
        self.stoch_k_color_btn.clicked.connect(lambda: self.choose_color(self.stoch_k_color_btn))
        layout.addRow("K Line Color:", self.stoch_k_color_btn)

        # D Line Color
        self.stoch_d_color_btn = QPushButton()
        self.stoch_d_color_btn.setStyleSheet(f"background-color: {self.current_settings['Stochastic']['d_color']}")
        self.stoch_d_color_btn.clicked.connect(lambda: self.choose_color(self.stoch_d_color_btn))
        layout.addRow("D Line Color:", self.stoch_d_color_btn)

        return group

    def choose_color(self, button):
        """Open color dialog and set button background color"""
        color = QColorDialog.getColor()
        if color.isValid():
            button.setStyleSheet(f"background-color: {color.name()}")

    def apply_settings(self):
        """Collect settings from UI and emit signal"""
        settings = {
            "Moving Average": {
                "enabled": self.ma_enabled.isChecked(),
                "type": self.ma_type.currentText(),
                "period": self.ma_period.value(),
                "color": self.ma_color_btn.styleSheet().split("background-color: ")[1].split(";")[0].strip(),
                "width": 1.0,
                "style": self.ma_style.currentText()
            },
            "Bollinger Bands": {
                "enabled": self.bb_enabled.isChecked(),
                "period": self.bb_period.value(),
                "std_dev": self.bb_std.value(),
                "color": self.bb_color_btn.styleSheet().split("background-color: ")[1].split(";")[0].strip(),
                "width": 1.0,
                "fill": self.bb_fill.isChecked(),
                "fill_alpha": 0.1
            },
            "ATR-EMA Bands": {
                "enabled": self.atr_ema_enabled.isChecked(),
                "ema_period": self.atr_ema_period.value(),
                "atr_period": self.atr_period.value(),
                "multipliers": [1, 2, 3],
                "ema_color": self.atr_ema_color_btn.styleSheet().split("background-color: ")[1].split(";")[0].strip(),
                "upper_band_color": self.atr_upper_color_btn.styleSheet().split("background-color: ")[1].split(";")[0].strip(),
                "lower_band_color": self.atr_lower_color_btn.styleSheet().split("background-color: ")[1].split(";")[0].strip(),
                "width": 1.0,
                "fill": self.atr_ema_fill.isChecked(),
                "fill_alpha": 0.05
            },
            "RSI": {
                "enabled": self.rsi_enabled.isChecked(),
                "period": self.rsi_period.value(),
                "overbought": self.rsi_overbought.value(),
                "oversold": self.rsi_oversold.value(),
                "color": self.rsi_color_btn.styleSheet().split("background-color: ")[1].split(";")[0].strip(),
                "width": 1.0
            },
            "MACD": {
                "enabled": self.macd_enabled.isChecked(),
                "fast_period": self.macd_fast.value(),
                "slow_period": self.macd_slow.value(),
                "signal_period": self.macd_signal.value(),
                "macd_color": self.macd_color_btn.styleSheet().split("background-color: ")[1].split(";")[0].strip(),
                "signal_color": self.signal_color_btn.styleSheet().split("background-color: ")[1].split(";")[0].strip(),
                "histogram_color": "#00ff00",
                "width": 1.0
            },
            "Stochastic": {
                "enabled": self.stoch_enabled.isChecked(),
                "k_period": self.stoch_k.value(),
                "d_period": self.stoch_d.value(),
                "slowing": self.stoch_slowing.value(),
                "overbought": self.stoch_overbought.value(),
                "oversold": self.stoch_oversold.value(),
                "k_color": self.stoch_k_color_btn.styleSheet().split("background-color: ")[1].split(";")[0].strip(),
                "d_color": self.stoch_d_color_btn.styleSheet().split("background-color: ")[1].split(";")[0].strip(),
                "width": 1.0
            }
        }

        self.settings_applied.emit(settings)
        self.accept()
