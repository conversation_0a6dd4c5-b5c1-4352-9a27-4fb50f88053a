# 🚀 EPINNOX v6 Advanced Testing & Monitoring Guide

## 📋 Overview

This guide covers the comprehensive advanced testing and monitoring features implemented for EPINNOX v6, including model drift detection, hyperparameter optimization, visualization dashboards, multi-agent simulation, and stress testing capabilities.

## 🧠 1. Model & Signal Drift Detection

### Features
- **Performance Drift Detection**: Monitors Sharpe ratio, win rate, drawdown, and profit factor
- **Confidence Drift Detection**: Tracks changes in model confidence levels
- **Signal Drift Detection**: Analyzes signal strength distribution and volatility
- **Market Regime Change Detection**: Identifies shifts in market conditions
- **Automated Alerts**: Configurable severity levels (Normal, Warning, Critical, Emergency)

### Usage
```python
from monitoring.drift_detector import ModelDriftDetector
from monitoring.performance_tracker import PerformanceTracker

# Initialize drift detector
tracker = PerformanceTracker("data/trading_performance.db")
drift_detector = ModelDriftDetector(tracker)

# Run comprehensive drift check
alerts = drift_detector.run_comprehensive_drift_check()

# Generate drift report
drift_detector.save_drift_report("drift_analysis.json")
```

### Configuration
```python
config = {
    'sharpe_thresholds': {'warning': 0.5, 'critical': 0.0, 'emergency': -0.5},
    'win_rate_thresholds': {'warning': 0.40, 'critical': 0.30, 'emergency': 0.20},
    'drawdown_thresholds': {'warning': 0.15, 'critical': 0.25, 'emergency': 0.40},
    'confidence_drift_threshold': 0.15,
    'min_trades_for_analysis': 10
}
```

## 🔧 2. Auto-Hyperparameter Testing

### Features
- **Grid Search Optimization**: Systematic parameter exploration
- **Random Search**: Efficient parameter sampling
- **Multi-objective Optimization**: Combines return, Sharpe ratio, drawdown, and win rate
- **Parameter Importance Analysis**: Identifies most impactful parameters
- **Automated Logging**: Complete parameter and result tracking

### CLI Usage
```bash
# Quick optimization
python run_param_search.py --preset quick

# Thorough grid search
python run_param_search.py --method grid --max-combinations 500 --parallel-jobs 8

# Risk-focused optimization
python run_param_search.py --preset risk-focused --days 60

# Custom parameter optimization
python run_param_search.py --custom-space --optimize-risk --optimize-trading --max-combinations 200
```

### Parameter Categories
- **Risk Management**: Portfolio risk, position size, leverage limits
- **Trading Parameters**: Confidence thresholds, slippage, commission
- **Signal Weights**: RSI, MACD, momentum indicator weights
- **RL Parameters**: Learning rates, batch sizes, network architecture

### Results Analysis
```python
from optimization.hyperparameter_optimizer import HyperparameterOptimizer

optimizer = HyperparameterOptimizer(BacktestRunner, base_config)
results = await optimizer.run_grid_search(max_combinations=100)

# Analyze results
analysis = optimizer.analyze_results()
print(f"Best parameters: {optimizer.best_parameters}")
print(f"Parameter importance: {analysis['parameter_importance']}")
```

## 📊 3. Visualization Dashboard

### Features
- **Real-time Performance Monitoring**: Live balance, P&L, win rate tracking
- **Interactive Charts**: Equity curve, P&L distribution, performance metrics
- **Trade Analysis**: Recent trades table with color-coded P&L
- **Symbol Performance**: Breakdown by trading pairs
- **System Status**: Database health, update timestamps

### Launch Dashboard
```bash
# Start dashboard on default port (8501)
python start_dashboard.py

# Custom host and port
python start_dashboard.py --host 0.0.0.0 --port 8080
```

### Dashboard URL
- **Local**: http://localhost:8501
- **Network**: http://your-ip:8501

### Dashboard Components
1. **Key Metrics Row**: Total trades, P&L, win rate, best/worst trades
2. **Equity Curve**: Portfolio value over time
3. **P&L Distribution**: Histogram of winning vs losing trades
4. **Performance Metrics**: Win rate, Sharpe ratio, drawdown trends
5. **Symbol Performance**: P&L breakdown by trading pair
6. **Recent Trades**: Latest trading activity with execution details

## 🤖 4. Multi-Symbol Multi-Agent Simulation

### Features
- **Multiple Agent Types**: Momentum, mean reversion, breakout, ensemble strategies
- **Portfolio Allocation**: Configurable allocation per agent
- **Parallel Execution**: Simultaneous agent operation
- **Performance Comparison**: Agent ranking and analysis
- **Strategy Diversification**: Risk reduction through multiple approaches

### CLI Usage
```bash
# Default diverse agents
python run_multi_agent_sim.py --preset diverse --days 30

# All momentum agents
python run_multi_agent_sim.py --preset momentum --initial-balance 50000

# Custom agent configuration
python run_multi_agent_sim.py --agents momentum:BTC/USDT:0.4 mean_reversion:ETH/USDT:0.3 breakout:BTC/USDT,ETH/USDT:0.3

# Extended simulation
python run_multi_agent_sim.py --preset mixed --days 90 --symbols BTC/USDT ETH/USDT ADA/USDT SOL/USDT
```

### Agent Types
- **Momentum**: Trend-following strategy
- **Mean Reversion**: Counter-trend strategy
- **Breakout**: Support/resistance breakout strategy
- **RL Agent**: Reinforcement learning-based decisions

### Configuration Format
```
agent_type:symbols:allocation
Examples:
- momentum:BTC/USDT:0.4
- mean_reversion:ETH/USDT,ADA/USDT:0.3
- breakout:BTC/USDT,ETH/USDT,ADA/USDT:0.3
```

## 💾 5. Model Persistence & Versioning

### Features
- **Automatic Versioning**: Timestamp-based model versions
- **Metadata Tracking**: Parameters, performance metrics, dependencies
- **Integrity Verification**: Checksum validation
- **Model Registry**: Centralized model management
- **Import/Export**: Model sharing and backup

### Usage
```python
from persistence.model_manager import ModelVersionManager

# Initialize model manager
model_manager = ModelVersionManager("models")

# Save model with metadata
model_id = model_manager.save_model(
    model=my_trained_model,
    model_type="rl_agent",
    parameters={"learning_rate": 0.001, "batch_size": 64},
    performance_metrics={"accuracy": 0.85, "sharpe_ratio": 1.2},
    training_data_info={"samples": 10000, "period": "2024-01-01 to 2024-06-01"}
)

# Load model
loaded_model = model_manager.load_model(model_id)

# Get latest model of type
latest_rl_model = model_manager.get_latest_model("rl_agent")

# List all models
all_models = model_manager.list_models()

# Cleanup old versions (keep latest 5)
model_manager.cleanup_old_models("rl_agent", keep_latest=5)
```

## 🔄 6. Trade Replay Engine

### Features
- **Session Replay**: Re-run complete trading sessions
- **Step-by-step Analysis**: Detailed event timeline
- **Decision Validation**: Compare original vs replay results
- **Bug Tracing**: Identify issues in trading logic
- **Performance Analysis**: Detailed trade-by-trade breakdown

### Usage
```python
from analysis.trade_replay import TradeReplayEngine, ReplayConfig

# Initialize replay engine
replay_engine = TradeReplayEngine("data/trading_performance.db")

# Get available sessions
sessions = replay_engine.get_available_sessions(days_back=30)

# Configure replay
config = ReplayConfig(
    session_id="session_20241201_143022",
    replay_speed=1.0,
    include_market_data=True,
    include_signals=True
)

# Run replay
summary = replay_engine.replay_session(config)

# Export detailed report
replay_engine.export_replay_report("replay_analysis.json")

# Compare two sessions
comparison = replay_engine.compare_sessions("session_1", "session_2")
```

## ✅ 7. Data Validation Layer

### Features
- **OHLCV Validation**: Price relationships, volume checks, gap detection
- **NLP Data Validation**: Text quality, sentiment ranges, confidence scores
- **Signal Validation**: Decision values, confidence ranges, data structure
- **Comprehensive Reporting**: Detailed validation reports with recommendations

### Usage
```python
from validation.data_validator import DataValidator

# Initialize validator
validator = DataValidator()

# Validate different data types
data_sources = {
    'btc_ohlcv': btc_dataframe,
    'news_sentiment': nlp_dataframe,
    'trading_signals': signal_dictionary
}

# Run comprehensive validation
validation_results = validator.run_comprehensive_validation(data_sources)

# Generate report
report = validator.generate_validation_report(validation_results)

# Check for critical issues
critical_issues = sum(1 for issues in validation_results.values() 
                     for issue in issues if issue.severity == ValidationSeverity.CRITICAL)
```

### Validation Categories
- **Data Availability**: Missing or empty datasets
- **Schema Validation**: Required columns/fields
- **Data Quality**: NaN values, text length, volume checks
- **Data Integrity**: Price relationships, value ranges
- **Data Continuity**: Time gaps, duplicate timestamps
- **Data Anomaly**: Extreme movements, outliers

## 🧪 8. Stress Testing Framework

### Features
- **Multiple Scenarios**: Flash crash, volatility spike, liquidity crisis, etc.
- **Configurable Intensity**: Adjustable stress levels
- **System Stability Assessment**: Error rates, uptime, recovery metrics
- **Performance Under Stress**: Return, drawdown, trade execution analysis
- **Automated Recommendations**: System improvement suggestions

### CLI Usage
```bash
# List available scenarios
python run_stress_test.py --list-scenarios

# Run flash crash test
python run_stress_test.py flash_crash --intensity 2.0 --duration 30

# Volatility spike test
python run_stress_test.py volatility_spike --intensity 1.5 --duration 60 --save-results

# Liquidity crisis simulation
python run_stress_test.py liquidity_crisis --symbols BTC/USDT ETH/USDT --initial-balance 50000
```

### Stress Scenarios
1. **Flash Crash**: Sudden 20%+ price drop with recovery
2. **Volatility Spike**: Extreme price volatility (5%+ per minute)
3. **Liquidity Crisis**: Low volume, wide spreads
4. **Trending Market**: Strong directional movement
5. **Sideways Market**: Range-bound trading
6. **Gap Opening**: Price gaps between periods
7. **News Shock**: Sudden news-driven price movements
8. **Correlation Breakdown**: Normal correlations fail

### Results Interpretation
- **PASSED**: Error count = 0, return > -5%
- **MARGINAL**: Error count = 0, return > -15%
- **FAILED**: Errors present or return < -15%

## 🚀 Quick Start Commands

```bash
# 1. Run comprehensive tests
python run_tests.py --verbose

# 2. Quick parameter optimization
python run_param_search.py --preset quick

# 3. Start monitoring dashboard
python start_dashboard.py

# 4. Run multi-agent simulation
python run_multi_agent_sim.py --preset diverse --days 7

# 5. Stress test the system
python run_stress_test.py flash_crash --intensity 1.5

# 6. Quick backtest
python run_backtest.py --days 7 --quiet

# 7. Paper trading session
python start_paper_trading.py --duration 60 --balance 5000
```

## 📁 File Structure

```
Epinnox_v6/
├── monitoring/
│   ├── drift_detector.py          # Model drift detection
│   └── performance_tracker.py     # Enhanced with drift detection
├── optimization/
│   └── hyperparameter_optimizer.py # Auto-hyperparameter testing
├── dashboard/
│   └── trading_dashboard.py       # Streamlit dashboard
├── simulation/
│   └── multi_agent_simulator.py   # Multi-agent simulation
├── persistence/
│   └── model_manager.py           # Model versioning
├── analysis/
│   └── trade_replay.py            # Trade replay engine
├── validation/
│   └── data_validator.py          # Data validation
├── stress_testing/
│   └── stress_tester.py           # Stress testing framework
├── run_param_search.py            # Parameter optimization CLI
├── start_dashboard.py             # Dashboard launcher
├── run_multi_agent_sim.py         # Multi-agent simulation CLI
├── run_stress_test.py             # Stress testing CLI
└── ADVANCED_TESTING_GUIDE.md      # This guide
```

## 🎯 Best Practices

1. **Regular Drift Monitoring**: Run drift detection daily
2. **Parameter Optimization**: Re-optimize monthly or after significant market changes
3. **Stress Testing**: Test before major deployments
4. **Data Validation**: Validate all data before trading
5. **Model Versioning**: Save models before retraining
6. **Dashboard Monitoring**: Keep dashboard running during live trading
7. **Multi-agent Validation**: Test strategy combinations
8. **Trade Replay**: Analyze unexpected behaviors immediately

## 🔧 Dependencies

```bash
# Core dependencies
pip install pandas numpy sqlite3 asyncio

# Dashboard dependencies
pip install streamlit plotly

# Optional ML dependencies
pip install torch stable-baselines3 gym
```

## 📞 Support

For issues or questions about the advanced testing framework:
1. Check the logs for detailed error messages
2. Validate your data using the data validation layer
3. Run stress tests to identify system limitations
4. Use the trade replay engine to debug specific issues
5. Monitor drift detection alerts for model degradation

---

**🎉 The EPINNOX v6 advanced testing framework provides comprehensive tools for ensuring robust, reliable, and profitable autonomous trading operations!**
