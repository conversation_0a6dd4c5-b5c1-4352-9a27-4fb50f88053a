#!/usr/bin/env python3
"""
Dashboard Launcher for EPINNOX v6
Launch the real-time trading dashboard
"""

import subprocess
import sys
import os
import argparse

def main():
    """Launch the trading dashboard"""
    parser = argparse.ArgumentParser(description='Launch EPINNOX v6 Trading Dashboard')
    parser.add_argument('--port', type=int, default=8501, help='Port to run dashboard on (default: 8501)')
    parser.add_argument('--host', type=str, default='localhost', help='Host to run dashboard on (default: localhost)')
    
    args = parser.parse_args()
    
    # Check if streamlit is installed
    try:
        import streamlit
    except ImportError:
        print("❌ Streamlit is not installed. Please install it with:")
        print("pip install streamlit plotly")
        sys.exit(1)
    
    # Launch dashboard
    dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard', 'trading_dashboard.py')
    
    cmd = [
        'streamlit', 'run', dashboard_path,
        '--server.port', str(args.port),
        '--server.address', args.host,
        '--server.headless', 'true'
    ]
    
    print(f"🚀 Launching EPINNOX v6 Trading Dashboard...")
    print(f"📊 Dashboard will be available at: http://{args.host}:{args.port}")
    print(f"🛑 Press Ctrl+C to stop the dashboard")
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n🛑 Dashboard stopped")
    except Exception as e:
        print(f"❌ Error launching dashboard: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
