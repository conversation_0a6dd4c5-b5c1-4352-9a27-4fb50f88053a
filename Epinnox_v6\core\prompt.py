"""
Prompt Generation Component
This module generates prompts for the LLaMA model based on market data.
"""
import re

def format_feature_value(key, value):
    """
    Format a feature value for display in the prompt.

    Args:
        key: Feature name
        value: Feature value

    Returns:
        str: Formatted feature string
    """
    # Format numerical values with appropriate precision
    if isinstance(value, float):
        if abs(value) < 0.01:
            formatted = f"{value:.6f}"
        elif abs(value) < 1:
            formatted = f"{value:.4f}"
        else:
            formatted = f"{value:.2f}"
    else:
        formatted = str(value)

    # Add interpretations for common indicators
    if key == 'rsi':
        if value > 70:
            formatted += " (overbought)"
        elif value < 30:
            formatted += " (oversold)"
    elif key == 'vwap_dist':
        if value > 0:
            formatted += "% above VWAP"
        else:
            formatted += "% below VWAP"
    elif key == 'relative_volume':
        if value > 1.5:
            formatted += " (high volume)"
        elif value < 0.5:
            formatted += " (low volume)"
    elif key == 'order_imbalance':
        if value > 10:
            formatted += "% ask-heavy"
        elif value < -10:
            formatted += "% bid-heavy"
        else:
            formatted += "% (balanced)"
    elif key == 'futures_premium_pct':
        if value > 0.5:
            formatted += "% (high premium)"
        elif value < -0.5:
            formatted += "% (discount)"
        else:
            formatted += "% (neutral)"
    elif key == 'spot_volume_imbalance' or key == 'futures_volume_imbalance':
        if value > 0.2:
            formatted += " (bid-heavy)"
        elif value < -0.2:
            formatted += " (ask-heavy)"
        else:
            formatted += " (balanced)"
    elif key == 'imbalance_divergence':
        if value > 0.3:
            formatted += " (futures more bid-heavy)"
        elif value < -0.3:
            formatted += " (spot more bid-heavy)"
        else:
            formatted += " (aligned)"

    return formatted

def build_prompt(features, tick_features=None, trade_log=None, market_context=None, exchange_data=None, signal_scores=None, market_regime=None, multi_timeframe_analysis=None):
    """
    Build a prompt for the LLaMA model based on market features.

    Args:
        features: Dictionary of market features
        tick_features: Dictionary of tick-level features (optional)
        trade_log: List of recent trades (optional)
        market_context: Additional market context (optional)
        exchange_data: Dictionary of exchange-specific data (optional)
        signal_scores: Dictionary of signal scores and confidence (optional)
        market_regime: Current market regime (optional)
        multi_timeframe_analysis: Analysis across multiple timeframes (optional)

    Returns:
        str: Formatted prompt for LLaMA
    """
    # Format the features section
    feature_lines = []
    for k, v in features.items():
        formatted_value = format_feature_value(k, v)
        feature_lines.append(f"- {k}: {formatted_value}")

    features_section = "\n".join(feature_lines)

    # Format the tick data section if available
    tick_section = ""
    if tick_features:
        tick_lines = []
        for k, v in tick_features.items():
            formatted_value = format_feature_value(k, v)
            tick_lines.append(f"- {k}: {formatted_value}")

        tick_section = "\nRecent Tick Data:\n" + "\n".join(tick_lines)

    # Format the trade log section if available
    trades_section = ""
    if trade_log and len(trade_log) > 0:
        trades = "\n".join(trade_log[-10:])  # Last 10 trades
        trades_section = f"\nRecent Trades:\n{trades}"
    else:
        trades_section = "\nRecent Trades:\nNo trades available."

    # Format the market context section if available
    context_section = ""
    if market_context:
        context_section = f"\nMarket Context:\n{market_context}"

    # Add exchange data section if available
    exchange_section = ""
    if exchange_data:
        exchange_lines = []
        for k, v in exchange_data.items():
            if k not in features:  # Avoid duplicates
                formatted_value = format_feature_value(k, v)
                exchange_lines.append(f"- {k}: {formatted_value}")

        if exchange_lines:
            exchange_section = "\nExchange Data:\n" + "\n".join(exchange_lines)

    # Add signal scores section if available
    scores_section = ""
    if signal_scores:
        # Organize scores into categories
        indicator_scores = []
        meta_scores = []
        decision_scores = []
        multi_timeframe_scores = []

        for k, v in signal_scores.items():
            if k in ['macd_score', 'orderbook_score', 'volume_score', 'price_action_score', 'trend_score']:
                if k == 'macd_score':
                    name = 'MACD Trend'
                elif k == 'orderbook_score':
                    name = 'Order Book Imbalance'
                elif k == 'volume_score':
                    name = 'Volume Analysis'
                elif k == 'price_action_score':
                    name = 'Price Action'
                elif k == 'trend_score':
                    name = 'Longer Timeframe Trend'
                else:
                    name = k

                # Format with direction indicator
                if v > 0.05:
                    direction = '📈 Bullish'
                elif v < -0.05:
                    direction = '📉 Bearish'
                else:
                    direction = '➡️ Neutral'

                formatted_value = f"{v:.4f} ({direction})"
                indicator_scores.append(f"- {name}: {formatted_value}")
            elif k in ['total_score', 'confidence', 'alignment']:
                if k == 'confidence':
                    formatted_value = f"{v:.2f}%"
                elif k == 'alignment':
                    formatted_value = f"{v:.2f}% signal agreement"
                else:
                    formatted_value = f"{v:.4f}"
                meta_scores.append(f"- {k}: {formatted_value}")
            elif k in ['trend_strength', 'trend_direction', 'trend_alignment', 'multi_timeframe_confidence']:
                if k == 'trend_strength':
                    # Format with direction indicator
                    if v > 0.5:
                        strength = 'Strong Bullish'
                    elif v > 0.2:
                        strength = 'Bullish'
                    elif v > -0.2:
                        strength = 'Neutral'
                    elif v > -0.5:
                        strength = 'Bearish'
                    else:
                        strength = 'Strong Bearish'
                    formatted_value = f"{v:.2f} ({strength})"
                elif k == 'trend_direction':
                    formatted_value = v
                elif k == 'trend_alignment':
                    if v > 0.8:
                        alignment = 'Very High'
                    elif v > 0.6:
                        alignment = 'High'
                    elif v > 0.4:
                        alignment = 'Moderate'
                    else:
                        alignment = 'Low'
                    formatted_value = f"{v:.2f} ({alignment})"
                elif k == 'multi_timeframe_confidence':
                    formatted_value = f"{v:.2f}%"
                else:
                    formatted_value = str(v)
                multi_timeframe_scores.append(f"- {k}: {formatted_value}")

        # Build the scores section with categories
        scores_section = "\nSignal Analysis:\n"

        if indicator_scores:
            scores_section += "\nIndicator Signals:\n" + "\n".join(indicator_scores)

        if multi_timeframe_scores:
            scores_section += "\nMulti-Timeframe Analysis:\n" + "\n".join(multi_timeframe_scores)

        if meta_scores:
            scores_section += "\nSignal Metrics:\n" + "\n".join(meta_scores)

    # Add market regime section if available
    regime_section = ""
    if market_regime:
        regime_section = f"\nMarket Regime: {market_regime}\n"

        # Add more details if multi_timeframe_analysis is available
        if multi_timeframe_analysis and 'trend_metrics' in multi_timeframe_analysis:
            metrics = multi_timeframe_analysis['trend_metrics']
            regime_section += f"Trend Strength: {metrics.get('trend_strength', 0):.2f}\n"
            regime_section += f"Trend Direction: {metrics.get('trend_direction', 'neutral')}\n"
            regime_section += f"Volatility: {metrics.get('volatility', 0):.2f}%\n"
            regime_section += f"Trend Alignment: {metrics.get('trend_alignment', 0):.2f}\n"

    # SMART ENHANCEMENT: Build enhanced prompt with better context and decision criteria
    prompt = f"""
🤖 ADVANCED TRADING ANALYSIS REQUEST

📊 MARKET DATA ANALYSIS:
{features_section}{tick_section}{trades_section}{context_section}{exchange_section}{scores_section}{regime_section}

🎯 ENHANCED DECISION CRITERIA:
You are an expert cryptocurrency trading AI with access to comprehensive market analysis. Consider these factors:

1. SIGNAL STRENGTH: Look for convergence between multiple indicators
2. MARKET REGIME: Adapt strategy based on current market conditions
3. MULTI-TIMEFRAME ALIGNMENT: Higher timeframe trends should guide decisions
4. RISK-REWARD RATIO: Ensure favorable risk-reward before taking positions
5. CONFIDENCE THRESHOLD: Only recommend trades with >60% confidence

📈 DECISION GUIDELINES:
- LONG: Strong bullish signals, positive trend alignment, favorable regime
- SHORT: Strong bearish signals, negative trend alignment, trending market
- WAIT: Mixed signals, low confidence, or unfavorable risk-reward

⚠️ RISK MANAGEMENT:
- Consider market volatility for position sizing
- Set appropriate stop-losses based on ATR and support/resistance
- Take profits should be 1.5-3x the stop-loss distance

🎯 REQUIRED RESPONSE FORMAT:
<decision>LONG/SHORT/WAIT</decision>
<confidence level: 0-100%>
<take profit: X%> (if LONG/SHORT)
<stop loss: X%> (if LONG/SHORT)
<explanation>
Provide a concise explanation (2-3 sentences) covering:
- Primary reason for the decision
- How market regime and multi-timeframe analysis influenced your choice
- Key risk factors or confirmation signals
</explanation>

Focus on high-probability setups with clear risk management. Be conservative with low-confidence signals.
"""

    return prompt

def parse_llm_response(response):
    """
    Parse the LLM's response to extract the decision, confidence, TP/SL, and explanation.

    Args:
        response: Raw response from the LLM

    Returns:
        dict: Parsed response with keys: decision, confidence, take_profit, stop_loss, explanation
    """
    # Clean up the response
    clean_response = response.strip()

    # Initialize result dictionary
    result = {
        'decision': 'WAIT',
        'confidence': None,
        'take_profit': None,
        'stop_loss': None,
        'explanation': "No explanation provided."
    }

    # Extract the decision (LONG, SHORT, or WAIT)
    decision_keywords = ['LONG', 'SHORT', 'WAIT']
    for keyword in decision_keywords:
        if keyword in clean_response:
            result['decision'] = keyword
            break

    # Extract confidence level
    confidence_match = re.search(r'confidence level:?\s*(\d+(?:\.\d+)?)\s*%', clean_response, re.IGNORECASE)
    if confidence_match:
        try:
            result['confidence'] = float(confidence_match.group(1))
        except ValueError:
            pass

    # Extract take profit
    tp_match = re.search(r'take profit:?\s*(\d+(?:\.\d+)?)\s*%', clean_response, re.IGNORECASE)
    if tp_match:
        try:
            result['take_profit'] = float(tp_match.group(1))
        except ValueError:
            pass

    # Extract stop loss
    sl_match = re.search(r'stop loss:?\s*(\d+(?:\.\d+)?)\s*%', clean_response, re.IGNORECASE)
    if sl_match:
        try:
            result['stop_loss'] = float(sl_match.group(1))
        except ValueError:
            pass

    # Extract explanation
    # Look for the explanation after all the structured data
    explanation_patterns = [
        r'<short explanation>\s*(.+)',
        r'explanation:?\s*(.+)',
        r'reasoning:?\s*(.+)'
    ]

    for pattern in explanation_patterns:
        match = re.search(pattern, clean_response, re.IGNORECASE | re.DOTALL)
        if match:
            result['explanation'] = match.group(1).strip()
            break

    # If no explanation found using patterns, use everything after the decision
    if result['explanation'] == "No explanation provided.":
        parts = clean_response.split(result['decision'], 1)
        if len(parts) > 1:
            # Remove any TP/SL/confidence lines
            explanation_text = parts[1]
            explanation_text = re.sub(r'<confidence level:?\s*\d+(?:\.\d+)?\s*%>.*?\n', '', explanation_text, flags=re.IGNORECASE)
            explanation_text = re.sub(r'<take profit:?\s*\d+(?:\.\d+)?\s*%>.*?\n', '', explanation_text, flags=re.IGNORECASE)
            explanation_text = re.sub(r'<stop loss:?\s*\d+(?:\.\d+)?\s*%>.*?\n', '', explanation_text, flags=re.IGNORECASE)
            result['explanation'] = explanation_text.strip()

    return result
