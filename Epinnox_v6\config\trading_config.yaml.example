# Epinnox v6 - Trading Configuration
# Copy this file to trading_config.yaml and customize for your trading strategy

# Trading Parameters
trading:
  symbol: "DOGE/USDT:USDT"  # Trading pair
  leverage: 20              # Leverage multiplier
  balance: 50               # Base balance in USDT
  timeframes: ["1m", "5m", "15m"]  # Analysis timeframes
  
# Exchange Configuration
exchange:
  name: "htx"               # Exchange name
  sandbox: true             # Use sandbox for testing
  demo_mode: false          # Enable for paper trading
  
# Risk Management
risk:
  max_position_size: 0.1    # Maximum position as % of balance
  stop_loss_pct: 0.02       # Stop loss percentage (2%)
  take_profit_pct: 0.04     # Take profit percentage (4%)
  max_daily_loss: 0.05      # Maximum daily loss (5%)
  
# ML Model Configuration
ml:
  models: ["svm", "random_forest", "lstm"]
  prediction_window: 5      # Prediction evaluation window (minutes)
  confidence_threshold: 0.6 # Minimum confidence for trading
  accuracy_threshold: 0.7   # Minimum accuracy for model trust
  
# Analysis Settings
analysis:
  continuous_mode: true     # Run continuously
  analysis_interval: 60     # Seconds between analyses
  live_data: true          # Use live market data
  
# GUI Settings
gui:
  theme: "matrix"           # Interface theme
  auto_save_layout: true    # Save panel arrangements
  update_frequency: 1000    # GUI update interval (ms)
  
# Logging
logging:
  level: "INFO"             # DEBUG, INFO, WARNING, ERROR
  file_logging: true        # Enable file logging
  console_logging: true     # Enable console output
  
# Performance
performance:
  cache_size: 1000          # Data cache size
  max_workers: 4            # Thread pool size
  memory_limit: 512         # Memory limit in MB
