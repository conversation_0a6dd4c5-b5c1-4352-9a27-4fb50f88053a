"""
Real-time Trading Dashboard for EPINNOX v6
Streamlit-based dashboard for monitoring autonomous trading performance
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import sqlite3
import json
import os
import time

# Dashboard configuration
st.set_page_config(
    page_title="EPINNOX v6 Trading Dashboard",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

class TradingDashboard:
    """Real-time trading dashboard"""
    
    def __init__(self):
        self.db_path = "data/trading_performance.db"
        self.refresh_interval = 30  # seconds
        
    def load_performance_data(self):
        """Load performance data from database"""
        try:
            if not os.path.exists(self.db_path):
                return pd.DataFrame(), pd.DataFrame()
            
            conn = sqlite3.connect(self.db_path)
            
            # Load trades
            trades_df = pd.read_sql_query('''
                SELECT * FROM trades 
                ORDER BY timestamp DESC 
                LIMIT 1000
            ''', conn)
            
            # Load performance metrics
            metrics_df = pd.read_sql_query('''
                SELECT * FROM performance_metrics 
                ORDER BY date DESC 
                LIMIT 90
            ''', conn)
            
            conn.close()
            
            # Convert timestamps
            if not trades_df.empty:
                trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'])
            if not metrics_df.empty:
                metrics_df['date'] = pd.to_datetime(metrics_df['date'])
            
            return trades_df, metrics_df
            
        except Exception as e:
            st.error(f"Error loading data: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def create_portfolio_summary(self, trades_df):
        """Create portfolio summary metrics"""
        if trades_df.empty:
            return {
                'total_trades': 0,
                'total_pnl': 0,
                'win_rate': 0,
                'best_trade': 0,
                'worst_trade': 0,
                'avg_trade_duration': 0
            }
        
        # Calculate metrics
        total_trades = len(trades_df)
        total_pnl = trades_df['pnl_usd'].sum() if 'pnl_usd' in trades_df.columns else 0
        winning_trades = len(trades_df[trades_df['pnl_usd'] > 0]) if 'pnl_usd' in trades_df.columns else 0
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        best_trade = trades_df['pnl_usd'].max() if 'pnl_usd' in trades_df.columns else 0
        worst_trade = trades_df['pnl_usd'].min() if 'pnl_usd' in trades_df.columns else 0
        avg_duration = trades_df['duration_minutes'].mean() if 'duration_minutes' in trades_df.columns else 0
        
        return {
            'total_trades': total_trades,
            'total_pnl': total_pnl,
            'win_rate': win_rate,
            'best_trade': best_trade,
            'worst_trade': worst_trade,
            'avg_trade_duration': avg_duration
        }
    
    def create_equity_curve(self, trades_df):
        """Create equity curve chart"""
        if trades_df.empty or 'pnl_usd' not in trades_df.columns:
            fig = go.Figure()
            fig.add_annotation(text="No trade data available", 
                             xref="paper", yref="paper", x=0.5, y=0.5)
            return fig
        
        # Calculate cumulative PnL
        trades_sorted = trades_df.sort_values('timestamp')
        trades_sorted['cumulative_pnl'] = trades_sorted['pnl_usd'].cumsum()
        trades_sorted['equity'] = 10000 + trades_sorted['cumulative_pnl']  # Assume $10k start
        
        fig = go.Figure()
        
        # Add equity curve
        fig.add_trace(go.Scatter(
            x=trades_sorted['timestamp'],
            y=trades_sorted['equity'],
            mode='lines',
            name='Portfolio Equity',
            line=dict(color='#00CC96', width=2)
        ))
        
        # Add baseline
        fig.add_hline(y=10000, line_dash="dash", line_color="gray", 
                     annotation_text="Initial Balance")
        
        fig.update_layout(
            title="Portfolio Equity Curve",
            xaxis_title="Date",
            yaxis_title="Portfolio Value ($)",
            hovermode='x unified'
        )
        
        return fig
    
    def create_pnl_distribution(self, trades_df):
        """Create P&L distribution chart"""
        if trades_df.empty or 'pnl_usd' not in trades_df.columns:
            return go.Figure()
        
        fig = go.Figure()
        
        # Separate winning and losing trades
        winning_trades = trades_df[trades_df['pnl_usd'] > 0]['pnl_usd']
        losing_trades = trades_df[trades_df['pnl_usd'] < 0]['pnl_usd']
        
        # Add histograms
        if not winning_trades.empty:
            fig.add_trace(go.Histogram(
                x=winning_trades,
                name='Winning Trades',
                marker_color='green',
                opacity=0.7,
                nbinsx=20
            ))
        
        if not losing_trades.empty:
            fig.add_trace(go.Histogram(
                x=losing_trades,
                name='Losing Trades',
                marker_color='red',
                opacity=0.7,
                nbinsx=20
            ))
        
        fig.update_layout(
            title="P&L Distribution",
            xaxis_title="P&L ($)",
            yaxis_title="Number of Trades",
            barmode='overlay'
        )
        
        return fig
    
    def create_performance_metrics_chart(self, metrics_df):
        """Create performance metrics over time"""
        if metrics_df.empty:
            return go.Figure()
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Win Rate', 'Total P&L', 'Sharpe Ratio', 'Max Drawdown'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # Win Rate
        fig.add_trace(
            go.Scatter(x=metrics_df['date'], y=metrics_df['win_rate'], 
                      name='Win Rate', line=dict(color='blue')),
            row=1, col=1
        )
        
        # Total P&L
        fig.add_trace(
            go.Scatter(x=metrics_df['date'], y=metrics_df['total_pnl'], 
                      name='Total P&L', line=dict(color='green')),
            row=1, col=2
        )
        
        # Sharpe Ratio
        if 'sharpe_ratio' in metrics_df.columns:
            fig.add_trace(
                go.Scatter(x=metrics_df['date'], y=metrics_df['sharpe_ratio'], 
                          name='Sharpe Ratio', line=dict(color='orange')),
                row=2, col=1
            )
        
        # Max Drawdown
        if 'max_drawdown' in metrics_df.columns:
            fig.add_trace(
                go.Scatter(x=metrics_df['date'], y=metrics_df['max_drawdown'], 
                          name='Max Drawdown', line=dict(color='red')),
                row=2, col=2
            )
        
        fig.update_layout(height=600, showlegend=False, title_text="Performance Metrics Over Time")
        
        return fig
    
    def create_symbol_performance(self, trades_df):
        """Create symbol performance breakdown"""
        if trades_df.empty or 'symbol' not in trades_df.columns:
            return go.Figure()
        
        # Group by symbol
        symbol_stats = trades_df.groupby('symbol').agg({
            'pnl_usd': ['sum', 'count', 'mean'],
            'confidence': 'mean'
        }).round(2)
        
        symbol_stats.columns = ['Total PnL', 'Trade Count', 'Avg PnL', 'Avg Confidence']
        symbol_stats = symbol_stats.reset_index()
        
        # Create bar chart
        fig = go.Figure()
        
        fig.add_trace(go.Bar(
            x=symbol_stats['symbol'],
            y=symbol_stats['Total PnL'],
            name='Total P&L',
            marker_color=['green' if x > 0 else 'red' for x in symbol_stats['Total PnL']]
        ))
        
        fig.update_layout(
            title="P&L by Symbol",
            xaxis_title="Symbol",
            yaxis_title="Total P&L ($)"
        )
        
        return fig
    
    def run_dashboard(self):
        """Run the main dashboard"""
        
        # Header
        st.title("📈 EPINNOX v6 Trading Dashboard")
        st.markdown("Real-time autonomous trading performance monitoring")
        
        # Sidebar controls
        st.sidebar.header("Dashboard Controls")
        
        auto_refresh = st.sidebar.checkbox("Auto Refresh", value=True)
        refresh_interval = st.sidebar.slider("Refresh Interval (seconds)", 10, 300, 30)
        
        if st.sidebar.button("🔄 Refresh Now"):
            st.experimental_rerun()
        
        # Load data
        with st.spinner("Loading trading data..."):
            trades_df, metrics_df = self.load_performance_data()
        
        # Portfolio summary
        portfolio_summary = self.create_portfolio_summary(trades_df)
        
        # Key metrics row
        col1, col2, col3, col4, col5 = st.columns(5)
        
        with col1:
            st.metric(
                label="Total Trades",
                value=portfolio_summary['total_trades']
            )
        
        with col2:
            st.metric(
                label="Total P&L",
                value=f"${portfolio_summary['total_pnl']:.2f}",
                delta=f"{portfolio_summary['total_pnl']:.2f}"
            )
        
        with col3:
            st.metric(
                label="Win Rate",
                value=f"{portfolio_summary['win_rate']:.1%}"
            )
        
        with col4:
            st.metric(
                label="Best Trade",
                value=f"${portfolio_summary['best_trade']:.2f}"
            )
        
        with col5:
            st.metric(
                label="Worst Trade",
                value=f"${portfolio_summary['worst_trade']:.2f}"
            )
        
        # Charts row 1
        col1, col2 = st.columns(2)
        
        with col1:
            st.plotly_chart(self.create_equity_curve(trades_df), use_container_width=True)
        
        with col2:
            st.plotly_chart(self.create_pnl_distribution(trades_df), use_container_width=True)
        
        # Charts row 2
        col1, col2 = st.columns(2)
        
        with col1:
            st.plotly_chart(self.create_performance_metrics_chart(metrics_df), use_container_width=True)
        
        with col2:
            st.plotly_chart(self.create_symbol_performance(trades_df), use_container_width=True)
        
        # Recent trades table
        st.subheader("📋 Recent Trades")
        
        if not trades_df.empty:
            # Display recent trades
            recent_trades = trades_df.head(20)[['timestamp', 'symbol', 'decision', 'confidence', 'pnl_usd', 'execution_status']]
            recent_trades['timestamp'] = recent_trades['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # Color code P&L
            def color_pnl(val):
                if pd.isna(val):
                    return ''
                color = 'green' if val > 0 else 'red' if val < 0 else 'black'
                return f'color: {color}'
            
            styled_trades = recent_trades.style.applymap(color_pnl, subset=['pnl_usd'])
            st.dataframe(styled_trades, use_container_width=True)
        else:
            st.info("No trade data available")
        
        # System status
        st.subheader("🔧 System Status")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.info("🟢 Trading System: Active")
        
        with col2:
            st.info(f"📊 Database: {len(trades_df)} trades")
        
        with col3:
            st.info(f"⏰ Last Update: {datetime.now().strftime('%H:%M:%S')}")
        
        # Auto refresh
        if auto_refresh:
            time.sleep(refresh_interval)
            st.experimental_rerun()

def main():
    """Main dashboard entry point"""
    dashboard = TradingDashboard()
    dashboard.run_dashboard()

if __name__ == "__main__":
    main()
