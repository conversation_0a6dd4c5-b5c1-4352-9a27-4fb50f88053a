"""
LLM Response Parsers - Parse and validate LLM responses for different prompt types
Handles JSON parsing, validation, and fallback mechanisms
"""

import json
import re
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class LLMResponseParsers:
    """
    Collection of response parsers for different LLM prompt types
    Each parser handles specific response formats and validation
    """
    
    def __init__(self):
        self.default_responses = {
            'emergency': {'ACTION': 'MONITOR', 'PRIORITY': 'LOW', 'CONFIDENCE': 50},
            'position': {'ACTION': 'HOLD', 'CONFIDENCE': 50, 'URGENCY': 'LOW'},
            'profit': {'ACTION': 'HOLD', 'CONFIDENCE': 50, 'CLOSE_PERCENTAGE': 0},
            'regime': {'REGIME': 'UNKNOWN', 'CONFIDENCE': 50, 'SCALP_SUITABILITY': 'MEDIUM'},
            'risk': {'APPROVED': True, 'RISK_SCORE': 30, 'CONFIDENCE': 70, 'MAX_POSITION_SIZE': 2.0},  # 🚀 FIXED: Default to APPROVED for small accounts
            'entry': {'ACTION': 'WAIT', 'CONFIDENCE': 50, 'ENTRY_TYPE': 'LIMIT'},
            'strategy': {'RISK_ADJUSTMENT': 1.0, 'CONFIDENCE': 50, 'HOLD_TIME_TARGET': 8},
            'opportunity': {'BEST_OPPORTUNITY': 'NONE', 'CONFIDENCE': 50, 'SETUP_TYPE': 'NONE'}
        }
    
    def parse_emergency_response(self, response_text: str) -> Dict[str, Any]:
        """Parse emergency response LLM output"""
        try:
            # Extract JSON from response
            json_data = self.extract_json_from_response(response_text)
            
            if not json_data:
                logger.warning("No JSON found in emergency response, using fallback")
                return self.default_responses['emergency']
            
            # Validate required fields
            required_fields = ['ACTION', 'PRIORITY']
            if not all(field in json_data for field in required_fields):
                logger.warning("Missing required fields in emergency response")
                return self.default_responses['emergency']
            
            # Validate ACTION values
            valid_actions = ['CLOSE_ALL', 'CLOSE_LOSING', 'HEDGE', 'MONITOR', 'REDUCE_SIZE']
            if json_data['ACTION'] not in valid_actions:
                logger.warning(f"Invalid emergency action: {json_data['ACTION']}")
                json_data['ACTION'] = 'MONITOR'
            
            # Validate PRIORITY values
            valid_priorities = ['IMMEDIATE', 'HIGH', 'MEDIUM', 'LOW']
            if json_data['PRIORITY'] not in valid_priorities:
                json_data['PRIORITY'] = 'MEDIUM'
            
            # Ensure confidence is within range
            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))
            
            logger.info(f"Emergency response parsed: {json_data['ACTION']} ({json_data['PRIORITY']} priority)")
            return json_data
            
        except Exception as e:
            logger.error(f"Error parsing emergency response: {e}")
            return self.default_responses['emergency']
    
    def parse_position_management_response(self, response_text: str) -> Dict[str, Any]:
        """Parse position management LLM output"""
        try:
            json_data = self.extract_json_from_response(response_text)
            
            if not json_data:
                return self.default_responses['position']
            
            # Validate ACTION
            valid_actions = ['HOLD', 'CLOSE', 'PARTIAL_CLOSE']
            if json_data.get('ACTION') not in valid_actions:
                json_data['ACTION'] = 'HOLD'
            
            # Validate and convert price fields
            for price_field in ['STOP_LOSS', 'TAKE_PROFIT']:
                if price_field in json_data:
                    try:
                        json_data[price_field] = float(json_data[price_field])
                    except (ValueError, TypeError):
                        json_data[price_field] = 0.0
            
            # Validate boolean fields
            json_data['TRAIL_STOP'] = bool(json_data.get('TRAIL_STOP', False))
            
            # Validate urgency
            valid_urgency = ['LOW', 'MEDIUM', 'HIGH', 'IMMEDIATE']
            if json_data.get('URGENCY') not in valid_urgency:
                json_data['URGENCY'] = 'LOW'
            
            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))
            
            logger.info(f"Position management parsed: {json_data['ACTION']} ({json_data['URGENCY']} urgency)")
            return json_data
            
        except Exception as e:
            logger.error(f"Error parsing position management response: {e}")
            return self.default_responses['position']
    
    def parse_profit_optimization_response(self, response_text: str) -> Dict[str, Any]:
        """Parse profit optimization LLM output"""
        try:
            json_data = self.extract_json_from_response(response_text)
            
            if not json_data:
                return self.default_responses['profit']
            
            # Validate ACTION
            valid_actions = ['HOLD', 'PARTIAL_CLOSE', 'FULL_CLOSE']
            if json_data.get('ACTION') not in valid_actions:
                json_data['ACTION'] = 'HOLD'
            
            # Validate CLOSE_PERCENTAGE
            if 'CLOSE_PERCENTAGE' in json_data:
                try:
                    close_pct = float(json_data['CLOSE_PERCENTAGE'])
                    json_data['CLOSE_PERCENTAGE'] = max(0, min(100, close_pct))
                except (ValueError, TypeError):
                    json_data['CLOSE_PERCENTAGE'] = 0
            
            # Validate price fields
            for price_field in ['NEW_STOP']:
                if price_field in json_data:
                    try:
                        json_data[price_field] = float(json_data[price_field])
                    except (ValueError, TypeError):
                        json_data[price_field] = 0.0
            
            json_data['TRAIL_STOP'] = bool(json_data.get('TRAIL_STOP', False))
            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))
            
            logger.info(f"Profit optimization parsed: {json_data['ACTION']} ({json_data.get('CLOSE_PERCENTAGE', 0)}%)")
            return json_data
            
        except Exception as e:
            logger.error(f"Error parsing profit optimization response: {e}")
            return self.default_responses['profit']
    
    def parse_market_regime_response(self, response_text: str) -> Dict[str, Any]:
        """Parse market regime detection LLM output"""
        try:
            json_data = self.extract_json_from_response(response_text)
            
            if not json_data:
                return self.default_responses['regime']
            
            # Validate REGIME
            valid_regimes = ['TRENDING_BULL', 'TRENDING_BEAR', 'RANGING_TIGHT', 
                           'RANGING_VOLATILE', 'BREAKOUT_PENDING', 'NEWS_DRIVEN']
            if json_data.get('REGIME') not in valid_regimes:
                json_data['REGIME'] = 'RANGING_TIGHT'
            
            # Validate SCALP_SUITABILITY
            valid_suitability = ['HIGH', 'MEDIUM', 'LOW']
            if json_data.get('SCALP_SUITABILITY') not in valid_suitability:
                json_data['SCALP_SUITABILITY'] = 'MEDIUM'
            
            # Validate RECOMMENDED_TIMEFRAME
            valid_timeframes = ['1m', '5m', '15m']
            if json_data.get('RECOMMENDED_TIMEFRAME') not in valid_timeframes:
                json_data['RECOMMENDED_TIMEFRAME'] = '1m'
            
            # Validate RISK_LEVEL
            valid_risk_levels = ['LOW', 'MEDIUM', 'HIGH']
            if json_data.get('RISK_LEVEL') not in valid_risk_levels:
                json_data['RISK_LEVEL'] = 'MEDIUM'
            
            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))
            
            logger.info(f"Market regime parsed: {json_data['REGIME']} ({json_data['SCALP_SUITABILITY']} scalp suitability)")
            return json_data
            
        except Exception as e:
            logger.error(f"Error parsing market regime response: {e}")
            return self.default_responses['regime']
    
    def parse_risk_assessment_response(self, response_text: str) -> Dict[str, Any]:
        """Parse risk assessment LLM output with enhanced decision extraction"""
        try:
            json_data = self.extract_json_from_response(response_text)

            if not json_data:
                # 🚀 FIXED: Try to extract decision from text for risk assessment
                decision_data = self.extract_trading_decision_from_text(response_text)
                if decision_data:
                    # Convert trading decision to risk assessment format
                    decision = decision_data.get('DECISION', 'WAIT')
                    confidence = decision_data.get('CONFIDENCE', 50)

                    # 🚀 FIXED: More aggressive approval for small accounts
                    if decision in ['LONG', 'SHORT']:
                        # Approve trades with reasonable confidence for small accounts
                        approved = confidence >= 60  # Lower threshold for small accounts
                        risk_score = max(10, 100 - confidence)  # Higher confidence = lower risk
                        # Calculate appropriate position size for small accounts
                        max_position = min(3.0, confidence * 0.05)  # $1-3 for small accounts
                    else:
                        approved = False
                        risk_score = min(90, 50 + (100 - confidence) / 2)  # Lower confidence = higher risk
                        max_position = 0.0

                    return {
                        'APPROVED': approved,
                        'RISK_SCORE': risk_score,
                        'CONFIDENCE': confidence,
                        'MAX_POSITION_SIZE': max_position,  # 🚀 FIXED: Add position sizing
                        'DECISION': decision,  # 🚀 PRESERVE the actual trading decision
                        'REASONING': decision_data.get('EXPLANATION', f'Risk assessment: {decision} with {confidence}% confidence')
                    }
                return self.default_responses['risk']

            # Validate APPROVED
            json_data['APPROVED'] = bool(json_data.get('APPROVED', False))

            # Validate RISK_SCORE
            if 'RISK_SCORE' in json_data:
                try:
                    risk_score = float(json_data['RISK_SCORE'])
                    json_data['RISK_SCORE'] = max(0, min(100, risk_score))
                except (ValueError, TypeError):
                    json_data['RISK_SCORE'] = 50

            # Validate MAX_POSITION_SIZE
            if 'MAX_POSITION_SIZE' in json_data:
                try:
                    json_data['MAX_POSITION_SIZE'] = float(json_data['MAX_POSITION_SIZE'])
                except (ValueError, TypeError):
                    json_data['MAX_POSITION_SIZE'] = 0

            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))

            logger.info(f"Risk assessment parsed: {'APPROVED' if json_data['APPROVED'] else 'REJECTED'} (Risk: {json_data.get('RISK_SCORE', 50)})")
            return json_data

        except Exception as e:
            logger.error(f"Error parsing risk assessment response: {e}")
            return self.default_responses['risk']
    
    def parse_entry_timing_response(self, response_text: str) -> Dict[str, Any]:
        """Parse entry timing LLM output with enhanced decision extraction"""
        try:
            json_data = self.extract_json_from_response(response_text)

            if not json_data:
                # 🚀 FIXED: Try to extract trading decision for entry timing
                decision_data = self.extract_trading_decision_from_text(response_text)
                if decision_data:
                    decision = decision_data.get('DECISION', 'WAIT')
                    confidence = decision_data.get('CONFIDENCE', 50)

                    # Map trading decision to entry timing action
                    if decision in ['LONG', 'SHORT'] and confidence > 70:
                        action = 'ENTER_NOW'
                    elif decision in ['LONG', 'SHORT'] and confidence > 50:
                        action = 'WAIT'  # Wait for better entry
                    else:
                        action = 'WAIT'

                    return {
                        'ACTION': action,
                        'CONFIDENCE': confidence,
                        'ENTRY_TYPE': 'LIMIT',
                        'DECISION': decision,  # 🚀 PRESERVE the actual trading decision
                        'REASONING': decision_data.get('EXPLANATION', f'Entry timing based on {decision} decision')
                    }
                return self.default_responses['entry']

            # Validate ACTION
            valid_actions = ['ENTER_NOW', 'WAIT', 'ABORT']
            if json_data.get('ACTION') not in valid_actions:
                # Try to map from DECISION field
                decision = json_data.get('DECISION', 'WAIT')
                if decision in ['LONG', 'SHORT']:
                    json_data['ACTION'] = 'ENTER_NOW'
                else:
                    json_data['ACTION'] = 'WAIT'

            # Validate ENTRY_TYPE
            valid_entry_types = ['MARKET', 'LIMIT']
            if json_data.get('ENTRY_TYPE') not in valid_entry_types:
                json_data['ENTRY_TYPE'] = 'LIMIT'

            # Validate MAX_WAIT_SECONDS
            if 'MAX_WAIT_SECONDS' in json_data:
                try:
                    json_data['MAX_WAIT_SECONDS'] = int(json_data['MAX_WAIT_SECONDS'])
                except (ValueError, TypeError):
                    json_data['MAX_WAIT_SECONDS'] = 60

            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))

            logger.info(f"Entry timing parsed: {json_data['ACTION']} ({json_data['ENTRY_TYPE']} order)")
            return json_data

        except Exception as e:
            logger.error(f"Error parsing entry timing response: {e}")
            return self.default_responses['entry']
    
    def parse_strategy_adaptation_response(self, response_text: str) -> Dict[str, Any]:
        """Parse strategy adaptation LLM output"""
        try:
            json_data = self.extract_json_from_response(response_text)
            
            if not json_data:
                return self.default_responses['strategy']
            
            # Validate RISK_ADJUSTMENT
            if 'RISK_ADJUSTMENT' in json_data:
                try:
                    risk_adj = float(json_data['RISK_ADJUSTMENT'])
                    json_data['RISK_ADJUSTMENT'] = max(0.5, min(2.0, risk_adj))
                except (ValueError, TypeError):
                    json_data['RISK_ADJUSTMENT'] = 1.0
            
            # Validate HOLD_TIME_TARGET
            if 'HOLD_TIME_TARGET' in json_data:
                try:
                    json_data['HOLD_TIME_TARGET'] = int(json_data['HOLD_TIME_TARGET'])
                except (ValueError, TypeError):
                    json_data['HOLD_TIME_TARGET'] = 8
            
            # Validate threshold values
            for threshold in ['ENTRY_THRESHOLD', 'EXIT_THRESHOLD']:
                if threshold in json_data:
                    try:
                        value = float(json_data[threshold])
                        json_data[threshold] = max(50, min(95, value))
                    except (ValueError, TypeError):
                        json_data[threshold] = 70
            
            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))
            
            logger.info(f"Strategy adaptation parsed: Risk adj {json_data.get('RISK_ADJUSTMENT', 1.0):.1f}x")
            return json_data
            
        except Exception as e:
            logger.error(f"Error parsing strategy adaptation response: {e}")
            return self.default_responses['strategy']
    
    def parse_opportunity_scanner_response(self, response_text: str) -> Dict[str, Any]:
        """Parse opportunity scanner LLM output with enhanced decision extraction"""
        try:
            json_data = self.extract_json_from_response(response_text)

            if not json_data:
                # 🚀 FIXED: Try to extract trading decision for opportunity scanner
                decision_data = self.extract_trading_decision_from_text(response_text)
                if decision_data:
                    decision = decision_data.get('DECISION', 'WAIT')
                    confidence = decision_data.get('CONFIDENCE', 50)

                    # Map trading decision to opportunity format
                    if decision in ['LONG', 'SHORT']:
                        best_opp = 'BREAKOUT' if confidence > 75 else 'MOMENTUM'
                        setup_type = 'MOMENTUM' if decision == 'LONG' else 'REVERSAL'
                    else:
                        best_opp = 'NONE'
                        setup_type = 'NONE'

                    return {
                        'BEST_OPPORTUNITY': best_opp,
                        'SETUP_TYPE': setup_type,
                        'CONFIDENCE': confidence,
                        'DECISION': decision,  # 🚀 PRESERVE the actual trading decision
                        'REASONING': decision_data.get('EXPLANATION', f'Opportunity based on {decision} decision')
                    }
                return self.default_responses['opportunity']

            # Validate SETUP_TYPE
            valid_setups = ['BREAKOUT', 'RANGE_BREAKOUT', 'TREND_CONTINUATION',
                          'REVERSAL', 'MOMENTUM', 'NONE']
            if json_data.get('SETUP_TYPE') not in valid_setups:
                json_data['SETUP_TYPE'] = 'NONE'

            json_data['CONFIDENCE'] = max(0, min(100, json_data.get('CONFIDENCE', 50)))

            logger.info(f"Opportunity scanner parsed: {json_data.get('BEST_OPPORTUNITY', 'NONE')} ({json_data.get('SETUP_TYPE', 'NONE')})")
            return json_data

        except Exception as e:
            logger.error(f"Error parsing opportunity scanner response: {e}")
            return self.default_responses['opportunity']
    
    def extract_json_from_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """Extract and parse JSON from LLM response text with fallback to structured text parsing"""
        try:
            # Clean the response
            cleaned = self.clean_response_text(response_text)

            # 🚀 FIXED: Try structured text parsing first (handles "DECISION: SHORT, CONFIDENCE: 85%" format)
            structured_data = self.parse_structured_text(cleaned)
            if structured_data:
                return structured_data

            # Try to find JSON object
            json_match = re.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', cleaned, re.DOTALL)

            if json_match:
                json_str = json_match.group(0)

                # Fix common JSON issues
                json_str = self.fix_json_syntax(json_str)

                # Parse JSON
                return json.loads(json_str)

            return None

        except json.JSONDecodeError as e:
            logger.warning(f"JSON decode error: {e}")
            # Try structured text parsing as fallback
            return self.parse_structured_text(response_text)
        except Exception as e:
            logger.error(f"Error extracting JSON: {e}")
            return None

    def parse_structured_text(self, text: str) -> Optional[Dict[str, Any]]:
        """Parse structured text format like 'DECISION: SHORT, CONFIDENCE: 85%'"""
        try:
            result = {}

            # Common patterns for LLM responses
            patterns = {
                'DECISION': r'DECISION:\s*([A-Z_]+)',
                'ACTION': r'ACTION:\s*([A-Z_]+)',
                'CONFIDENCE': r'CONFIDENCE:\s*(\d+(?:\.\d+)?)\s*%?',
                'REGIME': r'REGIME:\s*([A-Z_]+)',
                'APPROVED': r'APPROVED:\s*(TRUE|FALSE|YES|NO)',
                'RISK_SCORE': r'RISK[_\s]SCORE:\s*(\d+(?:\.\d+)?)',
                'ENTRY_TYPE': r'ENTRY[_\s]TYPE:\s*([A-Z]+)',
                'SCALP_SUITABILITY': r'SCALP[_\s]SUITABILITY:\s*([A-Z]+)',
                'SETUP_TYPE': r'SETUP[_\s]TYPE:\s*([A-Z_]+)',
                'BEST_OPPORTUNITY': r'BEST[_\s]OPPORTUNITY:\s*([A-Z_]+)',
                'RISK_ADJUSTMENT': r'RISK[_\s]ADJUSTMENT:\s*(\d+(?:\.\d+)?)',
                'PRIORITY': r'PRIORITY:\s*([A-Z]+)',
                'URGENCY': r'URGENCY:\s*([A-Z]+)',
                'CLOSE_PERCENTAGE': r'CLOSE[_\s]PERCENTAGE:\s*(\d+(?:\.\d+)?)',
                'REASONING': r'REASONING:\s*([^\n\r]+)',
                'EXPLANATION': r'EXPLANATION:\s*([^\n\r]+)',
            }

            # Extract values using regex patterns
            for key, pattern in patterns.items():
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    value = match.group(1).strip()

                    # Convert to appropriate type
                    if key in ['CONFIDENCE', 'RISK_SCORE', 'RISK_ADJUSTMENT', 'CLOSE_PERCENTAGE']:
                        try:
                            result[key] = float(value)
                        except ValueError:
                            result[key] = 50.0  # Default
                    elif key == 'APPROVED':
                        result[key] = value.upper() in ['TRUE', 'YES']
                    else:
                        result[key] = value.upper()

            # Map DECISION to ACTION if ACTION not found
            if 'DECISION' in result and 'ACTION' not in result:
                decision = result['DECISION']
                if decision in ['LONG', 'SHORT']:
                    result['ACTION'] = 'ENTER_NOW'
                elif decision == 'WAIT':
                    result['ACTION'] = 'WAIT'
                elif decision == 'CLOSE':
                    result['ACTION'] = 'CLOSE'

            # Ensure minimum required fields
            if not result:
                return None

            # Add default confidence if missing
            if 'CONFIDENCE' not in result:
                result['CONFIDENCE'] = 50.0

            logger.info(f"Parsed structured text: {result}")
            return result if result else None

        except Exception as e:
            logger.error(f"Error parsing structured text: {e}")
            return None
    
    def clean_response_text(self, text: str) -> str:
        """Clean response text for better JSON extraction"""
        # Remove common prefixes
        prefixes = ["Here's the JSON:", "JSON response:", "Response:", "```json", "```"]
        for prefix in prefixes:
            text = text.replace(prefix, "")
        
        # Remove comments
        text = re.sub(r'//.*?(?=\n|$)', '', text, flags=re.MULTILINE)
        text = re.sub(r'/\*.*?\*/', '', text, flags=re.DOTALL)
        
        return text.strip()
    
    def fix_json_syntax(self, json_str: str) -> str:
        """Fix common JSON syntax issues"""
        # Remove trailing commas
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)
        
        # Fix unquoted keys
        json_str = re.sub(r'(\w+):', r'"\1":', json_str)
        
        # Fix single quotes
        json_str = json_str.replace("'", '"')
        
        return json_str

    def extract_trading_decision_from_text(self, text: str) -> Optional[Dict[str, Any]]:
        """Extract trading decision from free-form text responses"""
        try:
            result = {}

            # Look for decision patterns
            decision_patterns = [
                r'DECISION:\s*([A-Z]+)',
                r'(?:I\s+)?(?:recommend|suggest|advise)(?:ing)?\s+(?:a\s+)?([A-Z]+)',
                r'(?:Go|Take|Enter)\s+([A-Z]+)',
                r'Signal:\s*([A-Z]+)',
                r'Action:\s*([A-Z]+)'
            ]

            for pattern in decision_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    decision = match.group(1).upper()
                    if decision in ['LONG', 'SHORT', 'WAIT', 'BUY', 'SELL', 'HOLD']:
                        # Normalize decision
                        if decision == 'BUY':
                            decision = 'LONG'
                        elif decision == 'SELL':
                            decision = 'SHORT'
                        elif decision == 'HOLD':
                            decision = 'WAIT'

                        result['DECISION'] = decision
                        break

            # Look for confidence patterns
            confidence_patterns = [
                r'CONFIDENCE:\s*(\d+(?:\.\d+)?)\s*%?',
                r'(\d+(?:\.\d+)?)\s*%\s*confidence',
                r'confidence\s*(?:of\s*)?(\d+(?:\.\d+)?)\s*%?',
                r'(\d+(?:\.\d+)?)\s*%\s*certain'
            ]

            for pattern in confidence_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    try:
                        confidence = float(match.group(1))
                        result['CONFIDENCE'] = max(0, min(100, confidence))
                        break
                    except ValueError:
                        continue

            # Look for explanation/reasoning
            explanation_patterns = [
                r'EXPLANATION:\s*([^\n\r]+)',
                r'REASONING:\s*([^\n\r]+)',
                r'(?:because|since|due to)\s+([^\n\r.!?]+)',
            ]

            for pattern in explanation_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    result['EXPLANATION'] = match.group(1).strip()
                    break

            # Set defaults if not found
            if 'CONFIDENCE' not in result:
                result['CONFIDENCE'] = 50.0

            return result if 'DECISION' in result else None

        except Exception as e:
            logger.error(f"Error extracting trading decision from text: {e}")
            return None
    
def parse_llm_entry_decision(raw_reply: str) -> dict:
        """
        Extracts:
          DECISION: BUY/SELL/WAIT
          CONFIDENCE: XX%
          RATIONALE: optional
        """
        result = {'decision': 'WAIT', 'confidence': 0.0, 'rationale': ''}
        try:
            lines = raw_reply.splitlines()
            for line in lines:
                if line.strip().upper().startswith('DECISION:'):
                    result['decision'] = line.split(':', 1)[1].strip().upper()
                elif line.strip().upper().startswith('CONFIDENCE:'):
                    val = line.split(':', 1)[1].strip().replace('%','')
                    try:
                        result['confidence'] = float(val)
                    except Exception:
                        result['confidence'] = 0.0
                elif line.strip().upper().startswith('RATIONALE:'):
                    result['rationale'] = line.split(':', 1)[1].strip()
        except Exception:
            pass
        return result

# Expose parse_llm_entry_decision at module level
parse_llm_entry_decision = parse_llm_entry_decision
