"""
Small Account Position Sizing Fix for Epinnox v6
Fixes position sizing logic for accounts under $100
"""

import logging

logger = logging.getLogger(__name__)

class SmallAccountPositionSizer:
    """Position sizing optimized for small accounts ($10-$100)"""
    
    def __init__(self):
        self.small_account_threshold = 100.0  # Accounts under $100
        self.micro_account_threshold = 25.0   # Accounts under $25
        
    def calculate_position_size(self, account_balance: float, confidence: float, 
                              symbol: str, current_price: float) -> dict:
        """
        Calculate appropriate position size for small accounts
        
        Args:
            account_balance: Current account balance in USD
            confidence: Trading confidence (0-100)
            symbol: Trading symbol
            current_price: Current price of the asset
            
        Returns:
            Dict with position sizing information
        """
        try:
            # Determine account category
            if account_balance < self.micro_account_threshold:
                # Micro accounts ($10-$25): Very conservative
                max_risk_pct = 2.0  # 2% max risk per trade
                max_position_pct = 15.0  # 15% max position size
                min_position_usd = 0.50  # Minimum $0.50 position
            elif account_balance < self.small_account_threshold:
                # Small accounts ($25-$100): Conservative
                max_risk_pct = 3.0  # 3% max risk per trade
                max_position_pct = 20.0  # 20% max position size
                min_position_usd = 1.00  # Minimum $1.00 position
            else:
                # Regular accounts: Standard sizing
                max_risk_pct = 5.0  # 5% max risk per trade
                max_position_pct = 30.0  # 30% max position size
                min_position_usd = 5.00  # Minimum $5.00 position
            
            # Calculate base position size
            base_position_usd = account_balance * (max_position_pct / 100)
            
            # Adjust based on confidence
            confidence_factor = max(0.3, min(1.0, confidence / 100))
            adjusted_position_usd = base_position_usd * confidence_factor
            
            # Ensure minimum position size
            final_position_usd = max(min_position_usd, adjusted_position_usd)
            
            # Convert to asset quantity
            if current_price > 0:
                position_quantity = final_position_usd / current_price
            else:
                position_quantity = 0
                
            # Calculate risk amount
            risk_amount_usd = account_balance * (max_risk_pct / 100)
            
            # Round to appropriate precision
            if symbol.startswith('BTC'):
                position_quantity = round(position_quantity, 6)
            elif symbol.startswith('ETH'):
                position_quantity = round(position_quantity, 4)
            else:
                position_quantity = round(position_quantity, 2)
            
            result = {
                'position_size_usd': round(final_position_usd, 2),
                'position_quantity': position_quantity,
                'risk_amount_usd': round(risk_amount_usd, 2),
                'max_risk_pct': max_risk_pct,
                'confidence_factor': confidence_factor,
                'account_category': self._get_account_category(account_balance),
                'approved': True if final_position_usd >= min_position_usd else False,
                'reasoning': f"Small account sizing: ${final_position_usd:.2f} position ({confidence:.0f}% confidence)"
            }
            
            logger.info(f"Position sizing for ${account_balance:.2f} account: "
                       f"${final_position_usd:.2f} position ({position_quantity:.4f} {symbol})")
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return {
                'position_size_usd': 1.0,
                'position_quantity': 0.001,
                'risk_amount_usd': 0.5,
                'approved': False,
                'reasoning': f"Error in position sizing: {e}"
            }
    
    def _get_account_category(self, balance: float) -> str:
        """Get account category based on balance"""
        if balance < self.micro_account_threshold:
            return "micro"
        elif balance < self.small_account_threshold:
            return "small"
        else:
            return "regular"
    
    def validate_trade_size(self, account_balance: float, position_size_usd: float) -> dict:
        """Validate if a trade size is appropriate for the account"""
        try:
            position_pct = (position_size_usd / account_balance) * 100
            
            # Get limits based on account size
            if account_balance < self.micro_account_threshold:
                max_position_pct = 15.0
            elif account_balance < self.small_account_threshold:
                max_position_pct = 20.0
            else:
                max_position_pct = 30.0
            
            is_valid = position_pct <= max_position_pct
            
            return {
                'valid': is_valid,
                'position_pct': round(position_pct, 2),
                'max_allowed_pct': max_position_pct,
                'reasoning': f"Position is {position_pct:.1f}% of account (max: {max_position_pct}%)"
            }
            
        except Exception as e:
            return {
                'valid': False,
                'reasoning': f"Validation error: {e}"
            }

def apply_small_account_fixes():
    """Apply small account position sizing fixes to the system"""
    logger.info("🚀 Applying small account position sizing fixes...")
    
    # This function can be called to initialize the fixes
    sizer = SmallAccountPositionSizer()
    
    logger.info("✅ Small account position sizing fixes applied")
    return sizer

if __name__ == "__main__":
    # Test the position sizer
    sizer = SmallAccountPositionSizer()
    
    # Test with $15.91 account (like current system)
    result = sizer.calculate_position_size(
        account_balance=15.91,
        confidence=70,
        symbol="BTC/USDT:USDT",
        current_price=95000
    )
    
    print(f"Test result for $15.91 account: {result}")
