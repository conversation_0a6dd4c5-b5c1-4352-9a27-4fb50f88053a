ml:
  adaptation_window: 100
  ensemble_weights:
    lstm: 0.25
    orderflow: 0.05
    random_forest: 0.25
    rsi: 0.05
    sentiment: 0.05
    svm: 0.25
    volatility: 0.05
    vwap: 0.05
  learning_rate: 0.01
  model_weights:
    lstm: 1.0
    random_forest: 1.0
    svm: 1.0
  performance_threshold: 0.55
  update_frequency: 24
monitoring:
  alert_thresholds:
    daily_loss: -0.05
    drawdown: -0.1
    win_rate: 0.4
  db_path: data/trading_performance.db
  enable_metrics: true
  log_level: INFO
  metrics_interval: 60
risk:
  max_concurrent_positions: 1
  max_daily_loss: 0.02
  max_leverage: 25.0
  max_portfolio_risk: 0.01
  max_position_size: 0.005
  portfolio_exposure_limit: 0.95
  stop_loss_pct: 0.015
  take_profit_pct: 0.03
rl:
  batch_size: 64
  clip_range: 0.2
  ent_coef: 0.01
  gae_lambda: 0.95
  gamma: 0.99
  learning_rate: 0.0003
  model_type: PPO
  n_epochs: 10
  n_steps: 2048
  total_timesteps: 100000
scanner:
  enabled: true
  metrics_weights:
    depth_score: 0.25
    flow_score: 0.05
    spread_score: 0.3
    tick_atr_score: 0.15
    volume_score: 0.25
  min_score_improvement: 10
  mode: conservative
  preferred_symbols:
  - DOGE/USDT:USDT
  - SHIB/USDT:USDT
  - ADA/USDT:USDT
  stability_threshold: 60
  update_interval: 30.0
trading:
  autonomous_mode: true
  auto_start_on_launch: false  # 🚀 FIX: Set to false to require manual activation
  cooldown_minutes: 120
  cycle_delay: 120
  initial_balance: 3.0
  max_positions: 1
  max_trades_per_day: 3
  min_confidence: 0.85
  trading_hours:
    end: 24
    start: 0
  use_rl: false
