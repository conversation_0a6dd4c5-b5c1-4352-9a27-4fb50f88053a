# Trading Module

This module contains the core trading functionality for the Epinnox Trading System.

## Components

### Trading System Interface

The trading system interface (`trading_system_interface.py`) provides an interface between the UI and the trading system. It:

- Manages trading parameters
- Makes trading decisions
- Executes trades
- Updates the UI with trading data

### Trading System Interface Extension

The trading system interface extension (`trading_system_interface_extension.py`) extends the trading system interface with manual trading capabilities. It:

- Connects the trading panel to the manual trader
- Handles trade execution signals
- Provides error handling and logging

### Manual Trader

The manual trader (`manual_trader.py`) handles the execution of manual trading operations. It:

- Connects to the exchange
- Places orders
- Manages open positions
- Provides error handling and logging

### Simulation Interface

The simulation interface (`simulation_interface.py`) provides an interface for simulating trading operations. It:

- Simulates market data
- Simulates account operations
- Provides a realistic trading environment for testing

## Usage

### Automated Trading

The trading system interface is used for automated trading. It:

1. Retrieves market data
2. Makes trading decisions based on signals
3. Executes trades
4. Updates the UI with trading data

### Manual Trading

The manual trader is used for manual trading. It:

1. Receives trading signals from the trading panel
2. Executes trades
3. Updates the UI with trading data

### Simulation

The simulation interface is used for simulating trading operations. It:

1. Simulates market data
2. Simulates account operations
3. Provides a realistic trading environment for testing

## Configuration

The trading module uses the exchange credentials from the config file. The default values for the trading parameters can be modified in the trading panel.
