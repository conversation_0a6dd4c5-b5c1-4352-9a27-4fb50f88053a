"""
Model Persistence & Versioning System
Save/load ML/RL models with versioning for reproducibility
"""

import os
import json
import pickle
import hashlib
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class ModelMetadata:
    """Metadata for saved models"""
    model_id: str
    model_type: str
    version: str
    created_at: datetime
    file_path: str
    file_size: int
    checksum: str
    parameters: Dict[str, Any]
    performance_metrics: Dict[str, float]
    training_data_info: Dict[str, Any]
    dependencies: Dict[str, str]

class ModelVersionManager:
    """
    Comprehensive model persistence and versioning system
    """
    
    def __init__(self, models_dir: str = "models"):
        self.models_dir = models_dir
        self.metadata_file = os.path.join(models_dir, "model_registry.json")
        self.model_registry = {}
        
        # Create directories
        os.makedirs(models_dir, exist_ok=True)
        
        # Load existing registry
        self._load_registry()
    
    def _load_registry(self):
        """Load model registry from file"""
        try:
            if os.path.exists(self.metadata_file):
                with open(self.metadata_file, 'r') as f:
                    registry_data = json.load(f)
                
                # Convert to ModelMetadata objects
                for model_id, metadata_dict in registry_data.items():
                    metadata_dict['created_at'] = datetime.fromisoformat(metadata_dict['created_at'])
                    self.model_registry[model_id] = ModelMetadata(**metadata_dict)
                
                logger.info(f"Loaded {len(self.model_registry)} models from registry")
        except Exception as e:
            logger.error(f"Error loading model registry: {e}")
            self.model_registry = {}
    
    def _save_registry(self):
        """Save model registry to file"""
        try:
            # Convert to serializable format
            registry_data = {}
            for model_id, metadata in self.model_registry.items():
                metadata_dict = asdict(metadata)
                metadata_dict['created_at'] = metadata.created_at.isoformat()
                registry_data[model_id] = metadata_dict
            
            with open(self.metadata_file, 'w') as f:
                json.dump(registry_data, f, indent=2, default=str)
            
        except Exception as e:
            logger.error(f"Error saving model registry: {e}")
    
    def _calculate_checksum(self, file_path: str) -> str:
        """Calculate file checksum for integrity verification"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _generate_model_id(self, model_type: str, version: str = None) -> str:
        """Generate unique model ID"""
        if version is None:
            version = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{model_type}_{version}"
    
    def save_model(self, model: Any, model_type: str, 
                   parameters: Dict[str, Any] = None,
                   performance_metrics: Dict[str, float] = None,
                   training_data_info: Dict[str, Any] = None,
                   version: str = None) -> str:
        """
        Save a model with metadata and versioning
        
        Args:
            model: The model object to save
            model_type: Type of model (e.g., 'rl_agent', 'lstm', 'ensemble')
            parameters: Model parameters/configuration
            performance_metrics: Performance metrics (accuracy, loss, etc.)
            training_data_info: Information about training data
            version: Model version (auto-generated if None)
        
        Returns:
            model_id: Unique identifier for the saved model
        """
        
        # Generate model ID
        model_id = self._generate_model_id(model_type, version)
        
        # Create model file path
        model_filename = f"{model_id}.pkl"
        model_path = os.path.join(self.models_dir, model_filename)
        
        try:
            # Save model using pickle
            with open(model_path, 'wb') as f:
                pickle.dump(model, f)
            
            # Calculate file info
            file_size = os.path.getsize(model_path)
            checksum = self._calculate_checksum(model_path)
            
            # Get dependencies info
            dependencies = self._get_dependencies()
            
            # Create metadata
            metadata = ModelMetadata(
                model_id=model_id,
                model_type=model_type,
                version=version or datetime.now().strftime("%Y%m%d_%H%M%S"),
                created_at=datetime.now(),
                file_path=model_path,
                file_size=file_size,
                checksum=checksum,
                parameters=parameters or {},
                performance_metrics=performance_metrics or {},
                training_data_info=training_data_info or {},
                dependencies=dependencies
            )
            
            # Add to registry
            self.model_registry[model_id] = metadata
            
            # Save registry
            self._save_registry()
            
            logger.info(f"Model saved: {model_id} ({file_size} bytes)")
            
            return model_id
            
        except Exception as e:
            logger.error(f"Error saving model {model_id}: {e}")
            # Clean up partial file
            if os.path.exists(model_path):
                os.remove(model_path)
            raise
    
    def load_model(self, model_id: str, verify_checksum: bool = True) -> Any:
        """
        Load a model by ID
        
        Args:
            model_id: Unique model identifier
            verify_checksum: Whether to verify file integrity
        
        Returns:
            The loaded model object
        """
        
        if model_id not in self.model_registry:
            raise ValueError(f"Model {model_id} not found in registry")
        
        metadata = self.model_registry[model_id]
        
        if not os.path.exists(metadata.file_path):
            raise FileNotFoundError(f"Model file not found: {metadata.file_path}")
        
        # Verify checksum if requested
        if verify_checksum:
            current_checksum = self._calculate_checksum(metadata.file_path)
            if current_checksum != metadata.checksum:
                raise ValueError(f"Model file corrupted: checksum mismatch for {model_id}")
        
        try:
            # Load model
            with open(metadata.file_path, 'rb') as f:
                model = pickle.load(f)
            
            logger.info(f"Model loaded: {model_id}")
            
            return model
            
        except Exception as e:
            logger.error(f"Error loading model {model_id}: {e}")
            raise
    
    def get_model_metadata(self, model_id: str) -> ModelMetadata:
        """Get metadata for a specific model"""
        if model_id not in self.model_registry:
            raise ValueError(f"Model {model_id} not found in registry")
        
        return self.model_registry[model_id]
    
    def list_models(self, model_type: str = None) -> List[ModelMetadata]:
        """List all models, optionally filtered by type"""
        models = list(self.model_registry.values())
        
        if model_type:
            models = [m for m in models if m.model_type == model_type]
        
        # Sort by creation date (newest first)
        models.sort(key=lambda x: x.created_at, reverse=True)
        
        return models
    
    def get_latest_model(self, model_type: str) -> Optional[ModelMetadata]:
        """Get the latest model of a specific type"""
        models = self.list_models(model_type)
        return models[0] if models else None
    
    def delete_model(self, model_id: str):
        """Delete a model and its metadata"""
        if model_id not in self.model_registry:
            raise ValueError(f"Model {model_id} not found in registry")
        
        metadata = self.model_registry[model_id]
        
        # Delete file
        if os.path.exists(metadata.file_path):
            os.remove(metadata.file_path)
        
        # Remove from registry
        del self.model_registry[model_id]
        
        # Save registry
        self._save_registry()
        
        logger.info(f"Model deleted: {model_id}")
    
    def cleanup_old_models(self, model_type: str, keep_latest: int = 5):
        """Clean up old models, keeping only the latest N versions"""
        models = self.list_models(model_type)
        
        if len(models) <= keep_latest:
            return
        
        # Delete old models
        models_to_delete = models[keep_latest:]
        for metadata in models_to_delete:
            self.delete_model(metadata.model_id)
        
        logger.info(f"Cleaned up {len(models_to_delete)} old {model_type} models")
    
    def export_model(self, model_id: str, export_path: str):
        """Export a model to a different location"""
        if model_id not in self.model_registry:
            raise ValueError(f"Model {model_id} not found in registry")
        
        metadata = self.model_registry[model_id]
        
        # Create export directory
        os.makedirs(os.path.dirname(export_path), exist_ok=True)
        
        # Copy model file
        import shutil
        shutil.copy2(metadata.file_path, export_path)
        
        # Export metadata
        metadata_path = export_path.replace('.pkl', '_metadata.json')
        metadata_dict = asdict(metadata)
        metadata_dict['created_at'] = metadata.created_at.isoformat()
        metadata_dict['file_path'] = export_path  # Update path
        
        with open(metadata_path, 'w') as f:
            json.dump(metadata_dict, f, indent=2, default=str)
        
        logger.info(f"Model exported: {model_id} -> {export_path}")
    
    def import_model(self, model_path: str, metadata_path: str = None) -> str:
        """Import a model from external location"""
        
        # Load metadata if provided
        if metadata_path and os.path.exists(metadata_path):
            with open(metadata_path, 'r') as f:
                metadata_dict = json.load(f)
            
            metadata_dict['created_at'] = datetime.fromisoformat(metadata_dict['created_at'])
            
            # Generate new model ID to avoid conflicts
            original_id = metadata_dict['model_id']
            new_id = self._generate_model_id(metadata_dict['model_type'])
            metadata_dict['model_id'] = new_id
            
            # Copy file to models directory
            new_path = os.path.join(self.models_dir, f"{new_id}.pkl")
            import shutil
            shutil.copy2(model_path, new_path)
            
            metadata_dict['file_path'] = new_path
            metadata_dict['checksum'] = self._calculate_checksum(new_path)
            
            # Create metadata object
            metadata = ModelMetadata(**metadata_dict)
            
        else:
            # Create minimal metadata
            model_type = os.path.basename(model_path).split('_')[0]
            new_id = self._generate_model_id(model_type)
            new_path = os.path.join(self.models_dir, f"{new_id}.pkl")
            
            # Copy file
            import shutil
            shutil.copy2(model_path, new_path)
            
            metadata = ModelMetadata(
                model_id=new_id,
                model_type=model_type,
                version=datetime.now().strftime("%Y%m%d_%H%M%S"),
                created_at=datetime.now(),
                file_path=new_path,
                file_size=os.path.getsize(new_path),
                checksum=self._calculate_checksum(new_path),
                parameters={},
                performance_metrics={},
                training_data_info={},
                dependencies=self._get_dependencies()
            )
        
        # Add to registry
        self.model_registry[new_id] = metadata
        self._save_registry()
        
        logger.info(f"Model imported: {new_id}")
        
        return new_id
    
    def _get_dependencies(self) -> Dict[str, str]:
        """Get current environment dependencies"""
        dependencies = {}
        
        try:
            import sys
            dependencies['python_version'] = sys.version
            
            # Try to get package versions
            try:
                import numpy
                dependencies['numpy'] = numpy.__version__
            except ImportError:
                pass
            
            try:
                import pandas
                dependencies['pandas'] = pandas.__version__
            except ImportError:
                pass
            
            try:
                import torch
                dependencies['torch'] = torch.__version__
            except ImportError:
                pass
            
        except Exception as e:
            logger.warning(f"Error getting dependencies: {e}")
        
        return dependencies
    
    def get_registry_summary(self) -> Dict:
        """Get summary of model registry"""
        
        summary = {
            'total_models': len(self.model_registry),
            'models_by_type': {},
            'total_size_mb': 0,
            'oldest_model': None,
            'newest_model': None
        }
        
        if not self.model_registry:
            return summary
        
        # Count by type and calculate total size
        for metadata in self.model_registry.values():
            model_type = metadata.model_type
            summary['models_by_type'][model_type] = summary['models_by_type'].get(model_type, 0) + 1
            summary['total_size_mb'] += metadata.file_size / (1024 * 1024)
        
        # Find oldest and newest
        all_models = list(self.model_registry.values())
        all_models.sort(key=lambda x: x.created_at)
        
        summary['oldest_model'] = {
            'model_id': all_models[0].model_id,
            'created_at': all_models[0].created_at.isoformat(),
            'model_type': all_models[0].model_type
        }
        
        summary['newest_model'] = {
            'model_id': all_models[-1].model_id,
            'created_at': all_models[-1].created_at.isoformat(),
            'model_type': all_models[-1].model_type
        }
        
        return summary
