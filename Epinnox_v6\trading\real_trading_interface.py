"""
Real Trading Interface for Epinnox v6
Integrates CCXT trading engine with position tracking and risk management
"""

from typing import Dict, List, Optional, Tuple
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
import time

from .ccxt_trading_engine import CCXTTradingEngine
from .position_tracker import PositionTracker


class RealTradingInterface(QObject):
    """
    Main trading interface that coordinates all trading components
    Provides a clean API for the GUI to interact with
    """
    
    # Signals for UI updates
    order_status_updated = pyqtSignal(dict)  # order info
    position_status_updated = pyqtSignal(dict)  # position info
    balance_status_updated = pyqtSignal(dict)  # balance info
    trading_error = pyqtSignal(str)  # error message
    trading_status = pyqtSignal(str)  # status message
    pnl_updated = pyqtSignal(dict)  # PnL summary
    risk_warning = pyqtSignal(str, str)  # warning type, message
    
    def __init__(self, exchange_name="htx", demo_mode=True):
        super().__init__()

        # Initialize components
        self.trading_engine = CCXTTradingEngine(exchange_name, demo_mode)
        self.position_tracker = PositionTracker()

        # 🔧 FIX 1: Expose the CCXT exchange object for direct access
        # This fixes the "no attribute 'exchange'" error
        self.exchange = self.trading_engine.exchange
        print(f"✅ RealTradingInterface: Exchange object exposed for direct CCXT access")

        # Trading state
        self.is_trading_enabled = True
        self.current_symbol = "DOGE/USDT:USDT"
        self.current_leverage = 20

        # Connect signals
        self.connect_signals()

        # Status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(2000)  # Update every 2 seconds
    
    def connect_signals(self):
        """Connect internal component signals"""
        # Trading engine signals
        self.trading_engine.order_placed.connect(self.on_order_placed)
        self.trading_engine.order_filled.connect(self.on_order_filled)
        self.trading_engine.order_cancelled.connect(self.on_order_cancelled)
        self.trading_engine.position_updated.connect(self.on_position_updated)
        self.trading_engine.balance_updated.connect(self.on_balance_updated)
        self.trading_engine.error_occurred.connect(self.on_trading_error)
        self.trading_engine.status_updated.connect(self.on_trading_status)
        
        # Position tracker signals
        self.position_tracker.position_updated.connect(self.on_position_tracker_update)
        self.position_tracker.trade_executed.connect(self.on_trade_executed)
        self.position_tracker.pnl_updated.connect(self.on_pnl_updated)
        self.position_tracker.margin_warning.connect(self.on_margin_warning)
    
    # 🔧 FIX 2: Helper method for minimum order size validation
    def validate_minimum_order_size(self, symbol: str, amount: float) -> bool:
        """Validate if order amount meets exchange minimum requirements"""
        try:
            # Check if exchange and markets data are available
            if not self.exchange or not hasattr(self.exchange, 'markets'):
                print(f"⚠️ Exchange markets data not available for validation")
                return True  # Allow order if we can't validate

            # Get market info for the symbol
            if symbol not in self.exchange.markets:
                print(f"⚠️ Symbol {symbol} not found in exchange markets")
                return True  # Allow order if symbol not found

            market = self.exchange.markets[symbol]
            limits = market.get('limits', {})
            amount_limits = limits.get('amount', {})
            min_amount = amount_limits.get('min')

            if min_amount is not None and amount < min_amount:
                error_msg = f"Order amount {amount:.8f} below minimum {min_amount:.8f} for {symbol}"
                print(f"❌ {error_msg}")
                self.trading_error.emit(error_msg)
                return False

            print(f"✅ Order amount {amount:.8f} meets minimum requirements for {symbol}")
            return True

        except Exception as e:
            print(f"⚠️ Error validating minimum order size: {e}")
            return True  # Allow order if validation fails

    # Public API methods for GUI
    def place_limit_long(self, symbol: str, amount: float, leverage: int = None) -> bool:
        """Place a limit long order using best bid price"""
        try:
            if not self.is_trading_enabled:
                self.trading_error.emit("Trading is disabled")
                return False

            # 🔧 FIX 2: Validate minimum order size before placing order
            if not self.validate_minimum_order_size(symbol, amount):
                return False

            # Set leverage if provided
            if leverage:
                self.set_leverage(symbol, leverage)

            # Get best bid price
            orderbook = self.trading_engine.get_orderbook(symbol)
            if not orderbook or not orderbook.get('bids'):
                self.trading_error.emit(f"Could not get order book for {symbol}")
                return False

            best_bid = orderbook['bids'][0][0]

            # Place order
            order = self.trading_engine.place_limit_order(symbol, 'buy', amount, best_bid)
            return order is not None

        except Exception as e:
            self.trading_error.emit(f"Error placing limit long: {str(e)}")
            return False
    
    def place_limit_short(self, symbol: str, amount: float, leverage: int = None) -> bool:
        """Place a limit short order using best ask price"""
        try:
            if not self.is_trading_enabled:
                self.trading_error.emit("Trading is disabled")
                return False

            # 🔧 FIX 2: Validate minimum order size before placing order
            if not self.validate_minimum_order_size(symbol, amount):
                return False

            # Set leverage if provided
            if leverage:
                self.set_leverage(symbol, leverage)

            # Get best ask price
            orderbook = self.trading_engine.get_orderbook(symbol)
            if not orderbook or not orderbook.get('asks'):
                self.trading_error.emit(f"Could not get order book for {symbol}")
                return False

            best_ask = orderbook['asks'][0][0]

            # Place order
            order = self.trading_engine.place_limit_order(symbol, 'sell', amount, best_ask)
            return order is not None

        except Exception as e:
            self.trading_error.emit(f"Error placing limit short: {str(e)}")
            return False
    
    def place_market_long(self, symbol: str, amount: float, leverage: int = None) -> bool:
        """Place a market long order"""
        try:
            if not self.is_trading_enabled:
                self.trading_error.emit("Trading is disabled")
                return False

            # 🔧 FIX 2: Validate minimum order size before placing order
            if not self.validate_minimum_order_size(symbol, amount):
                return False

            # Set leverage if provided
            if leverage:
                self.set_leverage(symbol, leverage)

            # Place order
            order = self.trading_engine.place_market_order(symbol, 'buy', amount)
            return order is not None

        except Exception as e:
            self.trading_error.emit(f"Error placing market long: {str(e)}")
            return False
    
    def place_market_short(self, symbol: str, amount: float, leverage: int = None) -> bool:
        """Place a market short order"""
        try:
            if not self.is_trading_enabled:
                self.trading_error.emit("Trading is disabled")
                return False

            # 🔧 FIX 2: Validate minimum order size before placing order
            if not self.validate_minimum_order_size(symbol, amount):
                return False

            # Set leverage if provided
            if leverage:
                self.set_leverage(symbol, leverage)

            # Place order
            order = self.trading_engine.place_market_order(symbol, 'sell', amount)
            return order is not None

        except Exception as e:
            self.trading_error.emit(f"Error placing market short: {str(e)}")
            return False
    
    def close_position(self, symbol: str) -> bool:
        """Close a position for the given symbol"""
        try:
            return self.trading_engine.close_position(symbol)
        except Exception as e:
            self.trading_error.emit(f"Error closing position: {str(e)}")
            return False
    
    def close_all_positions(self) -> int:
        """Close all open positions"""
        try:
            return self.trading_engine.close_all_positions()
        except Exception as e:
            self.trading_error.emit(f"Error closing all positions: {str(e)}")
            return 0
    
    def cancel_all_orders(self, symbol: str = None) -> int:
        """Cancel all orders"""
        try:
            return self.trading_engine.cancel_all_orders(symbol)
        except Exception as e:
            self.trading_error.emit(f"Error cancelling orders: {str(e)}")
            return 0
    
    def set_leverage(self, symbol: str, leverage: int) -> bool:
        """Set leverage for a symbol"""
        try:
            return self.trading_engine.set_leverage(symbol, leverage)
        except Exception as e:
            self.trading_error.emit(f"Error setting leverage: {str(e)}")
            return False
    
    # Data access methods
    def get_best_bid_ask(self, symbol: str) -> Tuple[Optional[float], Optional[float]]:
        """Get best bid and ask prices"""
        try:
            orderbook = self.trading_engine.get_orderbook(symbol)
            if not orderbook:
                return None, None
            
            best_bid = orderbook['bids'][0][0] if orderbook.get('bids') else None
            best_ask = orderbook['asks'][0][0] if orderbook.get('asks') else None
            
            return best_bid, best_ask
            
        except Exception as e:
            self.trading_error.emit(f"Error getting bid/ask: {str(e)}")
            return None, None
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price"""
        try:
            ticker = self.trading_engine.get_ticker(symbol)
            return ticker['last'] if ticker else None
        except Exception as e:
            self.trading_error.emit(f"Error getting price: {str(e)}")
            return None
    
    def get_position_info(self, symbol: str) -> Optional[Dict]:
        """Get position information"""
        return self.position_tracker.get_position(symbol)
    
    def get_all_positions(self) -> Dict[str, Dict]:
        """Get all positions"""
        return self.position_tracker.get_all_positions()
    
    def get_open_positions(self) -> Dict[str, Dict]:
        """Get only open positions"""
        return self.position_tracker.get_open_positions()
    
    def get_balance_info(self) -> Optional[Dict]:
        """Get account balance"""
        try:
            return self.trading_engine.get_balance()
        except Exception as e:
            self.trading_error.emit(f"Error getting balance: {str(e)}")
            return None
    
    def get_pnl_summary(self) -> Dict:
        """Get PnL summary"""
        return self.position_tracker.get_pnl_summary()
    
    def get_trade_history(self, symbol: str = None, limit: int = 100) -> List[Dict]:
        """Get trade history"""
        return self.position_tracker.get_trade_history(symbol, limit)
    
    def get_exchange_info(self) -> Dict:
        """Get exchange information"""
        return self.trading_engine.get_exchange_info()
    
    # Control methods
    def enable_trading(self):
        """Enable trading"""
        self.is_trading_enabled = True
        self.trading_status.emit("Trading enabled")
    
    def disable_trading(self):
        """Disable trading"""
        self.is_trading_enabled = False
        self.trading_status.emit("Trading disabled")
    
    def is_demo_mode(self) -> bool:
        """Check if in demo mode"""
        return self.trading_engine.is_demo_mode()
    
    def is_connected(self) -> bool:
        """Check connection status"""
        return self.trading_engine.get_connection_status()
    
    # Signal handlers
    def on_order_placed(self, order: Dict):
        """Handle order placed signal"""
        self.order_status_updated.emit({
            'type': 'order_placed',
            'order': order
        })
    
    def on_order_filled(self, order: Dict):
        """Handle order filled signal"""
        # Add trade to position tracker
        self.position_tracker.add_trade(order)
        
        self.order_status_updated.emit({
            'type': 'order_filled',
            'order': order
        })
    
    def on_order_cancelled(self, order: Dict):
        """Handle order cancelled signal"""
        self.order_status_updated.emit({
            'type': 'order_cancelled',
            'order': order
        })
    
    def on_position_updated(self, position: Dict):
        """Handle position updated from trading engine"""
        # Update position tracker with new mark price
        if 'markPrice' in position:
            self.position_tracker.update_mark_price(
                position['symbol'], 
                position['markPrice']
            )
    
    def on_balance_updated(self, balance: Dict):
        """Handle balance updated signal"""
        self.balance_status_updated.emit(balance)
    
    def on_trading_error(self, error: str):
        """Handle trading error signal"""
        self.trading_error.emit(error)
    
    def on_trading_status(self, status: str):
        """Handle trading status signal"""
        self.trading_status.emit(status)
    
    def on_position_tracker_update(self, position: Dict):
        """Handle position tracker update"""
        self.position_status_updated.emit(position)
    
    def on_trade_executed(self, trade: Dict):
        """Handle trade executed signal"""
        self.trading_status.emit(f"Trade executed: {trade['side']} {trade['amount']} {trade['symbol']}")
    
    def on_pnl_updated(self, unrealized_pnl: float, realized_pnl: float):
        """Handle PnL updated signal"""
        pnl_summary = {
            'unrealized_pnl': unrealized_pnl,
            'realized_pnl': realized_pnl,
            'total_pnl': unrealized_pnl + realized_pnl
        }
        self.pnl_updated.emit(pnl_summary)
    
    def on_margin_warning(self, warning: str, margin_ratio: float):
        """Handle margin warning signal"""
        self.risk_warning.emit('margin', f"{warning} (Ratio: {margin_ratio:.2%})")
    
    def update_status(self):
        """Update overall status (called by timer)"""
        try:
            # Update current symbol price in position tracker
            if self.current_symbol:
                price = self.get_current_price(self.current_symbol)
                if price:
                    self.position_tracker.update_mark_price(self.current_symbol, price)
                    
        except Exception as e:
            # Don't emit errors for routine updates
            pass
    
    def set_current_symbol(self, symbol: str):
        """Set the current trading symbol"""
        self.current_symbol = symbol
    
    def set_current_leverage(self, leverage: int):
        """Set the current leverage"""
        self.current_leverage = leverage
