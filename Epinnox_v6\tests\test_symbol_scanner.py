#!/usr/bin/env python3
"""
Unit tests for the Dynamic Symbol Scanner
Tests the SymbolScanner class functionality with mocked market data
"""

import unittest
import sys
import os
from unittest.mock import Mock, MagicMock, patch
import time

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from symbol_scanner import SymbolScanner, SymbolScannerConfig, SymbolMetrics


class MockMarketAPI:
    """Mock market API for testing"""
    
    def __init__(self):
        self.mock_data = {
            'BTC/USDT:USDT': {
                'ticker': {'last': 45000.0, 'quoteVolume': 1000000000},
                'orderbook': {
                    'bids': [[44950.0, 10.5], [44940.0, 8.2], [44930.0, 15.1], [44920.0, 5.8], [44910.0, 12.3]],
                    'asks': [[45050.0, 9.8], [45060.0, 7.4], [45070.0, 11.2], [45080.0, 6.9], [45090.0, 13.7]]
                },
                'trades': [
                    {'price': 45000.0, 'amount': 1.5, 'side': 'buy'},
                    {'price': 44995.0, 'amount': 2.1, 'side': 'sell'},
                    {'price': 45005.0, 'amount': 1.8, 'side': 'buy'},
                    {'price': 45010.0, 'amount': 0.9, 'side': 'buy'},
                    {'price': 44990.0, 'amount': 3.2, 'side': 'sell'}
                ]
            },
            'ETH/USDT:USDT': {
                'ticker': {'last': 3200.0, 'quoteVolume': 500000000},
                'orderbook': {
                    'bids': [[3195.0, 25.5], [3190.0, 18.2], [3185.0, 35.1], [3180.0, 15.8], [3175.0, 22.3]],
                    'asks': [[3205.0, 19.8], [3210.0, 27.4], [3215.0, 31.2], [3220.0, 16.9], [3225.0, 23.7]]
                },
                'trades': [
                    {'price': 3200.0, 'amount': 5.5, 'side': 'sell'},
                    {'price': 3195.0, 'amount': 8.1, 'side': 'buy'},
                    {'price': 3205.0, 'amount': 3.8, 'side': 'buy'},
                    {'price': 3210.0, 'amount': 2.9, 'side': 'sell'},
                    {'price': 3190.0, 'amount': 6.2, 'side': 'sell'}
                ]
            },
            'DOGE/USDT:USDT': {
                'ticker': {'last': 0.08, 'quoteVolume': 100000000},
                'orderbook': {
                    'bids': [[0.0799, 1000.5], [0.0798, 800.2], [0.0797, 1500.1], [0.0796, 500.8], [0.0795, 1200.3]],
                    'asks': [[0.0801, 900.8], [0.0802, 700.4], [0.0803, 1100.2], [0.0804, 600.9], [0.0805, 1300.7]]
                },
                'trades': [
                    {'price': 0.08, 'amount': 1000.0, 'side': 'buy'},
                    {'price': 0.0799, 'amount': 1500.0, 'side': 'sell'},
                    {'price': 0.0801, 'amount': 800.0, 'side': 'buy'},
                    {'price': 0.0802, 'amount': 1200.0, 'side': 'buy'},
                    {'price': 0.0798, 'amount': 2000.0, 'side': 'sell'}
                ]
            }
        }
    
    def fetch_ticker(self, symbol):
        """Mock fetch_ticker"""
        return self.mock_data.get(symbol, {}).get('ticker', {'last': 100.0, 'quoteVolume': 1000000})
    
    def fetch_order_book(self, symbol, limit=5):
        """Mock fetch_order_book"""
        return self.mock_data.get(symbol, {}).get('orderbook', {
            'bids': [[100.0, 10.0]] * limit,
            'asks': [[101.0, 10.0]] * limit
        })
    
    def fetch_trades(self, symbol, limit=50):
        """Mock fetch_trades"""
        trades = self.mock_data.get(symbol, {}).get('trades', [
            {'price': 100.0, 'amount': 1.0, 'side': 'buy'}
        ])
        return trades[:limit]


class TestSymbolScanner(unittest.TestCase):
    """Test cases for SymbolScanner"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_api = MockMarketAPI()
        self.test_symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'DOGE/USDT:USDT']
        self.test_weights = {
            'spread_score': 0.20,
            'tick_atr_score': 0.25,
            'flow_score': 0.10,
            'depth_score': 0.15,
            'volume_score': 0.15,
            'affordability_score': 0.15  # 🚀 NEW: Cheaper symbols better for small accounts
        }
        
        self.scanner = SymbolScanner(
            market_api=self.mock_api,
            symbols=self.test_symbols,
            metrics_weights=self.test_weights
        )
    
    def test_scanner_initialization(self):
        """Test scanner initialization"""
        self.assertEqual(self.scanner.symbols, self.test_symbols)
        self.assertEqual(self.scanner.metrics_weights, self.test_weights)
        self.assertIsNotNone(self.scanner.normalization_ranges)
        self.assertEqual(len(self.scanner.metric_history), 0)
    
    def test_fetch_metrics_btc(self):
        """Test fetching metrics for BTC"""
        metrics = self.scanner.fetch_metrics('BTC/USDT:USDT')
        
        self.assertIsNotNone(metrics)
        self.assertEqual(metrics.symbol, 'BTC/USDT:USDT')
        self.assertEqual(metrics.price, 45000.0)
        self.assertGreater(metrics.spread, 0)
        self.assertGreater(metrics.orderbook_depth, 0)
        self.assertGreater(metrics.volume_24h, 0)
        self.assertIsInstance(metrics.tick_atr, float)
        self.assertIsInstance(metrics.flow_imbalance, float)
    
    def test_fetch_metrics_eth(self):
        """Test fetching metrics for ETH"""
        metrics = self.scanner.fetch_metrics('ETH/USDT:USDT')
        
        self.assertIsNotNone(metrics)
        self.assertEqual(metrics.symbol, 'ETH/USDT:USDT')
        self.assertEqual(metrics.price, 3200.0)
        self.assertGreater(metrics.spread, 0)
        self.assertGreater(metrics.orderbook_depth, 0)
    
    def test_fetch_metrics_doge(self):
        """Test fetching metrics for DOGE"""
        metrics = self.scanner.fetch_metrics('DOGE/USDT:USDT')
        
        self.assertIsNotNone(metrics)
        self.assertEqual(metrics.symbol, 'DOGE/USDT:USDT')
        self.assertEqual(metrics.price, 0.08)
        self.assertGreater(metrics.spread, 0)
        self.assertGreater(metrics.orderbook_depth, 0)
    
    def test_fetch_metrics_invalid_symbol(self):
        """Test fetching metrics for invalid symbol"""
        metrics = self.scanner.fetch_metrics('INVALID/USDT:USDT')
        
        # Should return metrics with default/fallback values
        self.assertIsNotNone(metrics)
        self.assertEqual(metrics.symbol, 'INVALID/USDT:USDT')
    
    def test_score_symbol(self):
        """Test symbol scoring"""
        metrics = self.scanner.fetch_metrics('BTC/USDT:USDT')
        score = self.scanner.score_symbol(metrics)
        
        self.assertIsInstance(score, float)
        self.assertGreaterEqual(score, 0)
        self.assertLessEqual(score, 100)
    
    def test_find_best_single(self):
        """Test finding best single symbol"""
        best_symbols = self.scanner.find_best(n=1)
        
        self.assertIsInstance(best_symbols, list)
        self.assertEqual(len(best_symbols), 1)
        self.assertIn(best_symbols[0], self.test_symbols)
    
    def test_find_best_multiple(self):
        """Test finding best multiple symbols"""
        best_symbols = self.scanner.find_best(n=2)
        
        self.assertIsInstance(best_symbols, list)
        self.assertEqual(len(best_symbols), 2)
        for symbol in best_symbols:
            self.assertIn(symbol, self.test_symbols)
    
    def test_find_best_all(self):
        """Test finding all symbols ranked"""
        best_symbols = self.scanner.find_best(n=len(self.test_symbols))
        
        self.assertIsInstance(best_symbols, list)
        self.assertEqual(len(best_symbols), len(self.test_symbols))
        self.assertEqual(set(best_symbols), set(self.test_symbols))
    
    def test_get_symbol_metrics(self):
        """Test getting cached symbol metrics"""
        # First scan to populate cache
        self.scanner.find_best(n=1)
        
        # Get cached metrics
        metrics = self.scanner.get_symbol_metrics('BTC/USDT:USDT')
        self.assertIsNotNone(metrics)
        self.assertEqual(metrics.symbol, 'BTC/USDT:USDT')
    
    def test_get_all_metrics(self):
        """Test getting all cached metrics"""
        # First scan to populate cache
        self.scanner.find_best(n=len(self.test_symbols))
        
        # Get all metrics
        all_metrics = self.scanner.get_all_metrics()
        self.assertIsInstance(all_metrics, dict)
        self.assertEqual(len(all_metrics), len(self.test_symbols))
    
    def test_update_weights(self):
        """Test updating metrics weights"""
        new_weights = {'spread_score': 0.5, 'volume_score': 0.5}
        self.scanner.update_weights(new_weights)
        
        self.assertEqual(self.scanner.metrics_weights['spread_score'], 0.5)
        self.assertEqual(self.scanner.metrics_weights['volume_score'], 0.5)
        # Other weights should remain unchanged
        self.assertEqual(self.scanner.metrics_weights['tick_atr_score'], 0.20)
    
    def test_add_remove_symbol(self):
        """Test adding and removing symbols"""
        new_symbol = 'ADA/USDT:USDT'
        
        # Add symbol
        self.scanner.add_symbol(new_symbol)
        self.assertIn(new_symbol, self.scanner.symbols)
        
        # Remove symbol
        self.scanner.remove_symbol(new_symbol)
        self.assertNotIn(new_symbol, self.scanner.symbols)
    
    def test_get_scan_summary(self):
        """Test getting scan summary"""
        # First scan to populate data
        self.scanner.find_best(n=1)
        
        summary = self.scanner.get_scan_summary()
        self.assertIsInstance(summary, dict)
        self.assertIn('status', summary)
        self.assertIn('symbols_scanned', summary)
        
        if summary['status'] == 'success':
            self.assertGreater(summary['symbols_scanned'], 0)
            self.assertIn('best_symbol', summary)
    
    def test_rate_limiting(self):
        """Test scan rate limiting"""
        # Clear any existing cache
        self.scanner.cached_metrics = {}
        self.scanner.last_scan_time = 0

        # First scan - should populate cache
        start_time = time.time()
        result1 = self.scanner.find_best(n=1)
        first_scan_time = time.time()

        # Immediate second scan (should use cache)
        result2 = self.scanner.find_best(n=1)
        second_scan_time = time.time()

        # Calculate durations
        first_duration = first_scan_time - start_time
        second_duration = second_scan_time - first_scan_time

        # Both scans should return the same result
        self.assertEqual(result1, result2)

        # If both durations are very small (< 0.001), the test passes
        # Otherwise, second scan should be faster due to caching
        if first_duration >= 0.001 and second_duration >= 0.001:
            self.assertLess(second_duration, first_duration)
        else:
            # For very fast operations, just verify cache was used
            self.assertGreater(len(self.scanner.cached_metrics), 0)


class TestSymbolScannerConfig(unittest.TestCase):
    """Test cases for SymbolScannerConfig"""
    
    def test_default_symbols(self):
        """Test default symbols configuration"""
        symbols = SymbolScannerConfig.DEFAULT_SYMBOLS
        self.assertIsInstance(symbols, list)
        self.assertGreater(len(symbols), 0)
        
        # All symbols should be USDT futures
        for symbol in symbols:
            self.assertTrue(symbol.endswith('/USDT:USDT'))
    
    def test_default_weights(self):
        """Test default weights configuration"""
        weights = SymbolScannerConfig.DEFAULT_WEIGHTS
        self.assertIsInstance(weights, dict)
        
        # Check required weight keys
        required_keys = ['spread_score', 'tick_atr_score', 'flow_score', 'depth_score', 'volume_score', 'affordability_score']
        for key in required_keys:
            self.assertIn(key, weights)
            self.assertIsInstance(weights[key], float)
        
        # Weights should sum to approximately 1.0
        total_weight = sum(weights.values())
        self.assertAlmostEqual(total_weight, 1.0, places=1)
    
    def test_create_scanner(self):
        """Test creating scanner with config"""
        mock_api = MockMarketAPI()
        scanner = SymbolScannerConfig.create_scanner(mock_api)
        
        self.assertIsInstance(scanner, SymbolScanner)
        self.assertEqual(scanner.symbols, SymbolScannerConfig.DEFAULT_SYMBOLS)
        self.assertEqual(scanner.metrics_weights, SymbolScannerConfig.DEFAULT_WEIGHTS)


class TestSymbolMetrics(unittest.TestCase):
    """Test cases for SymbolMetrics dataclass"""
    
    def test_symbol_metrics_creation(self):
        """Test creating SymbolMetrics object"""
        metrics = SymbolMetrics(
            symbol='BTC/USDT:USDT',
            spread=50.0,
            spread_pct=0.1,
            tick_atr=0.001,
            flow_imbalance=5.0,
            orderbook_depth=1000.0,
            volume_24h=1000000000,
            price=45000.0,
            timestamp=time.time()
        )
        
        self.assertEqual(metrics.symbol, 'BTC/USDT:USDT')
        self.assertEqual(metrics.spread, 50.0)
        self.assertEqual(metrics.spread_pct, 0.1)
        self.assertEqual(metrics.tick_atr, 0.001)
        self.assertEqual(metrics.flow_imbalance, 5.0)
        self.assertEqual(metrics.orderbook_depth, 1000.0)
        self.assertEqual(metrics.volume_24h, 1000000000)
        self.assertEqual(metrics.price, 45000.0)
        self.assertEqual(metrics.score, 0.0)  # Default score


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
