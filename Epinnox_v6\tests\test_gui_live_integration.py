#!/usr/bin/env python3
"""
Epinnox v6 GUI Live Integration Testing Suite
Automated tests for GUI integration with live trading backend systems
"""

import pytest
import sys
import os
import time
import json
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication, QLabel, QPushButton, QTableWidget
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestLiveSystemGUIIntegration:
    """Test GUI integration with live trading system state"""
    
    @pytest.fixture(scope="class")
    def app(self):
        """Create QApplication for testing"""
        if not QApplication.instance():
            app = QApplication(sys.argv)
        else:
            app = QApplication.instance()
        yield app
    
    @pytest.fixture
    def live_position_data(self):
        """Mock current live position data from our test"""
        return {
            'symbol': 'DOGE/USDT:USDT',
            'side': 'short',
            'size': 26.0,
            'leverage': 75.0,
            'entry_price': 0.163478461538461538,
            'current_price': 0.16385,
            'unrealized_pnl': -0.966,
            'notional': 426.01,
            'margin': 5.***************,
            'liquidation_price': 0.181648570304890037
        }
    
    @pytest.fixture
    def live_portfolio_data(self):
        """Mock current portfolio exposure data"""
        return {
            'total_balance': 43.08,
            'available_balance': 37.42,
            'total_exposure': 426.01,
            'exposure_percentage': 991.0,
            'account_health': 'MODERATE_RISK',
            'daily_pnl': -0.96,
            'positions_count': 1
        }
    
    @pytest.fixture
    def live_llm_results(self):
        """Mock current LLM orchestrator results"""
        return {
            'risk_assessment': {'action': 'SHORT', 'confidence': 0.85, 'reasoning': 'High exposure detected'},
            'entry_timing': {'action': 'ENTER_NOW', 'confidence': 0.70, 'reasoning': 'Market conditions favorable'},
            'opportunity_scanner': {'action': 'FAVORABLE', 'confidence': 0.80, 'reasoning': 'Setup quality medium'},
            'overall_confidence': 0.85,
            'final_decision': 'SHORT',
            'vote_distribution': {'LONG': 0.0, 'SHORT': 3.8, 'WAIT': 1.5, 'CLOSE': 0.0}
        }
    
    def test_position_display_integration(self, app, live_position_data):
        """Test GUI displays current DOGE/USDT position correctly"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import TradingSystemGUI
                main_window = TradingSystemGUI()
        
        # Look for position table or display elements
        tables = main_window.findChildren(QTableWidget)
        labels = main_window.findChildren(QLabel)
        
        # Test position data integration
        position_elements = []
        for label in labels:
            if hasattr(label, 'objectName'):
                name = label.objectName().lower()
                if any(keyword in name for keyword in ['position', 'size', 'pnl', 'leverage']):
                    position_elements.append(label)
        
        # Verify position display capability exists
        assert len(position_elements) >= 0 or len(tables) >= 0, "Position display elements should be available"
        
        # Test position data structure
        assert live_position_data['symbol'] == 'DOGE/USDT:USDT'
        assert live_position_data['leverage'] == 75.0
        assert live_position_data['size'] == 26.0
        
        main_window.close()
    
    def test_portfolio_exposure_warning_display(self, app, live_portfolio_data):
        """Test GUI shows portfolio exposure warning for 991% exposure"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import TradingSystemGUI
                main_window = TradingSystemGUI()
        
        # Look for exposure warning elements
        labels = main_window.findChildren(QLabel)
        warning_elements = []
        
        for label in labels:
            if hasattr(label, 'objectName'):
                name = label.objectName().lower()
                if any(keyword in name for keyword in ['exposure', 'risk', 'warning', 'alert']):
                    warning_elements.append(label)
        
        # Test exposure data
        assert live_portfolio_data['exposure_percentage'] == 991.0
        assert live_portfolio_data['exposure_percentage'] > 80  # Should trigger warning
        assert live_portfolio_data['account_health'] == 'MODERATE_RISK'
        
        # Verify warning display capability
        assert len(warning_elements) >= 0, "Risk warning display elements should be available"
        
        main_window.close()
    
    def test_llm_orchestrator_results_display(self, app, live_llm_results):
        """Test GUI displays LLM orchestrator results with confidence scores"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import TradingSystemGUI
                main_window = TradingSystemGUI()
        
        # Look for LLM result display elements
        labels = main_window.findChildren(QLabel)
        llm_elements = []
        
        for label in labels:
            if hasattr(label, 'objectName'):
                name = label.objectName().lower()
                if any(keyword in name for keyword in ['llm', 'confidence', 'decision', 'orchestrator']):
                    llm_elements.append(label)
        
        # Test LLM results structure
        assert live_llm_results['risk_assessment']['confidence'] == 0.85
        assert live_llm_results['entry_timing']['confidence'] == 0.70
        assert live_llm_results['opportunity_scanner']['confidence'] == 0.80
        assert live_llm_results['final_decision'] == 'SHORT'
        
        # Verify LLM display capability
        assert len(llm_elements) >= 0, "LLM result display elements should be available"
        
        main_window.close()
    
    def test_leverage_mismatch_error_display(self, app):
        """Test GUI displays leverage mismatch errors appropriately"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import TradingSystemGUI
                main_window = TradingSystemGUI()
        
        # Simulate leverage mismatch error
        leverage_error = "The leverage for new orders does not match current positions. Please change the leverage."
        
        # Look for error display elements
        labels = main_window.findChildren(QLabel)
        error_elements = []
        
        for label in labels:
            if hasattr(label, 'objectName'):
                name = label.objectName().lower()
                if any(keyword in name for keyword in ['error', 'message', 'alert', 'status']):
                    error_elements.append(label)
        
        # Test error message structure
        assert "leverage" in leverage_error.lower()
        assert "mismatch" in leverage_error.lower() or "match" in leverage_error.lower()
        
        # Verify error display capability
        assert len(error_elements) >= 0, "Error display elements should be available"
        
        main_window.close()
    
    def test_emergency_stop_accessibility(self, app):
        """Test emergency stop button is accessible and functional"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import TradingSystemGUI
                main_window = TradingSystemGUI()
        
        # Find emergency stop button
        buttons = main_window.findChildren(QPushButton)
        emergency_buttons = []
        
        for btn in buttons:
            text = btn.text().lower()
            if "emergency" in text or ("stop" in text and "emergency" in btn.objectName().lower()):
                emergency_buttons.append(btn)
        
        # Test emergency button accessibility
        if len(emergency_buttons) > 0:
            emergency_btn = emergency_buttons[0]
            assert emergency_btn.isEnabled(), "Emergency stop button should be enabled"
            assert emergency_btn.isVisible(), "Emergency stop button should be visible"
        
        main_window.close()
    
    def test_real_time_data_integration(self, app):
        """Test real-time data integration with GUI components"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import TradingSystemGUI
                main_window = TradingSystemGUI()
        
        # Test timer-based updates
        timers = main_window.findChildren(QTimer)
        
        # Verify update mechanisms exist
        assert len(timers) >= 0, "Update timers should be present for real-time data"
        
        # Test price display elements
        labels = main_window.findChildren(QLabel)
        price_elements = []
        
        for label in labels:
            if hasattr(label, 'objectName'):
                name = label.objectName().lower()
                if any(keyword in name for keyword in ['price', 'ticker', 'market']):
                    price_elements.append(label)
        
        # Verify price display capability
        assert len(price_elements) >= 0, "Price display elements should be available"
        
        main_window.close()
    
    def test_account_balance_display(self, app, live_portfolio_data):
        """Test account balance and health status display"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import TradingSystemGUI
                main_window = TradingSystemGUI()
        
        # Look for balance display elements
        labels = main_window.findChildren(QLabel)
        balance_elements = []
        
        for label in labels:
            if hasattr(label, 'objectName'):
                name = label.objectName().lower()
                if any(keyword in name for keyword in ['balance', 'account', 'health', 'status']):
                    balance_elements.append(label)
        
        # Test balance data
        assert live_portfolio_data['total_balance'] == 43.08
        assert live_portfolio_data['account_health'] == 'MODERATE_RISK'
        
        # Verify balance display capability
        assert len(balance_elements) >= 0, "Balance display elements should be available"
        
        main_window.close()

class TestLiveSystemErrorHandling:
    """Test error handling and display in live system context"""
    
    @pytest.fixture(scope="class")
    def app(self):
        """Create QApplication for testing"""
        if not QApplication.instance():
            app = QApplication(sys.argv)
        else:
            app = QApplication.instance()
        yield app
    
    def test_websocket_disconnection_handling(self, app):
        """Test GUI handles WebSocket disconnection gracefully"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import TradingSystemGUI
                main_window = TradingSystemGUI()
        
        # Simulate WebSocket disconnection
        disconnection_message = "WebSocket disconnected from htx"
        
        # Test that system can handle disconnection
        assert "disconnected" in disconnection_message.lower()
        
        main_window.close()
    
    def test_portfolio_overexposure_prevention(self, app):
        """Test GUI prevents new trades when portfolio is over-exposed"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import TradingSystemGUI
                main_window = TradingSystemGUI()
        
        # Simulate over-exposure condition
        exposure_error = "Portfolio exposure would exceed 80% limit (current: 991.0%)"
        
        # Test error message structure
        assert "991.0%" in exposure_error
        assert "80%" in exposure_error
        assert "exceed" in exposure_error.lower()
        
        main_window.close()

class TestLiveSystemValidation:
    """Validate live system state matches GUI display"""
    
    def test_session_tracking_integration(self):
        """Test session tracking integration with GUI"""
        session_id = "live_DOGEUSDTUSDT_20250629_155135_86985db8"
        
        # Validate session ID structure
        assert "live_" in session_id
        assert "DOGEUSDTUSDT" in session_id
        assert "20250629" in session_id  # Date component
        
        # Test session tracking capability
        assert len(session_id) > 20, "Session ID should be sufficiently unique"
    
    def test_system_initialization_validation(self):
        """Test system initialization sequence validation"""
        initialization_steps = [
            "GUI Launch",
            "Credentials Loading", 
            "Exchange Connection",
            "LLM Orchestrator Init",
            "Session Management",
            "Performance Monitor"
        ]
        
        # Verify initialization sequence
        assert len(initialization_steps) == 6
        assert "LLM Orchestrator Init" in initialization_steps
        assert "Exchange Connection" in initialization_steps

if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
