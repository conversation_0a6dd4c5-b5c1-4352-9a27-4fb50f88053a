2025-07-06 06:56:04,357 - main - INFO - Epinnox v6 starting up...
2025-07-06 06:56:04,381 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 06:56:04,382 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 06:56:04,382 - main - INFO - Performance monitoring initialized
2025-07-06 06:56:04,391 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 06:56:04,392 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 06:56:04,392 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 06:56:43,355 - main - INFO - Epinnox v6 starting up...
2025-07-06 06:56:43,370 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 06:56:43,370 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 06:56:43,370 - main - INFO - Performance monitoring initialized
2025-07-06 06:56:43,380 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 06:56:43,380 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 06:56:43,381 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 06:56:55,508 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 06:56:56,886 - websocket - INFO - Websocket connected
2025-07-06 06:56:59,684 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-06 06:56:59,696 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-06 06:56:59,696 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-06 06:56:59,696 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-06 06:56:59,696 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-06 06:56:59,702 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-06 06:57:01,784 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-06 06:57:01,784 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-06 06:57:01,785 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-06 06:57:01,785 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-06 06:57:01,785 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-06 06:57:01,785 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 06:57:01,786 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 06:57:01,786 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-06 06:57:01,789 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-06 06:57:01,789 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-06 06:57:01,820 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-06 06:57:01,820 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-06 06:57:01,821 - storage.session_manager - INFO - Session Manager initialized
2025-07-06 06:57:01,826 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250706_065701_cd1a30ca
2025-07-06 06:57:01,827 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250706_065701_cd1a30ca
2025-07-06 06:57:02,000 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-06 06:57:02,002 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-06 06:57:02,002 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-06 06:57:02,003 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-06 06:57:02,003 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-06 06:57:02,003 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-06 06:57:02,005 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-06 06:57:02,008 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-06 06:57:02,008 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-06 06:57:02,009 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 06:58:34,732 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 06:58:45,677 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 06:58:45,677 - symbol_scanner - INFO -   1. ETH/USDT:USDT: 52.32 (spread: 0.000%, atr: 0.023684, depth: 35092)
2025-07-06 06:58:45,678 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 06:58:45,679 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 06:58:58,726 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 06:59:09,054 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 06:59:09,054 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 60.06 (spread: 0.026%, atr: 0.000000, depth: 1739508)
2025-07-06 06:59:09,054 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 06:59:13,806 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 06:59:24,404 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 06:59:24,404 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 62.76 (spread: 0.009%, atr: 0.000000, depth: 4853270)
2025-07-06 06:59:24,405 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 06:59:28,725 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 06:59:38,544 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 06:59:38,545 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 62.76 (spread: 0.009%, atr: 0.000000, depth: 4855114)
2025-07-06 06:59:38,545 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 06:59:43,725 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 06:59:53,600 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 06:59:53,601 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 62.34 (spread: 0.009%, atr: 0.000000, depth: 4732844)
2025-07-06 06:59:53,602 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 06:59:58,725 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 07:00:09,082 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 07:00:09,083 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 50.19 (spread: 0.009%, atr: 0.000000, depth: 1264857)
2025-07-06 07:00:09,083 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 07:00:13,724 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 07:00:24,820 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 07:00:24,821 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 49.02 (spread: 0.009%, atr: 0.000000, depth: 1238880)
2025-07-06 07:00:24,821 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 07:00:28,729 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 07:00:40,269 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 07:00:40,269 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 49.58 (spread: 0.009%, atr: 0.000000, depth: 1399127)
2025-07-06 07:00:40,270 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 15:16:39,900 - main - INFO - Epinnox v6 starting up...
2025-07-06 15:16:39,950 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 15:16:39,950 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 15:16:39,951 - main - INFO - Performance monitoring initialized
2025-07-06 15:16:39,966 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 15:16:39,967 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 15:16:39,968 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 15:16:52,366 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 15:16:54,043 - websocket - INFO - Websocket connected
2025-07-06 15:16:57,963 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-06 15:16:57,974 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-06 15:16:57,974 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-06 15:16:57,975 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-06 15:16:57,975 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-06 15:16:57,980 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-06 15:17:00,121 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-06 15:17:00,122 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-06 15:17:00,122 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-06 15:17:00,122 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-06 15:17:00,123 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-06 15:17:00,123 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 15:17:00,124 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 15:17:00,124 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-06 15:17:00,127 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-06 15:17:00,127 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-06 15:17:00,158 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-06 15:17:00,160 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-06 15:17:00,160 - storage.session_manager - INFO - Session Manager initialized
2025-07-06 15:17:00,166 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250706_151700_15f37a43
2025-07-06 15:17:00,169 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250706_151700_15f37a43
2025-07-06 15:17:00,256 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-06 15:17:00,258 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-06 15:17:00,259 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-06 15:17:00,259 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-06 15:17:00,259 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-06 15:17:00,260 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-06 15:17:00,262 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-06 15:17:00,264 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-06 15:17:00,265 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-06 15:17:00,266 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 15:18:13,383 - main - INFO - Epinnox v6 starting up...
2025-07-06 15:18:13,400 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 15:18:13,400 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 15:18:13,400 - main - INFO - Performance monitoring initialized
2025-07-06 15:18:13,411 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 15:18:13,412 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 15:18:13,413 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 15:18:23,533 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 15:18:25,027 - websocket - INFO - Websocket connected
2025-07-06 15:18:28,001 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-06 15:18:28,006 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-06 15:18:28,006 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-06 15:18:28,007 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-06 15:18:28,007 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-06 15:18:28,012 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-06 15:18:30,097 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-06 15:18:30,098 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-06 15:18:30,098 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-06 15:18:30,099 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-06 15:18:30,099 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-06 15:18:30,100 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 15:18:30,100 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 15:18:30,100 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-06 15:18:30,103 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-06 15:18:30,103 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-06 15:18:30,113 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-06 15:18:30,115 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-06 15:18:30,115 - storage.session_manager - INFO - Session Manager initialized
2025-07-06 15:18:30,122 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250706_151830_fec2cee0
2025-07-06 15:18:30,126 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250706_151830_fec2cee0
2025-07-06 15:18:30,191 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-06 15:18:30,193 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-06 15:18:30,194 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-06 15:18:30,195 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-06 15:18:30,195 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-06 15:18:30,195 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-06 15:18:30,197 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-06 15:18:30,199 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-06 15:18:30,200 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-06 15:18:30,201 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 15:19:31,059 - main - INFO - Epinnox v6 starting up...
2025-07-06 15:19:31,075 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 15:19:31,076 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 15:19:31,076 - main - INFO - Performance monitoring initialized
2025-07-06 15:19:31,085 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 15:19:31,087 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 15:19:31,088 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 15:19:40,446 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 15:19:41,239 - websocket - INFO - Websocket connected
2025-07-06 15:19:45,487 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-06 15:19:45,492 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-06 15:19:45,492 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-06 15:19:45,492 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-06 15:19:45,493 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-06 15:19:45,497 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-06 15:19:47,576 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-06 15:19:47,577 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-06 15:19:47,578 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-06 15:19:47,578 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-06 15:19:47,579 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-06 15:19:47,579 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 15:19:47,580 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 15:19:47,580 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-06 15:19:47,583 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-06 15:19:47,583 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-06 15:19:47,592 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-06 15:19:47,592 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-06 15:19:47,593 - storage.session_manager - INFO - Session Manager initialized
2025-07-06 15:19:47,598 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250706_151947_9bd93a30
2025-07-06 15:19:47,601 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250706_151947_9bd93a30
2025-07-06 15:19:47,659 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-06 15:19:47,661 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-06 15:19:47,662 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-06 15:19:47,662 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-06 15:19:47,663 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-06 15:19:47,663 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-06 15:19:47,664 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-06 15:19:47,668 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-06 15:19:47,668 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-06 15:19:47,669 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 15:21:51,142 - main - INFO - Epinnox v6 starting up...
2025-07-06 15:21:51,157 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 15:21:51,158 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 15:21:51,159 - main - INFO - Performance monitoring initialized
2025-07-06 15:21:51,170 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 15:21:51,170 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 15:21:51,172 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 15:22:03,319 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 15:22:04,709 - websocket - INFO - Websocket connected
2025-07-06 15:22:08,282 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-06 15:22:08,286 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-06 15:22:08,287 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-06 15:22:08,287 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-06 15:22:08,287 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-06 15:22:08,293 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-06 15:22:10,361 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-06 15:22:10,362 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-06 15:22:10,363 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-06 15:22:10,364 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-06 15:22:10,364 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-06 15:22:10,364 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 15:22:10,365 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 15:22:10,365 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-06 15:22:10,368 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-06 15:22:10,368 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-06 15:22:10,377 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-06 15:22:10,378 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-06 15:22:10,378 - storage.session_manager - INFO - Session Manager initialized
2025-07-06 15:22:10,383 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250706_152210_d025211a
2025-07-06 15:22:10,385 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250706_152210_d025211a
2025-07-06 15:22:10,469 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-06 15:22:10,471 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-06 15:22:10,471 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-06 15:22:10,472 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-06 15:22:10,472 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-06 15:22:10,473 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-06 15:22:10,474 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-06 15:22:10,477 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-06 15:22:10,478 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-06 15:22:10,479 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 19:32:42,469 - main - INFO - Epinnox v6 starting up...
2025-07-06 19:32:42,498 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 19:32:42,499 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 19:32:42,499 - main - INFO - Performance monitoring initialized
2025-07-06 19:32:42,509 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 19:32:42,509 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 19:32:42,510 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 19:33:01,381 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 19:33:02,706 - websocket - INFO - Websocket connected
2025-07-06 19:33:12,232 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-06 19:33:12,237 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-06 19:33:12,238 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-06 19:33:12,238 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-06 19:33:12,239 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-06 19:33:12,245 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-06 19:33:14,322 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-06 19:33:14,322 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-06 19:33:14,323 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-06 19:33:14,324 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-06 19:33:14,324 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-06 19:33:14,325 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 19:33:14,325 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 19:33:14,326 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-06 19:33:14,330 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-06 19:33:14,330 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-06 19:33:14,362 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-06 19:33:14,362 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-06 19:33:14,362 - storage.session_manager - INFO - Session Manager initialized
2025-07-06 19:33:14,368 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250706_193314_6e2c29f0
2025-07-06 19:33:14,368 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250706_193314_6e2c29f0
2025-07-06 19:33:14,511 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-06 19:33:14,518 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-06 19:33:14,518 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-06 19:33:14,519 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-06 19:33:14,519 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-06 19:33:14,519 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-06 19:33:14,520 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-06 19:33:14,523 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-06 19:33:14,524 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-06 19:33:14,524 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 19:37:10,741 - main - INFO - Epinnox v6 starting up...
2025-07-06 19:37:10,758 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 19:37:10,758 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 19:37:10,758 - main - INFO - Performance monitoring initialized
2025-07-06 19:37:10,770 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 19:37:10,770 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 19:37:10,771 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 19:37:21,789 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 19:37:23,089 - websocket - INFO - Websocket connected
2025-07-06 19:37:29,039 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-06 19:37:29,043 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-06 19:37:29,044 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-06 19:37:29,044 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-06 19:37:29,044 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-06 19:37:29,049 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-06 19:37:31,107 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-06 19:37:31,108 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-06 19:37:31,108 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-06 19:37:31,108 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-06 19:37:31,109 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-06 19:37:31,109 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 19:37:31,109 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 19:37:31,109 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-06 19:37:31,112 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-06 19:37:31,112 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-06 19:37:31,123 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-06 19:37:31,123 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-06 19:37:31,123 - storage.session_manager - INFO - Session Manager initialized
2025-07-06 19:37:31,128 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250706_193731_14840335
2025-07-06 19:37:31,128 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250706_193731_14840335
2025-07-06 19:37:31,257 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-06 19:37:31,259 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-06 19:37:31,259 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-06 19:37:31,260 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-06 19:37:31,260 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-06 19:37:31,260 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-06 19:37:31,261 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-06 19:37:31,263 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-06 19:37:31,264 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-06 19:37:31,265 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 19:47:21,160 - main - INFO - Epinnox v6 starting up...
2025-07-06 19:47:21,177 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 19:47:21,178 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 19:47:21,178 - main - INFO - Performance monitoring initialized
2025-07-06 19:47:21,187 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 19:47:21,188 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 19:47:21,188 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 19:47:37,632 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 19:47:39,214 - websocket - INFO - Websocket connected
2025-07-06 19:47:42,897 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-06 19:47:42,901 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-06 19:47:42,901 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-06 19:47:42,902 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-06 19:47:42,902 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-06 19:47:42,907 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-06 19:47:44,983 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-06 19:47:44,984 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-06 19:47:44,984 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-06 19:47:44,984 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-06 19:47:44,985 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-06 19:47:44,985 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 19:47:44,985 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 19:47:44,985 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-06 19:47:44,987 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-06 19:47:44,988 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-06 19:47:44,998 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-06 19:47:44,999 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-06 19:47:44,999 - storage.session_manager - INFO - Session Manager initialized
2025-07-06 19:47:45,003 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250706_194744_5b596394
2025-07-06 19:47:45,003 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250706_194744_5b596394
2025-07-06 19:47:45,136 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-06 19:47:45,138 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-06 19:47:45,138 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-06 19:47:45,138 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-06 19:47:45,138 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-06 19:47:45,139 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-06 19:47:45,140 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-06 19:47:45,143 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-06 19:47:45,143 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-06 19:47:45,144 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 19:47:51,114 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 19:48:02,065 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 19:48:02,066 - symbol_scanner - INFO -   1. ETH/USDT:USDT: 59.28 (spread: 0.000%, atr: 0.057895, depth: 39096)
2025-07-06 19:48:02,067 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 19:48:02,072 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 19:48:15,104 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 19:48:25,497 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 19:48:25,497 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 60.31 (spread: 0.008%, atr: 0.000000, depth: 3042838)
2025-07-06 19:48:25,498 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 19:48:25,499 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 19:48:40,104 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 19:48:50,155 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 19:48:50,155 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 61.57 (spread: 0.008%, atr: 0.000000, depth: 7274504)
2025-07-06 19:48:55,104 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 19:49:05,572 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 19:49:05,573 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 54.75 (spread: 0.025%, atr: 0.000000, depth: 5049586)
2025-07-06 19:49:10,104 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 19:49:20,631 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 19:49:20,631 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 50.18 (spread: 0.017%, atr: 0.000000, depth: 2372837)
2025-07-06 19:49:25,104 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 19:49:36,032 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 19:49:36,032 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 58.72 (spread: 0.008%, atr: 0.000000, depth: 5703651)
2025-07-06 19:49:40,107 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 19:49:52,851 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 19:49:52,852 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 51.34 (spread: 0.008%, atr: 0.000000, depth: 2482534)
2025-07-06 19:50:00,107 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 19:50:10,225 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 19:50:10,225 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 49.56 (spread: 0.008%, atr: 0.000000, depth: 1704148)
2025-07-06 19:50:15,106 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 19:50:25,619 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 19:50:25,620 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 47.38 (spread: 0.042%, atr: 0.000000, depth: 2162249)
2025-07-06 19:50:30,106 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 19:50:40,039 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 19:50:40,039 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 46.53 (spread: 0.025%, atr: 0.000000, depth: 1048638)
2025-07-06 19:50:45,107 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 19:50:55,751 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 19:50:55,751 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 59.30 (spread: 0.025%, atr: 0.000000, depth: 6667338)
2025-07-06 19:51:00,106 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 19:51:11,658 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 19:51:11,658 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 57.60 (spread: 0.025%, atr: 0.000000, depth: 5929750)
2025-07-06 19:53:57,072 - main - INFO - Epinnox v6 starting up...
2025-07-06 19:53:57,097 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 19:53:57,098 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 19:53:57,098 - main - INFO - Performance monitoring initialized
2025-07-06 19:53:57,108 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 19:53:57,109 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 19:53:57,110 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 19:54:08,292 - unicode_test - INFO - 🚀 System startup successful
2025-07-06 19:54:08,293 - unicode_test - INFO - ✅ Configuration loaded
2025-07-06 19:54:08,293 - unicode_test - INFO - 📊 Market data received
2025-07-06 19:54:08,293 - unicode_test - INFO - 💰 Balance updated
2025-07-06 19:54:08,294 - unicode_test - INFO - ⚠️ Warning message
2025-07-06 19:54:08,294 - unicode_test - INFO - ❌ Error occurred
2025-07-06 19:54:08,295 - portfolio.portfolio_manager - INFO - Portfolio manager initialized with $100.00 balance
2025-07-06 20:05:12,466 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x000002336A04D640>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-06 20:05:12,466 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x000002336A04D640>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-06 20:05:12,466 - websocket - INFO - tearing down on exception wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-06 20:05:20,927 - main - INFO - Epinnox v6 starting up...
2025-07-06 20:05:20,940 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 20:05:20,940 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 20:05:20,941 - main - INFO - Performance monitoring initialized
2025-07-06 20:05:20,949 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 20:05:20,950 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 20:05:20,950 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 20:05:35,880 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 20:05:37,440 - websocket - INFO - Websocket connected
2025-07-06 20:05:41,376 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-06 20:05:41,380 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-06 20:05:41,380 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-06 20:05:41,381 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-06 20:05:41,381 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-06 20:05:41,386 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-06 20:05:43,444 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-06 20:05:43,445 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-06 20:05:43,445 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-06 20:05:43,446 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-06 20:05:43,446 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-06 20:05:43,447 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 20:05:43,448 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 20:05:43,448 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-06 20:05:43,451 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-06 20:05:43,452 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-06 20:05:43,463 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-06 20:05:43,464 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-06 20:05:43,464 - storage.session_manager - INFO - Session Manager initialized
2025-07-06 20:05:43,469 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250706_200543_90f36674
2025-07-06 20:05:43,469 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250706_200543_90f36674
2025-07-06 20:05:43,600 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-06 20:05:43,602 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-06 20:05:43,602 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-06 20:05:43,602 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-06 20:05:43,603 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-06 20:05:43,603 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-06 20:05:43,605 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-06 20:05:43,607 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-06 20:05:43,608 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-06 20:05:43,609 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 20:06:20,613 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 20:06:33,784 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 20:06:33,785 - symbol_scanner - INFO -   1. ETH/USDT:USDT: 51.63 (spread: 0.000%, atr: 0.030526, depth: 28828)
2025-07-06 20:06:33,786 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 20:06:33,786 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 20:06:45,467 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 20:06:55,909 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 20:06:55,910 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 57.12 (spread: 0.009%, atr: 0.000000, depth: 4965116)
2025-07-06 20:06:55,911 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 20:06:55,912 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 20:07:10,031 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 20:07:22,613 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 20:07:22,614 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 55.24 (spread: 0.034%, atr: 0.000000, depth: 4638366)
2025-07-06 21:19:47,760 - main - INFO - Epinnox v6 starting up...
2025-07-06 21:19:47,779 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 21:19:47,779 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 21:19:47,779 - main - INFO - Performance monitoring initialized
2025-07-06 21:19:47,791 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 21:19:47,791 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 21:19:47,792 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 21:19:59,374 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 21:20:00,850 - websocket - INFO - Websocket connected
2025-07-06 21:20:04,551 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-06 21:20:04,556 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-06 21:20:04,556 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-06 21:20:04,556 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-06 21:20:04,556 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-06 21:20:04,562 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-06 21:20:06,622 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-06 21:20:06,622 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-06 21:20:06,622 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-06 21:20:06,623 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-06 21:20:06,623 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-06 21:20:06,623 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 21:20:06,623 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 21:20:06,624 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-06 21:20:06,627 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-06 21:20:06,627 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-06 21:20:06,664 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-06 21:20:06,664 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-06 21:20:06,664 - storage.session_manager - INFO - Session Manager initialized
2025-07-06 21:20:06,670 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250706_212006_bb43a69d
2025-07-06 21:20:06,670 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250706_212006_bb43a69d
2025-07-06 21:20:06,819 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-06 21:20:06,821 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-06 21:20:06,822 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-06 21:20:06,822 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-06 21:20:06,822 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-06 21:20:06,822 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-06 21:20:06,823 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-06 21:20:06,826 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-06 21:20:06,827 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-06 21:20:06,827 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 21:30:37,851 - main - INFO - Epinnox v6 starting up...
2025-07-06 21:30:37,871 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 21:30:37,872 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 21:30:37,872 - main - INFO - Performance monitoring initialized
2025-07-06 21:30:37,883 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 21:30:37,884 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 21:30:37,886 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 21:30:47,337 - __main__ - INFO - ✅ Successfully integrated with Epinnox system components
2025-07-06 21:30:47,338 - __main__ - INFO - 🔗 Running in integrated mode with Epinnox system
2025-07-06 21:30:47,338 - __main__ - INFO - 🔗 Integrated mode: Connecting to live trading system
2025-07-06 21:30:47,339 - __main__ - INFO - 📊 Starting scalping simulation...
2025-07-06 21:30:47,339 - __main__ - INFO - 🔍 Found 0 scalping opportunities
2025-07-06 21:30:47,443 - __main__ - INFO - 🔍 Found 0 scalping opportunities
2025-07-06 21:30:47,550 - __main__ - INFO - 🔍 Found 0 scalping opportunities
2025-07-06 21:30:47,659 - __main__ - INFO - 🔍 Found 0 scalping opportunities
2025-07-06 21:30:47,767 - __main__ - INFO - 🔍 Found 0 scalping opportunities
2025-07-06 21:30:47,876 - __main__ - INFO - 🔍 Found 0 scalping opportunities
2025-07-06 21:30:47,986 - __main__ - INFO - 🔍 Found 0 scalping opportunities
2025-07-06 21:30:48,094 - __main__ - INFO - 🔍 Found 0 scalping opportunities
2025-07-06 21:30:48,203 - __main__ - INFO - 🔍 Found 0 scalping opportunities
2025-07-06 21:30:48,313 - __main__ - INFO - 🔍 Found 0 scalping opportunities
2025-07-06 21:30:48,422 - __main__ - INFO - 📊 Simulation complete: 0 trades, 0 successful
2025-07-06 22:39:39,498 - main - INFO - Epinnox v6 starting up...
2025-07-06 22:39:39,525 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 22:39:39,525 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 22:39:39,526 - main - INFO - Performance monitoring initialized
2025-07-06 22:39:39,538 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 22:39:39,539 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 22:39:39,541 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 22:41:08,037 - main - INFO - Epinnox v6 starting up...
2025-07-06 22:41:08,041 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 22:41:08,042 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 22:41:08,042 - main - INFO - Performance monitoring initialized
2025-07-06 22:41:08,053 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 22:41:08,053 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 22:41:08,054 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 22:43:57,300 - main - INFO - Epinnox v6 starting up...
2025-07-06 22:43:57,304 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 22:43:57,306 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 22:43:57,306 - main - INFO - Performance monitoring initialized
2025-07-06 22:43:57,315 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 22:43:57,316 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 22:43:57,318 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 22:49:44,790 - main - INFO - Epinnox v6 starting up...
2025-07-06 22:49:44,809 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 22:49:44,809 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 22:49:44,809 - main - INFO - Performance monitoring initialized
2025-07-06 22:49:44,818 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 22:49:44,819 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 22:49:44,820 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 22:49:58,037 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 22:49:59,478 - websocket - INFO - Websocket connected
2025-07-06 22:50:04,087 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-06 22:50:04,092 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-06 22:50:04,092 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-06 22:50:04,092 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-06 22:50:04,092 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-06 22:50:04,098 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-06 22:50:06,166 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-06 22:50:06,166 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-06 22:50:06,166 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-06 22:50:06,167 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-06 22:50:06,167 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-06 22:50:06,167 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 22:50:06,167 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 22:50:06,167 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-06 22:50:06,171 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-06 22:50:06,171 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-06 22:50:06,193 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-06 22:50:06,194 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-06 22:50:06,194 - storage.session_manager - INFO - Session Manager initialized
2025-07-06 22:50:06,199 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250706_225006_6dff32b9
2025-07-06 22:50:06,200 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250706_225006_6dff32b9
2025-07-06 22:50:06,393 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-06 22:50:06,396 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-06 22:50:06,396 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-06 22:50:06,396 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-06 22:50:06,397 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-06 22:50:06,397 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-06 22:50:06,398 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-06 22:50:06,402 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-06 22:50:06,403 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-06 22:50:06,404 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 22:50:35,009 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 22:50:45,674 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 22:50:45,674 - symbol_scanner - INFO -   1. ETH/USDT:USDT: 51.47 (spread: 0.000%, atr: 0.081053, depth: 27755)
2025-07-06 22:50:45,677 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 22:50:45,683 - data.live_data_manager - INFO - Successfully subscribed to live data for ETH/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 22:50:59,367 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 22:51:10,417 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 22:51:10,418 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 50.16 (spread: 0.008%, atr: 0.000000, depth: 1866967)
2025-07-06 22:51:10,420 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 22:51:10,421 - data.live_data_manager - INFO - Successfully subscribed to live data for SHIB/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 22:51:24,019 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 22:51:34,658 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 22:51:34,659 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 63.64 (spread: 0.008%, atr: 0.000000, depth: 4454881)
2025-07-06 22:51:38,999 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 22:51:48,974 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 22:51:48,975 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 65.08 (spread: 0.008%, atr: 0.000000, depth: 6125515)
2025-07-06 22:51:54,004 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 22:52:03,925 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 22:52:03,926 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 51.78 (spread: 0.017%, atr: 0.000000, depth: 1828864)
2025-07-06 22:52:09,083 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 22:52:23,112 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 22:52:23,112 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 49.41 (spread: 0.017%, atr: 0.000000, depth: 1753357)
2025-07-06 22:52:23,973 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-07-06 22:52:23,973 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-06 22:52:23,974 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-07-06 22:52:23,974 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-06 22:52:23,974 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-06 22:52:23,974 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-06 22:52:23,974 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-06 22:52:34,207 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 243 chars
2025-07-06 22:52:34,208 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with volume spike indicating strong buying interest.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-06 22:52:34,208 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 96, Total: 270
2025-07-06 22:52:34,211 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-06 22:52:34,262 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-07-06 22:52:34,262 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-06 22:52:34,263 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-06 22:52:34,263 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - SHIB/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000012/$0.000012
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000012
Resistance: $0.000012
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-06 22:52:34,264 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-06 22:52:37,648 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 167 chars
2025-07-06 22:52:37,648 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "action": "WAIT",
  "entry_type": "MARKET/LIMIT",
  "wait_for": "VOLUME_CONFIRMATION",
  "max_wait_seconds": 60,
  "reasoning": "Waiting for volume confirmation"
}...
2025-07-06 22:52:37,648 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 74, Total: 677
2025-07-06 22:52:37,648 - core.llm_response_parsers - INFO - Entry timing parsed: WAIT (LIMIT order)
2025-07-06 22:52:37,699 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-07-06 22:52:37,699 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-06 22:52:37,700 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-06 22:52:37,700 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: MODERATE_RISK
💰 Balance: $15.71 | Health: Moderate Risk - Reduced Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000011 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | ...
2025-07-06 22:52:37,700 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-06 22:52:44,129 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1134 chars
2025-07-06 22:52:44,129 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 70%, TAKE_PROFIT: -1.2%, STOP_LOSS: -3.0%, EXPLANATION: The current market environment is characterized by a neutral sector momentum and normal volatility levels with an average volume profile indicating stable trading conditions, which aligns well for conservative strat...
2025-07-06 22:52:44,130 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 937, Completion: 259, Total: 1196
2025-07-06 22:52:44,130 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 70.0, 'EXPLANATION': "THE CURRENT MARKET ENVIRONMENT IS CHARACTERIZED BY A NEUTRAL SECTOR MOMENTUM AND NORMAL VOLATILITY LEVELS WITH AN AVERAGE VOLUME PROFILE INDICATING STABLE TRADING CONDITIONS, WHICH ALIGNS WELL FOR CONSERVATIVE STRATEGIES AIMED AT PRESERVING CAPITAL IN THE MODERATE RISK ACCOUNT STATUS OF THIS TRADER'S PORTFOLIO.", 'ACTION': 'ENTER_NOW'}
2025-07-06 22:52:44,130 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-06 22:52:44,181 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-06 22:52:44,182 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 20.21s - 3 prompts executed sequentially
2025-07-06 22:52:44,185 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-06 22:52:44,185 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-06 22:52:44,185 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for SHIB/USDT:USDT:

Current Price: 0.000012
ML Predictions: ['WAIT', 'WAIT', 'LONG', 'SHORT', 'SHORT', 'WAIT', 'WAIT', 'WAIT']
ML Confidences: ['64.6%', '60.5%', '60.0%', '76.3%', '82.1%', '67.4%', '65.8%', '74.9%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-07-06 22:52:44,185 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-07-06 22:52:48,006 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 386 chars
2025-07-06 22:52:48,007 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT | CONFIDENCE: 85% | REASONING: The ML predictions show a majority of 'WAIT' and only one prediction with high confidence ('SHORT') indicating an imminent price decrease. Considering the current low market cap, which often leads to higher volatility, this aligns well for taking advant...
2025-07-06 22:52:48,008 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 376, Completion: 100, Total: 476
2025-07-06 22:52:48,983 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-06 22:52:48,983 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-06 22:52:48,983 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP SHIB/USDT:USDT $0.00 | BID/ASK 0.00/0.00 (0.08%)
DEPTH: B:0.00×160427473 A:0.00×58408044
FLOW: +0% | VOL: +0%
ENSEMBLE: WAIT 69%
BAL: $16 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($16) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":26545.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0.5...
2025-07-06 22:52:48,983 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-07-06 22:52:52,001 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 95 chars
2025-07-06 22:52:52,002 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"action":"BUY","quantity":394,"leverage":20,"stop_loss":null,"take_profit":null, "risk_pct":2}...
2025-07-06 22:52:52,002 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 469, Completion: 40, Total: 509
2025-07-06 22:52:52,926 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 22:53:04,148 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 22:53:04,148 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 55.73 (spread: 0.025%, atr: 0.000000, depth: 4429629)
2025-07-06 22:53:09,051 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 22:53:19,649 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 22:53:19,650 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 55.90 (spread: 0.034%, atr: 0.000000, depth: 4858027)
2025-07-06 22:53:23,997 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 22:53:34,611 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 22:53:34,611 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 58.40 (spread: 0.042%, atr: 0.000000, depth: 7301146)
2025-07-06 22:53:44,022 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 22:53:55,232 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 22:53:55,233 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 58.40 (spread: 0.042%, atr: 0.000000, depth: 7623947)
2025-07-06 22:53:58,914 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts for speed
2025-07-06 22:53:58,915 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage Start: 0/16 threads
2025-07-06 22:53:58,916 - core.llm_orchestrator - INFO - 🧠 Executing prompt 1/3: risk_assessment
2025-07-06 22:53:58,916 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-06 22:53:58,917 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-06 22:53:58,917 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-06 22:53:58,917 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-06 22:54:02,680 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-06 22:54:02,680 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-06 22:54:02,681 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 94, Total: 268
2025-07-06 22:54:02,681 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-06 22:54:02,732 - core.llm_orchestrator - INFO - 🧠 Executing prompt 2/3: entry_timing
2025-07-06 22:54:02,732 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-06 22:54:02,733 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-06 22:54:02,733 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - SHIB/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.000012/$0.000012
Spread: 0.100% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS:
Support: $0.000012
Resistance: $0.000012
Distance to Support: 0.50%

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmation: PENDING
- Spread conditions: FAVORABLE
- M...
2025-07-06 22:54:02,733 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-06 22:54:06,737 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 318 chars
2025-07-06 22:54:06,737 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "ENTER_NOW",
  "entry_type": "LIMIT",
  "confidence": 80,
  "wait_for": null,
  "max_wait_seconds": 30,
  "reasoning": "Market conditions are favorable with a narrow spread and no significant volume spikes. Momentum is neutral but the risk/reward ratio of 2:1 justifies an immed...
2025-07-06 22:54:06,737 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 603, Completion: 110, Total: 713
2025-07-06 22:54:06,738 - core.llm_response_parsers - WARNING - JSON decode error: Expecting ',' delimiter: line 7 column 154 (char 271)
2025-07-06 22:54:06,789 - core.llm_orchestrator - INFO - 🧠 Executing prompt 3/3: opportunity_scanner
2025-07-06 22:54:06,790 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-06 22:54:06,790 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-06 22:54:06,790 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: MODERATE_RISK
💰 Balance: $15.71 | Health: Moderate Risk - Reduced Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.000011 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | ...
2025-07-06 22:54:06,791 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.1, Max Tokens: 512
2025-07-06 22:54:12,923 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1045 chars
2025-07-06 22:54:12,923 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 70%, TAKE_PROFIT: -1.2%, STOP_LOSS: -3.0%, EXPLANATION: Given the account's moderate risk status and current market conditions with neutral sector momentum but normal volatility levels, a conservative approach is warranted to preserve capital while seeking potential prof...
2025-07-06 22:54:12,924 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 937, Completion: 245, Total: 1182
2025-07-06 22:54:12,924 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 70.0, 'EXPLANATION': "GIVEN THE ACCOUNT'S MODERATE RISK STATUS AND CURRENT MARKET CONDITIONS WITH NEUTRAL SECTOR MOMENTUM BUT NORMAL VOLATILITY LEVELS, A CONSERVATIVE APPROACH IS WARRANTED TO PRESERVE CAPITAL WHILE SEEKING POTENTIAL PROFIT OPPORTUNITIES WITHIN SAFE LIMITS. THE SHIB/USDT PAIR SHOWS HIGH SETUP QUALITY DUE TO CLEAR PATTERNS OR STRONG SIGNALS IN HISTORICAL DATA THAT SUGGEST AN OVERBOUGHT CONDITION WHICH COULD LEAD TO PRICE CORRECTIONS DOWNWARDS. A SHORT POSITION WITH TIGHT STOP LOSS AND TAKE-PROFIT LEVELS IS RECOMMENDED, ENSURING THE RISK DOES NOT EXCEED 1% PER TRADE AS DICTATED BY ACCOUNT PRESERVATION CRITERIA WHILE AIMING FOR A PROFIT RATIO OF AT LEAST 3:1 DUE TO HIGHER CAUTION IN THIS HEALTH STATUS SCENARIO. THE HISTORICAL CONTEXT INDICATES THAT SHIB/USDT HAS BEEN SHOWING AN UPWARD TREND RECENTLY, BUT GIVEN THE CURRENT MARKET SENTIMENT AND VOLATILITY PROFILE, THERE'S POTENTIAL FOR REVERSAL WHICH JUSTIFIES SHORTING WITH A CONSERVATIVE RISK-REWARD SETUP.", 'ACTION': 'ENTER_NOW'}
2025-07-06 22:54:12,924 - core.llm_response_parsers - INFO - Opportunity scanner parsed: NONE (NONE)
2025-07-06 22:54:12,975 - core.llm_orchestrator - INFO - 📊 LLM Thread Usage End: 0/16 threads
2025-07-06 22:54:12,975 - core.llm_orchestrator - INFO - 🧠 LLM prompt cycle completed in 14.06s - 3 prompts executed sequentially
2025-07-06 22:54:12,977 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-06 22:54:12,977 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-06 22:54:12,977 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for SHIB/USDT:USDT:

Current Price: 0.000012
ML Predictions: ['WAIT', 'WAIT', 'LONG', 'LONG', 'LONG', 'SHORT', 'WAIT', 'WAIT']
ML Confidences: ['69.5%', '65.9%', '72.8%', '73.6%', '89.4%', '60.1%', '64.8%', '87.9%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-07-06 22:54:12,977 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-07-06 22:54:17,715 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 632 chars
2025-07-06 22:54:17,716 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG  
CONFIDENCE: 85%  
REASONING: The ML predictions show a strong trend towards 'LONG' with the sixth prediction at an above-average confidence of 60.1%. Although there are several 'WAIT' signals, they do not significantly counterbalance the high confidences in favorable positions for l...
2025-07-06 22:54:17,716 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 376, Completion: 165, Total: 541
2025-07-06 22:54:18,565 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-06 22:54:18,566 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-06 22:54:18,566 - llama.lmstudio_runner - INFO - 💬 User Prompt: SCALP SHIB/USDT:USDT $0.00 | BID/ASK 0.00/0.00 (0.08%)
DEPTH: B:0.00×219150830 A:0.00×135461220
FLOW: +0% | VOL: +0%
ENSEMBLE: WAIT 73%
BAL: $16 | POS: 0

TRADE NOW! BIAS: BUY/SELL over WAIT.
CALCULATE QUANTITY: Use 1-3% of balance ($16) for position sizing.

JSON ONLY, NO COMMENTS:
{"ACTION":"BUY","QUANTITY":26523.0,"LEVERAGE":20,"STOP_LOSS":0,"TAKE_PROFIT":0,"RISK_PCT":2,"ORDER_TYPE":"MARKET"}

ACTION: BUY/SELL/WAIT | QUANTITY: Calculate based on balance & risk | LEVERAGE: 1-200 | RISK_PCT: 0....
2025-07-06 22:54:18,567 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.05, Max Tokens: 200
2025-07-06 22:54:21,688 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 95 chars
2025-07-06 22:54:21,688 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"action":"BUY","quantity":396,"leverage":20,"stop_loss":null,"take_profit":null, "risk_pct":2}...
2025-07-06 22:54:21,688 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 470, Completion: 40, Total: 510
2025-07-06 22:54:23,652 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 22:54:35,318 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 22:54:35,318 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 60.05 (spread: 0.017%, atr: 0.000000, depth: 7071672)
2025-07-06 22:54:38,994 - symbol_scanner - INFO - Scanning 8 symbols for best trading opportunities...
2025-07-06 22:54:51,730 - symbol_scanner - INFO - Symbol ranking (top 1):
2025-07-06 22:54:51,730 - symbol_scanner - INFO -   1. SHIB/USDT:USDT: 51.10 (spread: 0.017%, atr: 0.000000, depth: 1916916)
2025-07-06 22:55:47,347 - main - INFO - Epinnox v6 starting up...
2025-07-06 22:55:47,362 - core.performance_monitor - INFO - Performance monitoring started
2025-07-06 22:55:47,362 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-06 22:55:47,363 - main - INFO - Performance monitoring initialized
2025-07-06 22:55:47,377 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 22:55:47,377 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-06 22:55:47,378 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-06 22:56:00,270 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-06 22:56:01,760 - websocket - INFO - Websocket connected
2025-07-06 22:56:05,561 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-06 22:56:05,566 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-06 22:56:05,566 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-06 22:56:05,566 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-06 22:56:05,566 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-06 22:56:05,572 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-06 22:56:07,620 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'phi-3.1-mini-128k-instruct', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-06 22:56:07,620 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-06 22:56:07,620 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-06 22:56:07,621 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-06 22:56:07,621 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-06 22:56:07,621 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-06 22:56:07,622 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-06 22:56:07,622 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-06 22:56:07,625 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-07-06 22:56:07,625 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-07-06 22:56:07,638 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-06 22:56:07,639 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-06 22:56:07,639 - storage.session_manager - INFO - Session Manager initialized
2025-07-06 22:56:07,645 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250706_225607_548f504c
2025-07-06 22:56:07,646 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250706_225607_548f504c
2025-07-06 22:56:07,830 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-06 22:56:07,833 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-06 22:56:07,833 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-06 22:56:07,833 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-06 22:56:07,833 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-06 22:56:07,833 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-06 22:56:07,835 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-06 22:56:07,839 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-06 22:56:07,840 - symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-06 22:56:07,840 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
