"""
Trading Configuration for EPINNOX v6
Centralized configuration management for trading parameters
"""

import os
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class TradingConfig:
    """Main trading configuration"""
    
    # Basic settings
    initial_balance: float = 10000.0
    max_positions: int = 3
    min_confidence: float = 0.65
    
    # Risk management
    max_portfolio_risk: float = 0.20
    max_position_size: float = 0.10
    max_leverage: float = 3.0
    stop_loss_pct: float = 0.05
    take_profit_pct: float = 0.10
    
    # Trading parameters
    slippage: float = 0.001
    commission: float = 0.001
    min_trade_amount: float = 10.0
    
    # Symbols and timeframes
    symbols: list = None
    timeframe: str = "1m"
    
    # ML/RL settings
    use_rl: bool = False
    use_lstm: bool = False
    model_update_frequency: int = 100  # trades
    
    # NLP settings
    use_sentiment: bool = True
    sentiment_weight: float = 0.2
    
    # Technical analysis weights
    rsi_weight: float = 0.3
    macd_weight: float = 0.3
    momentum_weight: float = 0.4
    
    # Execution settings
    paper_trading: bool = True
    autonomous_mode: bool = False
    max_daily_trades: int = 50
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ["BTC/USDT", "ETH/USDT"]
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'TradingConfig':
        """Create config from dictionary"""
        return cls(**config_dict)
    
    @classmethod
    def from_file(cls, filepath: str) -> 'TradingConfig':
        """Load config from JSON file"""
        try:
            with open(filepath, 'r') as f:
                config_dict = json.load(f)
            return cls.from_dict(config_dict)
        except Exception as e:
            logger.error(f"Failed to load config from {filepath}: {e}")
            return cls()  # Return default config
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return asdict(self)
    
    def save_to_file(self, filepath: str):
        """Save config to JSON file"""
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            with open(filepath, 'w') as f:
                json.dump(self.to_dict(), f, indent=2)
            logger.info(f"Config saved to {filepath}")
        except Exception as e:
            logger.error(f"Failed to save config to {filepath}: {e}")
    
    def validate(self) -> bool:
        """Validate configuration parameters"""
        errors = []
        
        # Validate ranges
        if not 0 < self.min_confidence <= 1:
            errors.append("min_confidence must be between 0 and 1")
        
        if not 0 < self.max_portfolio_risk <= 1:
            errors.append("max_portfolio_risk must be between 0 and 1")
        
        if not 0 < self.max_position_size <= 1:
            errors.append("max_position_size must be between 0 and 1")
        
        if self.max_leverage < 1:
            errors.append("max_leverage must be >= 1")
        
        if self.initial_balance <= 0:
            errors.append("initial_balance must be positive")
        
        if self.max_positions <= 0:
            errors.append("max_positions must be positive")
        
        if not self.symbols:
            errors.append("symbols list cannot be empty")
        
        # Log errors
        if errors:
            for error in errors:
                logger.error(f"Config validation error: {error}")
            return False
        
        return True
    
    def update(self, **kwargs):
        """Update configuration parameters"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                logger.info(f"Updated config: {key} = {value}")
            else:
                logger.warning(f"Unknown config parameter: {key}")

class ConfigManager:
    """Configuration manager for EPINNOX v6"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = config_dir
        self.config_file = os.path.join(config_dir, "trading_config.json")
        self._config = None
        
        # Ensure config directory exists
        os.makedirs(config_dir, exist_ok=True)
    
    @property
    def config(self) -> TradingConfig:
        """Get current configuration"""
        if self._config is None:
            self.load_config()
        return self._config
    
    def load_config(self) -> TradingConfig:
        """Load configuration from file"""
        if os.path.exists(self.config_file):
            self._config = TradingConfig.from_file(self.config_file)
            logger.info(f"Loaded config from {self.config_file}")
        else:
            self._config = TradingConfig()
            logger.info("Using default configuration")
            self.save_config()  # Save default config
        
        return self._config
    
    def save_config(self):
        """Save current configuration to file"""
        if self._config:
            self._config.save_to_file(self.config_file)
    
    def update_config(self, **kwargs):
        """Update configuration parameters"""
        if self._config is None:
            self.load_config()
        
        self._config.update(**kwargs)
        self.save_config()
    
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        self._config = TradingConfig()
        self.save_config()
        logger.info("Configuration reset to defaults")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary"""
        config = self.config
        return {
            "initial_balance": config.initial_balance,
            "max_positions": config.max_positions,
            "symbols": config.symbols,
            "paper_trading": config.paper_trading,
            "autonomous_mode": config.autonomous_mode,
            "use_rl": config.use_rl,
            "use_sentiment": config.use_sentiment
        }

# Global config manager instance
config_manager = ConfigManager()

def get_config() -> TradingConfig:
    """Get global trading configuration"""
    return config_manager.config

def update_config(**kwargs):
    """Update global trading configuration"""
    config_manager.update_config(**kwargs)

def save_config():
    """Save global trading configuration"""
    config_manager.save_config()
