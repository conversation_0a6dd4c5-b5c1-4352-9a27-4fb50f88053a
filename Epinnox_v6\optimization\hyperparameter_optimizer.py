"""
Auto-Hyperparameter Testing Framework
Grid search and optimization for trading strategy parameters
"""

import itertools
import json
import os
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass, asdict
import logging
import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)

@dataclass
class ParameterSpace:
    """Define parameter search space"""
    name: str
    values: List[Any]
    param_type: str = "discrete"  # discrete, continuous, categorical

@dataclass
class OptimizationResult:
    """Results from parameter optimization"""
    parameters: Dict[str, Any]
    performance_metrics: Dict[str, float]
    backtest_results: Dict
    optimization_score: float
    timestamp: datetime

class HyperparameterOptimizer:
    """
    Comprehensive hyperparameter optimization for trading strategies
    """
    
    def __init__(self, backtest_runner_class, base_config: Dict):
        self.backtest_runner_class = backtest_runner_class
        self.base_config = base_config.copy()
        self.optimization_results = []
        self.best_parameters = None
        self.best_score = float('-inf')
        
    def define_search_space(self) -> Dict[str, ParameterSpace]:
        """Define the hyperparameter search space"""
        return {
            # Risk Management Parameters
            'max_portfolio_risk': ParameterSpace(
                name='max_portfolio_risk',
                values=[0.10, 0.15, 0.20, 0.25, 0.30],
                param_type='discrete'
            ),
            'max_position_size': ParameterSpace(
                name='max_position_size',
                values=[0.05, 0.08, 0.10, 0.12, 0.15],
                param_type='discrete'
            ),
            'max_leverage': ParameterSpace(
                name='max_leverage',
                values=[1.0, 2.0, 3.0, 5.0, 8.0, 10.0],
                param_type='discrete'
            ),
            
            # Trading Parameters
            'min_confidence': ParameterSpace(
                name='min_confidence',
                values=[0.55, 0.60, 0.65, 0.70, 0.75, 0.80],
                param_type='discrete'
            ),
            'slippage': ParameterSpace(
                name='slippage',
                values=[0.0005, 0.001, 0.0015, 0.002, 0.0025],
                param_type='discrete'
            ),
            'commission': ParameterSpace(
                name='commission',
                values=[0.0005, 0.001, 0.0015, 0.002],
                param_type='discrete'
            ),
            
            # Signal Weights (if applicable)
            'rsi_weight': ParameterSpace(
                name='rsi_weight',
                values=[0.1, 0.2, 0.3, 0.4, 0.5],
                param_type='discrete'
            ),
            'macd_weight': ParameterSpace(
                name='macd_weight',
                values=[0.1, 0.2, 0.3, 0.4, 0.5],
                param_type='discrete'
            ),
            'momentum_weight': ParameterSpace(
                name='momentum_weight',
                values=[0.1, 0.2, 0.3, 0.4, 0.5],
                param_type='discrete'
            ),
            
            # RL Parameters (if using RL)
            'rl_learning_rate': ParameterSpace(
                name='rl_learning_rate',
                values=[1e-5, 3e-5, 1e-4, 3e-4, 1e-3],
                param_type='discrete'
            ),
            'rl_batch_size': ParameterSpace(
                name='rl_batch_size',
                values=[32, 64, 128, 256],
                param_type='discrete'
            )
        }
    
    def generate_parameter_combinations(self, search_space: Dict[str, ParameterSpace], 
                                      max_combinations: int = 1000) -> List[Dict[str, Any]]:
        """Generate parameter combinations for grid search"""
        
        # Get all parameter names and their values
        param_names = list(search_space.keys())
        param_values = [search_space[name].values for name in param_names]
        
        # Generate all combinations
        all_combinations = list(itertools.product(*param_values))
        
        # Limit combinations if too many
        if len(all_combinations) > max_combinations:
            logger.warning(f"Too many combinations ({len(all_combinations)}). Sampling {max_combinations}.")
            # Random sampling
            import random
            random.seed(42)
            all_combinations = random.sample(all_combinations, max_combinations)
        
        # Convert to list of dictionaries
        combinations = []
        for combo in all_combinations:
            param_dict = dict(zip(param_names, combo))
            combinations.append(param_dict)
        
        logger.info(f"Generated {len(combinations)} parameter combinations for testing")
        return combinations
    
    def calculate_optimization_score(self, backtest_result) -> float:
        """Calculate optimization score from backtest results"""
        
        # Multi-objective optimization score
        # Combines return, Sharpe ratio, max drawdown, and win rate
        
        total_return = backtest_result.total_return
        sharpe_ratio = backtest_result.sharpe_ratio
        max_drawdown = backtest_result.max_drawdown
        win_rate = backtest_result.win_rate
        
        # Normalize and weight the metrics
        return_score = min(total_return * 2, 1.0)  # Cap at 50% return
        sharpe_score = min(sharpe_ratio / 3.0, 1.0)  # Cap at Sharpe 3.0
        drawdown_score = max(0, 1.0 - max_drawdown * 2)  # Penalize drawdown
        win_rate_score = win_rate
        
        # Weighted combination
        optimization_score = (
            return_score * 0.3 +
            sharpe_score * 0.3 +
            drawdown_score * 0.2 +
            win_rate_score * 0.2
        )
        
        return optimization_score
    
    async def run_single_optimization(self, parameters: Dict[str, Any]) -> OptimizationResult:
        """Run optimization for a single parameter set"""
        
        try:
            # Create config with new parameters
            test_config = self.base_config.copy()
            test_config.update(parameters)
            
            # Create and run backtest
            from tests.backtesting.backtest_runner import BacktestConfig
            backtest_config = BacktestConfig(**test_config)
            
            runner = self.backtest_runner_class(backtest_config)
            backtest_result = runner.run_backtest()
            
            # Calculate optimization score
            optimization_score = self.calculate_optimization_score(backtest_result)
            
            # Create result
            result = OptimizationResult(
                parameters=parameters,
                performance_metrics={
                    'total_return': backtest_result.total_return,
                    'sharpe_ratio': backtest_result.sharpe_ratio,
                    'max_drawdown': backtest_result.max_drawdown,
                    'win_rate': backtest_result.win_rate,
                    'profit_factor': backtest_result.profit_factor,
                    'total_trades': backtest_result.total_trades
                },
                backtest_results=asdict(backtest_result),
                optimization_score=optimization_score,
                timestamp=datetime.now()
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in optimization run: {e}")
            # Return failed result
            return OptimizationResult(
                parameters=parameters,
                performance_metrics={},
                backtest_results={},
                optimization_score=float('-inf'),
                timestamp=datetime.now()
            )
    
    async def run_grid_search(self, search_space: Dict[str, ParameterSpace] = None,
                            max_combinations: int = 100, 
                            parallel_jobs: int = 4) -> List[OptimizationResult]:
        """Run grid search optimization"""
        
        if search_space is None:
            search_space = self.define_search_space()
        
        logger.info("🔍 Starting hyperparameter grid search optimization...")
        
        # Generate parameter combinations
        combinations = self.generate_parameter_combinations(search_space, max_combinations)
        
        # Run optimizations
        results = []
        
        # Process in batches for parallel execution
        batch_size = parallel_jobs
        for i in range(0, len(combinations), batch_size):
            batch = combinations[i:i + batch_size]
            
            logger.info(f"Processing batch {i//batch_size + 1}/{(len(combinations)-1)//batch_size + 1}")
            
            # Run batch in parallel
            batch_tasks = [self.run_single_optimization(params) for params in batch]
            batch_results = await asyncio.gather(*batch_tasks)
            
            results.extend(batch_results)
            
            # Update best parameters
            for result in batch_results:
                if result.optimization_score > self.best_score:
                    self.best_score = result.optimization_score
                    self.best_parameters = result.parameters
                    logger.info(f"New best score: {self.best_score:.4f} with parameters: {result.parameters}")
        
        self.optimization_results = results
        
        logger.info(f"✅ Grid search completed. Best score: {self.best_score:.4f}")
        
        return results
    
    def run_random_search(self, search_space: Dict[str, ParameterSpace] = None,
                         n_iterations: int = 50) -> List[OptimizationResult]:
        """Run random search optimization"""
        
        if search_space is None:
            search_space = self.define_search_space()
        
        logger.info(f"🎲 Starting random search optimization ({n_iterations} iterations)...")
        
        results = []
        
        for i in range(n_iterations):
            # Generate random parameters
            random_params = {}
            for param_name, param_space in search_space.items():
                random_params[param_name] = np.random.choice(param_space.values)
            
            # Run optimization
            result = asyncio.run(self.run_single_optimization(random_params))
            results.append(result)
            
            # Update best
            if result.optimization_score > self.best_score:
                self.best_score = result.optimization_score
                self.best_parameters = result.parameters
                logger.info(f"Iteration {i+1}: New best score {self.best_score:.4f}")
            
            if (i + 1) % 10 == 0:
                logger.info(f"Completed {i+1}/{n_iterations} iterations")
        
        self.optimization_results = results
        
        logger.info(f"✅ Random search completed. Best score: {self.best_score:.4f}")
        
        return results
    
    def analyze_results(self) -> Dict:
        """Analyze optimization results"""
        
        if not self.optimization_results:
            return {}
        
        # Sort results by score
        sorted_results = sorted(self.optimization_results, 
                              key=lambda x: x.optimization_score, reverse=True)
        
        # Get top 10 results
        top_results = sorted_results[:10]
        
        # Analyze parameter importance
        param_importance = self._analyze_parameter_importance()
        
        analysis = {
            'total_runs': len(self.optimization_results),
            'best_score': self.best_score,
            'best_parameters': self.best_parameters,
            'top_10_results': [
                {
                    'rank': i + 1,
                    'score': result.optimization_score,
                    'parameters': result.parameters,
                    'metrics': result.performance_metrics
                }
                for i, result in enumerate(top_results)
            ],
            'parameter_importance': param_importance,
            'score_statistics': {
                'mean': np.mean([r.optimization_score for r in self.optimization_results]),
                'std': np.std([r.optimization_score for r in self.optimization_results]),
                'min': min([r.optimization_score for r in self.optimization_results]),
                'max': max([r.optimization_score for r in self.optimization_results])
            }
        }
        
        return analysis
    
    def _analyze_parameter_importance(self) -> Dict:
        """Analyze which parameters have the most impact on performance"""
        
        if len(self.optimization_results) < 10:
            return {}
        
        # Create DataFrame for analysis
        data = []
        for result in self.optimization_results:
            row = result.parameters.copy()
            row['score'] = result.optimization_score
            data.append(row)
        
        df = pd.DataFrame(data)
        
        # Calculate correlation between parameters and score
        correlations = {}
        for param in df.columns:
            if param != 'score' and df[param].dtype in ['int64', 'float64']:
                corr = df[param].corr(df['score'])
                if not np.isnan(corr):
                    correlations[param] = abs(corr)
        
        # Sort by importance
        sorted_importance = sorted(correlations.items(), key=lambda x: x[1], reverse=True)
        
        return dict(sorted_importance)
    
    def save_results(self, filename: str = None):
        """Save optimization results to file"""
        
        if filename is None:
            filename = f"optimization_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # Prepare data for JSON serialization
        results_data = {
            'optimization_summary': {
                'timestamp': datetime.now().isoformat(),
                'total_runs': len(self.optimization_results),
                'best_score': self.best_score,
                'best_parameters': self.best_parameters
            },
            'analysis': self.analyze_results(),
            'all_results': [
                {
                    'parameters': result.parameters,
                    'performance_metrics': result.performance_metrics,
                    'optimization_score': result.optimization_score,
                    'timestamp': result.timestamp.isoformat()
                }
                for result in self.optimization_results
            ]
        }
        
        # Ensure directory exists
        os.makedirs('optimization_results', exist_ok=True)
        filepath = os.path.join('optimization_results', filename)
        
        with open(filepath, 'w') as f:
            json.dump(results_data, f, indent=2, default=str)
        
        logger.info(f"📊 Optimization results saved to {filepath}")
        
        return filepath
    
    def load_results(self, filename: str):
        """Load optimization results from file"""
        
        with open(filename, 'r') as f:
            data = json.load(f)
        
        # Reconstruct results
        self.optimization_results = []
        for result_data in data['all_results']:
            result = OptimizationResult(
                parameters=result_data['parameters'],
                performance_metrics=result_data['performance_metrics'],
                backtest_results={},  # Not saved in summary
                optimization_score=result_data['optimization_score'],
                timestamp=datetime.fromisoformat(result_data['timestamp'])
            )
            self.optimization_results.append(result)
        
        # Update best parameters
        if data['optimization_summary']['best_parameters']:
            self.best_parameters = data['optimization_summary']['best_parameters']
            self.best_score = data['optimization_summary']['best_score']
        
        logger.info(f"📊 Loaded {len(self.optimization_results)} optimization results from {filename}")
    
    def get_recommended_parameters(self) -> Dict[str, Any]:
        """Get recommended parameters based on optimization results"""
        
        if not self.best_parameters:
            logger.warning("No optimization results available. Using default parameters.")
            return self.base_config
        
        # Combine best parameters with base config
        recommended = self.base_config.copy()
        recommended.update(self.best_parameters)
        
        return recommended
