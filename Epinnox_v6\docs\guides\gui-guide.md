# Epinnox Trading System GUI v2.0

## 🚀 Overview

The Epinnox Trading System now features a completely rebuilt, comprehensive GUI that displays all the sophisticated analysis your trading system produces. This modern interface showcases:

- **Real-time market data** from HTX exchange
- **AI analysis** from your Phi-3.1-Mini model via LMStudio
- **Multi-timeframe analysis** (1m, 5m, 15m)
- **Advanced signal scoring** system
- **Market regime detection**
- **Adaptive risk management**
- **System performance metrics**

## 🎯 Key Features

### 📊 Market Data Panel
- Live OHLCV data for DOGE/USDT
- Real-time order book with top 5 bids/asks
- Recent trades with buy/sell indicators
- Price change indicators with color coding
- Data freshness indicators

### ⏱️ Multi-timeframe Analysis
- Individual timeframe trend analysis (1m, 5m, 15m)
- Trend strength and direction for each timeframe
- Overall trend consensus and alignment percentage
- Visual indicators for bullish/bearish/neutral status
- Trend change detection and momentum analysis

### 🎯 Signal Scoring System
- Individual signal scores (MACD, Order Book, Volume, Price Action, Trend)
- Total combined signal score and confidence percentage
- Signal alignment and consistency metrics
- Performance tracking for signal accuracy
- Visual breakdown of signal contributions

### 🌊 Market Regime Detection
- Current market regime identification (low/high volatility, trending, ranging)
- Regime-specific parameter adjustments
- Volatility and trend strength metrics
- Regime stability and transition tracking

### 🤖 AI Analysis Panel
- Complete LLM decision output (LONG/SHORT/WAIT)
- Full AI reasoning and explanation text
- Model information (Phi-3.1-Mini via LMStudio)
- Risk parameters (take profit, stop loss)
- Analysis quality metrics

### ⚖️ Risk Management
- Adaptive stop loss and take profit calculations
- Position sizing recommendations
- Risk score analysis and breakdown
- ATR-based volatility calculations
- Risk factor assessment

### 🖥️ System Status
- Connection and trading status indicators
- Analysis timing and countdown
- System health metrics
- Resource usage monitoring
- Recent events log

### 📈 Performance Metrics
- Signal success rates and statistics
- Time-based performance analysis
- Confidence distribution
- System efficiency metrics
- Performance trends

## 🚀 Getting Started

### Prerequisites
- Python 3.8+
- PyQt5
- All existing Epinnox dependencies

### Installation
```bash
# Install PyQt5 if not already installed
pip install PyQt5

# Install additional dependencies
pip install psutil
```

### Running the GUI

#### Option 1: GUI Only (with sample data)
```bash
# Start the GUI integration (provides sample data)
python gui_integration.py

# In another terminal, start the GUI
python launch_gui.py
```

#### Option 2: With Live Trading System
```bash
# Start your live trading system
python main.py --symbol DOGE/USDT --exchange --live --continuous

# Start the GUI integration to bridge data
python gui_integration.py

# Start the GUI
python launch_gui.py
```

## 🎨 Interface Layout

The GUI is organized into three main tabs:

### 📊 Market Data Tab
- Market price information
- Multi-timeframe analysis
- Real-time data feeds

### 🎯 Signal Analysis Tab
- Signal scoring breakdown
- Market regime detection
- Technical analysis details

### 🤖 AI Analysis Tab
- AI decision and reasoning
- Risk management parameters
- Model performance metrics

### Right Panel
- System status and health
- Performance metrics
- Resource monitoring

## 🔧 Configuration

The GUI automatically reads data from:
- `gui_data.json` - Main trading data
- `system_status.json` - System status information

Data is updated in real-time through the GUI integration script.

## 🎯 Key Benefits

1. **Comprehensive Visibility** - See every aspect of your trading system's analysis
2. **Real-time Updates** - Live data feeds with 1-second refresh rates
3. **Professional Interface** - Clean, dark theme with color-coded indicators
4. **Detailed Analysis** - Deep dive into signal scoring and AI reasoning
5. **Performance Tracking** - Monitor system efficiency and success rates
6. **Risk Awareness** - Clear visualization of risk management parameters

## 🔄 Data Flow

```
Trading System → GUI Integration → Data Files → GUI Display
     ↓              ↓                ↓           ↓
Live Analysis → Data Processing → JSON Files → Real-time UI
```

## 🎨 Visual Indicators

- 🟢 **Green**: Positive/Bullish/Connected/Good
- 🔴 **Red**: Negative/Bearish/Disconnected/Error
- 🟡 **Yellow**: Neutral/Warning/Simulation
- 🔵 **Blue**: Information/Processing

## 📱 Responsive Design

The interface is designed to be:
- **Scalable** - Works on different screen sizes
- **Organized** - Logical grouping of related information
- **Accessible** - Clear labels and color coding
- **Professional** - Clean, modern appearance

## 🔧 Troubleshooting

### GUI Won't Start
- Ensure PyQt5 is installed: `pip install PyQt5`
- Check Python version (3.8+ required)

### No Data Displayed
- Make sure `gui_integration.py` is running
- Check for `gui_data.json` file creation
- Verify trading system is generating data

### Performance Issues
- Reduce update frequency in `main_window.py`
- Close unused applications
- Check system resources

## 🚀 Future Enhancements

- **Charts Integration** - Real-time price charts
- **Alert System** - Desktop notifications for signals
- **Configuration Panel** - GUI-based parameter adjustment
- **Export Features** - Save analysis reports
- **Multi-symbol Support** - Track multiple trading pairs

## 📞 Support

The GUI integrates seamlessly with your existing Epinnox trading system. All the sophisticated analysis you've built is now beautifully visualized in a professional interface.

---

**Epinnox Trading System v2.0** - *Intelligent Trading with Comprehensive Visualization*
