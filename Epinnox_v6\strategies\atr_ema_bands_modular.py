#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ATR-EMA Bands Strategy (Modular Version)
--------------------------------------
A modular implementation of the ATR-EMA Bands strategy that uses individual indicator modules.
"""

import pandas as pd
import numpy as np
import logging

from strategies.composite_signal_generator import CompositeSignalGenerator
from strategies.indicators import EMABandsIndicator, RSIIndicator, MACDIndicator

logger = logging.getLogger('strategy.atr_ema_bands_modular')

class ATREMABandsStrategyModular:
    """
    Modular implementation of the ATR-EMA Bands Strategy.
    
    This strategy uses a composite signal generator to combine signals from multiple indicators.
    """
    
    def __init__(self, config):
        """
        Initialize the ATR-EMA Bands Strategy.
        
        Args:
            config (dict): Configuration dictionary.
        """
        self.config = config
        self.name = "ATR-EMA Bands (Modular)"
        
        # Load strategy parameters from config
        self._load_parameters()
        
        # Create composite signal generator
        self.signal_generator = CompositeSignalGenerator(config, name=self.name)
        
        # Add indicators based on configuration
        self._setup_indicators()
        
        logger.info(f"Initialized {self.name} Strategy with {len(self.signal_generator.indicators)} indicators")
        
    def _load_parameters(self):
        """Load strategy parameters from config."""
        # Get strategy-specific parameters from config
        strategy_config = self.config.get('strategies', {}).get('atr_ema_bands', {})
        
        # Feature toggles for indicators
        self.use_ema = strategy_config.get('use_ema', True)
        self.use_atr = strategy_config.get('use_atr', True)
        self.use_rsi = strategy_config.get('use_rsi', False)
        self.use_macd = strategy_config.get('use_macd', False)
        
        # Signal mode - 'threshold' or 'crossover'
        self.signal_mode = strategy_config.get('signal_mode', 'crossover')
        
        # Signal thresholds
        self.buy_threshold = strategy_config.get('buy_threshold', 0.6)
        self.sell_threshold = strategy_config.get('sell_threshold', 0.4)
        
        # Higher timeframe alignment
        self.use_htf_alignment = strategy_config.get('use_htf_alignment', False)
        self.htf_alignment_weight = strategy_config.get('htf_alignment_weight', 0.15)
        
    def _setup_indicators(self):
        """Set up indicators based on configuration."""
        # Add EMA Bands indicator if enabled
        if self.use_ema and self.use_atr:
            self.signal_generator.add_indicator(EMABandsIndicator(self.config))
        
        # Add RSI indicator if enabled
        if self.use_rsi:
            self.signal_generator.add_indicator(RSIIndicator(self.config))
        
        # Add MACD indicator if enabled
        if self.use_macd:
            self.signal_generator.add_indicator(MACDIndicator(self.config))
    
    def update_config(self, config):
        """
        Update the strategy configuration.
        
        Args:
            config (dict): The new configuration.
        """
        logger.info(f"Updating {self.name} Strategy configuration")
        self.config = config
        self._load_parameters()
        
        # Recreate signal generator with new config
        self.signal_generator = CompositeSignalGenerator(config, name=self.name)
        self._setup_indicators()
        
        logger.info(f"Updated {self.name} Strategy with {len(self.signal_generator.indicators)} indicators")
    
    def generate_signal(self, df, htf_df=None):
        """
        Generate trading signals based on the composite signal generator.
        
        Args:
            df (pd.DataFrame): OHLCV DataFrame.
            htf_df (pd.DataFrame, optional): Higher timeframe OHLCV DataFrame.
            
        Returns:
            dict: Signal dictionary with score, direction, and additional data.
        """
        try:
            # Generate signal using composite signal generator
            signal = self.signal_generator.generate_signal(df)
            
            # Apply higher timeframe alignment if enabled and htf_df is provided
            if self.use_htf_alignment and htf_df is not None and len(htf_df) > 20:
                # Generate signal on higher timeframe
                htf_signal = self.generate_signal(htf_df)
                htf_direction = htf_signal['direction']
                
                # If current direction doesn't match higher timeframe direction, reduce signal strength
                if htf_direction != 'neutral':
                    if (signal['score'] > 0.5 and htf_direction == 'sell') or (signal['score'] < 0.5 and htf_direction == 'buy'):
                        # Adjust score towards neutral based on htf_alignment_weight
                        signal['score'] = 0.5 + (signal['score'] - 0.5) * (1 - self.htf_alignment_weight)
                        logger.debug(f"Higher timeframe mismatch: {htf_direction}, reducing signal strength")
                        
                        # Update direction based on adjusted score
                        if signal['score'] >= self.buy_threshold:
                            signal['direction'] = 'buy'
                        elif signal['score'] <= self.sell_threshold:
                            signal['direction'] = 'sell'
                        else:
                            signal['direction'] = 'neutral'
                
                # Add HTF information to signal
                signal['htf_direction'] = htf_direction
                signal['htf_score'] = htf_signal['score']
            
            # Add signal mode and thresholds to output
            signal['signal_config'] = {
                'mode': self.signal_mode,
                'buy_threshold': self.buy_threshold,
                'sell_threshold': self.sell_threshold
            }
            
            # Add enabled indicators to output
            signal['enabled_indicators'] = {
                'ema': self.use_ema,
                'atr': self.use_atr,
                'rsi': self.use_rsi,
                'macd': self.use_macd,
                'htf_alignment': self.use_htf_alignment
            }
            
            return signal
            
        except Exception as e:
            logger.error(f"Error generating ATR-EMA Bands signal: {e}", exc_info=True)
            return {'score': 0.5, 'direction': 'neutral'}
