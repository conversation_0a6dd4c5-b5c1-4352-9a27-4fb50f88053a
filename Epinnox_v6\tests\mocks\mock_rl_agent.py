"""
Mock RL Agent for testing purposes
"""

import random
import numpy as np

class MockRLAgent:
    """Mock reinforcement learning agent for testing"""
    
    def __init__(self):
        self.action_space = ['buy', 'sell', 'hold']
        self.learning_rate = 0.01
        self.epsilon = 0.1  # Exploration rate
        self.experience_buffer = []
        self.training_step = 0
    
    def get_action(self, state):
        """Get action based on current state"""
        # Simple mock logic based on state
        if isinstance(state, dict):
            if 'volatility' in state:
                volatility = state.get('volatility', 0.01)
                if volatility > 0.02:  # High volatility
                    return random.choice(['buy', 'sell'])  # More aggressive
                else:
                    return random.choice(['hold', 'buy'])  # More conservative
            
            if 'trend' in state:
                trend = state.get('trend', 0.0)
                if trend > 0.01:  # Upward trend
                    return 'buy'
                elif trend < -0.01:  # Downward trend
                    return 'sell'
                else:
                    return 'hold'
        
        # Default random action with some bias
        return random.choices(
            self.action_space, 
            weights=[0.4, 0.3, 0.3]  # Slightly favor buy
        )[0]
    
    def train(self, state, action, reward, next_state):
        """Mock training method"""
        self.training_step += 1
        
        # Store experience
        experience = {
            'state': state,
            'action': action,
            'reward': reward,
            'next_state': next_state,
            'step': self.training_step
        }
        
        self.experience_buffer.append(experience)
        
        # Keep buffer size manageable
        if len(self.experience_buffer) > 1000:
            self.experience_buffer = self.experience_buffer[-1000:]
        
        # Mock learning - slightly adjust epsilon
        if reward > 0:
            self.epsilon = max(0.05, self.epsilon * 0.999)  # Reduce exploration
        else:
            self.epsilon = min(0.2, self.epsilon * 1.001)   # Increase exploration
        
        return True
    
    def get_q_values(self, state):
        """Mock Q-values for actions"""
        # Return random Q-values with some logic
        base_values = [0.5, 0.3, 0.2]  # buy, sell, hold
        
        if isinstance(state, dict) and 'trend' in state:
            trend = state.get('trend', 0.0)
            if trend > 0:
                base_values[0] += 0.2  # Increase buy Q-value
            elif trend < 0:
                base_values[1] += 0.2  # Increase sell Q-value
        
        # Add some noise
        noise = np.random.normal(0, 0.1, 3)
        q_values = np.array(base_values) + noise
        
        return dict(zip(self.action_space, q_values))
    
    def save_model(self, filepath):
        """Mock model saving"""
        model_data = {
            'training_step': self.training_step,
            'epsilon': self.epsilon,
            'experience_count': len(self.experience_buffer)
        }
        
        import json
        with open(filepath, 'w') as f:
            json.dump(model_data, f)
        
        return True
    
    def load_model(self, filepath):
        """Mock model loading"""
        try:
            import json
            with open(filepath, 'r') as f:
                model_data = json.load(f)
            
            self.training_step = model_data.get('training_step', 0)
            self.epsilon = model_data.get('epsilon', 0.1)
            
            return True
        except:
            return False
    
    def get_model_info(self):
        """Get information about the model"""
        return {
            'type': 'MockRLAgent',
            'training_steps': self.training_step,
            'epsilon': self.epsilon,
            'experience_buffer_size': len(self.experience_buffer),
            'action_space': self.action_space
        }
