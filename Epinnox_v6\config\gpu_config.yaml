# GPU Configuration for Epinnox Trading System

# GPU settings
gpu:
  # Enable GPU acceleration
  enabled: false
  
  # Memory settings
  memory:
    # Maximum percentage of GPU memory to use (0-100)
    max_memory_percent: 90
    # Reserve memory for other applications (in MB)
    reserve_memory: 1024
  
  # Performance settings
  performance:
    # Use mixed precision (float16/bfloat16) for better performance
    use_mixed_precision: true
    # Use tensor cores for faster computation (if available)
    use_tensor_cores: true
    # Use cudnn benchmark for faster convolutions (if applicable)
    use_cudnn_benchmark: true
  
  # Model cache settings
  model_cache:
    # Maximum number of models to keep in cache
    max_size: 3
    # Time to live in seconds (1 hour)
    ttl: 3600
  
  # Monitoring settings
  monitoring:
    # Enable GPU monitoring
    enabled: false
    # Monitoring interval in seconds
    interval: 5
    # Log GPU usage to file
    log_to_file: true
    # Log file path
    log_file: "logs/gpu_usage.log"
