"""
Advanced Sentiment Analysis Module for Financial Markets
This module provides sophisticated sentiment analysis capabilities for trading decisions.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import re
import asyncio
import aiohttp
from textblob import TextBlob
import nltk

# Optional transformers import
try:
    from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

# Download required NLTK data
try:
    nltk.download('punkt', quiet=True)
    nltk.download('punkt_tab', quiet=True)
    nltk.download('vader_lexicon', quiet=True)
    nltk.download('stopwords', quiet=True)
except:
    pass

from nltk.sentiment import SentimentIntensityAnalyzer
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize

logger = logging.getLogger(__name__)

class SentimentLabel(Enum):
    """Sentiment classification labels"""
    VERY_BULLISH = "very_bullish"
    BULLISH = "bullish"
    NEUTRAL = "neutral"
    BEARISH = "bearish"
    VERY_BEARISH = "very_bearish"

@dataclass
class SentimentScore:
    """Sentiment analysis result"""
    label: SentimentLabel
    confidence: float
    compound_score: float
    positive: float
    negative: float
    neutral: float
    reasoning: str
    source: str
    timestamp: datetime
    text_length: int
    keywords: List[str]

class FinancialLexicon:
    """Financial-specific sentiment lexicon"""
    
    def __init__(self):
        self.bullish_terms = {
            'pump': 2.0, 'moon': 2.0, 'rocket': 1.8, 'bullish': 1.5, 'buy': 1.0,
            'long': 1.0, 'hold': 0.8, 'support': 0.7, 'breakout': 1.5, 'rally': 1.3,
            'surge': 1.4, 'spike': 1.2, 'gain': 1.0, 'profit': 1.1, 'upturn': 1.2,
            'bull': 1.3, 'upward': 0.9, 'positive': 0.8, 'strong': 0.9, 'green': 0.7,
            'accumulate': 1.1, 'oversold': 1.0, 'bounce': 1.2, 'recovery': 1.1,
            'momentum': 0.8, 'trend': 0.6, 'opportunity': 0.9, 'target': 0.7
        }
        
        self.bearish_terms = {
            'dump': -2.0, 'crash': -2.0, 'drop': -1.5, 'bearish': -1.5, 'sell': -1.0,
            'short': -1.0, 'resistance': -0.7, 'breakdown': -1.5, 'decline': -1.3,
            'fall': -1.2, 'plummet': -1.8, 'tank': -1.6, 'loss': -1.1, 'red': -0.7,
            'bear': -1.3, 'downward': -0.9, 'negative': -0.8, 'weak': -0.9,
            'distribute': -1.1, 'overbought': -1.0, 'correction': -1.2, 'pullback': -1.0,
            'risk': -0.8, 'concern': -0.9, 'warning': -1.0, 'caution': -0.8
        }
        
        self.intensity_modifiers = {
            'very': 1.3, 'extremely': 1.5, 'highly': 1.2, 'strongly': 1.2,
            'massive': 1.4, 'huge': 1.3, 'significant': 1.1, 'major': 1.2,
            'minor': 0.7, 'slight': 0.6, 'small': 0.7, 'limited': 0.6
        }
    
    def get_sentiment_score(self, text: str) -> float:
        """Calculate sentiment score using financial lexicon"""
        text_lower = text.lower()
        words = word_tokenize(text_lower)
        
        total_score = 0.0
        word_count = 0
        
        for i, word in enumerate(words):
            base_score = 0.0
            
            # Check bullish terms
            if word in self.bullish_terms:
                base_score = self.bullish_terms[word]
            # Check bearish terms
            elif word in self.bearish_terms:
                base_score = self.bearish_terms[word]
            
            if base_score != 0.0:
                # Apply intensity modifiers
                modifier = 1.0
                if i > 0 and words[i-1] in self.intensity_modifiers:
                    modifier = self.intensity_modifiers[words[i-1]]
                
                total_score += base_score * modifier
                word_count += 1
        
        # Normalize score
        if word_count > 0:
            return total_score / word_count
        return 0.0

class SentimentAnalyzer:
    """Advanced sentiment analysis with multiple models and techniques"""
    
    def __init__(self, use_transformer: bool = True, model_name: str = "nlptown/bert-base-multilingual-uncased-sentiment"):
        """
        Initialize sentiment analyzer
        
        Args:
            use_transformer: Whether to use transformer-based models
            model_name: HuggingFace model name for sentiment analysis
        """
        self.use_transformer = use_transformer
        self.model_name = model_name
        
        # Initialize VADER sentiment analyzer
        self.vader_analyzer = SentimentIntensityAnalyzer()
        
        # Initialize financial lexicon
        self.financial_lexicon = FinancialLexicon()        # Initialize transformer model if requested and available
        self.transformer_pipeline = None
        if use_transformer and TRANSFORMERS_AVAILABLE:
            try:
                self.transformer_pipeline = pipeline(
                    "sentiment-analysis",
                    model=model_name,
                    tokenizer=model_name,
                    device=0 if torch.cuda.is_available() else -1
                )
                logger.info(f"Loaded transformer sentiment model: {model_name}")
            except Exception as e:
                logger.warning(f"Could not load transformer model: {e}. Using fallback methods.")
                self.use_transformer = False
        elif use_transformer and not TRANSFORMERS_AVAILABLE:
            logger.warning("Transformers library not available. Using fallback methods only.")
            self.use_transformer = False
        
        # Crypto/finance specific keywords
        self.crypto_keywords = [
            'bitcoin', 'btc', 'ethereum', 'eth', 'doge', 'dogecoin', 'crypto', 'cryptocurrency',
            'blockchain', 'defi', 'nft', 'trading', 'pump', 'dump', 'hodl', 'whale',
            'altcoin', 'bull', 'bear', 'moon', 'rocket', 'diamond', 'hands', 'paper'
        ]
        
        # Compile regex patterns for preprocessing
        self.url_pattern = re.compile(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+')
        self.mention_pattern = re.compile(r'@\w+')
        self.hashtag_pattern = re.compile(r'#\w+')
        self.emoji_pattern = re.compile(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]')
    
    def preprocess_text(self, text: str) -> Tuple[str, List[str]]:
        """
        Preprocess text for sentiment analysis
        
        Args:
            text: Raw text to preprocess
            
        Returns:
            Tuple of (cleaned_text, extracted_keywords)
        """
        # Store original for keyword extraction
        original_text = text.lower()
        
        # Remove URLs
        text = self.url_pattern.sub('', text)
        
        # Extract hashtags and mentions before removing
        hashtags = self.hashtag_pattern.findall(text)
        mentions = self.mention_pattern.findall(text)
        
        # Remove mentions and hashtags for sentiment analysis
        text = self.mention_pattern.sub('', text)
        text = self.hashtag_pattern.sub('', text)
        
        # Clean up extra whitespace
        text = ' '.join(text.split())
        
        # Extract crypto-related keywords
        keywords = []
        for keyword in self.crypto_keywords:
            if keyword in original_text:
                keywords.append(keyword)
        
        # Add hashtags as keywords (without #)
        keywords.extend([tag[1:] for tag in hashtags])
        
        return text, keywords
    
    def analyze_with_vader(self, text: str) -> Dict[str, float]:
        """Analyze sentiment using VADER"""
        scores = self.vader_analyzer.polarity_scores(text)
        return scores
    
    def analyze_with_textblob(self, text: str) -> Dict[str, float]:
        """Analyze sentiment using TextBlob"""
        blob = TextBlob(text)
        return {
            'polarity': blob.sentiment.polarity,
            'subjectivity': blob.sentiment.subjectivity
        }
    
    def analyze_with_transformer(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment using transformer model"""
        if not self.transformer_pipeline:
            return {'error': 'Transformer model not available'}
        
        try:
            # Truncate text if too long
            max_length = 512
            if len(text) > max_length:
                text = text[:max_length]
            
            result = self.transformer_pipeline(text)
            return {
                'label': result[0]['label'],
                'score': result[0]['score']
            }
        except Exception as e:
            logger.error(f"Transformer sentiment analysis error: {e}")
            return {'error': str(e)}
    
    def calculate_compound_sentiment(self, vader_scores: Dict[str, float], 
                                   textblob_scores: Dict[str, float],
                                   financial_score: float,
                                   transformer_result: Dict[str, Any]) -> float:
        """
        Calculate compound sentiment score using multiple methods
        
        Args:
            vader_scores: VADER sentiment scores
            textblob_scores: TextBlob sentiment scores
            financial_score: Financial lexicon score
            transformer_result: Transformer model result
            
        Returns:
            Compound sentiment score (-1 to 1)
        """
        scores = []
        weights = []
        
        # VADER compound score
        scores.append(vader_scores['compound'])
        weights.append(0.3)
        
        # TextBlob polarity
        scores.append(textblob_scores['polarity'])
        weights.append(0.2)
        
        # Financial lexicon score (normalized)
        normalized_financial = np.tanh(financial_score)  # Normalize to [-1, 1]
        scores.append(normalized_financial)
        weights.append(0.3)
        
        # Transformer score if available
        if 'score' in transformer_result and 'error' not in transformer_result:
            # Convert to [-1, 1] based on label
            label = transformer_result['label'].lower()
            transformer_score = transformer_result['score']
            
            if 'positive' in label or 'pos' in label:
                transformer_normalized = (transformer_score - 0.5) * 2
            elif 'negative' in label or 'neg' in label:
                transformer_normalized = (0.5 - transformer_score) * 2
            else:
                transformer_normalized = 0.0
            
            scores.append(transformer_normalized)
            weights.append(0.2)
        
        # Calculate weighted average
        weights = np.array(weights)
        weights = weights / weights.sum()  # Normalize weights
        
        compound_score = np.average(scores, weights=weights)
        return float(compound_score)
    
    def classify_sentiment(self, compound_score: float, confidence: float) -> SentimentLabel:
        """
        Classify sentiment based on compound score and confidence
        
        Args:
            compound_score: Compound sentiment score (-1 to 1)
            confidence: Confidence level (0 to 1)
            
        Returns:
            SentimentLabel
        """
        # Adjust thresholds based on confidence
        high_conf_threshold = 0.6 if confidence > 0.8 else 0.7
        low_conf_threshold = 0.2 if confidence > 0.8 else 0.3
        
        if compound_score >= high_conf_threshold:
            return SentimentLabel.VERY_BULLISH
        elif compound_score >= low_conf_threshold:
            return SentimentLabel.BULLISH
        elif compound_score <= -high_conf_threshold:
            return SentimentLabel.VERY_BEARISH
        elif compound_score <= -low_conf_threshold:
            return SentimentLabel.BEARISH
        else:
            return SentimentLabel.NEUTRAL
    
    def analyze_sentiment(self, text: str, source: str = "unknown") -> SentimentScore:
        """
        Perform comprehensive sentiment analysis
        
        Args:
            text: Text to analyze
            source: Source of the text (e.g., 'twitter', 'news', 'reddit')
            
        Returns:
            SentimentScore object with detailed analysis
        """
        start_time = datetime.now()
        
        # Preprocess text
        cleaned_text, keywords = self.preprocess_text(text)
        
        if not cleaned_text.strip():
            return SentimentScore(
                label=SentimentLabel.NEUTRAL,
                confidence=0.0,
                compound_score=0.0,
                positive=0.0,
                negative=0.0,
                neutral=1.0,
                reasoning="Empty or invalid text",
                source=source,
                timestamp=start_time,
                text_length=len(text),
                keywords=keywords
            )
        
        # Analyze with multiple methods
        vader_scores = self.analyze_with_vader(cleaned_text)
        textblob_scores = self.analyze_with_textblob(cleaned_text)
        financial_score = self.financial_lexicon.get_sentiment_score(cleaned_text)
        transformer_result = self.analyze_with_transformer(cleaned_text) if self.use_transformer else {}
        
        # Calculate compound sentiment
        compound_score = self.calculate_compound_sentiment(
            vader_scores, textblob_scores, financial_score, transformer_result
        )
        
        # Calculate confidence based on agreement between methods
        confidence = self._calculate_confidence(vader_scores, textblob_scores, financial_score, transformer_result)
        
        # Classify sentiment
        sentiment_label = self.classify_sentiment(compound_score, confidence)
        
        # Generate reasoning
        reasoning = self._generate_reasoning(
            sentiment_label, compound_score, confidence, keywords,
            vader_scores, textblob_scores, financial_score
        )
        
        return SentimentScore(
            label=sentiment_label,
            confidence=confidence,
            compound_score=compound_score,
            positive=vader_scores['pos'],
            negative=vader_scores['neg'],
            neutral=vader_scores['neu'],
            reasoning=reasoning,
            source=source,
            timestamp=start_time,
            text_length=len(text),
            keywords=keywords
        )
    
    def _calculate_confidence(self, vader_scores: Dict[str, float], 
                            textblob_scores: Dict[str, float],
                            financial_score: float,
                            transformer_result: Dict[str, Any]) -> float:
        """Calculate confidence based on method agreement"""
        
        # Get sentiment directions
        directions = []
        
        # VADER direction
        if vader_scores['compound'] > 0.1:
            directions.append(1)
        elif vader_scores['compound'] < -0.1:
            directions.append(-1)
        else:
            directions.append(0)
        
        # TextBlob direction
        if textblob_scores['polarity'] > 0.1:
            directions.append(1)
        elif textblob_scores['polarity'] < -0.1:
            directions.append(-1)
        else:
            directions.append(0)
        
        # Financial lexicon direction
        if financial_score > 0.1:
            directions.append(1)
        elif financial_score < -0.1:
            directions.append(-1)
        else:
            directions.append(0)
        
        # Transformer direction if available
        if 'score' in transformer_result and 'error' not in transformer_result:
            label = transformer_result['label'].lower()
            if 'positive' in label or 'pos' in label:
                directions.append(1)
            elif 'negative' in label or 'neg' in label:
                directions.append(-1)
            else:
                directions.append(0)
        
        # Calculate agreement
        if len(directions) < 2:
            return 0.5
        
        # Count agreements
        positive_count = directions.count(1)
        negative_count = directions.count(-1)
        neutral_count = directions.count(0)
        
        total_methods = len(directions)
        max_agreement = max(positive_count, negative_count, neutral_count)
        
        # Base confidence on agreement ratio
        base_confidence = max_agreement / total_methods
        
        # Boost confidence for strong signals
        if max_agreement == positive_count or max_agreement == negative_count:
            if max_agreement >= 3:  # At least 3 methods agree
                base_confidence = min(0.95, base_confidence + 0.2)
        
        return base_confidence
    
    def _generate_reasoning(self, sentiment_label: SentimentLabel, compound_score: float,
                          confidence: float, keywords: List[str],
                          vader_scores: Dict[str, float], textblob_scores: Dict[str, float],
                          financial_score: float) -> str:
        """Generate human-readable reasoning for sentiment analysis"""
        
        reasoning_parts = []
        
        # Main sentiment
        reasoning_parts.append(f"Sentiment: {sentiment_label.value.replace('_', ' ').title()}")
        reasoning_parts.append(f"Confidence: {confidence:.1%}")
        reasoning_parts.append(f"Compound Score: {compound_score:.3f}")
        
        # Method contributions
        method_details = []
        method_details.append(f"VADER: {vader_scores['compound']:.3f}")
        method_details.append(f"TextBlob: {textblob_scores['polarity']:.3f}")
        if financial_score != 0:
            method_details.append(f"Financial: {financial_score:.3f}")
        
        reasoning_parts.append(f"Methods: {' | '.join(method_details)}")
        
        # Keywords
        if keywords:
            reasoning_parts.append(f"Key terms: {', '.join(keywords[:5])}")
        
        return " | ".join(reasoning_parts)
    
    def analyze_batch(self, texts: List[str], sources: List[str] = None) -> List[SentimentScore]:
        """
        Analyze sentiment for multiple texts
        
        Args:
            texts: List of texts to analyze
            sources: List of sources (optional)
            
        Returns:
            List of SentimentScore objects
        """
        if sources is None:
            sources = ["unknown"] * len(texts)
        
        results = []
        for text, source in zip(texts, sources):
            result = self.analyze_sentiment(text, source)
            results.append(result)
        
        return results
    
    def get_market_sentiment_score(self, sentiment_scores: List[SentimentScore]) -> Dict[str, Any]:
        """
        Aggregate individual sentiment scores into market sentiment
        
        Args:
            sentiment_scores: List of sentiment scores
            
        Returns:
            Dictionary with aggregated market sentiment metrics
        """
        if not sentiment_scores:
            return {
                'overall_sentiment': SentimentLabel.NEUTRAL,
                'confidence': 0.0,
                'compound_score': 0.0,
                'bullish_ratio': 0.0,
                'bearish_ratio': 0.0,
                'neutral_ratio': 0.0,
                'total_samples': 0
            }
        
        # Calculate ratios
        total_samples = len(sentiment_scores)
        bullish_count = sum(1 for score in sentiment_scores 
                          if score.label in [SentimentLabel.BULLISH, SentimentLabel.VERY_BULLISH])
        bearish_count = sum(1 for score in sentiment_scores 
                          if score.label in [SentimentLabel.BEARISH, SentimentLabel.VERY_BEARISH])
        neutral_count = sum(1 for score in sentiment_scores 
                          if score.label == SentimentLabel.NEUTRAL)
        
        bullish_ratio = bullish_count / total_samples
        bearish_ratio = bearish_count / total_samples
        neutral_ratio = neutral_count / total_samples
        
        # Calculate weighted compound score
        weights = [score.confidence for score in sentiment_scores]
        compound_scores = [score.compound_score for score in sentiment_scores]
        
        if sum(weights) > 0:
            weighted_compound = np.average(compound_scores, weights=weights)
        else:
            weighted_compound = np.mean(compound_scores)
        
        # Calculate overall confidence
        overall_confidence = np.mean([score.confidence for score in sentiment_scores])
        
        # Determine overall sentiment
        if bullish_ratio > 0.6:
            overall_sentiment = SentimentLabel.BULLISH
        elif bearish_ratio > 0.6:
            overall_sentiment = SentimentLabel.BEARISH
        elif weighted_compound > 0.3:
            overall_sentiment = SentimentLabel.BULLISH
        elif weighted_compound < -0.3:
            overall_sentiment = SentimentLabel.BEARISH
        else:
            overall_sentiment = SentimentLabel.NEUTRAL
        
        return {
            'overall_sentiment': overall_sentiment,
            'confidence': overall_confidence,
            'compound_score': weighted_compound,
            'bullish_ratio': bullish_ratio,
            'bearish_ratio': bearish_ratio,
            'neutral_ratio': neutral_ratio,
            'total_samples': total_samples,
            'sentiment_distribution': {
                'very_bullish': sum(1 for s in sentiment_scores if s.label == SentimentLabel.VERY_BULLISH),
                'bullish': sum(1 for s in sentiment_scores if s.label == SentimentLabel.BULLISH),
                'neutral': neutral_count,
                'bearish': sum(1 for s in sentiment_scores if s.label == SentimentLabel.BEARISH),
                'very_bearish': sum(1 for s in sentiment_scores if s.label == SentimentLabel.VERY_BEARISH)
            }
        }

# Example usage and testing
if __name__ == "__main__":
    # Test the sentiment analyzer
    analyzer = SentimentAnalyzer(use_transformer=False)  # Set to True if you have transformers
    
    test_texts = [
        "DOGE is going to the moon! 🚀🚀🚀 #DogeArmy #ToTheMoon",
        "Bitcoin is crashing hard. Time to sell everything!",
        "Steady growth in ETH. Looking good for long term hodlers.",
        "Market is uncertain. Not sure which way this will go.",
        "Massive pump incoming! Get ready for 10x gains! 💎🙌"
    ]
    
    print("Sentiment Analysis Results:")
    print("=" * 60)
    
    for text in test_texts:
        result = analyzer.analyze_sentiment(text, "test")
        print(f"\nText: {text}")
        print(f"Sentiment: {result.label.value}")
        print(f"Confidence: {result.confidence:.2%}")
        print(f"Compound Score: {result.compound_score:.3f}")
        print(f"Keywords: {result.keywords}")
        print(f"Reasoning: {result.reasoning}")
        print("-" * 40)
