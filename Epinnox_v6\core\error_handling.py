"""
Centralized Error Handling for Epinnox v6
Provides consistent error handling patterns across all modules
"""

import logging
import traceback
import functools
from typing import Any, Callable, Optional, Dict, Union
from datetime import datetime
import time

# Configure logger
logger = logging.getLogger(__name__)

class EpinnoxError(Exception):
    """Base exception class for Epinnox trading system"""
    pass

class TradingError(EpinnoxError):
    """Exception for trading-related errors"""
    pass

class DataError(EpinnoxError):
    """Exception for data-related errors"""
    pass

class APIError(EpinnoxError):
    """Exception for API-related errors"""
    pass

class ValidationError(EpinnoxError):
    """Exception for validation errors"""
    pass

def safe_execute(func: Callable, *args, default_return=None, error_message: str = None, 
                 log_level: str = "error", **kwargs) -> Any:
    """
    Safely execute a function with standardized error handling
    
    Args:
        func: Function to execute
        *args: Function arguments
        default_return: Value to return on error
        error_message: Custom error message
        log_level: Logging level for errors
        **kwargs: Function keyword arguments
    
    Returns:
        Function result or default_return on error
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_msg = error_message or f"Error executing {func.__name__}: {str(e)}"
        
        if log_level == "error":
            logger.error(error_msg)
        elif log_level == "warning":
            logger.warning(error_msg)
        elif log_level == "info":
            logger.info(error_msg)
        
        logger.debug(f"Full traceback: {traceback.format_exc()}")
        return default_return

def retry_on_failure(max_retries: int = 3, delay: float = 1.0, 
                    backoff_factor: float = 2.0, exceptions: tuple = (Exception,)):
    """
    Decorator to retry function execution on failure
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries (seconds)
        backoff_factor: Multiplier for delay on each retry
        exceptions: Tuple of exceptions to catch and retry on
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_delay = delay
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {str(e)}. "
                                     f"Retrying in {current_delay:.1f}s...")
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
                        break
            
            # Re-raise the last exception if all retries failed
            raise last_exception
        
        return wrapper
    return decorator

def validate_input(validation_func: Callable, error_message: str = None):
    """
    Decorator to validate function inputs
    
    Args:
        validation_func: Function that takes the same arguments and returns True if valid
        error_message: Custom error message for validation failure
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                if not validation_func(*args, **kwargs):
                    msg = error_message or f"Input validation failed for {func.__name__}"
                    raise ValidationError(msg)
                return func(*args, **kwargs)
            except ValidationError:
                raise
            except Exception as e:
                logger.error(f"Validation error in {func.__name__}: {str(e)}")
                raise ValidationError(f"Validation failed: {str(e)}")
        
        return wrapper
    return decorator

def log_execution_time(func: Callable) -> Callable:
    """
    Decorator to log function execution time
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"{func.__name__} executed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func.__name__} failed after {execution_time:.3f}s: {str(e)}")
            raise
    
    return wrapper

class ErrorContext:
    """Context manager for standardized error handling"""
    
    def __init__(self, operation_name: str, default_return=None, 
                 log_level: str = "error", raise_on_error: bool = False):
        self.operation_name = operation_name
        self.default_return = default_return
        self.log_level = log_level
        self.raise_on_error = raise_on_error
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        logger.debug(f"Starting operation: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        execution_time = time.time() - self.start_time
        
        if exc_type is None:
            logger.debug(f"Operation '{self.operation_name}' completed successfully in {execution_time:.3f}s")
            return False
        
        error_msg = f"Operation '{self.operation_name}' failed after {execution_time:.3f}s: {str(exc_val)}"
        
        if self.log_level == "error":
            logger.error(error_msg)
        elif self.log_level == "warning":
            logger.warning(error_msg)
        elif self.log_level == "info":
            logger.info(error_msg)
        
        logger.debug(f"Full traceback: {traceback.format_exc()}")
        
        if self.raise_on_error:
            return False  # Re-raise the exception
        
        return True  # Suppress the exception

def validate_trading_inputs(symbol: str, side: str, amount: float, price: float = None) -> bool:
    """
    Validate trading inputs
    
    Args:
        symbol: Trading symbol
        side: Order side ('buy' or 'sell')
        amount: Order amount
        price: Order price (optional for market orders)
    
    Returns:
        bool: True if inputs are valid
    """
    if not symbol or not isinstance(symbol, str):
        logger.error("Invalid symbol: must be a non-empty string")
        return False
    
    if side not in ['buy', 'sell', 'long', 'short']:
        logger.error(f"Invalid side: {side}. Must be 'buy', 'sell', 'long', or 'short'")
        return False
    
    if not isinstance(amount, (int, float)) or amount <= 0:
        logger.error(f"Invalid amount: {amount}. Must be a positive number")
        return False
    
    if price is not None and (not isinstance(price, (int, float)) or price <= 0):
        logger.error(f"Invalid price: {price}. Must be a positive number")
        return False
    
    return True

def validate_api_response(response: Dict, required_fields: list = None) -> bool:
    """
    Validate API response structure
    
    Args:
        response: API response dictionary
        required_fields: List of required fields
    
    Returns:
        bool: True if response is valid
    """
    if not isinstance(response, dict):
        logger.error("API response must be a dictionary")
        return False
    
    if required_fields:
        missing_fields = [field for field in required_fields if field not in response]
        if missing_fields:
            logger.error(f"API response missing required fields: {missing_fields}")
            return False
    
    return True

# Convenience functions for common error handling patterns
def handle_trading_error(func: Callable) -> Callable:
    """Decorator for trading-related functions"""
    return retry_on_failure(max_retries=2, delay=0.5, exceptions=(APIError, TradingError))(func)

def handle_data_error(func: Callable) -> Callable:
    """Decorator for data-related functions"""
    return retry_on_failure(max_retries=3, delay=1.0, exceptions=(DataError, APIError))(func)

def handle_api_error(func: Callable) -> Callable:
    """Decorator for API-related functions"""
    return retry_on_failure(max_retries=3, delay=2.0, backoff_factor=1.5, exceptions=(APIError,))(func)
