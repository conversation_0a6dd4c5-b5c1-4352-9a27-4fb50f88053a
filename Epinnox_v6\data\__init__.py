"""
Data Package for Trading System
Handles live data feeds, WebSocket connections, and data management
"""
from .exchange import ExchangeDataFetcher

# Import new live data components
try:
    from .live_data_manager import LiveDataManager
    from .websocket_client import WebSocketClient
    __all__ = ['ExchangeDataFetcher', 'LiveDataManager', 'WebSocketClient']
except ImportError:
    # Fallback if new components not yet created
    __all__ = ['ExchangeDataFetcher']
