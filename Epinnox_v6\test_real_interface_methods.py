#!/usr/bin/env python3
"""
Real Interface Methods Test
Test that all required methods actually exist in the real interface
"""

import sys
import os
import inspect

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_interface_methods():
    """Test that all required methods exist in the real interface"""
    print("🧪 TESTING REAL INTERFACE METHODS")
    print("=" * 50)
    
    try:
        # Import the real interface
        from launch_epinnox import EpinnoxTradingInterface
        
        # Create a list of required methods from the test suite
        required_methods = [
            # Trading methods
            'place_limit_buy',
            'place_market_buy', 
            'place_limit_sell',
            'place_market_sell',
            'place_limit_close',
            'place_market_close',
            'close_all_positions',
            'cancel_all_orders',
            
            # Data refresh methods
            'refresh_positions',
            'refresh_orders', 
            'refresh_balance',
            'refresh_market_data',
            'refresh_portfolio_status',
            'update_balance_display',
            
            # Orchestrator methods
            'toggle_orchestrator',
            'emergency_stop_orchestrator',
            'run_orchestrator_cycle',
            
            # Utility methods
            'log_message',
        ]
        
        print(f"📋 Checking {len(required_methods)} required methods...")
        print()
        
        missing_methods = []
        existing_methods = []
        
        # Check each required method
        for method_name in required_methods:
            if hasattr(EpinnoxTradingInterface, method_name):
                method = getattr(EpinnoxTradingInterface, method_name)
                if callable(method):
                    existing_methods.append(method_name)
                    print(f"✅ {method_name}: EXISTS and CALLABLE")
                    
                    # Get method signature for additional info
                    try:
                        sig = inspect.signature(method)
                        print(f"   📝 Signature: {method_name}{sig}")
                    except Exception:
                        print(f"   📝 Signature: Could not inspect")
                else:
                    missing_methods.append(method_name)
                    print(f"❌ {method_name}: EXISTS but NOT CALLABLE")
            else:
                missing_methods.append(method_name)
                print(f"❌ {method_name}: MISSING")
        
        print()
        print("📊 RESULTS SUMMARY")
        print("=" * 30)
        print(f"Total methods checked: {len(required_methods)}")
        print(f"✅ Existing methods: {len(existing_methods)}")
        print(f"❌ Missing methods: {len(missing_methods)}")
        print(f"📈 Success rate: {len(existing_methods)/len(required_methods)*100:.1f}%")
        
        if missing_methods:
            print()
            print("❌ MISSING METHODS:")
            for method in missing_methods:
                print(f"   • {method}")
        
        print()
        if len(existing_methods) == len(required_methods):
            print("🎉 ALL REQUIRED METHODS EXIST - INTERFACE IS COMPLETE!")
            return True
        else:
            print(f"⚠️ {len(missing_methods)} METHODS STILL MISSING")
            return False
            
    except ImportError as e:
        print(f"❌ Could not import interface: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

if __name__ == "__main__":
    success = test_interface_methods()
    sys.exit(0 if success else 1)
