"""
Multi-Symbol Multi-Agent Trading Simulation
Simulate multiple trading agents/strategies in parallel with portfolio allocation
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import json

logger = logging.getLogger(__name__)

class AgentType(Enum):
    """Types of trading agents"""
    MOMENTUM = "momentum"
    MEAN_REVERSION = "mean_reversion"
    BREAKOUT = "breakout"
    RL_AGENT = "rl_agent"
    ENSEMBLE = "ensemble"

@dataclass
class AgentConfig:
    """Configuration for a trading agent"""
    agent_id: str
    agent_type: AgentType
    symbols: List[str]
    allocation: float  # Percentage of total portfolio
    parameters: Dict
    min_confidence: float = 0.65
    max_leverage: float = 3.0

@dataclass
class SimulationResult:
    """Results from multi-agent simulation"""
    agent_id: str
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    final_balance: float
    trade_history: List[Dict]

class TradingAgent:
    """Base trading agent class"""
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.balance = 0.0
        self.positions = {}
        self.trade_history = []
        self.performance_metrics = {}
    
    def set_allocation(self, total_portfolio_value: float):
        """Set agent's allocation from total portfolio"""
        self.balance = total_portfolio_value * self.config.allocation
    
    async def generate_signal(self, market_data: Dict[str, pd.DataFrame], 
                            timestamp: pd.Timestamp) -> Dict:
        """Generate trading signal - to be implemented by subclasses"""
        raise NotImplementedError
    
    def calculate_position_size(self, symbol: str, confidence: float, 
                              current_price: float) -> float:
        """Calculate position size based on confidence and risk management"""
        max_position_value = self.balance * 0.1  # Max 10% per position
        confidence_factor = confidence / 100.0
        position_value = max_position_value * confidence_factor
        return position_value / current_price
    
    def update_performance(self):
        """Update performance metrics"""
        if not self.trade_history:
            return
        
        trades_df = pd.DataFrame(self.trade_history)
        
        # Calculate metrics
        total_pnl = trades_df['pnl'].sum()
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        total_trades = len(trades_df)
        
        self.performance_metrics = {
            'total_return': total_pnl / (self.balance if self.balance > 0 else 1),
            'win_rate': winning_trades / total_trades if total_trades > 0 else 0,
            'total_trades': total_trades,
            'avg_trade_pnl': trades_df['pnl'].mean() if total_trades > 0 else 0
        }

class MomentumAgent(TradingAgent):
    """Momentum-based trading agent"""
    
    async def generate_signal(self, market_data: Dict[str, pd.DataFrame], 
                            timestamp: pd.Timestamp) -> Dict:
        signals = {}
        
        for symbol in self.config.symbols:
            if symbol not in market_data:
                continue
            
            df = market_data[symbol]
            if timestamp not in df.index or len(df.loc[:timestamp]) < 20:
                continue
            
            # Get recent data
            recent_data = df.loc[:timestamp].tail(20)
            
            # Calculate momentum
            price_change = (recent_data['close'].iloc[-1] - recent_data['close'].iloc[0]) / recent_data['close'].iloc[0]
            
            # Generate signal
            if price_change > 0.02:  # 2% upward momentum
                signals[symbol] = {
                    'decision': 'LONG',
                    'confidence': min(95, 60 + abs(price_change) * 1000),
                    'current_price': recent_data['close'].iloc[-1]
                }
            elif price_change < -0.02:  # 2% downward momentum
                signals[symbol] = {
                    'decision': 'SHORT',
                    'confidence': min(95, 60 + abs(price_change) * 1000),
                    'current_price': recent_data['close'].iloc[-1]
                }
        
        return signals

class MeanReversionAgent(TradingAgent):
    """Mean reversion trading agent"""
    
    async def generate_signal(self, market_data: Dict[str, pd.DataFrame], 
                            timestamp: pd.Timestamp) -> Dict:
        signals = {}
        
        for symbol in self.config.symbols:
            if symbol not in market_data:
                continue
            
            df = market_data[symbol]
            if timestamp not in df.index or len(df.loc[:timestamp]) < 50:
                continue
            
            # Get recent data
            recent_data = df.loc[:timestamp].tail(50)
            
            # Calculate mean reversion signals
            sma_20 = recent_data['close'].rolling(20).mean().iloc[-1]
            current_price = recent_data['close'].iloc[-1]
            std_20 = recent_data['close'].rolling(20).std().iloc[-1]
            
            # Z-score
            z_score = (current_price - sma_20) / std_20 if std_20 > 0 else 0
            
            # Generate signals
            if z_score > 2:  # Overbought
                signals[symbol] = {
                    'decision': 'SHORT',
                    'confidence': min(95, 60 + abs(z_score) * 10),
                    'current_price': current_price
                }
            elif z_score < -2:  # Oversold
                signals[symbol] = {
                    'decision': 'LONG',
                    'confidence': min(95, 60 + abs(z_score) * 10),
                    'current_price': current_price
                }
        
        return signals

class BreakoutAgent(TradingAgent):
    """Breakout trading agent"""
    
    async def generate_signal(self, market_data: Dict[str, pd.DataFrame], 
                            timestamp: pd.Timestamp) -> Dict:
        signals = {}
        
        for symbol in self.config.symbols:
            if symbol not in market_data:
                continue
            
            df = market_data[symbol]
            if timestamp not in df.index or len(df.loc[:timestamp]) < 30:
                continue
            
            # Get recent data
            recent_data = df.loc[:timestamp].tail(30)
            
            # Calculate breakout levels
            high_20 = recent_data['high'].rolling(20).max().iloc[-1]
            low_20 = recent_data['low'].rolling(20).min().iloc[-1]
            current_price = recent_data['close'].iloc[-1]
            
            # Generate breakout signals
            if current_price > high_20 * 1.001:  # Breakout above resistance
                signals[symbol] = {
                    'decision': 'LONG',
                    'confidence': 75,
                    'current_price': current_price
                }
            elif current_price < low_20 * 0.999:  # Breakdown below support
                signals[symbol] = {
                    'decision': 'SHORT',
                    'confidence': 75,
                    'current_price': current_price
                }
        
        return signals

class MultiAgentSimulator:
    """
    Multi-agent trading simulation system
    """
    
    def __init__(self, initial_balance: float = 100000.0):
        self.initial_balance = initial_balance
        self.agents: List[TradingAgent] = []
        self.simulation_results = []
        self.portfolio_history = []
        
    def add_agent(self, config: AgentConfig) -> TradingAgent:
        """Add a trading agent to the simulation"""
        
        # Create agent based on type
        if config.agent_type == AgentType.MOMENTUM:
            agent = MomentumAgent(config)
        elif config.agent_type == AgentType.MEAN_REVERSION:
            agent = MeanReversionAgent(config)
        elif config.agent_type == AgentType.BREAKOUT:
            agent = BreakoutAgent(config)
        else:
            agent = TradingAgent(config)  # Base agent
        
        # Set allocation
        agent.set_allocation(self.initial_balance)
        
        self.agents.append(agent)
        logger.info(f"Added {config.agent_type.value} agent '{config.agent_id}' with {config.allocation:.1%} allocation")
        
        return agent
    
    def create_default_agents(self) -> List[TradingAgent]:
        """Create a default set of diverse agents"""
        
        # BTC-focused momentum agent
        btc_momentum = AgentConfig(
            agent_id="btc_momentum",
            agent_type=AgentType.MOMENTUM,
            symbols=["BTC/USDT"],
            allocation=0.3,
            parameters={"lookback": 20, "threshold": 0.02}
        )
        
        # ETH mean reversion agent
        eth_mean_reversion = AgentConfig(
            agent_id="eth_mean_reversion",
            agent_type=AgentType.MEAN_REVERSION,
            symbols=["ETH/USDT"],
            allocation=0.25,
            parameters={"lookback": 50, "z_threshold": 2.0}
        )
        
        # Multi-symbol breakout agent
        breakout_agent = AgentConfig(
            agent_id="multi_breakout",
            agent_type=AgentType.BREAKOUT,
            symbols=["BTC/USDT", "ETH/USDT", "ADA/USDT"],
            allocation=0.25,
            parameters={"lookback": 20}
        )
        
        # Conservative momentum agent
        conservative_momentum = AgentConfig(
            agent_id="conservative_momentum",
            agent_type=AgentType.MOMENTUM,
            symbols=["BTC/USDT", "ETH/USDT"],
            allocation=0.2,
            parameters={"lookback": 30, "threshold": 0.03},
            min_confidence=0.75,
            max_leverage=2.0
        )
        
        agents = []
        for config in [btc_momentum, eth_mean_reversion, breakout_agent, conservative_momentum]:
            agents.append(self.add_agent(config))
        
        return agents
    
    def load_market_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """Load market data for all symbols"""
        market_data = {}
        
        for symbol in symbols:
            # Generate synthetic data for simulation
            dates = pd.date_range(start=start_date, end=end_date, freq='1H')
            
            # Generate realistic price data
            np.random.seed(hash(symbol) % 2**32)  # Consistent seed per symbol
            base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 0.5
            
            returns = np.random.normal(0, 0.02, len(dates))  # 2% hourly volatility
            prices = [base_price]
            
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            # Create OHLCV data
            data = []
            for i, (date, price) in enumerate(zip(dates, prices)):
                high = price * (1 + abs(np.random.normal(0, 0.01)))
                low = price * (1 - abs(np.random.normal(0, 0.01)))
                open_price = prices[i-1] if i > 0 else price
                close_price = price
                volume = np.random.uniform(100, 1000)
                
                data.append({
                    'timestamp': date,
                    'open': open_price,
                    'high': high,
                    'low': low,
                    'close': close_price,
                    'volume': volume
                })
            
            df = pd.DataFrame(data)
            df.set_index('timestamp', inplace=True)
            market_data[symbol] = df
        
        return market_data
    
    async def run_simulation(self, start_date: str, end_date: str, 
                           symbols: List[str] = None) -> List[SimulationResult]:
        """Run the multi-agent simulation"""
        
        if not self.agents:
            self.create_default_agents()
        
        if symbols is None:
            symbols = list(set([symbol for agent in self.agents for symbol in agent.config.symbols]))
        
        logger.info(f"Starting multi-agent simulation with {len(self.agents)} agents")
        logger.info(f"Symbols: {symbols}")
        logger.info(f"Date range: {start_date} to {end_date}")
        
        # Load market data
        market_data = self.load_market_data(symbols, start_date, end_date)
        
        # Get common timestamps
        all_timestamps = set()
        for df in market_data.values():
            all_timestamps.update(df.index)
        
        timestamps = sorted(list(all_timestamps))
        
        # Run simulation
        for i, timestamp in enumerate(timestamps):
            if i % 100 == 0:
                logger.info(f"Processing timestamp {i}/{len(timestamps)}: {timestamp}")
            
            # Get signals from all agents
            agent_signals = {}
            for agent in self.agents:
                try:
                    signals = await agent.generate_signal(market_data, timestamp)
                    agent_signals[agent.config.agent_id] = signals
                except Exception as e:
                    logger.error(f"Error getting signals from agent {agent.config.agent_id}: {e}")
                    agent_signals[agent.config.agent_id] = {}
            
            # Execute trades for each agent
            for agent in self.agents:
                signals = agent_signals.get(agent.config.agent_id, {})
                
                for symbol, signal in signals.items():
                    if signal['confidence'] >= agent.config.min_confidence * 100:
                        # Calculate position size
                        position_size = agent.calculate_position_size(
                            symbol, signal['confidence'], signal['current_price']
                        )
                        
                        # Simulate trade execution
                        trade = {
                            'timestamp': timestamp,
                            'symbol': symbol,
                            'decision': signal['decision'],
                            'confidence': signal['confidence'],
                            'price': signal['current_price'],
                            'position_size': position_size,
                            'pnl': np.random.normal(0, 10)  # Simplified P&L simulation
                        }
                        
                        agent.trade_history.append(trade)
            
            # Update portfolio history
            portfolio_value = sum(agent.balance for agent in self.agents)
            self.portfolio_history.append({
                'timestamp': timestamp,
                'total_value': portfolio_value,
                'agent_values': {agent.config.agent_id: agent.balance for agent in self.agents}
            })
        
        # Calculate final results
        results = []
        for agent in self.agents:
            agent.update_performance()
            
            result = SimulationResult(
                agent_id=agent.config.agent_id,
                total_return=agent.performance_metrics.get('total_return', 0),
                sharpe_ratio=0.0,  # Simplified
                max_drawdown=0.0,  # Simplified
                win_rate=agent.performance_metrics.get('win_rate', 0),
                total_trades=agent.performance_metrics.get('total_trades', 0),
                final_balance=agent.balance,
                trade_history=agent.trade_history
            )
            
            results.append(result)
        
        self.simulation_results = results
        
        logger.info(f"Simulation completed. Results for {len(results)} agents:")
        for result in results:
            logger.info(f"  {result.agent_id}: {result.total_trades} trades, "
                       f"{result.win_rate:.1%} win rate, "
                       f"{result.total_return:.2%} return")
        
        return results
    
    def analyze_results(self) -> Dict:
        """Analyze simulation results"""
        
        if not self.simulation_results:
            return {}
        
        # Overall portfolio performance
        total_initial = self.initial_balance
        total_final = sum(result.final_balance for result in self.simulation_results)
        total_return = (total_final - total_initial) / total_initial
        
        # Agent performance comparison
        agent_performance = []
        for result in self.simulation_results:
            agent_performance.append({
                'agent_id': result.agent_id,
                'total_return': result.total_return,
                'win_rate': result.win_rate,
                'total_trades': result.total_trades,
                'final_balance': result.final_balance
            })
        
        # Sort by performance
        agent_performance.sort(key=lambda x: x['total_return'], reverse=True)
        
        analysis = {
            'portfolio_summary': {
                'initial_balance': total_initial,
                'final_balance': total_final,
                'total_return': total_return,
                'num_agents': len(self.simulation_results)
            },
            'agent_performance': agent_performance,
            'best_agent': agent_performance[0] if agent_performance else None,
            'worst_agent': agent_performance[-1] if agent_performance else None
        }
        
        return analysis
    
    def save_results(self, filename: str = None):
        """Save simulation results to file"""
        
        if filename is None:
            filename = f"multi_agent_simulation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # Prepare data for JSON serialization
        results_data = {
            'simulation_info': {
                'timestamp': datetime.now().isoformat(),
                'initial_balance': self.initial_balance,
                'num_agents': len(self.agents),
                'agent_configs': [
                    {
                        'agent_id': agent.config.agent_id,
                        'agent_type': agent.config.agent_type.value,
                        'symbols': agent.config.symbols,
                        'allocation': agent.config.allocation,
                        'parameters': agent.config.parameters
                    }
                    for agent in self.agents
                ]
            },
            'results': [asdict(result) for result in self.simulation_results],
            'analysis': self.analyze_results()
        }
        
        # Convert timestamps to strings
        for result in results_data['results']:
            for trade in result['trade_history']:
                if 'timestamp' in trade:
                    trade['timestamp'] = trade['timestamp'].isoformat()
        
        with open(filename, 'w') as f:
            json.dump(results_data, f, indent=2, default=str)
        
        logger.info(f"Multi-agent simulation results saved to {filename}")
        
        return filename
