"""
GPU Utilities for EPINNOX v6
GPU detection, device management, and optimization utilities
"""

import torch
import logging
from typing import Tuple, Optional

logger = logging.getLogger(__name__)

def check_gpu_availability() -> Tuple[bool, bool, str]:
    """
    Check if GPU is available for computation

    Returns:
        Tuple of (torch_available, cuda_available, device_info)
    """
    try:
        torch_available = True
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
            return torch_available, True, f"CUDA GPU: {device_name} ({device_count} device(s))"
        else:
            return torch_available, False, "CUDA not available"
    except Exception as e:
        logger.warning(f"Error checking GPU availability: {e}")
        return False, False, f"GPU check failed: {e}"

def get_device() -> torch.device:
    """
    Get the optimal device for computation
    
    Returns:
        torch.device object (cuda or cpu)
    """
    if torch.cuda.is_available():
        return torch.device("cuda")
    else:
        return torch.device("cpu")

def get_optimal_dtype() -> torch.dtype:
    """
    Get optimal data type based on available hardware
    
    Returns:
        torch.dtype (float16 for GPU, float32 for CPU)
    """
    if torch.cuda.is_available():
        return torch.float16  # Use half precision on GPU for speed
    else:
        return torch.float32  # Use full precision on CPU

def optimize_for_gpu(model: torch.nn.Module) -> torch.nn.Module:
    """
    Optimize model for GPU if available
    
    Args:
        model: PyTorch model to optimize
        
    Returns:
        Optimized model
    """
    device = get_device()
    
    try:
        # Move model to device
        model = model.to(device)
        
        # Enable GPU optimizations if available
        if device.type == "cuda":
            # Enable mixed precision if supported
            try:
                model = torch.jit.script(model)
                logger.info("Model optimized with TorchScript")
            except Exception as e:
                logger.warning(f"TorchScript optimization failed: {e}")
        
        logger.info(f"Model optimized for device: {device}")
        return model
        
    except Exception as e:
        logger.error(f"GPU optimization failed: {e}")
        return model

def get_memory_info() -> dict:
    """
    Get GPU memory information
    
    Returns:
        Dictionary with memory statistics
    """
    if not torch.cuda.is_available():
        return {"available": False, "message": "CUDA not available"}
    
    try:
        device = torch.cuda.current_device()
        total_memory = torch.cuda.get_device_properties(device).total_memory
        allocated_memory = torch.cuda.memory_allocated(device)
        cached_memory = torch.cuda.memory_reserved(device)
        
        return {
            "available": True,
            "device": device,
            "total_memory_gb": total_memory / (1024**3),
            "allocated_memory_gb": allocated_memory / (1024**3),
            "cached_memory_gb": cached_memory / (1024**3),
            "free_memory_gb": (total_memory - allocated_memory) / (1024**3)
        }
    except Exception as e:
        return {"available": False, "error": str(e)}

def clear_gpu_cache():
    """Clear GPU cache to free memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        logger.info("GPU cache cleared")

def set_gpu_memory_fraction(fraction: float = 0.8):
    """
    Set GPU memory fraction to use
    
    Args:
        fraction: Fraction of GPU memory to use (0.0 to 1.0)
    """
    if torch.cuda.is_available():
        try:
            torch.cuda.set_per_process_memory_fraction(fraction)
            logger.info(f"GPU memory fraction set to {fraction}")
        except Exception as e:
            logger.warning(f"Failed to set GPU memory fraction: {e}")

# Initialize GPU settings on import
if __name__ != "__main__":
    torch_available, cuda_available, gpu_info = check_gpu_availability()
    if cuda_available:
        logger.info(f"GPU acceleration available: {gpu_info}")
        # Set conservative memory usage
        set_gpu_memory_fraction(0.8)
    else:
        logger.info(f"GPU not available: {gpu_info}")
