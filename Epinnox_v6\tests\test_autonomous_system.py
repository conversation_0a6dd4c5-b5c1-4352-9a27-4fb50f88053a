"""
Comprehensive test suite for the autonomous trading system
"""

import pytest
import asyncio
import unittest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Import the components to test
from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator, TradingMode, SystemState
from core.enhanced_llm_analyzer import <PERSON>hancedLLMAnalyzer, MarketAnalysis
from core.safety_monitor import SafetyMonitor, SafetyRule, AlertLevel
from execution.simulation_executor import SimulationExecutor

class TestAutonomousTradingOrchestrator(unittest.TestCase):
    """Test the main autonomous trading orchestrator"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = {
            'trading_mode': 'simulation',
            'symbols': ['BTC/USDT:USDT', 'ETH/USDT:USDT'],
            'max_daily_loss': -500.0,
            'max_position_size': 1000.0,
            'max_leverage': 10.0,
            'max_open_positions': 5,
            'initial_balance': 10000.0
        }
        self.orchestrator = AutonomousTradingOrchestrator(self.config)
    
    def test_initialization(self):
        """Test orchestrator initialization"""
        self.assertEqual(self.orchestrator.mode, TradingMode.SIMULATION)
        self.assertEqual(self.orchestrator.state, SystemState.INITIALIZING)
        self.assertFalse(self.orchestrator.trading_enabled)
        self.assertFalse(self.orchestrator.emergency_stop_triggered)
        self.assertEqual(len(self.orchestrator.active_symbols), 2)
    
    @pytest.mark.asyncio
    async def test_initialization_process(self):
        """Test the initialization process"""
        with patch.multiple(
            self.orchestrator,
            _initialize_data_manager=AsyncMock(),
            _initialize_ai_components=AsyncMock(),
            _initialize_risk_management=AsyncMock(),
            _initialize_execution_engine=AsyncMock(),
            _initialize_portfolio_management=AsyncMock(),
            _perform_health_check=AsyncMock(return_value={'healthy': True, 'issues': []})
        ):
            result = await self.orchestrator.initialize()
            self.assertTrue(result)
            self.assertEqual(self.orchestrator.state, SystemState.RUNNING)
    
    @pytest.mark.asyncio
    async def test_initialization_failure(self):
        """Test initialization failure handling"""
        with patch.multiple(
            self.orchestrator,
            _initialize_data_manager=AsyncMock(side_effect=Exception("Test error")),
            _initialize_ai_components=AsyncMock(),
            _initialize_risk_management=AsyncMock(),
            _initialize_execution_engine=AsyncMock(),
            _initialize_portfolio_management=AsyncMock()
        ):
            result = await self.orchestrator.initialize()
            self.assertFalse(result)
            self.assertEqual(self.orchestrator.state, SystemState.EMERGENCY_STOP)
    
    @pytest.mark.asyncio
    async def test_emergency_stop(self):
        """Test emergency stop procedure"""
        # Mock portfolio and execution managers
        self.orchestrator.portfolio_manager = AsyncMock()
        self.orchestrator.execution_engine = AsyncMock()
        
        await self.orchestrator._trigger_emergency_stop("Test emergency")
        
        self.assertTrue(self.orchestrator.emergency_stop_triggered)
        self.assertEqual(self.orchestrator.state, SystemState.EMERGENCY_STOP)
        self.orchestrator.portfolio_manager.close_all_positions.assert_called_once()
        self.orchestrator.execution_engine.cancel_all_orders.assert_called_once()
    
    def test_trading_decision_creation(self):
        """Test trading decision data structure"""
        from core.autonomous_trading_orchestrator import TradingDecision
        
        decision = TradingDecision(
            symbol='BTC/USDT:USDT',
            action='BUY',
            confidence=0.8,
            position_size=500.0,
            leverage=2.0,
            reasoning="Strong bullish signal"
        )
        
        self.assertEqual(decision.symbol, 'BTC/USDT:USDT')
        self.assertEqual(decision.action, 'BUY')
        self.assertEqual(decision.confidence, 0.8)
        self.assertIsInstance(decision.timestamp, datetime)

class TestEnhancedLLMAnalyzer(unittest.TestCase):
    """Test the enhanced LLM analyzer"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = {
            'llm_config': {
                'model': 'test-model',
                'temperature': 0.3
            }
        }
        self.analyzer = EnhancedLLMAnalyzer(self.config)
    
    def test_initialization(self):
        """Test analyzer initialization"""
        self.assertIsNotNone(self.analyzer.config)
        self.assertEqual(self.analyzer.cache_duration, 300)
        self.assertEqual(len(self.analyzer.analysis_cache), 0)
    
    def test_market_context_preparation(self):
        """Test market context preparation"""
        market_data = {
            'ohlcv': {
                '1m': [{'open': 50000, 'high': 50100, 'low': 49900, 'close': 50050, 'volume': 1000}],
                '5m': [{'open': 49800, 'high': 50200, 'low': 49700, 'close': 50050, 'volume': 5000}]
            },
            'orderbook': {
                'bids': [[50000, 1.0], [49990, 2.0]],
                'asks': [[50060, 1.0], [50070, 2.0]]
            }
        }
        
        context = self.analyzer._prepare_market_context('BTC/USDT:USDT', market_data)
        
        self.assertEqual(context['symbol'], 'BTC/USDT:USDT')
        self.assertIn('market_data', context)
        self.assertIn('1m_price', context['market_data'])
        self.assertIn('orderbook', context['market_data'])
        self.assertIn('technical_indicators', context)
    
    def test_basic_indicators_calculation(self):
        """Test basic technical indicators calculation"""
        market_data = {
            'ohlcv': {
                '1m': [
                    {'close': 50000, 'volume': 1000},
                    {'close': 50100, 'volume': 1100},
                    {'close': 50050, 'volume': 900},
                    {'close': 50200, 'volume': 1200},
                    {'close': 50150, 'volume': 1050}
                ] * 5  # 25 data points
            }
        }
        
        indicators = self.analyzer._calculate_basic_indicators(market_data)
        
        self.assertIn('sma_20', indicators)
        self.assertIn('volume_ratio', indicators)
        self.assertIn('momentum_5m', indicators)
    
    def test_fallback_analysis(self):
        """Test fallback analysis when LLM is unavailable"""
        analysis = self.analyzer._fallback_analysis('BTC/USDT:USDT', {})
        
        self.assertEqual(analysis.symbol, 'BTC/USDT:USDT')
        self.assertEqual(analysis.decision, 'HOLD')
        self.assertEqual(analysis.confidence, 0.0)
        self.assertIn('LLM unavailable', analysis.reasoning)
    
    @pytest.mark.asyncio
    async def test_market_analysis_with_mock_llm(self):
        """Test market analysis with mocked LLM"""
        market_data = {
            'ohlcv': {'1m': [{'close': 50000, 'volume': 1000}]},
            'orderbook': {'bids': [[50000, 1.0]], 'asks': [[50060, 1.0]]}
        }
        
        # Mock LLM response
        mock_response = '''
        {
            "decision": "BUY",
            "confidence": 0.75,
            "reasoning": "Strong technical indicators",
            "risk_assessment": "Moderate risk",
            "position_size_recommendation": 0.5
        }
        '''
        
        with patch.object(self.analyzer, '_call_llm', return_value=mock_response):
            result = await self.analyzer.analyze_market('BTC/USDT:USDT', market_data)
            
            self.assertTrue(result['success'])
            self.assertEqual(result['decision'], 'BUY')
            self.assertEqual(result['confidence'], 0.75)

class TestSafetyMonitor(unittest.TestCase):
    """Test the safety monitoring system"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = {
            'max_daily_loss': -500.0,
            'max_position_size': 1000.0,
            'max_leverage': 10.0,
            'max_open_positions': 5,
            'min_account_balance': 100.0,
            'max_drawdown': -20.0
        }
        self.monitor = SafetyMonitor(self.config)
    
    def test_initialization(self):
        """Test safety monitor initialization"""
        self.assertTrue(self.monitor.monitoring_enabled)
        self.assertEqual(len(self.monitor.safety_rules), 9)  # All safety rules
        self.assertEqual(len(self.monitor.alerts), 0)
    
    def test_safety_rules_initialization(self):
        """Test safety rules are properly initialized"""
        rules = self.monitor.safety_rules
        
        self.assertIn(SafetyRule.MAX_DAILY_LOSS, rules)
        self.assertEqual(rules[SafetyRule.MAX_DAILY_LOSS]['threshold'], -500.0)
        self.assertTrue(rules[SafetyRule.MAX_DAILY_LOSS]['enabled'])
        
        self.assertIn(SafetyRule.MIN_ACCOUNT_BALANCE, rules)
        self.assertEqual(rules[SafetyRule.MIN_ACCOUNT_BALANCE]['threshold'], 100.0)
    
    @pytest.mark.asyncio
    async def test_daily_loss_alert(self):
        """Test daily loss limit alert"""
        from core.safety_monitor import SystemMetrics
        
        # Create metrics that violate daily loss limit
        metrics = SystemMetrics(
            cpu_usage=50.0,
            memory_usage=60.0,
            disk_usage=70.0,
            network_latency=10.0,
            data_freshness=30.0,
            exchange_connectivity=True,
            active_positions=2,
            daily_pnl=-600.0,  # Exceeds -500 limit
            account_balance=5000.0,
            max_drawdown=-5.0,
            timestamp=datetime.now()
        )
        
        rule_config = self.monitor.safety_rules[SafetyRule.MAX_DAILY_LOSS]
        alert = await self.monitor._check_safety_rule(SafetyRule.MAX_DAILY_LOSS, metrics, rule_config)
        
        self.assertIsNotNone(alert)
        self.assertEqual(alert.rule, SafetyRule.MAX_DAILY_LOSS)
        self.assertEqual(alert.level, AlertLevel.CRITICAL)
        self.assertEqual(alert.value, -600.0)
    
    @pytest.mark.asyncio
    async def test_account_balance_alert(self):
        """Test minimum account balance alert"""
        from core.safety_monitor import SystemMetrics
        
        metrics = SystemMetrics(
            cpu_usage=50.0,
            memory_usage=60.0,
            disk_usage=70.0,
            network_latency=10.0,
            data_freshness=30.0,
            exchange_connectivity=True,
            active_positions=2,
            daily_pnl=0.0,
            account_balance=50.0,  # Below 100 minimum
            max_drawdown=-5.0,
            timestamp=datetime.now()
        )
        
        rule_config = self.monitor.safety_rules[SafetyRule.MIN_ACCOUNT_BALANCE]
        alert = await self.monitor._check_safety_rule(SafetyRule.MIN_ACCOUNT_BALANCE, metrics, rule_config)
        
        self.assertIsNotNone(alert)
        self.assertEqual(alert.rule, SafetyRule.MIN_ACCOUNT_BALANCE)
        self.assertEqual(alert.value, 50.0)
    
    def test_emergency_callback_registration(self):
        """Test emergency callback registration"""
        callback_called = False
        
        def test_callback(alert):
            nonlocal callback_called
            callback_called = True
        
        self.monitor.add_emergency_callback(test_callback)
        self.assertEqual(len(self.monitor.emergency_callbacks), 1)
    
    def test_system_status(self):
        """Test system status reporting"""
        status = self.monitor.get_system_status()
        
        self.assertIn('monitoring_enabled', status)
        self.assertIn('active_alerts_count', status)
        self.assertIn('critical_alerts_count', status)
        self.assertIn('safety_rules_enabled', status)
        self.assertTrue(status['monitoring_enabled'])
        self.assertEqual(status['active_alerts_count'], 0)

class TestSimulationExecutor(unittest.TestCase):
    """Test the simulation executor"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.executor = SimulationExecutor(initial_balance=10000.0)
    
    def test_initialization(self):
        """Test executor initialization"""
        self.assertEqual(self.executor.initial_balance, 10000.0)
        self.assertEqual(self.executor.balance, 10000.0)
        self.assertEqual(len(self.executor.positions), 0)
        self.assertEqual(len(self.executor.orders), 0)
    
    @pytest.mark.asyncio
    async def test_buy_order_execution(self):
        """Test buy order execution"""
        from core.autonomous_trading_orchestrator import TradingDecision
        
        decision = TradingDecision(
            symbol='BTC/USDT:USDT',
            action='BUY',
            confidence=0.8,
            position_size=1000.0,
            leverage=2.0
        )
        
        result = await self.executor.execute_decision(decision)
        
        self.assertTrue(result['success'])
        self.assertIn('order_id', result)
        self.assertIn('execution_price', result)
        self.assertLess(self.executor.balance, 10000.0)  # Balance should decrease due to margin
    
    @pytest.mark.asyncio
    async def test_insufficient_balance(self):
        """Test handling of insufficient balance"""
        from core.autonomous_trading_orchestrator import TradingDecision
        
        decision = TradingDecision(
            symbol='BTC/USDT:USDT',
            action='BUY',
            confidence=0.8,
            position_size=50000.0,  # Very large position
            leverage=1.0
        )
        
        result = await self.executor.execute_decision(decision)
        
        self.assertFalse(result['success'])
        self.assertIn('Insufficient balance', result['error'])
    
    @pytest.mark.asyncio
    async def test_position_tracking(self):
        """Test position tracking"""
        from core.autonomous_trading_orchestrator import TradingDecision
        
        # Open a position
        decision = TradingDecision(
            symbol='BTC/USDT:USDT',
            action='BUY',
            confidence=0.8,
            position_size=1000.0,
            leverage=2.0
        )
        
        result = await self.executor.execute_decision(decision)
        self.assertTrue(result['success'])
        
        # Check position was created
        positions = await self.executor.get_positions()
        self.assertIn('BTC/USDT:USDT', positions)
        self.assertGreater(positions['BTC/USDT:USDT']['size'], 0)
    
    def test_performance_summary(self):
        """Test performance summary generation"""
        summary = self.executor.get_performance_summary()
        
        self.assertIn('initial_balance', summary)
        self.assertIn('current_balance', summary)
        self.assertIn('total_pnl', summary)
        self.assertIn('total_pnl_pct', summary)
        self.assertEqual(summary['initial_balance'], 10000.0)
        self.assertEqual(summary['current_balance'], 10000.0)
        self.assertEqual(summary['total_pnl'], 0.0)

if __name__ == '__main__':
    # Run the tests
    unittest.main()
