"""
Enhanced LLM Analyzer
Advanced LLM integration for comprehensive market analysis and trading decisions
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class MarketAnalysis:
    symbol: str
    decision: str  # BUY, SELL, HOLD
    confidence: float  # 0.0 to 1.0
    reasoning: str
    risk_assessment: str
    entry_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    position_size_recommendation: float = 0.0
    timeframe: str = "1h"
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class EnhancedLLMAnalyzer:
    """
    Enhanced LLM analyzer that provides comprehensive market analysis
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.llm_runner = None
        self.analysis_cache = {}
        self.cache_duration = 300  # 5 minutes
        
        # Initialize LLM runner
        self._initialize_llm()
        
        logger.info("Enhanced LLM Analyzer initialized")
    
    def _initialize_llm(self):
        """Initialize the LLM runner"""
        try:
            from llama.runner import LlamaRunner
            self.llm_runner = LlamaRunner()
            logger.info("✅ LLM runner initialized")
        except Exception as e:
            logger.error(f"Failed to initialize LLM runner: {e}")
            self.llm_runner = None
    
    async def analyze_market(self, symbol: str, market_data: Dict) -> Dict[str, Any]:
        """
        Perform comprehensive market analysis using LLM
        """
        try:
            # Check cache first
            cache_key = f"{symbol}_{int(datetime.now().timestamp() // self.cache_duration)}"
            if cache_key in self.analysis_cache:
                return self.analysis_cache[cache_key]
            
            # Prepare market context
            context = self._prepare_market_context(symbol, market_data)
            
            # Generate analysis prompt
            prompt = self._build_analysis_prompt(symbol, context)
            
            # Get LLM analysis
            if self.llm_runner:
                llm_response = await self._call_llm(prompt)
                analysis = self._parse_llm_response(symbol, llm_response)
            else:
                # Fallback analysis
                analysis = self._fallback_analysis(symbol, context)
            
            # Cache the result
            result = {
                'success': True,
                'analysis': analysis,
                'decision': analysis.decision,
                'confidence': analysis.confidence,
                'reasoning': analysis.reasoning,
                'timestamp': datetime.now()
            }
            
            self.analysis_cache[cache_key] = result
            return result
            
        except Exception as e:
            logger.error(f"Error in LLM market analysis for {symbol}: {e}")
            return {
                'success': False,
                'error': str(e),
                'decision': 'HOLD',
                'confidence': 0.0
            }
    
    def _prepare_market_context(self, symbol: str, market_data: Dict) -> Dict[str, Any]:
        """Prepare comprehensive market context for LLM analysis"""
        context = {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'market_data': {}
        }
        
        try:
            # Extract OHLCV data
            if 'ohlcv' in market_data:
                ohlcv = market_data['ohlcv']
                
                # Get latest prices for different timeframes
                for timeframe in ['1m', '5m', '15m']:
                    if timeframe in ohlcv and ohlcv[timeframe]:
                        latest = ohlcv[timeframe][-1] if isinstance(ohlcv[timeframe], list) else ohlcv[timeframe]
                        context['market_data'][f'{timeframe}_price'] = {
                            'open': latest.get('open', 0),
                            'high': latest.get('high', 0),
                            'low': latest.get('low', 0),
                            'close': latest.get('close', 0),
                            'volume': latest.get('volume', 0)
                        }
            
            # Extract order book data
            if 'orderbook' in market_data and market_data['orderbook']:
                orderbook = market_data['orderbook']
                context['market_data']['orderbook'] = {
                    'best_bid': orderbook.get('bids', [[0]])[0][0] if orderbook.get('bids') else 0,
                    'best_ask': orderbook.get('asks', [[0]])[0][0] if orderbook.get('asks') else 0,
                    'bid_depth': sum([bid[1] for bid in orderbook.get('bids', [])[:5]]),
                    'ask_depth': sum([ask[1] for ask in orderbook.get('asks', [])[:5]])
                }
            
            # Calculate technical indicators
            context['technical_indicators'] = self._calculate_basic_indicators(market_data)
            
            # Market sentiment indicators
            context['market_sentiment'] = self._assess_market_sentiment(market_data)
            
        except Exception as e:
            logger.error(f"Error preparing market context: {e}")
        
        return context
    
    def _build_analysis_prompt(self, symbol: str, context: Dict) -> str:
        """Build comprehensive analysis prompt for LLM"""
        
        prompt = f"""
You are an expert cryptocurrency trader analyzing {symbol}. Provide a comprehensive trading analysis based on the following market data:

MARKET DATA:
{json.dumps(context['market_data'], indent=2)}

TECHNICAL INDICATORS:
{json.dumps(context.get('technical_indicators', {}), indent=2)}

MARKET SENTIMENT:
{json.dumps(context.get('market_sentiment', {}), indent=2)}

Please provide your analysis in the following JSON format:
{{
    "decision": "BUY|SELL|HOLD",
    "confidence": 0.0-1.0,
    "reasoning": "Detailed explanation of your decision",
    "risk_assessment": "Assessment of risks involved",
    "entry_price": null or price_value,
    "stop_loss": null or price_value,
    "take_profit": null or price_value,
    "position_size_recommendation": 0.0-1.0,
    "key_factors": ["factor1", "factor2", "factor3"]
}}

Consider the following in your analysis:
1. Price action and momentum
2. Volume patterns
3. Order book depth and spread
4. Technical indicator signals
5. Risk-reward ratio
6. Market volatility
7. Support and resistance levels

Be conservative with your confidence levels and always consider risk management.
"""
        
        return prompt
    
    async def _call_llm(self, prompt: str) -> str:
        """Call the LLM with the analysis prompt"""
        try:
            if hasattr(self.llm_runner, 'run_inference_async'):
                response = await self.llm_runner.run_inference_async(prompt, temperature=0.3)
            else:
                # Fallback to sync call
                response = self.llm_runner.run_inference(prompt, temperature=0.3)
            
            return response
            
        except Exception as e:
            logger.error(f"Error calling LLM: {e}")
            return ""
    
    def _parse_llm_response(self, symbol: str, response: str) -> MarketAnalysis:
        """Parse LLM response into structured analysis"""
        try:
            # Try to extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                analysis_data = json.loads(json_str)
                
                return MarketAnalysis(
                    symbol=symbol,
                    decision=analysis_data.get('decision', 'HOLD'),
                    confidence=float(analysis_data.get('confidence', 0.0)),
                    reasoning=analysis_data.get('reasoning', 'No reasoning provided'),
                    risk_assessment=analysis_data.get('risk_assessment', 'Unknown risk'),
                    entry_price=analysis_data.get('entry_price'),
                    stop_loss=analysis_data.get('stop_loss'),
                    take_profit=analysis_data.get('take_profit'),
                    position_size_recommendation=float(analysis_data.get('position_size_recommendation', 0.0))
                )
            else:
                # Fallback parsing
                return self._fallback_parse(symbol, response)
                
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return self._fallback_analysis(symbol, {})
    
    def _fallback_parse(self, symbol: str, response: str) -> MarketAnalysis:
        """Fallback parsing when JSON extraction fails"""
        decision = 'HOLD'
        confidence = 0.0
        
        response_lower = response.lower()
        
        if 'buy' in response_lower or 'long' in response_lower:
            decision = 'BUY'
            confidence = 0.5
        elif 'sell' in response_lower or 'short' in response_lower:
            decision = 'SELL'
            confidence = 0.5
        
        return MarketAnalysis(
            symbol=symbol,
            decision=decision,
            confidence=confidence,
            reasoning=response[:500],  # First 500 chars
            risk_assessment="Unable to parse detailed risk assessment"
        )
    
    def _fallback_analysis(self, symbol: str, context: Dict) -> MarketAnalysis:
        """Provide fallback analysis when LLM is unavailable"""
        return MarketAnalysis(
            symbol=symbol,
            decision='HOLD',
            confidence=0.0,
            reasoning="LLM unavailable - defaulting to HOLD",
            risk_assessment="Cannot assess risk without LLM analysis"
        )
    
    def _calculate_basic_indicators(self, market_data: Dict) -> Dict[str, Any]:
        """Calculate basic technical indicators"""
        indicators = {}
        
        try:
            # Extract price data
            if 'ohlcv' in market_data and '1m' in market_data['ohlcv']:
                prices = market_data['ohlcv']['1m']
                if isinstance(prices, list) and len(prices) > 0:
                    latest_price = prices[-1].get('close', 0)
                    
                    # Simple moving averages
                    if len(prices) >= 20:
                        sma_20 = sum([p.get('close', 0) for p in prices[-20:]]) / 20
                        indicators['sma_20'] = sma_20
                        indicators['price_vs_sma20'] = (latest_price - sma_20) / sma_20 if sma_20 > 0 else 0
                    
                    # Volume analysis
                    if len(prices) >= 10:
                        avg_volume = sum([p.get('volume', 0) for p in prices[-10:]]) / 10
                        latest_volume = prices[-1].get('volume', 0)
                        indicators['volume_ratio'] = latest_volume / avg_volume if avg_volume > 0 else 1
                    
                    # Price momentum
                    if len(prices) >= 5:
                        price_5_ago = prices[-5].get('close', latest_price)
                        indicators['momentum_5m'] = (latest_price - price_5_ago) / price_5_ago if price_5_ago > 0 else 0
        
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
        
        return indicators
    
    def _assess_market_sentiment(self, market_data: Dict) -> Dict[str, Any]:
        """Assess market sentiment from available data"""
        sentiment = {
            'overall': 'neutral',
            'volatility': 'normal',
            'liquidity': 'normal'
        }
        
        try:
            # Assess volatility from price movements
            if 'ohlcv' in market_data and '1m' in market_data['ohlcv']:
                prices = market_data['ohlcv']['1m']
                if isinstance(prices, list) and len(prices) >= 10:
                    price_changes = []
                    for i in range(1, min(len(prices), 11)):
                        prev_close = prices[-i-1].get('close', 0)
                        curr_close = prices[-i].get('close', 0)
                        if prev_close > 0:
                            change = abs(curr_close - prev_close) / prev_close
                            price_changes.append(change)
                    
                    if price_changes:
                        avg_volatility = sum(price_changes) / len(price_changes)
                        if avg_volatility > 0.02:  # 2%
                            sentiment['volatility'] = 'high'
                        elif avg_volatility < 0.005:  # 0.5%
                            sentiment['volatility'] = 'low'
            
            # Assess liquidity from order book
            if 'orderbook' in market_data and market_data['orderbook']:
                orderbook = market_data['orderbook']
                bids = orderbook.get('bids', [])
                asks = orderbook.get('asks', [])
                
                if bids and asks:
                    spread = asks[0][0] - bids[0][0]
                    mid_price = (asks[0][0] + bids[0][0]) / 2
                    spread_pct = spread / mid_price if mid_price > 0 else 0
                    
                    if spread_pct > 0.01:  # 1%
                        sentiment['liquidity'] = 'poor'
                    elif spread_pct < 0.001:  # 0.1%
                        sentiment['liquidity'] = 'excellent'
        
        except Exception as e:
            logger.error(f"Error assessing sentiment: {e}")
        
        return sentiment
