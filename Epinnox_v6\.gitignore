# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyQt
*.ui

# Logs
*.log
logs/*.log

# Cache
cache/*.json

# ===== SENSITIVE CREDENTIALS =====
# Never commit API keys, secrets, or credentials
credentials.py
credentials.yaml
credentials.yml
config/credentials.yaml
config/credentials.yml
config/api_credentials.json
*.key
*.secret
*.pem
.env
.env.local
.env.production

# ===== TRADING DATA & LOGS =====
# Live trading logs and session data
*_report_*.json
test_report_*.json
performance_report_*.json
backtest_*.json
paper_trading_session_*.json
live_trading_session_*.json
trading_session_*.json

# Trade journals and performance data
epinnox_trade_journal.csv
trade_journal_*.csv
performance_data_*.json
daily_report_*.json

# Trading data
trades/*.csv
data/*.db
data/*.json
simulation_charts/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# ===== DATABASE FILES =====
# SQLite databases with trading data
*.db
*.sqlite
*.sqlite3
test_performance.db
trading_performance.db
epinnox_trading.db

# ===== MODEL FILES =====
# Large ML model files
models/saved_models/
ml/saved_models/
*.pkl
*.joblib
*.h5
*.pt
*.pth
*.onnx
model_cache/

# ===== LIVE TRADING SPECIFIC =====
# Files generated during live trading
live_trading_report_*.json
autonomous_system_report_*.json
emergency_stop_report_*.json
risk_management_report_*.json

# Account balance and position files
account_balance_*.json
positions_*.json
portfolio_state_*.json

# Temporary files
temp_*
*.tmp
system_status.json
gui_data.json
trading_parameters.json
epinnox_settings.json
user_preferences.json
