"""
Data Validation Layer
Comprehensive validation for OHLCV and NLP data quality
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ValidationSeverity(Enum):
    """Validation issue severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class ValidationIssue:
    """Data validation issue"""
    timestamp: datetime
    severity: ValidationSeverity
    category: str
    message: str
    affected_data: str
    suggested_action: str
    metadata: Dict[str, Any] = None

class DataValidator:
    """
    Comprehensive data validation system for trading data
    """
    
    def __init__(self, config: Dict = None):
        self.config = config or self._default_config()
        self.validation_issues = []
        
    def _default_config(self) -> Dict:
        """Default validation configuration"""
        return {
            'ohlcv_validation': {
                'required_columns': ['open', 'high', 'low', 'close', 'volume'],
                'max_price_change_pct': 0.50,  # 50% max price change
                'min_volume': 0,
                'max_gap_minutes': 60,  # Max gap between data points
                'price_precision': 8,
                'volume_precision': 8
            },
            'nlp_validation': {
                'min_text_length': 10,
                'max_text_length': 10000,
                'required_fields': ['text', 'timestamp'],
                'sentiment_range': [-1.0, 1.0],
                'confidence_range': [0.0, 1.0]
            },
            'general_validation': {
                'max_nan_percentage': 0.05,  # 5% max NaN values
                'max_duplicate_percentage': 0.01,  # 1% max duplicates
                'timezone_aware': True
            }
        }
    
    def validate_ohlcv_data(self, df: pd.DataFrame, symbol: str = "Unknown") -> List[ValidationIssue]:
        """Validate OHLCV market data"""
        issues = []
        
        if df.empty:
            issues.append(ValidationIssue(
                timestamp=datetime.now(),
                severity=ValidationSeverity.CRITICAL,
                category="data_availability",
                message=f"No OHLCV data available for {symbol}",
                affected_data=symbol,
                suggested_action="Check data source and connection"
            ))
            return issues
        
        # Check required columns
        required_cols = self.config['ohlcv_validation']['required_columns']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            issues.append(ValidationIssue(
                timestamp=datetime.now(),
                severity=ValidationSeverity.ERROR,
                category="schema_validation",
                message=f"Missing required columns: {missing_cols}",
                affected_data=symbol,
                suggested_action="Ensure data source provides all required OHLCV fields"
            ))
        
        # Check for NaN values
        for col in required_cols:
            if col in df.columns:
                nan_count = df[col].isna().sum()
                nan_percentage = nan_count / len(df)
                
                if nan_percentage > self.config['general_validation']['max_nan_percentage']:
                    issues.append(ValidationIssue(
                        timestamp=datetime.now(),
                        severity=ValidationSeverity.WARNING,
                        category="data_quality",
                        message=f"High NaN percentage in {col}: {nan_percentage:.2%}",
                        affected_data=f"{symbol}.{col}",
                        suggested_action="Investigate data source quality or implement interpolation",
                        metadata={'nan_count': nan_count, 'total_rows': len(df)}
                    ))
        
        # Check price relationships (High >= Low, etc.)
        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            # High should be >= Low
            invalid_high_low = df[df['high'] < df['low']]
            if not invalid_high_low.empty:
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.ERROR,
                    category="data_integrity",
                    message=f"Invalid OHLC relationships: {len(invalid_high_low)} rows where High < Low",
                    affected_data=symbol,
                    suggested_action="Review data source for corrupted price data"
                ))
            
            # High should be >= Open and Close
            invalid_high_open = df[df['high'] < df['open']]
            invalid_high_close = df[df['high'] < df['close']]
            
            if not invalid_high_open.empty or not invalid_high_close.empty:
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.ERROR,
                    category="data_integrity",
                    message=f"Invalid High prices: {len(invalid_high_open)} vs Open, {len(invalid_high_close)} vs Close",
                    affected_data=symbol,
                    suggested_action="Validate OHLC data integrity"
                ))
            
            # Low should be <= Open and Close
            invalid_low_open = df[df['low'] > df['open']]
            invalid_low_close = df[df['low'] > df['close']]
            
            if not invalid_low_open.empty or not invalid_low_close.empty:
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.ERROR,
                    category="data_integrity",
                    message=f"Invalid Low prices: {len(invalid_low_open)} vs Open, {len(invalid_low_close)} vs Close",
                    affected_data=symbol,
                    suggested_action="Validate OHLC data integrity"
                ))
        
        # Check for extreme price movements
        if 'close' in df.columns and len(df) > 1:
            price_changes = df['close'].pct_change().abs()
            max_change_threshold = self.config['ohlcv_validation']['max_price_change_pct']
            extreme_changes = price_changes > max_change_threshold
            
            if extreme_changes.any():
                extreme_count = extreme_changes.sum()
                max_change = price_changes.max()
                
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.WARNING,
                    category="data_anomaly",
                    message=f"Extreme price movements detected: {extreme_count} changes > {max_change_threshold:.1%} (max: {max_change:.1%})",
                    affected_data=symbol,
                    suggested_action="Review for market events or data errors",
                    metadata={'extreme_count': extreme_count, 'max_change': max_change}
                ))
        
        # Check volume data
        if 'volume' in df.columns:
            zero_volume = df[df['volume'] == 0]
            negative_volume = df[df['volume'] < 0]
            
            if not zero_volume.empty:
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.INFO,
                    category="data_quality",
                    message=f"Zero volume periods: {len(zero_volume)} rows",
                    affected_data=f"{symbol}.volume",
                    suggested_action="Normal for low-activity periods, monitor if excessive"
                ))
            
            if not negative_volume.empty:
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.ERROR,
                    category="data_integrity",
                    message=f"Negative volume values: {len(negative_volume)} rows",
                    affected_data=f"{symbol}.volume",
                    suggested_action="Fix data source - volume cannot be negative"
                ))
        
        # Check timestamp gaps
        if hasattr(df.index, 'to_series'):
            time_diffs = df.index.to_series().diff()
            max_gap = timedelta(minutes=self.config['ohlcv_validation']['max_gap_minutes'])
            large_gaps = time_diffs > max_gap
            
            if large_gaps.any():
                gap_count = large_gaps.sum()
                max_gap_found = time_diffs.max()
                
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.WARNING,
                    category="data_continuity",
                    message=f"Large time gaps detected: {gap_count} gaps > {max_gap} (largest: {max_gap_found})",
                    affected_data=symbol,
                    suggested_action="Check for market closures or data feed interruptions"
                ))
        
        # Check for duplicates
        if hasattr(df.index, 'duplicated'):
            duplicate_timestamps = df.index.duplicated()
            if duplicate_timestamps.any():
                dup_count = duplicate_timestamps.sum()
                
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.WARNING,
                    category="data_integrity",
                    message=f"Duplicate timestamps: {dup_count} rows",
                    affected_data=symbol,
                    suggested_action="Remove or aggregate duplicate entries"
                ))
        
        return issues
    
    def validate_nlp_data(self, df: pd.DataFrame, source: str = "Unknown") -> List[ValidationIssue]:
        """Validate NLP/sentiment data"""
        issues = []
        
        if df.empty:
            issues.append(ValidationIssue(
                timestamp=datetime.now(),
                severity=ValidationSeverity.WARNING,
                category="data_availability",
                message=f"No NLP data available from {source}",
                affected_data=source,
                suggested_action="Check NLP data source and processing pipeline"
            ))
            return issues
        
        # Check required fields
        required_fields = self.config['nlp_validation']['required_fields']
        missing_fields = [field for field in required_fields if field not in df.columns]
        
        if missing_fields:
            issues.append(ValidationIssue(
                timestamp=datetime.now(),
                severity=ValidationSeverity.ERROR,
                category="schema_validation",
                message=f"Missing required NLP fields: {missing_fields}",
                affected_data=source,
                suggested_action="Ensure NLP pipeline provides all required fields"
            ))
        
        # Validate text content
        if 'text' in df.columns:
            min_length = self.config['nlp_validation']['min_text_length']
            max_length = self.config['nlp_validation']['max_text_length']
            
            # Check text length
            text_lengths = df['text'].str.len()
            too_short = text_lengths < min_length
            too_long = text_lengths > max_length
            
            if too_short.any():
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.WARNING,
                    category="data_quality",
                    message=f"Short text entries: {too_short.sum()} texts < {min_length} characters",
                    affected_data=f"{source}.text",
                    suggested_action="Review text preprocessing or filtering rules"
                ))
            
            if too_long.any():
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.INFO,
                    category="data_quality",
                    message=f"Long text entries: {too_long.sum()} texts > {max_length} characters",
                    affected_data=f"{source}.text",
                    suggested_action="Consider text truncation or chunking"
                ))
            
            # Check for empty or null text
            empty_text = df['text'].isna() | (df['text'].str.strip() == '')
            if empty_text.any():
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.WARNING,
                    category="data_quality",
                    message=f"Empty text entries: {empty_text.sum()} rows",
                    affected_data=f"{source}.text",
                    suggested_action="Filter out empty text entries"
                ))
        
        # Validate sentiment scores
        if 'sentiment' in df.columns:
            sentiment_range = self.config['nlp_validation']['sentiment_range']
            out_of_range = (df['sentiment'] < sentiment_range[0]) | (df['sentiment'] > sentiment_range[1])
            
            if out_of_range.any():
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.ERROR,
                    category="data_integrity",
                    message=f"Sentiment scores out of range: {out_of_range.sum()} values outside {sentiment_range}",
                    affected_data=f"{source}.sentiment",
                    suggested_action="Check sentiment analysis model output normalization"
                ))
        
        # Validate confidence scores
        if 'confidence' in df.columns:
            confidence_range = self.config['nlp_validation']['confidence_range']
            out_of_range = (df['confidence'] < confidence_range[0]) | (df['confidence'] > confidence_range[1])
            
            if out_of_range.any():
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.ERROR,
                    category="data_integrity",
                    message=f"Confidence scores out of range: {out_of_range.sum()} values outside {confidence_range}",
                    affected_data=f"{source}.confidence",
                    suggested_action="Check confidence calculation in NLP pipeline"
                ))
        
        return issues
    
    def validate_signal_data(self, signals: Dict[str, Any], source: str = "Unknown") -> List[ValidationIssue]:
        """Validate trading signal data"""
        issues = []
        
        if not signals:
            issues.append(ValidationIssue(
                timestamp=datetime.now(),
                severity=ValidationSeverity.INFO,
                category="data_availability",
                message=f"No signals generated from {source}",
                affected_data=source,
                suggested_action="Normal if no trading opportunities detected"
            ))
            return issues
        
        # Validate signal structure
        for symbol, signal_data in signals.items():
            if not isinstance(signal_data, dict):
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.ERROR,
                    category="data_structure",
                    message=f"Invalid signal format for {symbol}: expected dict, got {type(signal_data)}",
                    affected_data=f"{source}.{symbol}",
                    suggested_action="Fix signal generation to return proper dictionary format"
                ))
                continue
            
            # Check required signal fields
            required_fields = ['decision', 'confidence']
            missing_fields = [field for field in required_fields if field not in signal_data]
            
            if missing_fields:
                issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.ERROR,
                    category="data_structure",
                    message=f"Missing signal fields for {symbol}: {missing_fields}",
                    affected_data=f"{source}.{symbol}",
                    suggested_action="Ensure signal generation includes all required fields"
                ))
            
            # Validate decision values
            if 'decision' in signal_data:
                valid_decisions = ['BUY', 'SELL', 'LONG', 'SHORT', 'WAIT', 'HOLD']
                if signal_data['decision'] not in valid_decisions:
                    issues.append(ValidationIssue(
                        timestamp=datetime.now(),
                        severity=ValidationSeverity.ERROR,
                        category="data_integrity",
                        message=f"Invalid decision value for {symbol}: {signal_data['decision']}",
                        affected_data=f"{source}.{symbol}.decision",
                        suggested_action=f"Use valid decision values: {valid_decisions}"
                    ))
            
            # Validate confidence values
            if 'confidence' in signal_data:
                confidence = signal_data['confidence']
                if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 100:
                    issues.append(ValidationIssue(
                        timestamp=datetime.now(),
                        severity=ValidationSeverity.ERROR,
                        category="data_integrity",
                        message=f"Invalid confidence value for {symbol}: {confidence}",
                        affected_data=f"{source}.{symbol}.confidence",
                        suggested_action="Confidence should be numeric value between 0 and 100"
                    ))
        
        return issues
    
    def run_comprehensive_validation(self, data_sources: Dict[str, Any]) -> Dict[str, List[ValidationIssue]]:
        """Run comprehensive validation on all data sources"""
        
        all_issues = {}
        
        logger.info("🔍 Running comprehensive data validation...")
        
        for source_name, data in data_sources.items():
            source_issues = []
            
            try:
                if isinstance(data, pd.DataFrame):
                    # Determine data type based on columns
                    if all(col in data.columns for col in ['open', 'high', 'low', 'close']):
                        source_issues.extend(self.validate_ohlcv_data(data, source_name))
                    elif 'text' in data.columns:
                        source_issues.extend(self.validate_nlp_data(data, source_name))
                    else:
                        logger.warning(f"Unknown DataFrame format for {source_name}")
                
                elif isinstance(data, dict):
                    # Assume signal data
                    source_issues.extend(self.validate_signal_data(data, source_name))
                
                else:
                    logger.warning(f"Unknown data type for {source_name}: {type(data)}")
            
            except Exception as e:
                source_issues.append(ValidationIssue(
                    timestamp=datetime.now(),
                    severity=ValidationSeverity.CRITICAL,
                    category="validation_error",
                    message=f"Error validating {source_name}: {str(e)}",
                    affected_data=source_name,
                    suggested_action="Check data format and validation logic"
                ))
            
            all_issues[source_name] = source_issues
        
        # Log summary
        total_issues = sum(len(issues) for issues in all_issues.values())
        critical_issues = sum(1 for issues in all_issues.values() 
                            for issue in issues if issue.severity == ValidationSeverity.CRITICAL)
        
        logger.info(f"✅ Validation completed: {total_issues} total issues, {critical_issues} critical")
        
        return all_issues
    
    def generate_validation_report(self, validation_results: Dict[str, List[ValidationIssue]]) -> Dict:
        """Generate comprehensive validation report"""
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_sources': len(validation_results),
                'total_issues': 0,
                'issues_by_severity': {severity.value: 0 for severity in ValidationSeverity},
                'issues_by_category': {},
                'sources_with_issues': 0
            },
            'source_details': {},
            'recommendations': []
        }
        
        # Analyze results
        for source_name, issues in validation_results.items():
            source_detail = {
                'total_issues': len(issues),
                'issues_by_severity': {severity.value: 0 for severity in ValidationSeverity},
                'issues': [
                    {
                        'severity': issue.severity.value,
                        'category': issue.category,
                        'message': issue.message,
                        'suggested_action': issue.suggested_action
                    }
                    for issue in issues
                ]
            }
            
            # Count by severity
            for issue in issues:
                report['summary']['issues_by_severity'][issue.severity.value] += 1
                source_detail['issues_by_severity'][issue.severity.value] += 1
                
                # Count by category
                category = issue.category
                report['summary']['issues_by_category'][category] = report['summary']['issues_by_category'].get(category, 0) + 1
            
            report['source_details'][source_name] = source_detail
            
            if issues:
                report['summary']['sources_with_issues'] += 1
        
        report['summary']['total_issues'] = sum(report['summary']['issues_by_severity'].values())
        
        # Generate recommendations
        if report['summary']['issues_by_severity']['critical'] > 0:
            report['recommendations'].append("CRITICAL: Address critical data issues immediately before trading")
        
        if report['summary']['issues_by_severity']['error'] > 0:
            report['recommendations'].append("ERROR: Fix data integrity issues to ensure reliable trading")
        
        if report['summary']['issues_by_category'].get('data_continuity', 0) > 0:
            report['recommendations'].append("Review data feed reliability and implement gap handling")
        
        if report['summary']['issues_by_category'].get('data_quality', 0) > 0:
            report['recommendations'].append("Improve data preprocessing and quality controls")
        
        return report
