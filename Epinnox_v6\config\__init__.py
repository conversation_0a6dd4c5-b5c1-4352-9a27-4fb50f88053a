"""
Configuration Module

This module handles loading and saving configuration files.
"""

import os
import yaml
import logging

logger = logging.getLogger('config')

# Default configuration file path
DEFAULT_CONFIG_PATH = os.path.join(os.path.dirname(__file__), 'strategy_config.yaml')

def load_config(config_path=None):
    """
    Load configuration from a YAML file.
    
    Args:
        config_path (str, optional): Path to the configuration file. 
                                    If None, uses the default path.
                                    
    Returns:
        dict: Configuration dictionary.
    """
    if config_path is None:
        config_path = DEFAULT_CONFIG_PATH
        
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        logger.info(f"Loaded configuration from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Error loading configuration from {config_path}: {e}")
        # Return a default configuration
        return {
            'strategies': {
                'atr_ema_bands': {
                    'atr_period': 14,
                    'ema_period': 20,
                    'band_multipliers': [1, 2, 3],
                    'buy_threshold': 0.6,
                    'sell_threshold': 0.4
                }
            },
            'trading': {
                'symbol': 'DOGE/USDT:USDT',
                'timeframe': '5m',
                'position_size_pct': 10.0,
                'leverage': 20,
                'take_profit_pct': 0.3,
                'stop_loss_pct': 2.0,
                'auto_trading_enabled': False,
                'check_interval_seconds': 60
            }
        }
        
def save_config(config, config_path=None):
    """
    Save configuration to a YAML file.
    
    Args:
        config (dict): Configuration dictionary.
        config_path (str, optional): Path to the configuration file.
                                    If None, uses the default path.
                                    
    Returns:
        bool: True if successful, False otherwise.
    """
    if config_path is None:
        config_path = DEFAULT_CONFIG_PATH
        
    try:
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
        logger.info(f"Saved configuration to {config_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving configuration to {config_path}: {e}")
        return False
