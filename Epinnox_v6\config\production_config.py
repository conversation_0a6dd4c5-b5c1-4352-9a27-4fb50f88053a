#!/usr/bin/env python3
"""
Production Configuration Management for Epinnox v6
Manages environment-specific settings, secure credentials, and deployment configurations
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class Environment(Enum):
    """Deployment environments"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"

@dataclass
class TradingLimits:
    """Trading limits configuration"""
    max_daily_loss_pct: float = 5.0
    max_position_size_pct: float = 10.0
    max_leverage: int = 20
    max_daily_trades: int = 10
    max_concurrent_positions: int = 3
    emergency_stop_loss_pct: float = 10.0

@dataclass
class RiskSettings:
    """Risk management settings"""
    trading_limits: TradingLimits
    enable_emergency_stops: bool = True
    require_confirmation_above_usd: float = 100.0
    enable_correlation_checks: bool = True
    enable_daily_limits: bool = True

@dataclass
class APISettings:
    """API configuration settings"""
    exchange_name: str = "okx"
    api_timeout_seconds: int = 30
    max_retries: int = 3
    retry_delay_seconds: float = 1.0
    rate_limit_requests_per_minute: int = 60
    enable_testnet: bool = True

@dataclass
class LLMSettings:
    """LLM orchestrator settings"""
    enable_llm_trading: bool = True
    min_confidence_threshold: float = 75.0
    cycle_interval_seconds: int = 30
    max_analysis_time_seconds: int = 60
    enable_autonomous_mode: bool = False

@dataclass
class MonitoringSettings:
    """Monitoring and logging settings"""
    enable_real_time_monitoring: bool = True
    log_level: str = "INFO"
    enable_performance_tracking: bool = True
    enable_health_checks: bool = True
    export_reports_interval_hours: int = 24
    max_log_file_size_mb: int = 100

@dataclass
class ProductionConfig:
    """Complete production configuration"""
    environment: Environment
    risk_settings: RiskSettings
    api_settings: APISettings
    llm_settings: LLMSettings
    monitoring_settings: MonitoringSettings
    deployment_timestamp: Optional[str] = None
    version: str = "6.0.0"

class ProductionConfigManager:
    """
    Production configuration management system
    """
    
    def __init__(self, config_dir: str = "config"):
        """Initialize configuration manager"""
        self.config_dir = config_dir
        self.config_file = os.path.join(config_dir, "production.json")
        self.secrets_file = os.path.join(config_dir, "secrets.json")
        
        # Ensure config directory exists
        os.makedirs(config_dir, exist_ok=True)
        
        # Current configuration
        self.current_config: Optional[ProductionConfig] = None
        self.current_environment = Environment.DEVELOPMENT
        
        logger.info("🔧 Production Configuration Manager initialized")
    
    def create_default_configs(self):
        """Create default configurations for all environments"""
        try:
            configs = {
                Environment.DEVELOPMENT: self._create_development_config(),
                Environment.TESTING: self._create_testing_config(),
                Environment.STAGING: self._create_staging_config(),
                Environment.PRODUCTION: self._create_production_config()
            }
            
            for env, config in configs.items():
                self._save_config(config, env)
            
            logger.info("✅ Default configurations created for all environments")
            
        except Exception as e:
            logger.error(f"Error creating default configs: {e}")
    
    def load_config(self, environment: Environment) -> ProductionConfig:
        """Load configuration for specific environment"""
        try:
            config_file = os.path.join(self.config_dir, f"{environment.value}.json")
            
            if not os.path.exists(config_file):
                logger.warning(f"Config file not found: {config_file}, creating default")
                self.create_default_configs()
            
            with open(config_file, 'r') as f:
                config_data = json.load(f)
            
            # Convert to ProductionConfig object
            config = self._dict_to_config(config_data)
            config.environment = environment
            
            self.current_config = config
            self.current_environment = environment
            
            logger.info(f"✅ Configuration loaded for {environment.value}")
            return config
            
        except Exception as e:
            logger.error(f"Error loading config for {environment.value}: {e}")
            # Return default config as fallback
            return self._create_development_config()
    
    def save_config(self, config: ProductionConfig):
        """Save configuration to file"""
        try:
            self._save_config(config, config.environment)
            self.current_config = config
            logger.info(f"✅ Configuration saved for {config.environment.value}")
            
        except Exception as e:
            logger.error(f"Error saving config: {e}")
    
    def validate_config(self, config: ProductionConfig) -> tuple[bool, list[str]]:
        """Validate configuration settings"""
        errors = []
        
        try:
            # Validate trading limits
            limits = config.risk_settings.trading_limits
            if limits.max_daily_loss_pct <= 0 or limits.max_daily_loss_pct > 50:
                errors.append("max_daily_loss_pct must be between 0 and 50")
            
            if limits.max_position_size_pct <= 0 or limits.max_position_size_pct > 100:
                errors.append("max_position_size_pct must be between 0 and 100")
            
            if limits.max_leverage < 1 or limits.max_leverage > 100:
                errors.append("max_leverage must be between 1 and 100")
            
            # Validate API settings
            api = config.api_settings
            if api.api_timeout_seconds < 5 or api.api_timeout_seconds > 300:
                errors.append("api_timeout_seconds must be between 5 and 300")
            
            # Validate LLM settings
            llm = config.llm_settings
            if llm.min_confidence_threshold < 0 or llm.min_confidence_threshold > 100:
                errors.append("min_confidence_threshold must be between 0 and 100")
            
            # Environment-specific validations
            if config.environment == Environment.PRODUCTION:
                if config.api_settings.enable_testnet:
                    errors.append("Production environment cannot use testnet")
                
                if config.risk_settings.trading_limits.max_daily_loss_pct > 10:
                    errors.append("Production daily loss limit should not exceed 10%")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"Error validating config: {e}")
            return False, [f"Validation error: {e}"]
    
    def get_current_config(self) -> Optional[ProductionConfig]:
        """Get current loaded configuration"""
        return self.current_config
    
    def get_environment_summary(self) -> Dict[str, Any]:
        """Get summary of current environment configuration"""
        try:
            if not self.current_config:
                return {"error": "No configuration loaded"}
            
            config = self.current_config
            return {
                "environment": config.environment.value,
                "version": config.version,
                "trading_enabled": config.llm_settings.enable_llm_trading,
                "autonomous_mode": config.llm_settings.enable_autonomous_mode,
                "testnet_mode": config.api_settings.enable_testnet,
                "max_daily_loss": f"{config.risk_settings.trading_limits.max_daily_loss_pct}%",
                "max_position_size": f"{config.risk_settings.trading_limits.max_position_size_pct}%",
                "max_leverage": f"{config.risk_settings.trading_limits.max_leverage}x",
                "emergency_stops": config.risk_settings.enable_emergency_stops,
                "monitoring_enabled": config.monitoring_settings.enable_real_time_monitoring
            }
            
        except Exception as e:
            logger.error(f"Error getting environment summary: {e}")
            return {"error": str(e)}
    
    def _create_development_config(self) -> ProductionConfig:
        """Create development environment configuration"""
        return ProductionConfig(
            environment=Environment.DEVELOPMENT,
            risk_settings=RiskSettings(
                trading_limits=TradingLimits(
                    max_daily_loss_pct=2.0,
                    max_position_size_pct=5.0,
                    max_leverage=10,
                    max_daily_trades=5,
                    max_concurrent_positions=2,
                    emergency_stop_loss_pct=5.0
                ),
                require_confirmation_above_usd=50.0
            ),
            api_settings=APISettings(
                enable_testnet=True,
                api_timeout_seconds=30,
                max_retries=3
            ),
            llm_settings=LLMSettings(
                enable_llm_trading=True,
                min_confidence_threshold=80.0,
                enable_autonomous_mode=False
            ),
            monitoring_settings=MonitoringSettings(
                log_level="DEBUG",
                enable_performance_tracking=True
            )
        )
    
    def _create_testing_config(self) -> ProductionConfig:
        """Create testing environment configuration"""
        config = self._create_development_config()
        config.environment = Environment.TESTING
        config.llm_settings.enable_llm_trading = False  # Disable trading in tests
        config.monitoring_settings.log_level = "INFO"
        return config
    
    def _create_staging_config(self) -> ProductionConfig:
        """Create staging environment configuration"""
        config = self._create_development_config()
        config.environment = Environment.STAGING
        config.risk_settings.trading_limits.max_daily_loss_pct = 3.0
        config.llm_settings.min_confidence_threshold = 85.0
        config.monitoring_settings.log_level = "INFO"
        return config
    
    def _create_production_config(self) -> ProductionConfig:
        """Create production environment configuration"""
        return ProductionConfig(
            environment=Environment.PRODUCTION,
            risk_settings=RiskSettings(
                trading_limits=TradingLimits(
                    max_daily_loss_pct=5.0,
                    max_position_size_pct=10.0,
                    max_leverage=20,
                    max_daily_trades=10,
                    max_concurrent_positions=3,
                    emergency_stop_loss_pct=10.0
                ),
                require_confirmation_above_usd=200.0
            ),
            api_settings=APISettings(
                enable_testnet=False,  # Live trading
                api_timeout_seconds=15,
                max_retries=5
            ),
            llm_settings=LLMSettings(
                enable_llm_trading=True,
                min_confidence_threshold=85.0,
                enable_autonomous_mode=True
            ),
            monitoring_settings=MonitoringSettings(
                log_level="INFO",
                enable_real_time_monitoring=True,
                enable_performance_tracking=True,
                enable_health_checks=True
            )
        )
    
    def _save_config(self, config: ProductionConfig, environment: Environment):
        """Save configuration to file"""
        config_file = os.path.join(self.config_dir, f"{environment.value}.json")
        config_dict = asdict(config)
        
        with open(config_file, 'w') as f:
            json.dump(config_dict, f, indent=2, default=str)
    
    def _dict_to_config(self, config_dict: Dict[str, Any]) -> ProductionConfig:
        """Convert dictionary to ProductionConfig object"""
        # This is a simplified conversion - in a real implementation,
        # you'd want more robust deserialization
        return ProductionConfig(
            environment=Environment(config_dict.get('environment', 'development')),
            risk_settings=RiskSettings(
                trading_limits=TradingLimits(**config_dict.get('risk_settings', {}).get('trading_limits', {})),
                **{k: v for k, v in config_dict.get('risk_settings', {}).items() if k != 'trading_limits'}
            ),
            api_settings=APISettings(**config_dict.get('api_settings', {})),
            llm_settings=LLMSettings(**config_dict.get('llm_settings', {})),
            monitoring_settings=MonitoringSettings(**config_dict.get('monitoring_settings', {})),
            deployment_timestamp=config_dict.get('deployment_timestamp'),
            version=config_dict.get('version', '6.0.0')
        )
