#!/usr/bin/env python3
"""
Simple NLP Integration Test
Tests the NLP sentiment analysis integration without the full trading system.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

print("🧠 Testing NLP Integration...")

# Test NLP imports
try:
    from nlp.sentiment_analyzer import <PERSON>timentAnalyzer
    from nlp.news_scraper import NewsScraperManager, NewsSource
    from nlp.social_monitor import SocialMediaMonitor, SocialPlatform
    from nlp.market_sentiment import MarketSentimentAggregator
    from nlp.nlp_features import NLPFeatureExtractor
    print("✅ All NLP modules imported successfully")
except ImportError as e:
    print(f"❌ NLP import error: {e}")
    sys.exit(1)

# Test basic sentiment analysis
print("\n📊 Testing Basic Sentiment Analysis...")
try:
    analyzer = SentimentAnalyzer(use_transformer=False)
    
    test_texts = [
        "DOGE is going to the moon! 🚀",
        "This crypto crash is terrible",
        "Bitcoin looking bullish today",
        "Market sentiment neutral"
    ]
    
    for text in test_texts:
        result = analyzer.analyze_sentiment(text)
        print(f"  '{text}' -> {result.label.value} (confidence: {result.confidence:.2%})")
        
    print("✅ Basic sentiment analysis working")
except Exception as e:
    print(f"❌ Sentiment analysis error: {e}")

# Test news scraper initialization
print("\n📰 Testing News Scraper...")
try:
    news_scraper = NewsScraperManager(
        sources=[NewsSource.COINDESK],
        max_articles_per_source=5
    )
    print("✅ News scraper initialized successfully")
except Exception as e:
    print(f"❌ News scraper error: {e}")

# Test social monitor initialization
print("\n💬 Testing Social Monitor...")
try:
    social_monitor = SocialMediaMonitor(platforms=[SocialPlatform.REDDIT])
    print("✅ Social monitor initialized successfully")
except Exception as e:
    print(f"❌ Social monitor error: {e}")

# Test market sentiment aggregator
print("\n🎯 Testing Market Sentiment Aggregator...")
try:
    sentiment_aggregator = MarketSentimentAggregator(
        sentiment_analyzer=analyzer,
        news_scraper=news_scraper,
        social_monitor=social_monitor
    )
    print("✅ Market sentiment aggregator initialized successfully")
except Exception as e:
    print(f"❌ Market sentiment aggregator error: {e}")

# Test NLP feature extractor
print("\n🔍 Testing NLP Feature Extractor...")
try:
    nlp_extractor = NLPFeatureExtractor()
    print("✅ NLP feature extractor initialized successfully")
except Exception as e:
    print(f"❌ NLP feature extractor error: {e}")

print("\n🎉 NLP Integration Test Complete!")
print("All core components are working. Ready for integration with trading system.")
