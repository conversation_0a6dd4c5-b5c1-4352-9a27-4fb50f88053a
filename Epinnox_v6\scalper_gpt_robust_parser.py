
import json
import re

def parse_scalper_gpt_response_robust(response_text, account_balance=1000.0, current_price=1.0):
    """Robust parser for ScalperGPT responses"""
    try:
        # Import the helper functions
        from scalper_gpt_field_mapper import fix_scalper_gpt_json_fields
        from scalper_gpt_action_detector import detect_and_fix_action_field
        from scalper_gpt_quantity_calculator import fix_scalper_gpt_quantity
        from scalper_gpt_validator import validate_scalper_gpt_fields
        
        # Clean the response text
        cleaned_text = response_text.strip()
        
        # Remove markdown code blocks
        if cleaned_text.startswith('```'):
            lines = cleaned_text.split('\n')
            if len(lines) > 2:
                cleaned_text = '\n'.join(lines[1:-1])
        
        # Try to parse as JSON
        try:
            parsed_data = json.loads(cleaned_text)
        except json.JSONDecodeError:
            # Try to extract JSON from text
            json_pattern = r'\{[^}]*\}'
            matches = re.findall(json_pattern, cleaned_text, re.DOTALL)
            
            if matches:
                try:
                    parsed_data = json.loads(matches[0])
                except json.JSONDecodeError:
                    # Create from text analysis
                    parsed_data = extract_data_from_text(cleaned_text)
            else:
                parsed_data = extract_data_from_text(cleaned_text)
        
        # Apply all fixes
        parsed_data = fix_scalper_gpt_json_fields(parsed_data)
        parsed_data = detect_and_fix_action_field(parsed_data)
        parsed_data = fix_scalper_gpt_quantity(parsed_data, account_balance, current_price)
        parsed_data = validate_scalper_gpt_fields(parsed_data)
        
        return parsed_data
        
    except Exception as e:
        # Return safe default
        return {
            'action': 'WAIT',
            'ACTION': 'WAIT',
            'quantity': 0.001,
            'QUANTITY': 0.001,
            'leverage': 1,
            'LEVERAGE': 1,
            'risk_pct': 1.0,
            'RISK_PCT': 1.0,
            'order_type': 'MARKET',
            'ORDER_TYPE': 'MARKET',
            'stop_loss': 1.0,
            'take_profit': 2.0,
            'error': str(e)
        }

def extract_data_from_text(text):
    """Extract trading data from text when JSON parsing fails"""
    data = {}
    
    # Look for action keywords
    text_upper = text.upper()
    if any(word in text_upper for word in ['BUY', 'LONG', 'BULL']):
        data['action'] = 'BUY'
    elif any(word in text_upper for word in ['SELL', 'SHORT', 'BEAR']):
        data['action'] = 'SELL'
    else:
        data['action'] = 'WAIT'
    
    # Extract numbers for quantity, leverage, etc.
    numbers = re.findall(r'\d+(?:\.\d+)?', text)
    
    if len(numbers) >= 1:
        data['quantity'] = float(numbers[0])
    if len(numbers) >= 2:
        data['leverage'] = int(float(numbers[1]))
    if len(numbers) >= 3:
        data['risk_pct'] = float(numbers[2])
    
    return data
