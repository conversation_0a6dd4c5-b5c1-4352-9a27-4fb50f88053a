"""
Comprehensive Backtesting Framework for EPINNOX v6
Simulates autonomous trading with historical data
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class BacktestConfig:
    """Configuration for backtesting"""
    initial_balance: float = 10000.0
    start_date: str = "2024-01-01"
    end_date: str = "2024-12-31"
    symbols: List[str] = None
    timeframe: str = "1m"
    slippage: float = 0.001  # 0.1%
    commission: float = 0.001  # 0.1%
    max_positions: int = 5
    min_confidence: float = 0.65
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']

@dataclass
class BacktestResult:
    """Results from backtesting"""
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_trade_duration: float
    final_balance: float
    trade_log: List[Dict]
    equity_curve: List[Dict]
    
class BacktestRunner:
    """
    Comprehensive backtesting engine for autonomous trading strategies
    """
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.current_balance = config.initial_balance
        self.positions = {}
        self.trade_log = []
        self.equity_curve = []
        self.peak_balance = config.initial_balance
        self.max_drawdown = 0.0
        
    def load_historical_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """Load historical OHLCV data for backtesting"""
        # In a real implementation, this would load from your data source
        # For testing, generate synthetic data
        
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        
        # Generate minute-by-minute data
        dates = pd.date_range(start=start, end=end, freq='1min')
        
        # Generate realistic price data
        np.random.seed(42)  # For reproducible results
        base_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 0.5
        
        returns = np.random.normal(0, 0.001, len(dates))  # 0.1% volatility
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # Create OHLCV data
        data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            high = price * (1 + abs(np.random.normal(0, 0.002)))
            low = price * (1 - abs(np.random.normal(0, 0.002)))
            open_price = prices[i-1] if i > 0 else price
            close_price = price
            volume = np.random.uniform(100, 1000)
            
            data.append({
                'timestamp': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close_price,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def simulate_trading_decision(self, market_data: pd.DataFrame, timestamp: pd.Timestamp) -> Dict:
        """Simulate trading decision based on market data"""
        # Get current and recent data
        current_idx = market_data.index.get_loc(timestamp)
        if current_idx < 50:  # Need enough history
            return {'decision': 'WAIT', 'confidence': 0}
        
        # Simple momentum strategy for testing
        recent_data = market_data.iloc[current_idx-20:current_idx+1]
        price_change = (recent_data['close'].iloc[-1] - recent_data['close'].iloc[0]) / recent_data['close'].iloc[0]
        
        # Generate decision based on momentum
        if price_change > 0.02:  # 2% upward momentum
            decision = 'LONG'
            confidence = min(95, 60 + abs(price_change) * 1000)
        elif price_change < -0.02:  # 2% downward momentum
            decision = 'SHORT'
            confidence = min(95, 60 + abs(price_change) * 1000)
        else:
            decision = 'WAIT'
            confidence = 30
        
        return {
            'decision': decision,
            'confidence': confidence,
            'price_change': price_change,
            'current_price': recent_data['close'].iloc[-1]
        }
    
    def calculate_position_size(self, symbol: str, decision: str, confidence: float, current_price: float) -> Dict:
        """Calculate position sizing based on confidence and risk management"""
        if decision == 'WAIT':
            return {'position_units': 0, 'position_usd': 0, 'leverage': 1.0}
        
        # Risk-based position sizing
        confidence_factor = confidence / 100.0
        max_risk_per_trade = 0.02  # 2% max risk per trade
        
        # Calculate position size
        risk_amount = self.current_balance * max_risk_per_trade * confidence_factor
        position_usd = min(risk_amount * 10, self.current_balance * 0.1)  # Max 10% of balance
        position_units = position_usd / current_price
        
        # Leverage based on confidence
        leverage = min(3.0, 1.0 + (confidence_factor * 2))  # Max 3x leverage
        
        return {
            'position_units': position_units,
            'position_usd': position_usd,
            'leverage': leverage,
            'risk_amount': risk_amount
        }
    
    def execute_trade(self, symbol: str, decision: str, position_data: Dict, 
                     current_price: float, timestamp: pd.Timestamp) -> Dict:
        """Execute trade with slippage and commission simulation"""
        if decision == 'WAIT' or position_data['position_units'] == 0:
            return {'status': 'WAIT', 'reason': 'No trade executed'}
        
        # Apply slippage
        if decision == 'LONG':
            execution_price = current_price * (1 + self.config.slippage)
            side = 'buy'
        else:
            execution_price = current_price * (1 - self.config.slippage)
            side = 'sell'
        
        # Calculate costs
        trade_value = position_data['position_units'] * execution_price
        commission = trade_value * self.config.commission
        total_cost = trade_value + commission
        
        # Check if we have enough balance
        if total_cost > self.current_balance:
            return {'status': 'REJECTED', 'reason': 'Insufficient balance'}
        
        # Close existing position if switching directions
        if symbol in self.positions:
            self.close_position(symbol, current_price, timestamp)
        
        # Open new position
        position = {
            'symbol': symbol,
            'side': side,
            'entry_price': execution_price,
            'position_units': position_data['position_units'],
            'leverage': position_data['leverage'],
            'entry_time': timestamp,
            'commission_paid': commission
        }
        
        self.positions[symbol] = position
        self.current_balance -= total_cost
        
        return {
            'status': 'FILLED',
            'symbol': symbol,
            'side': side,
            'amount': position_data['position_units'],
            'price': execution_price,
            'commission': commission,
            'timestamp': timestamp
        }
    
    def close_position(self, symbol: str, current_price: float, timestamp: pd.Timestamp) -> Dict:
        """Close an existing position"""
        if symbol not in self.positions:
            return {'status': 'ERROR', 'reason': 'No position to close'}
        
        position = self.positions[symbol]
        
        # Apply slippage for closing
        if position['side'] == 'buy':
            exit_price = current_price * (1 - self.config.slippage)
        else:
            exit_price = current_price * (1 + self.config.slippage)
        
        # Calculate P&L
        if position['side'] == 'buy':
            pnl = (exit_price - position['entry_price']) * position['position_units'] * position['leverage']
        else:
            pnl = (position['entry_price'] - exit_price) * position['position_units'] * position['leverage']
        
        # Calculate commission for closing
        trade_value = position['position_units'] * exit_price
        exit_commission = trade_value * self.config.commission
        
        # Net P&L after commissions
        net_pnl = pnl - position['commission_paid'] - exit_commission
        
        # Update balance
        self.current_balance += trade_value - exit_commission
        
        # Calculate trade duration
        duration = (timestamp - position['entry_time']).total_seconds() / 60  # minutes
        
        # Record trade
        trade_record = {
            'symbol': symbol,
            'side': position['side'],
            'entry_price': position['entry_price'],
            'exit_price': exit_price,
            'position_units': position['position_units'],
            'leverage': position['leverage'],
            'pnl': net_pnl,
            'pnl_pct': (net_pnl / (position['position_units'] * position['entry_price'])) * 100,
            'duration_minutes': duration,
            'entry_time': position['entry_time'],
            'exit_time': timestamp,
            'total_commission': position['commission_paid'] + exit_commission
        }
        
        self.trade_log.append(trade_record)
        
        # Remove position
        del self.positions[symbol]
        
        return {
            'status': 'CLOSED',
            'pnl': net_pnl,
            'trade_record': trade_record
        }
    
    def update_equity_curve(self, timestamp: pd.Timestamp, market_prices: Dict[str, float]):
        """Update equity curve with current portfolio value"""
        # Calculate unrealized P&L
        unrealized_pnl = 0.0
        for symbol, position in self.positions.items():
            current_price = market_prices.get(symbol, position['entry_price'])
            
            if position['side'] == 'buy':
                unrealized = (current_price - position['entry_price']) * position['position_units'] * position['leverage']
            else:
                unrealized = (position['entry_price'] - current_price) * position['position_units'] * position['leverage']
            
            unrealized_pnl += unrealized
        
        total_equity = self.current_balance + unrealized_pnl
        
        # Update peak and drawdown
        if total_equity > self.peak_balance:
            self.peak_balance = total_equity
        
        current_drawdown = (self.peak_balance - total_equity) / self.peak_balance
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
        
        # Record equity point
        self.equity_curve.append({
            'timestamp': timestamp,
            'balance': self.current_balance,
            'unrealized_pnl': unrealized_pnl,
            'total_equity': total_equity,
            'drawdown': current_drawdown
        })
    
    def run_backtest(self) -> BacktestResult:
        """Run the complete backtest"""
        logger.info(f"Starting backtest from {self.config.start_date} to {self.config.end_date}")
        
        # Load data for all symbols
        market_data = {}
        for symbol in self.config.symbols:
            market_data[symbol] = self.load_historical_data(
                symbol, self.config.start_date, self.config.end_date
            )
        
        # Get common timestamps
        all_timestamps = set()
        for df in market_data.values():
            all_timestamps.update(df.index)
        
        timestamps = sorted(list(all_timestamps))
        
        # Run simulation
        for i, timestamp in enumerate(timestamps):
            if i % 1000 == 0:  # Progress logging
                logger.info(f"Processing {i}/{len(timestamps)} timestamps")
            
            # Get current market prices
            current_prices = {}
            for symbol, df in market_data.items():
                if timestamp in df.index:
                    current_prices[symbol] = df.loc[timestamp, 'close']
            
            # Make trading decisions for each symbol
            for symbol in self.config.symbols:
                if symbol not in current_prices:
                    continue
                
                # Generate trading decision
                decision_data = self.simulate_trading_decision(market_data[symbol], timestamp)
                
                # Skip if confidence too low
                if decision_data['confidence'] < self.config.min_confidence * 100:
                    continue
                
                # Calculate position sizing
                position_data = self.calculate_position_size(
                    symbol, decision_data['decision'], 
                    decision_data['confidence'], current_prices[symbol]
                )
                
                # Execute trade
                execution_result = self.execute_trade(
                    symbol, decision_data['decision'], position_data,
                    current_prices[symbol], timestamp
                )
            
            # Update equity curve
            self.update_equity_curve(timestamp, current_prices)
        
        # Close all remaining positions
        final_prices = {symbol: df.iloc[-1]['close'] for symbol, df in market_data.items()}
        for symbol in list(self.positions.keys()):
            self.close_position(symbol, final_prices[symbol], timestamps[-1])
        
        # Calculate final results
        return self.calculate_results()
    
    def calculate_results(self) -> BacktestResult:
        """Calculate final backtest results"""
        if not self.trade_log:
            return BacktestResult(
                total_return=0.0, sharpe_ratio=0.0, max_drawdown=0.0,
                win_rate=0.0, profit_factor=0.0, total_trades=0,
                winning_trades=0, losing_trades=0, avg_trade_duration=0.0,
                final_balance=self.current_balance, trade_log=[], equity_curve=self.equity_curve
            )
        
        # Basic metrics
        total_trades = len(self.trade_log)
        winning_trades = len([t for t in self.trade_log if t['pnl'] > 0])
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # P&L metrics
        total_pnl = sum(t['pnl'] for t in self.trade_log)
        total_return = total_pnl / self.config.initial_balance
        
        # Profit factor
        gross_profit = sum(t['pnl'] for t in self.trade_log if t['pnl'] > 0)
        gross_loss = abs(sum(t['pnl'] for t in self.trade_log if t['pnl'] < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Sharpe ratio (simplified)
        returns = [t['pnl'] / self.config.initial_balance for t in self.trade_log]
        if len(returns) > 1:
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Average trade duration
        avg_duration = np.mean([t['duration_minutes'] for t in self.trade_log])
        
        return BacktestResult(
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=self.max_drawdown,
            win_rate=win_rate,
            profit_factor=profit_factor,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            avg_trade_duration=avg_duration,
            final_balance=self.current_balance,
            trade_log=self.trade_log,
            equity_curve=self.equity_curve
        )
    
    def save_results(self, result: BacktestResult, filename: str):
        """Save backtest results to file"""
        result_dict = asdict(result)
        
        # Convert timestamps to strings for JSON serialization
        for trade in result_dict['trade_log']:
            if 'entry_time' in trade:
                trade['entry_time'] = trade['entry_time'].isoformat()
            if 'exit_time' in trade:
                trade['exit_time'] = trade['exit_time'].isoformat()
        
        for equity_point in result_dict['equity_curve']:
            if 'timestamp' in equity_point:
                equity_point['timestamp'] = equity_point['timestamp'].isoformat()
        
        with open(filename, 'w') as f:
            json.dump(result_dict, f, indent=2, default=str)
        
        logger.info(f"Backtest results saved to {filename}")
    
    def print_summary(self, result: BacktestResult):
        """Print backtest summary"""
        print(f"\n{'='*60}")
        print(f"BACKTEST RESULTS SUMMARY")
        print(f"{'='*60}")
        print(f"Initial Balance:     ${self.config.initial_balance:,.2f}")
        print(f"Final Balance:       ${result.final_balance:,.2f}")
        print(f"Total Return:        {result.total_return:.2%}")
        print(f"Total Trades:        {result.total_trades}")
        print(f"Winning Trades:      {result.winning_trades}")
        print(f"Losing Trades:       {result.losing_trades}")
        print(f"Win Rate:            {result.win_rate:.2%}")
        print(f"Profit Factor:       {result.profit_factor:.2f}")
        print(f"Sharpe Ratio:        {result.sharpe_ratio:.2f}")
        print(f"Max Drawdown:        {result.max_drawdown:.2%}")
        print(f"Avg Trade Duration:  {result.avg_trade_duration:.1f} minutes")
        print(f"{'='*60}\n")
