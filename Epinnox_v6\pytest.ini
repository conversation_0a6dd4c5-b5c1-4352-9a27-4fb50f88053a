[tool:pytest]
# Pytest configuration for EPINNOX v6

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes

# Markers
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interaction
    slow: Slow running tests
    autonomous: Tests for autonomous trading components
    rl: Tests for reinforcement learning components
    backtest: Backtesting related tests
    paper: Paper trading tests
    risk: Risk management tests

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Warnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# Coverage options (if pytest-cov is installed)
# addopts = --cov=. --cov-report=html --cov-report=term-missing
