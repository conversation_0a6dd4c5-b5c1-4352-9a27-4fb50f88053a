#!/usr/bin/env python3
"""
Epinnox v6 GUI Backend Integration Testing Suite
Comprehensive testing for GUI integration with autonomous trading backend
"""

import pytest
import sys
import os
import json
import time
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication, QLabel
from PyQt5.QtCore import QTimer

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestGUIBackendIntegration:
    """Test GUI integration with autonomous trading backend components"""
    
    @pytest.fixture(scope="class")
    def app(self):
        """Create QApplication for testing"""
        if not QApplication.instance():
            app = QApplication(sys.argv)
        else:
            app = QApplication.instance()
        yield app
    
    @pytest.fixture
    def mock_autonomous_controller(self):
        """Mock AutonomousController for testing"""
        with patch('core.autonomous_controller.AutonomousController') as mock:
            controller = mock.return_value
            controller.get_current_decision.return_value = {
                'action': 'SHORT',
                'confidence': 0.85,
                'reasoning': 'Market showing bearish momentum with high volume',
                'risk_level': 'MODERATE',
                'timestamp': '2025-06-29 15:51:35'
            }
            controller.is_active.return_value = True
            controller.get_status.return_value = 'ACTIVE'
            yield controller
    
    @pytest.fixture
    def mock_llm_orchestrator(self):
        """Mock LLM Orchestrator for testing"""
        with patch('core.llm_orchestrator.LLMOrchestrator') as mock:
            orchestrator = mock.return_value
            orchestrator.get_latest_results.return_value = {
                'risk_assessment': {'action': 'SHORT', 'confidence': 0.85},
                'entry_timing': {'action': 'ENTER_NOW', 'confidence': 0.75},
                'opportunity_scanner': {'action': 'FAVORABLE', 'confidence': 0.80}
            }
            orchestrator.get_confidence_scores.return_value = {
                'overall': 0.85,
                'risk_assessment': 0.85,
                'entry_timing': 0.75,
                'opportunity_scanner': 0.80
            }
            yield orchestrator
    
    @pytest.fixture
    def mock_rl_agent(self):
        """Mock RL Agent for testing"""
        with patch('rl.rl_agent.RLAgent') as mock:
            agent = mock.return_value
            agent.get_metrics.return_value = {
                'learning_rate': 0.001,
                'reward': 0.15,
                'exploration_rate': 0.1,
                'episodes': 1250,
                'avg_reward': 0.12
            }
            agent.get_last_action.return_value = 'SHORT'
            agent.get_confidence.return_value = 0.78
            yield agent
    
    @pytest.fixture
    def mock_portfolio_manager(self):
        """Mock Portfolio Manager for testing"""
        with patch('trading.portfolio_manager.PortfolioManager') as mock:
            manager = mock.return_value
            manager.get_portfolio_status.return_value = {
                'total_balance': 42.86,
                'available_balance': 37.18,
                'total_exposure': 426.01,
                'exposure_percentage': 991.0,
                'positions': 1,
                'daily_pnl': -0.96,
                'unrealized_pnl': -0.96
            }
            manager.get_risk_metrics.return_value = {
                'risk_level': 'MODERATE_RISK',
                'max_drawdown': 5.2,
                'sharpe_ratio': -0.15,
                'var_95': 2.1
            }
            yield manager
    
    def test_autonomous_controller_decision_display(self, app, mock_autonomous_controller):
        """Test GUI displays AutonomousController decisions and reasoning"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import MainWindow
                main_window = MainWindow()
        
        # Get decision from controller
        decision = mock_autonomous_controller.get_current_decision()
        
        # Test decision display in GUI
        # Look for decision display elements
        labels = main_window.findChildren(QLabel)
        decision_labels = [l for l in labels if 'decision' in l.objectName().lower()]
        
        # Verify decision information is available
        assert decision['action'] == 'SHORT'
        assert decision['confidence'] == 0.85
        assert 'bearish momentum' in decision['reasoning']
        
        main_window.close()
    
    def test_llm_confidence_display(self, app, mock_llm_orchestrator):
        """Test GUI shows LLM confidence and parsed structured output"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import MainWindow
                main_window = MainWindow()
        
        # Get LLM results
        results = mock_llm_orchestrator.get_latest_results()
        confidence = mock_llm_orchestrator.get_confidence_scores()
        
        # Test confidence display
        assert confidence['overall'] == 0.85
        assert confidence['risk_assessment'] == 0.85
        assert confidence['entry_timing'] == 0.75
        
        # Verify structured output parsing
        assert results['risk_assessment']['action'] == 'SHORT'
        assert results['entry_timing']['action'] == 'ENTER_NOW'
        assert results['opportunity_scanner']['action'] == 'FAVORABLE'
        
        main_window.close()
    
    def test_rl_agent_metrics_display(self, app, mock_rl_agent):
        """Test GUI shows RL agent metrics (learning rate, reward, exploration status)"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import MainWindow
                main_window = MainWindow()
        
        # Get RL agent metrics
        metrics = mock_rl_agent.get_metrics()
        
        # Test metrics display
        assert metrics['learning_rate'] == 0.001
        assert metrics['reward'] == 0.15
        assert metrics['exploration_rate'] == 0.1
        assert metrics['episodes'] == 1250
        
        # Verify action and confidence
        last_action = mock_rl_agent.get_last_action()
        confidence = mock_rl_agent.get_confidence()
        
        assert last_action == 'SHORT'
        assert confidence == 0.78
        
        main_window.close()
    
    def test_portfolio_exposure_display(self, app, mock_portfolio_manager):
        """Test GUI shows portfolio exposure vs. balance vs. current risk limits"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import MainWindow
                main_window = MainWindow()
        
        # Get portfolio status
        portfolio = mock_portfolio_manager.get_portfolio_status()
        risk_metrics = mock_portfolio_manager.get_risk_metrics()
        
        # Test portfolio display
        assert portfolio['total_balance'] == 42.86
        assert portfolio['total_exposure'] == 426.01
        assert portfolio['exposure_percentage'] == 991.0
        assert portfolio['daily_pnl'] == -0.96
        
        # Test risk metrics
        assert risk_metrics['risk_level'] == 'MODERATE_RISK'
        assert risk_metrics['max_drawdown'] == 5.2
        
        main_window.close()
    
    def test_symbol_scanner_updates(self, app):
        """Test GUI shows symbol scanner updates: top ranked pairs"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner') as mock_scanner:
                # Mock scanner results
                mock_scanner.return_value.get_top_symbols.return_value = [
                    {'symbol': 'DOGE/USDT:USDT', 'score': 0.85, 'setup': 'MEDIUM'},
                    {'symbol': 'BTC/USDT:USDT', 'score': 0.72, 'setup': 'LOW'},
                    {'symbol': 'ETH/USDT:USDT', 'score': 0.68, 'setup': 'LOW'}
                ]
                
                from gui.main_window import MainWindow
                main_window = MainWindow()
        
        # Test scanner display
        top_symbols = mock_scanner.return_value.get_top_symbols()
        
        assert len(top_symbols) == 3
        assert top_symbols[0]['symbol'] == 'DOGE/USDT:USDT'
        assert top_symbols[0]['score'] == 0.85
        
        main_window.close()
    
    def test_log_matching_validation(self, app):
        """Test UI logs match backend logs (session_manager & performance_tracker)"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                with patch('storage.session_manager.SessionManager') as mock_session:
                    with patch('core.performance_monitor.PerformanceMonitor') as mock_perf:
                        # Mock log entries
                        mock_session.return_value.get_session_logs.return_value = [
                            {'timestamp': '2025-06-29 15:51:35', 'level': 'INFO', 'message': 'Session started'},
                            {'timestamp': '2025-06-29 15:51:36', 'level': 'INFO', 'message': 'LLM analysis completed'}
                        ]
                        
                        mock_perf.return_value.get_performance_logs.return_value = [
                            {'timestamp': '2025-06-29 15:51:35', 'metric': 'cpu_usage', 'value': 15.2},
                            {'timestamp': '2025-06-29 15:51:36', 'metric': 'memory_usage', 'value': 45.8}
                        ]
                        
                        from gui.main_window import MainWindow
                        main_window = MainWindow()
                
                # Test log consistency
                session_logs = mock_session.return_value.get_session_logs()
                perf_logs = mock_perf.return_value.get_performance_logs()
                
                assert len(session_logs) == 2
                assert len(perf_logs) == 2
                assert session_logs[0]['message'] == 'Session started'
                
                main_window.close()
    
    def test_risk_warning_gui_highlights(self, app, mock_portfolio_manager):
        """Test risk warnings trigger GUI highlights / color changes"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import MainWindow
                main_window = MainWindow()
        
        # Simulate high risk condition
        mock_portfolio_manager.get_portfolio_status.return_value = {
            'exposure_percentage': 991.0,  # Very high exposure
            'daily_pnl': -5.2,  # Significant loss
            'risk_level': 'HIGH_RISK'
        }
        
        portfolio = mock_portfolio_manager.get_portfolio_status()
        
        # Test risk warning conditions
        assert portfolio['exposure_percentage'] > 80  # Should trigger warning
        assert portfolio['daily_pnl'] < -5  # Should trigger loss warning
        assert portfolio['risk_level'] == 'HIGH_RISK'  # Should trigger risk highlight
        
        main_window.close()

class TestRealTimeIntegration:
    """Test real-time integration between GUI and backend"""
    
    def test_real_time_data_flow(self, app):
        """Test real-time data flow from backend to GUI"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import MainWindow
                main_window = MainWindow()
        
        # Test timer-based updates
        timers = main_window.findChildren(QTimer)
        assert len(timers) >= 0  # Should have update timers
        
        main_window.close()
    
    def test_signal_slot_connections(self, app):
        """Test PyQt signal/slot connections for real-time updates"""
        with patch('gui.main_window.CredentialsManager'):
            with patch('gui.main_window.SymbolScanner'):
                from gui.main_window import MainWindow
                main_window = MainWindow()
        
        # Test signal connections exist
        # This would verify specific signal/slot connections
        assert True  # Placeholder for actual signal testing
        
        main_window.close()

if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
