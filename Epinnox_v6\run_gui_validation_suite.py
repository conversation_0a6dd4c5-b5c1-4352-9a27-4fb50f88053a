#!/usr/bin/env python3
"""
Comprehensive GUI Validation Suite Runner for Epinnox v6 Autonomous Trading System
Executes all GUI tests and provides complete validation report
"""

import sys
import os
import unittest
import time
from datetime import datetime
import subprocess

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_header():
    """Print validation suite header"""
    print("=" * 80)
    print("🧪 EPINNOX v6 GUI COMPREHENSIVE VALIDATION SUITE")
    print("=" * 80)
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Goal: Complete GUI interface testing and validation")
    print(f"📋 Scope: All GUI components and autonomous trading integration")
    print("=" * 80)
    print()

def run_test_module(module_name, description):
    """Run a specific test module and return results"""
    print(f"🧪 Running {description}...")
    print("-" * 60)
    
    try:
        # Import and run the test module
        module = __import__(f"tests.{module_name}", fromlist=[module_name])
        
        if hasattr(module, 'run_gui_tests'):
            success = module.run_gui_tests()
        elif hasattr(module, 'run_e2e_workflow_tests'):
            success = module.run_e2e_workflow_tests()
        elif hasattr(module, 'run_integration_validation_tests'):
            success = module.run_integration_validation_tests()
        else:
            # Run as standard unittest module
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromModule(module)
            runner = unittest.TextTestRunner(verbosity=1)
            result = runner.run(suite)
            success = result.wasSuccessful()
        
        print(f"✅ {description}: {'PASSED' if success else 'FAILED'}")
        return success
        
    except ImportError as e:
        print(f"⚠️  {description}: SKIPPED - {e}")
        return None
    except Exception as e:
        print(f"❌ {description}: ERROR - {e}")
        return False

def check_gui_dependencies():
    """Check if GUI dependencies are available"""
    print("🔍 Checking GUI Dependencies...")
    print("-" * 40)
    
    dependencies = {
        'PyQt5': False,
        'GUI Components': False,
        'Autonomous System': False
    }
    
    # Check PyQt5
    try:
        import PyQt5
        dependencies['PyQt5'] = True
        print("✅ PyQt5: Available")
    except ImportError:
        print("❌ PyQt5: Not available")
    
    # Check GUI components
    try:
        from gui.main_window import TradingSystemGUI
        dependencies['GUI Components'] = True
        print("✅ GUI Components: Available")
    except ImportError:
        print("❌ GUI Components: Not available")
    
    # Check autonomous system
    try:
        from core.autonomous_controller import AutonomousController
        dependencies['Autonomous System'] = True
        print("✅ Autonomous System: Available")
    except ImportError:
        print("❌ Autonomous System: Not available")
    
    print()
    return dependencies

def generate_validation_report(test_results, dependencies):
    """Generate comprehensive validation report"""
    print("=" * 80)
    print("📊 GUI VALIDATION COMPREHENSIVE REPORT")
    print("=" * 80)
    
    # Dependency Status
    print("🔧 DEPENDENCY STATUS:")
    for dep, status in dependencies.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {dep}")
    print()
    
    # Test Results Summary
    print("🧪 TEST RESULTS SUMMARY:")
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result is True)
    failed_tests = sum(1 for result in test_results.values() if result is False)
    skipped_tests = sum(1 for result in test_results.values() if result is None)
    
    for test_name, result in test_results.items():
        if result is True:
            print(f"  ✅ {test_name}: PASSED")
        elif result is False:
            print(f"  ❌ {test_name}: FAILED")
        else:
            print(f"  ⚠️  {test_name}: SKIPPED")
    
    print()
    print(f"📈 OVERALL STATISTICS:")
    print(f"  Total Tests: {total_tests}")
    print(f"  Passed: {passed_tests}")
    print(f"  Failed: {failed_tests}")
    print(f"  Skipped: {skipped_tests}")
    
    if total_tests > 0:
        success_rate = (passed_tests / (total_tests - skipped_tests)) * 100 if (total_tests - skipped_tests) > 0 else 0
        print(f"  Success Rate: {success_rate:.1f}%")
    
    # Overall Assessment
    print()
    print("🎯 OVERALL ASSESSMENT:")
    
    if failed_tests == 0 and passed_tests > 0:
        print("  🎉 EXCELLENT: All available tests passed!")
        assessment = "READY"
    elif failed_tests == 0 and passed_tests == 0:
        print("  ⚠️  WARNING: No tests could be executed (dependency issues)")
        assessment = "BLOCKED"
    elif failed_tests <= 2:
        print("  ✅ GOOD: Minor issues detected, mostly functional")
        assessment = "MOSTLY_READY"
    else:
        print("  ❌ CRITICAL: Multiple test failures detected")
        assessment = "NOT_READY"
    
    # Recommendations
    print()
    print("💡 RECOMMENDATIONS:")
    
    if not dependencies['PyQt5']:
        print("  🔧 Install PyQt5: pip install PyQt5")
    
    if not dependencies['GUI Components']:
        print("  🔧 GUI components need implementation/fixing")
    
    if not dependencies['Autonomous System']:
        print("  🔧 Autonomous system components need implementation")
    
    if failed_tests > 0:
        print("  🔧 Review failed tests and fix underlying issues")
        print("  🔧 Check integration between GUI and autonomous components")
    
    if skipped_tests > 0:
        print("  🔧 Resolve dependency issues to enable skipped tests")
    
    # Next Steps
    print()
    print("🚀 NEXT STEPS:")
    
    if assessment == "READY":
        print("  1. Deploy GUI interface for autonomous trading")
        print("  2. Conduct user acceptance testing")
        print("  3. Monitor performance in production")
    elif assessment == "MOSTLY_READY":
        print("  1. Fix remaining test failures")
        print("  2. Complete final validation")
        print("  3. Prepare for deployment")
    elif assessment == "BLOCKED":
        print("  1. Install missing dependencies")
        print("  2. Implement missing GUI components")
        print("  3. Re-run validation suite")
    else:
        print("  1. Review and fix critical test failures")
        print("  2. Implement missing functionality")
        print("  3. Strengthen integration between components")
    
    print("=" * 80)
    return assessment

def main():
    """Run comprehensive GUI validation suite"""
    print_header()
    
    # Check dependencies
    dependencies = check_gui_dependencies()
    
    # Define test modules to run
    test_modules = [
        ("test_gui_comprehensive", "GUI Component Unit Tests"),
        ("test_gui_e2e_workflows", "End-to-End Workflow Tests"),
        ("test_gui_autonomous_integration", "Autonomous Integration Tests"),
        ("test_gui", "Basic GUI Functionality Tests"),
        ("test_dashboard_interface", "Dashboard Interface Tests")
    ]
    
    # Run all test modules
    test_results = {}
    
    print("🧪 EXECUTING TEST MODULES:")
    print("=" * 60)
    
    for module_name, description in test_modules:
        result = run_test_module(module_name, description)
        test_results[description] = result
        print()
    
    # Generate comprehensive report
    assessment = generate_validation_report(test_results, dependencies)
    
    # Return appropriate exit code
    if assessment == "READY":
        return 0
    elif assessment == "MOSTLY_READY":
        return 1
    else:
        return 2

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  Validation suite interrupted by user")
        sys.exit(3)
    except Exception as e:
        print(f"\n❌ Validation suite failed with error: {e}")
        sys.exit(4)
