"""
Pytest configuration and fixtures for EPINNOX v6 testing
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@pytest.fixture
def sample_ohlcv_data():
    """Generate sample OHLCV data for testing"""
    dates = pd.date_range(start='2024-01-01', periods=1000, freq='1min')
    
    # Generate realistic price data with some volatility
    np.random.seed(42)
    base_price = 100.0
    returns = np.random.normal(0, 0.001, len(dates))
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Create OHLCV data
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        high = price * (1 + abs(np.random.normal(0, 0.002)))
        low = price * (1 - abs(np.random.normal(0, 0.002)))
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        volume = np.random.uniform(1000, 10000)
        
        data.append({
            'timestamp': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    return pd.DataFrame(data)

@pytest.fixture
def sample_trade_data():
    """Generate sample trade data for testing"""
    return {
        'timestamp': datetime.now(),
        'symbol': 'BTC/USDT',
        'decision': 'LONG',
        'confidence': 75.0,
        'entry_price': 50000.0,
        'exit_price': 51000.0,
        'position_size': 0.1,
        'leverage': 2.0,
        'pnl_usd': 200.0,
        'pnl_pct': 4.0,
        'duration_minutes': 30.0,
        'execution_status': 'FILLED',
        'trade_source': 'autonomous'
    }

@pytest.fixture
def mock_exchange():
    """Mock exchange for testing"""
    class MockExchange:
        def __init__(self):
            self.orders = []
            self.balance = {'USDT': {'free': 1000.0, 'used': 0.0, 'total': 1000.0}}
            self.positions = {}
        
        def create_order(self, symbol, type, side, amount, price=None, **kwargs):
            order_id = f"MOCK_{len(self.orders) + 1}"
            order = {
                'id': order_id,
                'symbol': symbol,
                'type': type,
                'side': side,
                'amount': amount,
                'price': price or 50000.0,
                'filled': amount,
                'average': price or 50000.0,
                'status': 'closed',
                'timestamp': datetime.now().timestamp() * 1000,
                'fee': {'cost': amount * 0.001, 'currency': 'USDT'}
            }
            self.orders.append(order)
            return order
        
        def fetch_balance(self):
            return self.balance
        
        def fetch_ticker(self, symbol):
            return {
                'symbol': symbol,
                'bid': 49950.0,
                'ask': 50050.0,
                'last': 50000.0,
                'baseVolume': 1000.0,
                'quoteVolume': 50000000.0
            }
        
        def fetch_order_book(self, symbol, limit=20):
            return {
                'bids': [[49950.0, 1.0], [49940.0, 2.0]],
                'asks': [[50050.0, 1.0], [50060.0, 2.0]]
            }
    
    return MockExchange()

@pytest.fixture
def sample_market_features():
    """Generate sample market features for ML testing"""
    return {
        'rsi': 65.0,
        'macd': 0.5,
        'bb_upper': 51000.0,
        'bb_lower': 49000.0,
        'volume_sma': 5000.0,
        'price_sma': 50000.0,
        'volatility': 0.02,
        'momentum': 0.01,
        'support_level': 49500.0,
        'resistance_level': 50500.0
    }

@pytest.fixture
def temp_db_path(tmp_path):
    """Temporary database path for testing"""
    return str(tmp_path / "test_trading.db")

@pytest.fixture
def sample_config():
    """Sample configuration for testing"""
    return {
        'initial_balance': 1000.0,
        'max_positions': 3,
        'min_confidence': 0.6,
        'use_rl': False,
        'base_delay': 30,
        'max_portfolio_risk': 0.15,
        'max_position_size': 0.08,
        'max_leverage': 5.0,
        'max_daily_loss': 0.03
    }

# Test data cleanup
@pytest.fixture(autouse=True)
def cleanup_test_files():
    """Automatically cleanup test files after each test"""
    yield
    # Cleanup any test files created during testing
    test_files = [
        'test_trading.db',
        'test_models.pkl',
        'test_performance.json'
    ]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)

# Logging configuration for tests
@pytest.fixture(autouse=True)
def configure_test_logging():
    """Configure logging for tests"""
    import logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise during testing
    yield
    # Reset logging after tests
    logging.getLogger().handlers.clear()
