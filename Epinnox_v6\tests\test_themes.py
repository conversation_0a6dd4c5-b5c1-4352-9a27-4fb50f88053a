#!/usr/bin/env python3
"""
Test script to demonstrate theme switching and layout reset functionality
"""
import time
import json
from datetime import datetime
from pathlib import Path

def update_gui_with_matrix_demo():
    """Update GUI with demo data optimized for Matrix theme"""
    
    current_time = datetime.now()
    
    # Matrix-themed demo data
    matrix_data = {
        'timestamp': current_time.isoformat(),
        'market_data': {
            'symbol': 'DOGE/USDT',
            'price': 0.1762,
            'volume': 2500000,
            'change_24h': 0.0125,
            'change_percent': 1.25,
            'high_24h': 0.178,
            'low_24h': 0.172,
            'order_book': {
                'bids': [[0.1761, 1000], [0.1760, 1500]],
                'asks': [[0.1763, 1200], [0.1764, 800]],
                'spread': 0.0002,
                'imbalance': 0.0
            },
            'recent_trades': [
                ['13:08:41', 0.1762, 681.49, 'buy'],
                ['13:08:40', 0.1762, 371.83, 'sell'],
                ['13:08:39', 0.1761, 341.17, 'buy']
            ]
        },
        'timeframe_analysis': {
            'timeframes': ['1m', '5m', '15m'],
            'trends': {
                '1m': {'direction': 'bullish', 'strength': 0.85},
                '5m': {'direction': 'strong_bullish', 'strength': 1.0},
                '15m': {'direction': 'bullish', 'strength': 0.75}
            },
            'alignment_percentage': 87,
            'overall_trend': 'strong_bullish',
            'trend_strength': 0.87
        },
        'signal_scoring': {
            'individual_scores': {
                'macd': 0.045,
                'order_book': 0.0,
                'volume': 0.032,
                'price_action': 0.067,
                'trend': 0.089
            },
            'total_score': 0.233,
            'confidence': 87.3,
            'alignment_percentage': 87.0,
            'signal_strength': 'strong'
        },
        'market_regime': {
            'current_regime': 'trending',
            'volatility': 0.0045,
            'trend_strength': 0.87,
            'adjustments': {
                'leverage_factor': 0.9234,
                'position_size_factor': 0.9567,
                'stop_loss_factor': 0.8934,
                'take_profit_factor': 1.1234,
                'entry_confidence': 0.8734
            }
        },
        'ai_analysis': {
            'decision': 'LONG',
            'confidence': 87.3,
            'reasoning': 'MATRIX PROTOCOL ANALYSIS: Strong bullish momentum detected across all timeframes. Multi-dimensional signal convergence at 87% alignment indicates high probability upward trajectory. Quantum flux indicators show positive energy flow. Risk parameters optimized for maximum profit extraction. Execute LONG position with confidence. The digital rain flows in our favor.',
            'take_profit': 0.325,
            'stop_loss': 0.145,
            'model_info': 'MATRIX-AI-PROTOCOL v3.0',
            'analysis_timestamp': current_time.isoformat()
        },
        'risk_management': {
            'adaptive_stop_loss': 0.145,
            'adaptive_take_profit': 0.325,
            'position_size': 100.0,
            'atr_volatility': 0.18,
            'risk_score': 2.1,
            'max_position_size': 100.0
        },
        'system_status': {
            'connected': True,
            'uptime': '02:15:33',
            'cpu_usage': 15.7,
            'memory_usage': 42.3,
            'network_latency': 23,
            'last_heartbeat': current_time.isoformat()
        }
    }
    
    # Read existing data
    gui_data = {}
    if Path('gui_data.json').exists():
        with open('gui_data.json', 'r') as f:
            gui_data = json.load(f)
    
    # Update with Matrix demo data
    gui_data.update(matrix_data)
    
    # Save back
    with open('gui_data.json', 'w') as f:
        json.dump(gui_data, f, indent=2)
    
    print("🟢 MATRIX PROTOCOL ACTIVATED")
    print(f"🎯 Decision: {matrix_data['ai_analysis']['decision']}")
    print(f"🔥 Confidence: {matrix_data['ai_analysis']['confidence']}%")
    print(f"📊 Signal Strength: {matrix_data['signal_scoring']['signal_strength']}")
    print(f"⚡ Trend: {matrix_data['timeframe_analysis']['overall_trend']}")
    print("🔄 GUI updated with Matrix-optimized data")

def main():
    """Main function"""
    print("🚀 EPINNOX MATRIX THEME DEMO")
    print("=" * 50)
    print("🎨 Testing Matrix theme and layout reset functionality")
    print("📊 Updating GUI with Matrix-optimized demo data")
    print("=" * 50)
    
    update_gui_with_matrix_demo()
    
    print("\n✅ Demo complete!")
    print("\n📋 Instructions:")
    print("1. 🎨 Change theme to 'Matrix' in the GUI header dropdown")
    print("2. 🔄 Use 'Reset Layout' button to restore default panel positions")
    print("3. 🖱️ Drag panels around to test moveable functionality")
    print("4. 📏 Resize panels by dragging borders")
    print("5. 🎯 Notice how all components adapt to Matrix theme colors")

if __name__ == "__main__":
    main()
