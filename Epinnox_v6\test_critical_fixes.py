#!/usr/bin/env python3
"""
Test script for critical method fixes in Epinnox v6
Tests the enhanced methods without requiring GUI initialization
"""

import sys
import os
sys.path.append('.')

def test_position_sizing_logic():
    """Test the simplified position sizing logic"""
    print("🧪 Testing position sizing logic...")
    
    # Test the core logic without GUI dependencies
    def calculate_confidence_based_position_size(balance, price, confidence, decision):
        """Simplified position sizing logic for testing"""
        # Input validation
        if not all([balance > 0, price > 0, 0 <= confidence <= 100]):
            print(f"❌ Invalid inputs: balance={balance}, price={price}, confidence={confidence}")
            return 0.0
        
        try:
            # Constants for position sizing
            MIN_POSITION_VALUE = 10.0  # Minimum $10 position
            MAX_RISK_PCT = 3.0  # Maximum 3% risk per trade
            MIN_RISK_PCT = 1.0  # Minimum 1% risk per trade
            
            # Calculate risk percentage based on confidence (1-3%)
            risk_pct = MIN_RISK_PCT + (confidence / 100) * (MAX_RISK_PCT - MIN_RISK_PCT)
            
            # Calculate leverage based on confidence (3-20x)
            leverage = max(3, min(20, int(3 + (confidence / 100) * 17)))
            
            # Calculate base position value
            position_value = balance * (risk_pct / 100) * leverage
            
            # Ensure minimum position value
            position_value = max(position_value, MIN_POSITION_VALUE)
            
            # Convert to quantity
            base_size = position_value / price
            
            # Apply confidence multiplier for final adjustment
            def get_confidence_multiplier(confidence):
                if confidence >= 95:
                    return 1.2  # Increase size for very high confidence
                elif confidence >= 90:
                    return 1.1  # Slight increase for high confidence
                elif confidence >= 85:
                    return 1.0  # Normal size for good confidence
                elif confidence >= 80:
                    return 0.8  # Reduce size for moderate confidence
                else:
                    return 0.6  # Significantly reduce for low confidence
            
            confidence_multiplier = get_confidence_multiplier(confidence)
            final_size = base_size * confidence_multiplier
            
            print(f"📏 Position sizing: Risk={risk_pct:.1f}%, Leverage={leverage}x, "
                  f"Value=${position_value:.2f}, Size={final_size:.4f}")
            
            return final_size
            
        except Exception as e:
            print(f"❌ Error calculating position size: {e}")
            # Return safe fallback
            return MIN_POSITION_VALUE / price if price > 0 else 0.0
    
    # Test cases
    test_cases = [
        # (balance, price, confidence, decision, expected_behavior)
        (100.0, 0.35, 85.0, 'LONG', 'normal_case'),
        (50.0, 0.35, 95.0, 'LONG', 'high_confidence'),
        (100.0, 0.35, 75.0, 'LONG', 'low_confidence'),
        (0, 0.35, 85.0, 'LONG', 'zero_balance'),
        (100.0, 0, 85.0, 'LONG', 'zero_price'),
        (100.0, 0.35, -10, 'LONG', 'invalid_confidence'),
        (100.0, 0.35, 110, 'LONG', 'invalid_confidence_high'),
    ]
    
    for i, (balance, price, confidence, decision, expected) in enumerate(test_cases):
        print(f"\n🧪 Test {i+1}: {expected}")
        result = calculate_confidence_based_position_size(balance, price, confidence, decision)
        print(f"   Result: {result:.6f}")
        
        # Validate results
        if expected == 'zero_balance' or expected == 'zero_price' or 'invalid_confidence' in expected:
            if result == 0.0:
                print("   ✅ Correctly returned 0 for invalid input")
            else:
                print("   ❌ Should have returned 0 for invalid input")
        else:
            if result > 0:
                print("   ✅ Returned valid position size")
            else:
                print("   ❌ Should have returned positive position size")
    
    print("\n✅ Position sizing logic tests completed!")

def test_validation_logic():
    """Test order validation logic"""
    print("\n🧪 Testing order validation logic...")
    
    def validate_basic_inputs(symbol, quantity, leverage, price=None):
        """Basic validation logic for testing"""
        try:
            # 1. Symbol validation
            if not symbol or not isinstance(symbol, str) or len(symbol.strip()) == 0:
                return False, "Invalid symbol: must be a non-empty string"
            
            # Check symbol format (should contain '/' and ':')
            if '/' not in symbol or ':' not in symbol:
                return False, f"Invalid symbol format: {symbol}. Expected format: 'BASE/QUOTE:SETTLE'"

            # 2. Quantity validation
            if not isinstance(quantity, (int, float)) or quantity <= 0:
                return False, "Quantity must be a positive number greater than 0"
            
            # Check maximum position size
            max_quantity = 10000
            if quantity > max_quantity:
                return False, f"Quantity {quantity} exceeds maximum allowed {max_quantity}"

            # 3. Leverage validation
            if not isinstance(leverage, (int, float)) or leverage < 1 or leverage > 125:
                return False, "Leverage must be between 1 and 125"

            # 4. Price validation (if provided)
            if price is not None:
                if not isinstance(price, (int, float)) or price <= 0:
                    return False, "Price must be a positive number greater than 0"

            return True, "All validations passed successfully"
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    # Test cases for validation
    validation_tests = [
        # (symbol, quantity, leverage, price, expected_valid)
        ('DOGE/USDT:USDT', 100.0, 10, 0.35, True),
        ('', 100.0, 10, 0.35, False),  # Empty symbol
        ('INVALID', 100.0, 10, 0.35, False),  # Invalid symbol format
        ('DOGE/USDT:USDT', 0, 10, 0.35, False),  # Zero quantity
        ('DOGE/USDT:USDT', -100, 10, 0.35, False),  # Negative quantity
        ('DOGE/USDT:USDT', 100.0, 0, 0.35, False),  # Zero leverage
        ('DOGE/USDT:USDT', 100.0, 200, 0.35, False),  # Excessive leverage
        ('DOGE/USDT:USDT', 100.0, 10, 0, False),  # Zero price
        ('DOGE/USDT:USDT', 100.0, 10, -0.35, False),  # Negative price
    ]
    
    for i, (symbol, quantity, leverage, price, expected_valid) in enumerate(validation_tests):
        print(f"\n🧪 Validation Test {i+1}:")
        is_valid, message = validate_basic_inputs(symbol, quantity, leverage, price)
        
        if is_valid == expected_valid:
            print(f"   ✅ Expected: {expected_valid}, Got: {is_valid}")
        else:
            print(f"   ❌ Expected: {expected_valid}, Got: {is_valid}")
        
        print(f"   Message: {message}")
    
    print("\n✅ Validation logic tests completed!")

def test_real_trading_interface_methods():
    """Test that RealTradingInterface has required methods"""
    print("\n🧪 Testing RealTradingInterface methods...")
    
    try:
        from trading.real_trading_interface import RealTradingInterface
        
        required_methods = [
            'place_limit_long',
            'place_market_long', 
            'get_balance_info',
            'disconnect',
            'is_connected'
        ]
        
        for method in required_methods:
            if hasattr(RealTradingInterface, method):
                print(f"   ✅ {method} exists")
            else:
                print(f"   ❌ {method} missing")
        
        print("✅ RealTradingInterface method tests completed!")
        
    except ImportError as e:
        print(f"❌ Could not import RealTradingInterface: {e}")

def main():
    """Run all tests"""
    print("🚀 Running Critical Method Fixes Tests")
    print("=" * 50)
    
    test_position_sizing_logic()
    test_validation_logic()
    test_real_trading_interface_methods()
    
    print("\n" + "=" * 50)
    print("✅ All critical method tests completed!")

if __name__ == "__main__":
    main()
