"""
Core Main Module for EPINNOX v6
Central processing functions for market data and trading decisions
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

def process_market_data(ohlcv_data: pd.DataFrame, 
                       symbol: str = "BTC/USDT",
                       config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Process market data and generate trading signals
    
    Args:
        ohlcv_data: OHLCV DataFrame with columns [open, high, low, close, volume]
        symbol: Trading symbol
        config: Configuration dictionary
        
    Returns:
        Dictionary containing processed data and signals
    """
    try:
        if ohlcv_data is None or ohlcv_data.empty:
            logger.warning("Empty market data provided")
            return {
                "success": False,
                "error": "Empty market data",
                "symbol": symbol,
                "timestamp": datetime.now()
            }
        
        # Validate required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in ohlcv_data.columns]
        
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            return {
                "success": False,
                "error": f"Missing columns: {missing_columns}",
                "symbol": symbol,
                "timestamp": datetime.now()
            }
        
        # Basic data validation
        if len(ohlcv_data) < 20:
            logger.warning(f"Insufficient data points: {len(ohlcv_data)}")
            return {
                "success": False,
                "error": "Insufficient data points",
                "symbol": symbol,
                "timestamp": datetime.now()
            }
        
        # Calculate basic technical indicators
        indicators = calculate_technical_indicators(ohlcv_data)
        
        # Generate trading signals
        signals = generate_trading_signals(ohlcv_data, indicators, config)
        
        # Calculate market metrics
        metrics = calculate_market_metrics(ohlcv_data)
        
        # Prepare result
        result = {
            "success": True,
            "symbol": symbol,
            "timestamp": datetime.now(),
            "data_points": len(ohlcv_data),
            "current_price": float(ohlcv_data['close'].iloc[-1]),
            "indicators": indicators,
            "signals": signals,
            "metrics": metrics,
            "raw_data": ohlcv_data.tail(5).to_dict('records')  # Last 5 candles
        }
        
        logger.info(f"Successfully processed market data for {symbol}")
        return result
        
    except Exception as e:
        logger.error(f"Error processing market data for {symbol}: {e}")
        return {
            "success": False,
            "error": str(e),
            "symbol": symbol,
            "timestamp": datetime.now()
        }

def calculate_technical_indicators(ohlcv_data: pd.DataFrame) -> Dict[str, Any]:
    """
    Calculate technical indicators from OHLCV data
    
    Args:
        ohlcv_data: OHLCV DataFrame
        
    Returns:
        Dictionary of technical indicators
    """
    try:
        close = ohlcv_data['close']
        high = ohlcv_data['high']
        low = ohlcv_data['low']
        volume = ohlcv_data['volume']
        
        # Simple Moving Averages
        sma_20 = close.rolling(window=20).mean()
        sma_50 = close.rolling(window=50).mean() if len(close) >= 50 else None
        
        # Exponential Moving Average
        ema_12 = close.ewm(span=12).mean()
        ema_26 = close.ewm(span=26).mean()
        
        # RSI
        rsi = calculate_rsi(close)
        
        # MACD
        macd_line = ema_12 - ema_26
        macd_signal = macd_line.ewm(span=9).mean()
        macd_histogram = macd_line - macd_signal
        
        # Bollinger Bands
        bb_middle = sma_20
        bb_std = close.rolling(window=20).std()
        bb_upper = bb_middle + (bb_std * 2)
        bb_lower = bb_middle - (bb_std * 2)
        
        # Volume indicators
        volume_sma = volume.rolling(window=20).mean()
        volume_ratio = volume.iloc[-1] / volume_sma.iloc[-1] if not volume_sma.empty else 1.0
        
        # Current values
        current_price = close.iloc[-1]
        
        indicators = {
            "sma_20": float(sma_20.iloc[-1]) if not sma_20.empty else None,
            "sma_50": float(sma_50.iloc[-1]) if sma_50 is not None and not sma_50.empty else None,
            "ema_12": float(ema_12.iloc[-1]) if not ema_12.empty else None,
            "ema_26": float(ema_26.iloc[-1]) if not ema_26.empty else None,
            "rsi": float(rsi.iloc[-1]) if not rsi.empty else None,
            "macd": float(macd_line.iloc[-1]) if not macd_line.empty else None,
            "macd_signal": float(macd_signal.iloc[-1]) if not macd_signal.empty else None,
            "macd_histogram": float(macd_histogram.iloc[-1]) if not macd_histogram.empty else None,
            "bb_upper": float(bb_upper.iloc[-1]) if not bb_upper.empty else None,
            "bb_middle": float(bb_middle.iloc[-1]) if not bb_middle.empty else None,
            "bb_lower": float(bb_lower.iloc[-1]) if not bb_lower.empty else None,
            "volume_ratio": float(volume_ratio),
            "current_price": float(current_price)
        }
        
        return indicators
        
    except Exception as e:
        logger.error(f"Error calculating technical indicators: {e}")
        return {}

def calculate_rsi(prices: pd.Series, period: int = 14) -> pd.Series:
    """Calculate RSI (Relative Strength Index)"""
    try:
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    except Exception as e:
        logger.error(f"Error calculating RSI: {e}")
        return pd.Series()

def generate_trading_signals(ohlcv_data: pd.DataFrame, 
                           indicators: Dict[str, Any],
                           config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Generate trading signals based on technical indicators
    
    Args:
        ohlcv_data: OHLCV DataFrame
        indicators: Technical indicators
        config: Configuration dictionary
        
    Returns:
        Dictionary of trading signals
    """
    try:
        signals = {
            "decision": "WAIT",
            "confidence": 0.0,
            "reasons": [],
            "strength": "WEAK"
        }
        
        if not indicators:
            return signals
        
        score = 0
        reasons = []
        
        # RSI signals
        rsi = indicators.get("rsi")
        if rsi is not None:
            if rsi < 30:
                score += 1
                reasons.append("RSI oversold")
            elif rsi > 70:
                score -= 1
                reasons.append("RSI overbought")
        
        # MACD signals
        macd = indicators.get("macd")
        macd_signal = indicators.get("macd_signal")
        if macd is not None and macd_signal is not None:
            if macd > macd_signal:
                score += 1
                reasons.append("MACD bullish")
            else:
                score -= 1
                reasons.append("MACD bearish")
        
        # Moving average signals
        current_price = indicators.get("current_price")
        sma_20 = indicators.get("sma_20")
        if current_price is not None and sma_20 is not None:
            if current_price > sma_20:
                score += 1
                reasons.append("Price above SMA20")
            else:
                score -= 1
                reasons.append("Price below SMA20")
        
        # Volume confirmation
        volume_ratio = indicators.get("volume_ratio", 1.0)
        if volume_ratio > 1.5:
            score += 0.5
            reasons.append("High volume")
        
        # Determine decision
        if score >= 2:
            signals["decision"] = "LONG"
            signals["strength"] = "STRONG" if score >= 3 else "MEDIUM"
        elif score <= -2:
            signals["decision"] = "SHORT"
            signals["strength"] = "STRONG" if score <= -3 else "MEDIUM"
        
        # Calculate confidence
        signals["confidence"] = min(abs(score) / 4.0, 1.0)
        signals["reasons"] = reasons
        signals["score"] = score
        
        return signals
        
    except Exception as e:
        logger.error(f"Error generating trading signals: {e}")
        return {
            "decision": "WAIT",
            "confidence": 0.0,
            "reasons": ["Error in signal generation"],
            "strength": "WEAK"
        }

def calculate_market_metrics(ohlcv_data: pd.DataFrame) -> Dict[str, Any]:
    """
    Calculate market metrics and statistics
    
    Args:
        ohlcv_data: OHLCV DataFrame
        
    Returns:
        Dictionary of market metrics
    """
    try:
        close = ohlcv_data['close']
        high = ohlcv_data['high']
        low = ohlcv_data['low']
        volume = ohlcv_data['volume']
        
        # Price metrics
        current_price = close.iloc[-1]
        price_change_24h = ((current_price - close.iloc[-24]) / close.iloc[-24]) * 100 if len(close) >= 24 else 0
        
        # Volatility
        returns = close.pct_change().dropna()
        volatility = returns.std() * np.sqrt(24)  # 24-hour volatility
        
        # Volume metrics
        avg_volume = volume.mean()
        volume_change = ((volume.iloc[-1] - avg_volume) / avg_volume) * 100
        
        # Price range
        high_24h = high.tail(24).max() if len(high) >= 24 else high.max()
        low_24h = low.tail(24).min() if len(low) >= 24 else low.min()
        
        metrics = {
            "current_price": float(current_price),
            "price_change_24h": float(price_change_24h),
            "volatility": float(volatility),
            "volume_change": float(volume_change),
            "high_24h": float(high_24h),
            "low_24h": float(low_24h),
            "avg_volume": float(avg_volume),
            "data_quality": "GOOD" if len(ohlcv_data) >= 100 else "LIMITED"
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Error calculating market metrics: {e}")
        return {}
