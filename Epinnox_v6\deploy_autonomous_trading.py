#!/usr/bin/env python3
"""
Autonomous Trading System Deployment Script
Production deployment for fully autonomous trading operations
"""

import asyncio
import logging
import signal
import sys
import yaml
import argparse
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'autonomous_trading_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutonomousTradingDeployment:
    """
    Main deployment class for autonomous trading system
    """
    
    def __init__(self, config_path: str, mode: str = 'simulation'):
        self.config_path = config_path
        self.mode = mode
        self.config = None
        self.orchestrator = None
        self.safety_monitor = None
        self.running = False
        
        # Load configuration
        self._load_configuration()
        
        logger.info(f"Autonomous Trading Deployment initialized in {mode} mode")
    
    def _load_configuration(self):
        """Load system configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            
            # Override mode in config
            self.config['trading_mode'] = self.mode
            
            logger.info(f"Configuration loaded from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            sys.exit(1)
    
    async def initialize_system(self):
        """Initialize all system components"""
        try:
            logger.info("🚀 Initializing Autonomous Trading System...")
            
            # Initialize orchestrator
            from core.autonomous_trading_orchestrator import AutonomousTradingOrchestrator
            self.orchestrator = AutonomousTradingOrchestrator(self.config)
            
            # Initialize safety monitor
            from core.safety_monitor import SafetyMonitor
            self.safety_monitor = SafetyMonitor(self.config)
            
            # Add emergency callback to safety monitor
            self.safety_monitor.add_emergency_callback(self._emergency_callback)
            
            # Initialize orchestrator components
            success = await self.orchestrator.initialize()
            if not success:
                raise Exception("Failed to initialize orchestrator")
            
            logger.info("✅ System initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return False
    
    async def start_autonomous_trading(self):
        """Start autonomous trading operations"""
        try:
            logger.info("🤖 Starting Autonomous Trading Operations...")
            
            # Start safety monitoring
            safety_task = asyncio.create_task(self.safety_monitor.start_monitoring())
            
            # Start autonomous trading
            trading_task = asyncio.create_task(self.orchestrator.start_autonomous_trading())
            
            # Start status reporting
            status_task = asyncio.create_task(self._status_reporting_loop())
            
            self.running = True
            
            # Wait for all tasks
            await asyncio.gather(safety_task, trading_task, status_task)
            
        except Exception as e:
            logger.error(f"Error in autonomous trading: {e}")
        finally:
            await self._shutdown()
    
    async def _status_reporting_loop(self):
        """Periodic status reporting"""
        while self.running:
            try:
                # Get system status
                orchestrator_status = self.orchestrator.get_status()
                safety_status = self.safety_monitor.get_system_status()
                
                # Log status summary
                logger.info(f"📊 STATUS: State={orchestrator_status['state']}, "
                           f"Cycles={orchestrator_status['cycle_count']}, "
                           f"Alerts={safety_status['active_alerts_count']}")
                
                # Check for critical alerts
                if safety_status['critical_alerts_count'] > 0:
                    logger.warning(f"⚠️  {safety_status['critical_alerts_count']} critical alerts active")
                
                await asyncio.sleep(60)  # Report every minute
                
            except Exception as e:
                logger.error(f"Error in status reporting: {e}")
                await asyncio.sleep(10)
    
    async def _emergency_callback(self, alert):
        """Handle emergency alerts"""
        logger.critical(f"🚨 EMERGENCY ALERT: {alert.message}")
        
        # Trigger emergency stop in orchestrator
        if self.orchestrator:
            await self.orchestrator._trigger_emergency_stop(f"Safety alert: {alert.rule.value}")
    
    async def _shutdown(self):
        """Graceful shutdown procedure"""
        logger.info("🛑 Initiating graceful shutdown...")
        
        self.running = False
        
        # Stop orchestrator
        if self.orchestrator:
            await self.orchestrator.stop()
        
        # Stop safety monitor
        if self.safety_monitor:
            self.safety_monitor.stop_monitoring()
        
        logger.info("✅ Shutdown completed")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, initiating shutdown...")
        asyncio.create_task(self._shutdown())

def create_default_config():
    """Create default configuration file"""
    config = {
        'trading_mode': 'simulation',
        'symbols': ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'DOGE/USDT:USDT'],
        'initial_balance': 10000.0,
        
        # Risk management
        'max_daily_loss': -500.0,
        'max_position_size': 1000.0,
        'max_leverage': 10.0,
        'max_open_positions': 5,
        'min_account_balance': 100.0,
        'max_drawdown': -20.0,
        
        # LLM configuration
        'llm_config': {
            'model': 'phi-3.1-mini',
            'temperature': 0.3,
            'max_tokens': 512
        },
        
        # Exchange configuration
        'exchange': {
            'name': 'htx',
            'demo_mode': True
        },
        
        # Monitoring
        'monitoring': {
            'health_check_interval': 30,
            'metrics_collection_interval': 10,
            'alert_processing_interval': 5
        }
    }
    
    return config

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Deploy Autonomous Trading System')
    parser.add_argument('--config', default='config/autonomous_trading.yaml',
                       help='Configuration file path')
    parser.add_argument('--mode', choices=['simulation', 'paper', 'live'],
                       default='simulation', help='Trading mode')
    parser.add_argument('--create-config', action='store_true',
                       help='Create default configuration file')
    
    args = parser.parse_args()
    
    # Create default config if requested
    if args.create_config:
        config = create_default_config()
        config_path = Path(args.config)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        
        print(f"✅ Default configuration created at {config_path}")
        return
    
    # Check if config file exists
    if not Path(args.config).exists():
        print(f"❌ Configuration file not found: {args.config}")
        print(f"Create one with: python {sys.argv[0]} --create-config")
        sys.exit(1)
    
    # Validate mode for live trading
    if args.mode == 'live':
        response = input("⚠️  You are about to start LIVE trading with real money. "
                        "Are you sure? (type 'YES' to confirm): ")
        if response != 'YES':
            print("Live trading cancelled.")
            sys.exit(0)
    
    # Create deployment instance
    deployment = AutonomousTradingDeployment(args.config, args.mode)
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, deployment._signal_handler)
    signal.signal(signal.SIGTERM, deployment._signal_handler)
    
    # Run the system
    async def run():
        try:
            # Initialize system
            if not await deployment.initialize_system():
                logger.error("System initialization failed")
                sys.exit(1)
            
            # Pre-flight checks
            logger.info("🔍 Performing pre-flight checks...")
            
            # Check system health
            if deployment.orchestrator.state.value != 'running':
                logger.error("System not in running state")
                sys.exit(1)
            
            # Final confirmation for live trading
            if args.mode == 'live':
                logger.warning("🚨 LIVE TRADING MODE - Real money will be used!")
                await asyncio.sleep(5)  # Give time to cancel
            
            logger.info("✅ Pre-flight checks passed")
            logger.info(f"🚀 Starting autonomous trading in {args.mode} mode...")
            
            # Start autonomous trading
            await deployment.start_autonomous_trading()
            
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
        finally:
            await deployment._shutdown()
    
    # Run the async main function
    try:
        asyncio.run(run())
    except KeyboardInterrupt:
        logger.info("Deployment interrupted by user")
    except Exception as e:
        logger.error(f"Deployment failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
