"""
Simulation Executor
Handles trade execution in simulation/paper trading mode
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SimulatedOrder:
    id: str
    symbol: str
    side: str
    amount: float
    price: float
    leverage: float
    timestamp: datetime
    status: str = "filled"
    
class SimulationExecutor:
    """
    Simulates trade execution for paper trading and backtesting
    """
    
    def __init__(self, initial_balance: float = 10000.0):
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.positions = {}
        self.orders = {}
        self.order_counter = 0
        
        # Simulation parameters
        self.slippage = 0.001  # 0.1% slippage
        self.commission = 0.0005  # 0.05% commission
        
        logger.info(f"Simulation executor initialized with ${initial_balance} balance")
    
    async def execute_decision(self, decision) -> Dict[str, Any]:
        """Execute a trading decision in simulation"""
        try:
            # Generate order ID
            self.order_counter += 1
            order_id = f"sim_{self.order_counter}_{int(time.time())}"
            
            # Simulate market price with slippage
            market_price = await self._get_simulated_price(decision.symbol)
            if decision.action == 'BUY':
                execution_price = market_price * (1 + self.slippage)
            else:
                execution_price = market_price * (1 - self.slippage)
            
            # Calculate position value and required margin
            position_value = decision.position_size
            margin_required = position_value / decision.leverage
            
            # Check if we have enough balance
            if margin_required > self.balance:
                return {
                    'success': False,
                    'error': 'Insufficient balance',
                    'required_margin': margin_required,
                    'available_balance': self.balance
                }
            
            # Create simulated order
            order = SimulatedOrder(
                id=order_id,
                symbol=decision.symbol,
                side=decision.action,
                amount=position_value / execution_price,
                price=execution_price,
                leverage=decision.leverage,
                timestamp=datetime.now()
            )
            
            # Update balance and positions
            self.balance -= margin_required
            
            # Calculate commission
            commission = position_value * self.commission
            self.balance -= commission
            
            # Store order
            self.orders[order_id] = order
            
            # Update position
            if decision.symbol not in self.positions:
                self.positions[decision.symbol] = {
                    'size': 0,
                    'entry_price': 0,
                    'leverage': decision.leverage,
                    'margin': 0,
                    'unrealized_pnl': 0
                }
            
            position = self.positions[decision.symbol]
            
            if decision.action == 'BUY':
                if position['size'] < 0:  # Closing short position
                    # Calculate PnL for closing position
                    pnl = abs(position['size']) * (position['entry_price'] - execution_price)
                    self.balance += pnl + position['margin']
                    
                    remaining_size = order.amount + position['size']
                    if remaining_size > 0:
                        # Opening new long position
                        position['size'] = remaining_size
                        position['entry_price'] = execution_price
                        position['margin'] = margin_required
                    else:
                        # Position fully closed
                        position['size'] = 0
                        position['margin'] = 0
                else:
                    # Adding to long position or opening new long
                    total_value = (position['size'] * position['entry_price']) + (order.amount * execution_price)
                    total_size = position['size'] + order.amount
                    position['entry_price'] = total_value / total_size if total_size > 0 else execution_price
                    position['size'] = total_size
                    position['margin'] += margin_required
                    
            elif decision.action == 'SELL':
                if position['size'] > 0:  # Closing long position
                    # Calculate PnL for closing position
                    pnl = abs(position['size']) * (execution_price - position['entry_price'])
                    self.balance += pnl + position['margin']
                    
                    remaining_size = position['size'] - order.amount
                    if remaining_size < 0:
                        # Opening new short position
                        position['size'] = remaining_size
                        position['entry_price'] = execution_price
                        position['margin'] = margin_required
                    else:
                        # Position partially closed or fully closed
                        if remaining_size == 0:
                            position['size'] = 0
                            position['margin'] = 0
                        else:
                            position['size'] = remaining_size
                            position['margin'] = position['margin'] * (remaining_size / (remaining_size + order.amount))
                else:
                    # Adding to short position or opening new short
                    total_value = (abs(position['size']) * position['entry_price']) + (order.amount * execution_price)
                    total_size = abs(position['size']) + order.amount
                    position['entry_price'] = total_value / total_size if total_size > 0 else execution_price
                    position['size'] = -total_size
                    position['margin'] += margin_required
            
            logger.info(f"✅ Simulated {decision.action} order executed: {order.amount:.6f} {decision.symbol} @ ${execution_price:.2f}")
            
            return {
                'success': True,
                'order_id': order_id,
                'execution_price': execution_price,
                'amount': order.amount,
                'commission': commission,
                'balance_after': self.balance,
                'position': position.copy()
            }
            
        except Exception as e:
            logger.error(f"Error executing simulated order: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _get_simulated_price(self, symbol: str) -> float:
        """Get simulated market price for a symbol"""
        # For simulation, use some base prices
        base_prices = {
            'BTC/USDT:USDT': 50000.0,
            'ETH/USDT:USDT': 3000.0,
            'DOGE/USDT:USDT': 0.08,
            'ADA/USDT:USDT': 0.5,
            'SOL/USDT:USDT': 100.0
        }
        
        base_price = base_prices.get(symbol, 1000.0)
        
        # Add some random variation (±2%)
        import random
        variation = random.uniform(-0.02, 0.02)
        return base_price * (1 + variation)
    
    async def get_balance(self) -> Dict[str, float]:
        """Get current balance"""
        return {
            'USDT': self.balance,
            'total': self.balance
        }
    
    async def get_positions(self) -> Dict[str, Dict]:
        """Get current positions"""
        return self.positions.copy()
    
    async def cancel_all_orders(self, reason: str = ""):
        """Cancel all pending orders (no-op in simulation)"""
        logger.info(f"Simulation: Cancel all orders requested - {reason}")
        return True
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        total_pnl = self.balance - self.initial_balance
        return {
            'initial_balance': self.initial_balance,
            'current_balance': self.balance,
            'total_pnl': total_pnl,
            'total_pnl_pct': (total_pnl / self.initial_balance) * 100,
            'total_orders': len(self.orders),
            'open_positions': len([p for p in self.positions.values() if p['size'] != 0])
        }
