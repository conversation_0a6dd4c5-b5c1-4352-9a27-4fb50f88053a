"""
Test runner script for the Epinnox Trading System.
This script runs all the unit and integration tests.
"""
import unittest
import sys
import os
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def run_tests():
    """Run all the tests."""
    # Discover and run all tests
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover('tests', pattern='test_*.py')
    
    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Return the result
    return result

def run_specific_test(test_name):
    """Run a specific test."""
    # Discover and run a specific test
    test_loader = unittest.TestLoader()
    test_suite = test_loader.loadTestsFromName(f'tests.{test_name}')
    
    # Run the test
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Return the result
    return result

if __name__ == '__main__':
    # Check if a specific test was specified
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        logger.info(f"Running test: {test_name}")
        result = run_specific_test(test_name)
    else:
        logger.info("Running all tests")
        result = run_tests()
    
    # Exit with appropriate code
    sys.exit(not result.wasSuccessful())
