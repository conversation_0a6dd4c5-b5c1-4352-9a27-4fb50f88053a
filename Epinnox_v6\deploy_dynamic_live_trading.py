#!/usr/bin/env python3
"""
Enhanced Live Trading Deployment with Dynamic Symbol Selection
Deploy Epinnox autonomous trading system with dynamic symbol scanning
"""

import asyncio
import logging
import signal
import sys
import os
import time
import yaml
from datetime import datetime
from pathlib import Path

# Configure logging for live trading
log_filename = f'dynamic_live_trading_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DynamicLiveTradingDeployment:
    """
    Enhanced live trading deployment with dynamic symbol selection
    """
    
    def __init__(self):
        self.config_path = "config/live_production.yaml"
        self.deployment = None
        self.symbol_scanner = None
        self.running = False
        self.start_time = None
        self.current_symbol = None
        self.symbol_switch_count = 0
        
    async def deploy(self):
        """Deploy enhanced live trading system with dynamic symbols"""
        try:
            logger.info("EPINNOX DYNAMIC LIVE TRADING DEPLOYMENT")
            logger.info("="*60)
            
            # Step 1: Load credentials
            if not self._load_credentials():
                return False
            
            # Step 2: Load and validate configuration
            if not self._load_configuration():
                return False
            
            # Step 3: Initialize symbol scanner
            if not await self._initialize_symbol_scanner():
                return False
            
            # Step 4: Run pre-deployment validation
            if not await self._run_pre_deployment_validation():
                return False
            
            # Step 5: Final safety confirmation
            if not self._final_safety_confirmation():
                return False
            
            # Step 6: Initialize deployment
            if not await self._initialize_deployment():
                return False
            
            # Step 7: Start dynamic live trading
            await self._start_dynamic_live_trading()
            
            return True
            
        except KeyboardInterrupt:
            logger.info("Deployment interrupted by user")
            await self._emergency_shutdown()
        except Exception as e:
            logger.error(f"Deployment failed: {e}")
            await self._emergency_shutdown()
            return False
    
    def _load_credentials(self):
        """Load API credentials"""
        try:
            logger.info("Loading API credentials...")
            
            # Try to load from credentials.yaml file first
            credentials_file = Path("credentials.yaml")
            if credentials_file.exists():
                logger.info("Loading credentials from credentials.yaml...")
                with open(credentials_file, 'r') as f:
                    import yaml
                    creds = yaml.safe_load(f)
                
                # Extract HTX credentials
                htx_creds = creds.get('htx', {})
                api_key = htx_creds.get('apiKey')
                secret_key = htx_creds.get('secret')
                passphrase = htx_creds.get('password', '')
                
                if api_key and secret_key:
                    # Set environment variables for the system to use
                    os.environ['HTX_API_KEY'] = api_key
                    os.environ['HTX_SECRET_KEY'] = secret_key
                    if passphrase:
                        os.environ['HTX_PASSPHRASE'] = passphrase
                    
                    logger.info("Credentials loaded from credentials.yaml")
                    return True
            
            logger.error("No API credentials found")
            return False
            
        except Exception as e:
            logger.error(f"Failed to load credentials: {e}")
            return False
    
    def _load_configuration(self):
        """Load and validate configuration"""
        try:
            logger.info("Loading dynamic symbol configuration...")
            
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            
            # Validate dynamic symbol selection configuration
            if not self.config.get('dynamic_symbol_selection', {}).get('enabled', False):
                logger.error("Dynamic symbol selection is not enabled in configuration")
                return False
            
            # Log configuration summary
            symbol_pool = self.config.get('symbol_pool', [])
            scanner_config = self.config.get('symbol_scanner', {})
            
            logger.info(f"Dynamic symbol selection enabled")
            logger.info(f"Symbol pool: {len(symbol_pool)} pairs")
            logger.info(f"Update interval: {self.config['dynamic_symbol_selection']['update_interval']}s")
            logger.info(f"Scanner mode: {self.config['dynamic_symbol_selection']['mode']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Configuration loading failed: {e}")
            return False
    
    async def _initialize_symbol_scanner(self):
        """Initialize the dynamic symbol scanner"""
        try:
            logger.info("Initializing dynamic symbol scanner...")
            
            # Import symbol scanner
            from symbol_scanner import SymbolScannerConfig
            import ccxt
            
            # Initialize exchange for scanner
            exchange_config = {
                'apiKey': os.getenv('HTX_API_KEY'),
                'secret': os.getenv('HTX_SECRET_KEY'),
                'sandbox': False,
                'enableRateLimit': True,
            }
            
            if os.getenv('HTX_PASSPHRASE'):
                exchange_config['password'] = os.getenv('HTX_PASSPHRASE')
            
            exchange = ccxt.htx(exchange_config)
            
            # Get configuration
            dynamic_config = self.config['dynamic_symbol_selection']
            scanner_config = self.config['symbol_scanner']
            symbol_pool = self.config['symbol_pool']
            
            # Create scanner with live trading configuration
            self.symbol_scanner = SymbolScannerConfig.create_scanner(
                market_api=exchange,
                symbols=symbol_pool,
                weights=scanner_config['metrics_weights'],
                mode=dynamic_config['mode'],
                update_interval=dynamic_config['update_interval']
            )
            
            # Test scanner
            logger.info("Testing symbol scanner...")
            best_symbols = self.symbol_scanner.find_best(n=3)
            
            if best_symbols:
                logger.info(f"Scanner test successful. Best symbols: {best_symbols}")
                self.current_symbol = best_symbols[0]
                logger.info(f"Initial trading symbol: {self.current_symbol}")
            else:
                logger.warning("Scanner test returned no symbols, using fallback")
                fallback_symbols = self.config.get('fallback_symbols', ['BTC/USDT:USDT'])
                self.current_symbol = fallback_symbols[0]
            
            return True
            
        except Exception as e:
            logger.error(f"Symbol scanner initialization failed: {e}")
            return False
    
    async def _run_pre_deployment_validation(self):
        """Run comprehensive pre-deployment validation"""
        try:
            logger.info("Running pre-deployment validation...")
            
            # Import and run validation
            from pre_deployment_validation import LiveTradingValidator
            
            validator = LiveTradingValidator(self.config_path)
            success = await validator.run_validation()
            
            if not success:
                logger.error("Pre-deployment validation failed")
                return False
            
            logger.info("Pre-deployment validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Validation error: {e}")
            return False
    
    def _final_safety_confirmation(self):
        """Final safety confirmation for dynamic live trading"""
        logger.warning("\n" + "="*60)
        logger.warning("DYNAMIC LIVE TRADING SAFETY CONFIRMATION")
        logger.warning("="*60)
        logger.warning("\nYou are about to start LIVE TRADING with REAL MONEY")
        logger.warning("Enhanced Features:")
        logger.warning("• DYNAMIC SYMBOL SELECTION enabled")
        logger.warning("• Automatic switching between optimal trading pairs")
        logger.warning("• Real-time market analysis and adaptation")
        logger.warning(f"• Symbol pool: {len(self.config['symbol_pool'])} USDT-M futures")
        logger.warning(f"• Current best symbol: {self.current_symbol}")
        logger.warning("\nRisk Settings:")
        logger.warning("• Max Daily Loss: $10 (20% of account)")
        logger.warning("• Max Position Size: $15 (30% of account)")
        logger.warning("• Max Leverage: 3x")
        logger.warning("• Max Positions: 2")
        logger.warning("\nSafety Features Active:")
        logger.warning("• Emergency stop procedures")
        logger.warning("• Real-time risk monitoring")
        logger.warning("• Automatic position closure")
        logger.warning("• Dynamic symbol quality filtering")
        
        print("\n" + "WARNING: "*10)
        response = input("Type 'START DYNAMIC LIVE TRADING' to confirm (anything else cancels): ")
        
        if response.strip() != 'START DYNAMIC LIVE TRADING':
            logger.info("Dynamic live trading cancelled by user")
            return False
        
        logger.warning("Dynamic live trading confirmed - Starting in 5 seconds...")
        time.sleep(5)
        return True
    
    async def _initialize_deployment(self):
        """Initialize the deployment system"""
        try:
            logger.info("Initializing dynamic deployment system...")
            
            from deploy_autonomous_trading import AutonomousTradingDeployment
            
            # Update config with current symbol
            self.config['current_symbol'] = self.current_symbol
            
            self.deployment = AutonomousTradingDeployment(
                config_path=self.config_path,
                mode='live'
            )
            
            # Initialize system
            success = await self.deployment.initialize_system()
            if not success:
                logger.error("System initialization failed")
                return False
            
            logger.info("Dynamic deployment system initialized")
            return True
            
        except Exception as e:
            logger.error(f"Deployment initialization failed: {e}")
            return False
    
    async def _start_dynamic_live_trading(self):
        """Start dynamic live trading with symbol monitoring"""
        try:
            self.start_time = datetime.now()
            self.running = True
            
            logger.info("STARTING DYNAMIC AUTONOMOUS TRADING")
            logger.info("="*60)
            logger.info(f"Start Time: {self.start_time}")
            logger.info(f"Initial Symbol: {self.current_symbol}")
            logger.info(f"Log File: {log_filename}")
            logger.info("="*60)
            
            # Set up signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # Start monitoring tasks
            symbol_monitor_task = asyncio.create_task(self._symbol_monitoring_loop())
            system_monitor_task = asyncio.create_task(self._system_monitoring_loop())
            
            # Start trading task
            trading_task = asyncio.create_task(self.deployment.start_autonomous_trading())
            
            # Wait for any task to complete
            done, pending = await asyncio.wait(
                [symbol_monitor_task, system_monitor_task, trading_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancel remaining tasks
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
        except Exception as e:
            logger.error(f"Dynamic live trading error: {e}")
        finally:
            await self._emergency_shutdown()
    
    async def _symbol_monitoring_loop(self):
        """Monitor and update symbol selection"""
        update_interval = self.config['dynamic_symbol_selection']['update_interval']
        
        while self.running:
            try:
                # Scan for best symbols
                best_symbols = self.symbol_scanner.find_best(n=3)
                
                if best_symbols and best_symbols[0] != self.current_symbol:
                    # Check if we should switch symbols
                    if self._should_switch_symbol(best_symbols[0]):
                        await self._switch_symbol(best_symbols[0])
                
                # Log current status
                if best_symbols:
                    logger.info(f"Symbol scan: Current={self.current_symbol}, "
                              f"Best={best_symbols[0]}, Top3={best_symbols[:3]}")
                
                await asyncio.sleep(update_interval)
                
            except Exception as e:
                logger.error(f"Symbol monitoring error: {e}")
                await asyncio.sleep(10)
    
    def _should_switch_symbol(self, new_symbol):
        """Determine if we should switch to a new symbol"""
        # Implement switching logic based on configuration
        strategy_config = self.config.get('strategy', {})
        max_switches_per_hour = strategy_config.get('max_symbol_switches_per_hour', 6)
        
        # Check switch frequency limits
        if self.symbol_switch_count >= max_switches_per_hour:
            logger.info(f"Symbol switch limit reached ({max_switches_per_hour}/hour)")
            return False
        
        return True
    
    async def _switch_symbol(self, new_symbol):
        """Switch to a new trading symbol"""
        try:
            old_symbol = self.current_symbol
            logger.info(f"Switching symbol: {old_symbol} -> {new_symbol}")
            
            # Update current symbol
            self.current_symbol = new_symbol
            self.symbol_switch_count += 1
            
            # Update deployment configuration
            if self.deployment:
                # Update the trading system with new symbol
                # This would need to be implemented in the deployment system
                pass
            
            logger.info(f"Symbol switch completed: {new_symbol}")
            
        except Exception as e:
            logger.error(f"Symbol switch error: {e}")
    
    async def _system_monitoring_loop(self):
        """Monitor overall system health"""
        while self.running:
            try:
                if self.deployment and self.deployment.orchestrator:
                    status = self.deployment.orchestrator.get_status()
                    
                    runtime = datetime.now() - self.start_time
                    logger.info(f"DYNAMIC TRADING STATUS - Runtime: {runtime}")
                    logger.info(f"   Current Symbol: {self.current_symbol}")
                    logger.info(f"   Symbol Switches: {self.symbol_switch_count}")
                    logger.info(f"   System State: {status['state']}")
                    logger.info(f"   Trading Cycles: {status['cycle_count']}")
                    logger.info(f"   Emergency Stop: {status['emergency_stop']}")
                    
                    # Check for emergency conditions
                    if status['emergency_stop']:
                        logger.critical("EMERGENCY STOP DETECTED - SHUTTING DOWN")
                        self.running = False
                        break
                
                await asyncio.sleep(60)  # Monitor every minute
                
            except Exception as e:
                logger.error(f"System monitoring error: {e}")
                await asyncio.sleep(10)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum} - Initiating graceful shutdown...")
        self.running = False
    
    async def _emergency_shutdown(self):
        """Emergency shutdown procedure"""
        try:
            logger.critical("INITIATING EMERGENCY SHUTDOWN")
            
            self.running = False
            
            if self.deployment:
                await self.deployment._shutdown()
            
            runtime = datetime.now() - self.start_time if self.start_time else "Unknown"
            logger.info(f"Dynamic live trading stopped - Runtime: {runtime}")
            logger.info(f"Total symbol switches: {self.symbol_switch_count}")
            logger.info(f"Final symbol: {self.current_symbol}")
            logger.info(f"Log file saved: {log_filename}")
            
        except Exception as e:
            logger.error(f"Emergency shutdown error: {e}")

async def main():
    """Main deployment function"""
    # Check if configuration exists
    config_path = Path("config/live_production.yaml")
    if not config_path.exists():
        logger.error(f"Configuration file not found: {config_path}")
        sys.exit(1)
    
    # Create deployment instance
    deployment = DynamicLiveTradingDeployment()
    
    # Run deployment
    success = await deployment.deploy()
    
    if not success:
        logger.error("Dynamic live trading deployment failed")
        sys.exit(1)

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Deployment interrupted")
    except Exception as e:
        logger.error(f"Deployment error: {e}")
        sys.exit(1)
