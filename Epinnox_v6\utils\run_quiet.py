#!/usr/bin/env python
"""
Run the Epinnox Trading System with reduced console output
"""
import sys
import os
import logging
import subprocess

# Configure logging - reduce verbosity
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_system.log'),
    ]
)

# Set environment variables to reduce console output
os.environ["QT_LOGGING_RULES"] = "*.debug=false;qt.qpa.*=false"
os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "1"

# Run the application
subprocess.run([sys.executable, "launch_epinnox.py"] + sys.argv[1:])
