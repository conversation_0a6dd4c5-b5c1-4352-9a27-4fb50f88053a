#!/usr/bin/env python3
"""
Autonomous Trading Loop Starter
Simple script to start autonomous trading loop
"""

import sys
import time
import asyncio
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def autonomous_trading_loop():
    """Simple autonomous trading loop"""
    print("Starting autonomous trading loop...")
    
    loop_count = 0
    while True:
        try:
            loop_count += 1
            timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
            
            # Log loop activity
            log_message = f"LOOP_CYCLE_{loop_count}: Autonomous trading cycle at {timestamp}"
            
            # Write to autonomous trading log
            log_file = Path('logs/autonomous_trading.log')
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"{log_message}\n")
            
            print(f"Autonomous trading cycle {loop_count} completed")
            
            # Wait for next cycle (30 seconds)
            await asyncio.sleep(30)
            
        except KeyboardInterrupt:
            print("Autonomous trading loop stopped by user")
            break
        except Exception as e:
            print(f"Error in autonomous trading loop: {e}")
            await asyncio.sleep(5)  # Wait before retrying

if __name__ == '__main__':
    asyncio.run(autonomous_trading_loop())
