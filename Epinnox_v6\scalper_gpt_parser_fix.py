
def fix_scalper_gpt_response(response_text):
    """Fix ScalperGPT response parsing issues"""
    import json
    import re
    
    try:
        # Clean the response
        cleaned = response_text.strip()
        
        # Remove markdown code blocks
        if cleaned.startswith('```'):
            lines = cleaned.split('\n')
            cleaned = '\n'.join(lines[1:-1])
        
        # Parse JSON
        parsed = json.loads(cleaned)
        
        # Fix field name inconsistencies
        field_mapping = {
            'action': ['ACTION', 'action', 'Action'],
            'quantity': ['QUANTITY', 'quantity', 'Quantity'],
            'leverage': ['LEVERAGE', 'leverage', 'Leverage'],
            'stop_loss': ['STOP_LOSS', 'stop_loss', 'stopLoss'],
            'take_profit': ['TAKE_PROFIT', 'take_profit', 'takeProfit'],
            'risk_pct': ['RISK_PCT', 'risk_pct', 'riskPct'],
            'order_type': ['ORDER_TYPE', 'order_type', 'orderType']
        }
        
        # Normalize field names
        normalized = {}
        for standard_field, possible_names in field_mapping.items():
            for possible_name in possible_names:
                if possible_name in parsed:
                    normalized[standard_field] = parsed[possible_name]
                    break
            
            # Set defaults if missing
            if standard_field not in normalized:
                defaults = {
                    'action': 'WAIT',
                    'quantity': 0.001,
                    'leverage': 1,
                    'stop_loss': 1.0,
                    'take_profit': 2.0,
                    'risk_pct': 1.0,
                    'order_type': 'MARKET'
                }
                normalized[standard_field] = defaults[standard_field]
        
        # Ensure ACTION field exists (uppercase)
        normalized['ACTION'] = normalized['action'].upper()
        normalized['QUANTITY'] = float(normalized['quantity'])
        normalized['LEVERAGE'] = int(normalized['leverage'])
        normalized['RISK_PCT'] = float(normalized['risk_pct'])
        normalized['ORDER_TYPE'] = normalized['order_type'].upper()
        
        return normalized
        
    except Exception as e:
        # Return safe default
        return {
            'action': 'WAIT',
            'ACTION': 'WAIT',
            'quantity': 0.001,
            'QUANTITY': 0.001,
            'leverage': 1,
            'LEVERAGE': 1,
            'stop_loss': 1.0,
            'take_profit': 2.0,
            'risk_pct': 1.0,
            'RISK_PCT': 1.0,
            'order_type': 'MARKET',
            'ORDER_TYPE': 'MARKET',
            'error': str(e)
        }
