"""
ML Prediction Accuracy Tracker for Epinnox v6
Tracks and calculates real-time accuracy of ML model predictions
"""

import time
from typing import Dict, Optional, List, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


@dataclass
class PredictionRecord:
    """Record of a single ML prediction"""
    model_name: str
    prediction: str  # 'LONG', 'SHORT', 'WAIT'
    confidence: float
    price_at_prediction: float
    timestamp: float
    symbol: str


class PredictionAccuracyTracker:
    """
    Tracks ML model predictions and calculates accuracy based on actual price movements
    """
    
    def __init__(self, evaluation_window_minutes: int = 5):
        """
        Initialize the accuracy tracker
        
        Args:
            evaluation_window_minutes: Time window to evaluate prediction accuracy
        """
        self.evaluation_window = evaluation_window_minutes * 60  # Convert to seconds
        self.predictions: Dict[str, List[PredictionRecord]] = {}  # model_name -> list of predictions
        self.current_prices: Dict[str, float] = {}  # symbol -> current price
        self.accuracy_cache: Dict[str, float] = {}  # model_name -> cached accuracy
        self.last_accuracy_update = 0
        
        # Accuracy calculation parameters
        self.price_movement_threshold = 0.001  # 0.1% minimum movement to consider significant
        
    def record_prediction(self, model_name: str, prediction: str, confidence: float, 
                         price: float, symbol: str):
        """
        Record a new ML model prediction
        
        Args:
            model_name: Name of the ML model (e.g., 'SVM', 'Random Forest', 'LSTM')
            prediction: Predicted direction ('LONG', 'SHORT', 'WAIT')
            confidence: Model confidence (0.0 to 1.0)
            price: Price at the time of prediction
            symbol: Trading symbol
        """
        try:
            if model_name not in self.predictions:
                self.predictions[model_name] = []
            
            # Create prediction record
            record = PredictionRecord(
                model_name=model_name,
                prediction=prediction,
                confidence=confidence,
                price_at_prediction=price,
                timestamp=time.time(),
                symbol=symbol
            )
            
            # Add to predictions list
            self.predictions[model_name].append(record)
            
            # Clean old predictions (keep only last 24 hours)
            self._cleanup_old_predictions(model_name)
            
            logger.debug(f"Recorded prediction: {model_name} -> {prediction} @ {price:.6f}")
            
        except Exception as e:
            logger.error(f"Error recording prediction: {e}")
    
    def update_current_price(self, symbol: str, price: float):
        """
        Update current market price for accuracy calculations
        
        Args:
            symbol: Trading symbol
            price: Current market price
        """
        self.current_prices[symbol] = price
        
        # Update accuracy cache periodically (every 10 seconds)
        current_time = time.time()
        if current_time - self.last_accuracy_update > 10:
            self._update_accuracy_cache()
            self.last_accuracy_update = current_time
    
    def get_model_accuracy(self, model_name: str) -> Optional[float]:
        """
        Get the current accuracy percentage for a specific model
        
        Args:
            model_name: Name of the ML model
            
        Returns:
            Accuracy percentage (0.0 to 100.0) or None if no data available
        """
        return self.accuracy_cache.get(model_name)
    
    def get_all_accuracies(self) -> Dict[str, float]:
        """
        Get accuracy percentages for all models
        
        Returns:
            Dictionary mapping model names to accuracy percentages
        """
        return self.accuracy_cache.copy()
    
    def _update_accuracy_cache(self):
        """Update the accuracy cache for all models"""
        try:
            for model_name in self.predictions:
                accuracy = self._calculate_model_accuracy(model_name)
                if accuracy is not None:
                    self.accuracy_cache[model_name] = accuracy
                    
        except Exception as e:
            logger.error(f"Error updating accuracy cache: {e}")
    
    def _calculate_model_accuracy(self, model_name: str) -> Optional[float]:
        """
        Calculate accuracy for a specific model based on recent predictions
        
        Args:
            model_name: Name of the ML model
            
        Returns:
            Accuracy percentage (0.0 to 100.0) or None if insufficient data
        """
        try:
            if model_name not in self.predictions:
                return None
            
            predictions = self.predictions[model_name]
            if not predictions:
                return None
            
            current_time = time.time()
            correct_predictions = 0
            total_predictions = 0
            
            for record in predictions:
                # Only evaluate predictions that are old enough (evaluation window passed)
                if current_time - record.timestamp < self.evaluation_window:
                    continue
                
                # Get current price for the symbol
                current_price = self.current_prices.get(record.symbol)
                if current_price is None:
                    continue
                
                # Calculate price movement
                price_change = (current_price - record.price_at_prediction) / record.price_at_prediction
                
                # Determine if prediction was correct
                is_correct = self._evaluate_prediction_accuracy(record.prediction, price_change)
                
                if is_correct:
                    correct_predictions += 1
                total_predictions += 1
            
            # Return accuracy percentage
            if total_predictions > 0:
                accuracy = (correct_predictions / total_predictions) * 100
                return round(accuracy, 1)
            
            return None
            
        except Exception as e:
            logger.error(f"Error calculating accuracy for {model_name}: {e}")
            return None
    
    def _evaluate_prediction_accuracy(self, prediction: str, price_change: float) -> bool:
        """
        Evaluate if a prediction was accurate based on actual price movement
        
        Args:
            prediction: The model's prediction ('LONG', 'SHORT', 'WAIT')
            price_change: Actual price change percentage
            
        Returns:
            True if prediction was accurate, False otherwise
        """
        try:
            if prediction == 'LONG':
                # For LONG predictions, price should have gone up significantly
                return price_change > self.price_movement_threshold
            
            elif prediction == 'SHORT':
                # For SHORT predictions, price should have gone down significantly
                return price_change < -self.price_movement_threshold
            
            elif prediction == 'WAIT':
                # For WAIT predictions, price should have remained relatively stable
                return abs(price_change) <= self.price_movement_threshold
            
            return False
            
        except Exception as e:
            logger.error(f"Error evaluating prediction accuracy: {e}")
            return False
    
    def _cleanup_old_predictions(self, model_name: str):
        """
        Remove predictions older than 24 hours to prevent memory bloat
        
        Args:
            model_name: Name of the ML model
        """
        try:
            if model_name not in self.predictions:
                return
            
            current_time = time.time()
            cutoff_time = current_time - (24 * 60 * 60)  # 24 hours ago
            
            # Keep only recent predictions
            self.predictions[model_name] = [
                record for record in self.predictions[model_name]
                if record.timestamp > cutoff_time
            ]
            
        except Exception as e:
            logger.error(f"Error cleaning up old predictions: {e}")
    
    def get_prediction_stats(self, model_name: str) -> Dict:
        """
        Get detailed statistics for a model
        
        Args:
            model_name: Name of the ML model
            
        Returns:
            Dictionary with prediction statistics
        """
        try:
            if model_name not in self.predictions:
                return {}
            
            predictions = self.predictions[model_name]
            current_time = time.time()
            
            # Count predictions by type
            long_count = sum(1 for p in predictions if p.prediction == 'LONG')
            short_count = sum(1 for p in predictions if p.prediction == 'SHORT')
            wait_count = sum(1 for p in predictions if p.prediction == 'WAIT')
            
            # Count recent predictions (last hour)
            recent_predictions = sum(
                1 for p in predictions 
                if current_time - p.timestamp < 3600
            )
            
            return {
                'total_predictions': len(predictions),
                'recent_predictions': recent_predictions,
                'long_predictions': long_count,
                'short_predictions': short_count,
                'wait_predictions': wait_count,
                'accuracy': self.get_model_accuracy(model_name)
            }
            
        except Exception as e:
            logger.error(f"Error getting prediction stats: {e}")
            return {}
