"""
Unit tests for trade instruction parsing functionality
Tests the parse_trade_instruction function to ensure it correctly handles valid and malformed JSON.
"""

import unittest
import sys
import os

# Add the parent directory to the path to import the main module
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class MockEpinnoxInterface:
    """Mock class to test the parse_trade_instruction method"""
    
    def __init__(self):
        pass
    
    def log_message(self, message):
        """Mock log message function"""
        print(f"LOG: {message}")
    
    def parse_trade_instruction(self, final_response: str):
        """Parse and validate JSON trade instruction from LLM response"""
        try:
            import json
            import re
            
            # Extract JSON from response (handle cases where LLM adds extra text)
            json_match = re.search(r'\{[^{}]*\}', final_response, re.DOTALL)
            if not json_match:
                self.log_message("❌ No JSON found in LLM response")
                return None
            
            json_str = json_match.group(0)
            
            # Parse JSON
            trade_instruction = json.loads(json_str)
            
            # Validate required fields
            required_fields = ["ACTION", "QUANTITY", "LEVERAGE", "RISK_PCT", "ORDER_TYPE"]
            for field in required_fields:
                if field not in trade_instruction:
                    self.log_message(f"❌ Missing required field: {field}")
                    return None
            
            # Validate ACTION
            if trade_instruction["ACTION"] not in ["BUY", "SELL", "WAIT"]:
                self.log_message(f"❌ Invalid ACTION: {trade_instruction['ACTION']}")
                return None
            
            # Validate and constrain values
            trade_instruction["QUANTITY"] = max(0.0, float(trade_instruction["QUANTITY"]))
            trade_instruction["LEVERAGE"] = max(1, min(200, int(trade_instruction["LEVERAGE"])))
            trade_instruction["RISK_PCT"] = max(0.5, min(5.0, float(trade_instruction["RISK_PCT"])))
            
            # Ensure optional fields have defaults
            trade_instruction.setdefault("STOP_LOSS", 0.0)
            trade_instruction.setdefault("TAKE_PROFIT", 0.0)
            
            # Convert to expected format for compatibility
            trade_instruction["verdict"] = trade_instruction["ACTION"].replace("BUY", "LONG").replace("SELL", "SHORT")
            trade_instruction["confidence"] = 85.0  # Default confidence for LLM decisions
            trade_instruction["position_size"] = trade_instruction["QUANTITY"]
            trade_instruction["entry_price"] = trade_instruction.get("ENTRY_PRICE", 0.0)
            trade_instruction["stop_loss"] = trade_instruction.get("STOP_LOSS", 0.0)
            trade_instruction["take_profit"] = trade_instruction.get("TAKE_PROFIT", 0.0)
            trade_instruction["leverage"] = f"{trade_instruction['LEVERAGE']}x"
            trade_instruction["risk_level"] = "MEDIUM"
            trade_instruction["reasoning"] = "ScalperGPT autonomous decision based on comprehensive market analysis"
            
            self.log_message(f"✅ Parsed trade instruction: {trade_instruction['ACTION']} {trade_instruction['QUANTITY']:.4f}")
            return trade_instruction

        except json.JSONDecodeError as e:
            self.log_message(f"❌ JSON parsing error: {e}")
            return None
        except Exception as e:
            self.log_message(f"❌ Error parsing trade instruction: {e}")
            return None


class TestTradeInstructionParsing(unittest.TestCase):
    """Test cases for trade instruction parsing"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.parser = MockEpinnoxInterface()
    
    def test_valid_buy_instruction(self):
        """Test parsing a valid BUY instruction"""
        response = '''
        {
          "ACTION": "BUY",
          "QUANTITY": 150.0,
          "LEVERAGE": 20,
          "STOP_LOSS": 0.0235,
          "TAKE_PROFIT": 0.0250,
          "RISK_PCT": 2.0,
          "ORDER_TYPE": "MARKET"
        }
        '''
        
        result = self.parser.parse_trade_instruction(response)
        
        self.assertIsNotNone(result)
        self.assertEqual(result["ACTION"], "BUY")
        self.assertEqual(result["verdict"], "LONG")
        self.assertEqual(result["QUANTITY"], 150.0)
        self.assertEqual(result["LEVERAGE"], 20)
        self.assertEqual(result["RISK_PCT"], 2.0)
        self.assertEqual(result["ORDER_TYPE"], "MARKET")
    
    def test_valid_sell_instruction(self):
        """Test parsing a valid SELL instruction"""
        response = '''
        {
          "ACTION": "SELL",
          "QUANTITY": 75.5,
          "LEVERAGE": 10,
          "STOP_LOSS": 0.0265,
          "TAKE_PROFIT": 0.0240,
          "RISK_PCT": 1.5,
          "ORDER_TYPE": "LIMIT"
        }
        '''
        
        result = self.parser.parse_trade_instruction(response)
        
        self.assertIsNotNone(result)
        self.assertEqual(result["ACTION"], "SELL")
        self.assertEqual(result["verdict"], "SHORT")
        self.assertEqual(result["QUANTITY"], 75.5)
        self.assertEqual(result["LEVERAGE"], 10)
        self.assertEqual(result["RISK_PCT"], 1.5)
        self.assertEqual(result["ORDER_TYPE"], "LIMIT")
    
    def test_valid_wait_instruction(self):
        """Test parsing a valid WAIT instruction"""
        response = '''
        {
          "ACTION": "WAIT",
          "QUANTITY": 0.0,
          "LEVERAGE": 1,
          "RISK_PCT": 0.5,
          "ORDER_TYPE": "MARKET"
        }
        '''
        
        result = self.parser.parse_trade_instruction(response)
        
        self.assertIsNotNone(result)
        self.assertEqual(result["ACTION"], "WAIT")
        self.assertEqual(result["verdict"], "WAIT")
        self.assertEqual(result["QUANTITY"], 0.0)
    
    def test_json_with_extra_text(self):
        """Test parsing JSON embedded in extra text"""
        response = '''
        Based on the market analysis, I recommend:
        
        {
          "ACTION": "BUY",
          "QUANTITY": 100.0,
          "LEVERAGE": 15,
          "RISK_PCT": 2.5,
          "ORDER_TYPE": "MARKET"
        }
        
        This decision is based on strong bullish signals.
        '''
        
        result = self.parser.parse_trade_instruction(response)
        
        self.assertIsNotNone(result)
        self.assertEqual(result["ACTION"], "BUY")
        self.assertEqual(result["QUANTITY"], 100.0)
    
    def test_missing_required_field(self):
        """Test handling of missing required fields"""
        response = '''
        {
          "ACTION": "BUY",
          "QUANTITY": 100.0,
          "LEVERAGE": 15
        }
        '''
        
        result = self.parser.parse_trade_instruction(response)
        
        self.assertIsNone(result)
    
    def test_invalid_action(self):
        """Test handling of invalid ACTION values"""
        response = '''
        {
          "ACTION": "INVALID",
          "QUANTITY": 100.0,
          "LEVERAGE": 15,
          "RISK_PCT": 2.0,
          "ORDER_TYPE": "MARKET"
        }
        '''
        
        result = self.parser.parse_trade_instruction(response)
        
        self.assertIsNone(result)
    
    def test_leverage_bounds_enforcement(self):
        """Test leverage bounds enforcement"""
        response = '''
        {
          "ACTION": "BUY",
          "QUANTITY": 100.0,
          "LEVERAGE": 500,
          "RISK_PCT": 2.0,
          "ORDER_TYPE": "MARKET"
        }
        '''
        
        result = self.parser.parse_trade_instruction(response)
        
        self.assertIsNotNone(result)
        self.assertEqual(result["LEVERAGE"], 200)  # Should be capped at 200
    
    def test_risk_pct_bounds_enforcement(self):
        """Test risk percentage bounds enforcement"""
        response = '''
        {
          "ACTION": "BUY",
          "QUANTITY": 100.0,
          "LEVERAGE": 20,
          "RISK_PCT": 10.0,
          "ORDER_TYPE": "MARKET"
        }
        '''
        
        result = self.parser.parse_trade_instruction(response)
        
        self.assertIsNotNone(result)
        self.assertEqual(result["RISK_PCT"], 5.0)  # Should be capped at 5.0
    
    def test_malformed_json(self):
        """Test handling of malformed JSON"""
        response = '''
        {
          "ACTION": "BUY",
          "QUANTITY": 100.0,
          "LEVERAGE": 20,
          "RISK_PCT": 2.0,
          "ORDER_TYPE": "MARKET"
        '''  # Missing closing brace
        
        result = self.parser.parse_trade_instruction(response)
        
        self.assertIsNone(result)
    
    def test_no_json_in_response(self):
        """Test handling of response with no JSON"""
        response = "I cannot provide a trading recommendation at this time."
        
        result = self.parser.parse_trade_instruction(response)
        
        self.assertIsNone(result)
    
    def test_optional_fields_defaults(self):
        """Test that optional fields get default values"""
        response = '''
        {
          "ACTION": "BUY",
          "QUANTITY": 100.0,
          "LEVERAGE": 20,
          "RISK_PCT": 2.0,
          "ORDER_TYPE": "MARKET"
        }
        '''
        
        result = self.parser.parse_trade_instruction(response)
        
        self.assertIsNotNone(result)
        self.assertEqual(result["STOP_LOSS"], 0.0)
        self.assertEqual(result["TAKE_PROFIT"], 0.0)
        self.assertEqual(result["confidence"], 85.0)
        self.assertEqual(result["risk_level"], "MEDIUM")


if __name__ == '__main__':
    unittest.main()
