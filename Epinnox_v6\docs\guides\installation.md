# Installation Guide

Complete installation guide for Epinnox v6 Trading System.

## 📋 Prerequisites

### System Requirements
- **Python**: 3.8 or higher
- **Operating System**: Windows 10/11, macOS, or Linux
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 2GB free space
- **Internet**: Stable connection for live trading

### Required Accounts
- **HTX Exchange Account**: For live trading (optional for demo)
- **OpenAI API Key**: For GPT-4 integration (optional)

## 🚀 Quick Installation

### 1. Clone Repository
```bash
git clone https://github.com/Geo222222/potential-octo-parakeet.git
cd potential-octo-parakeet/Epinnox_v6
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Configure Settings
```bash
# Copy example config files
cp config/credentials.yaml.example config/credentials.yaml
cp config/trading_config.yaml.example config/trading_config.yaml

# Edit with your settings
nano config/credentials.yaml
```

### 4. Launch Application
```bash
python launch_epinnox.py
```

## 🔧 Detailed Installation

### Step 1: Python Environment Setup

#### Option A: Using Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv epinnox_env

# Activate environment
# Windows:
epinnox_env\Scripts\activate
# macOS/Linux:
source epinnox_env/bin/activate

# Install dependencies
pip install -r requirements.txt
```

#### Option B: Using Conda
```bash
# Create conda environment
conda create -n epinnox python=3.9
conda activate epinnox

# Install dependencies
pip install -r requirements.txt
```

### Step 2: Configuration Setup

#### Trading Configuration
Edit `config/trading_config.yaml`:
```yaml
# Trading Parameters
symbol: "DOGE/USDT:USDT"
leverage: 20
balance: 50
timeframes: ["1m", "5m", "15m"]

# Exchange Settings
exchange:
  name: "htx"
  sandbox: false  # Set to true for testing
  
# Risk Management
risk:
  max_position_size: 0.1
  stop_loss_pct: 0.02
  take_profit_pct: 0.04
```

#### API Credentials
Edit `config/credentials.yaml`:
```yaml
# HTX Exchange
htx:
  apiKey: "your_htx_api_key"
  secret: "your_htx_secret"
  password: "your_htx_passphrase"  # If required
  sandbox: false

# OpenAI (Optional)
openai:
  api_key: "your_openai_api_key"
  model: "gpt-4"
```

#### Model Configuration
Edit `config/models_config.yaml`:
```yaml
# LLM Configuration
llm:
  provider: "lmstudio"  # Options: lmstudio, openai, mock
  model_name: "phi-3.1-mini-128k-instruct"
  temperature: 0.7
  max_tokens: 1000

# ML Models
ml:
  models: ["svm", "random_forest", "lstm"]
  prediction_window: 5  # minutes
  confidence_threshold: 0.6
```

### Step 3: Verify Installation

#### Test Basic Functionality
```bash
# Test imports
python -c "import launch_epinnox; print('✓ Imports successful')"

# Test configuration
python -c "from config.config import *; print('✓ Configuration loaded')"

# Test ML components
python -c "from ml.prediction_accuracy_tracker import PredictionAccuracyTracker; print('✓ ML components ready')"
```

#### Test GUI Launch
```bash
python launch_epinnox.py
```

Expected output:
```
✓ PyQt5 loaded successfully
✓ PyQtGraph loaded successfully
✓ CCXT library available
✓ Real trading interface initialized
✓ Epinnox v6 GUI started successfully
```

## 🔍 Troubleshooting

### Common Issues

#### 1. PyQt5 Installation Issues
```bash
# Windows
pip install PyQt5==5.15.7

# macOS (with Homebrew)
brew install pyqt5
pip install PyQt5

# Linux (Ubuntu/Debian)
sudo apt-get install python3-pyqt5
pip install PyQt5
```

#### 2. CCXT Installation Issues
```bash
# Force reinstall
pip uninstall ccxt
pip install ccxt --no-cache-dir
```

#### 3. WebSocket Connection Issues
- Check firewall settings
- Verify internet connection
- Ensure HTX API credentials are correct

#### 4. GUI Display Issues
```bash
# Install additional Qt dependencies
pip install PyQt5-tools
```

### Environment Variables

#### Optional Environment Setup
```bash
# Windows
set EPINNOX_CONFIG_PATH=C:\path\to\config
set EPINNOX_LOG_LEVEL=INFO

# macOS/Linux
export EPINNOX_CONFIG_PATH=/path/to/config
export EPINNOX_LOG_LEVEL=INFO
```

## 🎯 Post-Installation Setup

### 1. Test Trading Connection
1. Launch the GUI: `python launch_epinnox.py`
2. Check WebSocket connection status
3. Verify live data feeds are working
4. Test order placement (use sandbox mode first)

### 2. Configure ML Models
1. Navigate to ML Models Status panel
2. Click "ANALYZE SYMBOL" to test predictions
3. Wait 5 minutes to see accuracy tracking
4. Verify color-coded accuracy indicators

### 3. Customize Interface
1. Arrange dockable panels to your preference
2. Select preferred chart type (candlestick recommended)
3. Configure Matrix theme settings
4. Save layout for future sessions

## 🔐 Security Setup

### API Key Security
```bash
# Set restrictive permissions on config files
chmod 600 config/credentials.yaml
chmod 600 config/api_credentials.json
```

### Firewall Configuration
- Allow outbound connections to HTX exchange
- Port 443 (HTTPS) for API calls
- WebSocket connections for live data

## 📊 Performance Optimization

### System Optimization
```bash
# Increase Python memory limit
export PYTHONMALLOC=malloc

# Enable GPU acceleration (if available)
pip install torch torchvision  # For LSTM models
```

### Database Optimization
```bash
# Initialize database
python -c "from storage.database_manager import DatabaseManager; DatabaseManager().initialize()"
```

## 🆘 Getting Help

### Log Files
Check these locations for troubleshooting:
- `logs/epinnox_v6.log` - Main application log
- `logs/trading_system.log` - Trading-specific logs
- `logs/simulation.log` - Simulation mode logs

### Support Resources
- [GUI User Guide](gui-guide.md) - Interface help
- [Trading API Documentation](../api/trading-api.md) - API reference
- [GitHub Issues](https://github.com/Geo222222/potential-octo-parakeet/issues) - Bug reports

### Community
- GitHub Discussions for questions
- Issue tracker for bug reports
- Pull requests for contributions

---

**Installation complete! You're ready to start trading with Epinnox v6.**
