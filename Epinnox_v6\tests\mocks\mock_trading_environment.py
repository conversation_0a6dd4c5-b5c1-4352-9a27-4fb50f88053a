"""
Mock Trading Environment for testing purposes
"""

import random
import numpy as np

class MockTradingEnvironment:
    """Mock trading environment for RL agent testing"""
    
    def __init__(self, initial_balance=10000.0):
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.position = 0.0  # Current position size
        self.position_type = None  # 'long', 'short', or None
        self.current_price = 50000.0
        self.price_history = []
        self.step_count = 0
        self.max_steps = 100
        self.transaction_cost = 0.001  # 0.1% transaction cost
        
    def reset(self, prices=None):
        """Reset environment to initial state"""
        self.current_balance = self.initial_balance
        self.position = 0.0
        self.position_type = None
        self.step_count = 0
        
        if prices:
            self.price_history = prices
            self.current_price = prices[0] if prices else 50000.0
        else:
            # Generate random price history
            self.price_history = self._generate_price_series()
            self.current_price = self.price_history[0]
        
        return self.get_state()
    
    def step(self, action):
        """Execute one step in the environment"""
        if self.step_count >= len(self.price_history) - 1:
            return 0.0  # Episode finished
        
        # Get current and next price
        current_price = self.price_history[self.step_count]
        next_price = self.price_history[self.step_count + 1]
        
        # Calculate reward based on action
        reward = self._calculate_reward(action, current_price, next_price)
        
        # Update position based on action
        self._execute_action(action, current_price)
        
        # Update environment state
        self.current_price = next_price
        self.step_count += 1
        
        return reward
    
    def get_state(self):
        """Get current environment state"""
        # Calculate technical indicators
        if len(self.price_history) > self.step_count + 10:
            recent_prices = self.price_history[max(0, self.step_count-10):self.step_count+1]
        else:
            recent_prices = self.price_history[:self.step_count+1] if self.step_count < len(self.price_history) else [self.current_price]
        
        if len(recent_prices) > 1:
            returns = np.diff(recent_prices) / recent_prices[:-1]
            volatility = np.std(returns) if len(returns) > 1 else 0.01
            trend = (recent_prices[-1] - recent_prices[0]) / recent_prices[0] if len(recent_prices) > 1 else 0.0
        else:
            volatility = 0.01
            trend = 0.0
        
        state = {
            'price': self.current_price,
            'balance': self.current_balance,
            'position': self.position,
            'position_type': self.position_type,
            'volatility': volatility,
            'trend': trend,
            'step': self.step_count,
            'recent_prices': recent_prices[-5:] if len(recent_prices) >= 5 else recent_prices
        }
        
        return state
    
    def _execute_action(self, action, price):
        """Execute trading action"""
        if action == 'buy':
            if self.position_type != 'long':
                # Close short position if exists
                if self.position_type == 'short':
                    self._close_position(price)
                
                # Open long position
                position_value = self.current_balance * 0.1  # Use 10% of balance
                self.position = position_value / price
                self.position_type = 'long'
                self.current_balance -= position_value * self.transaction_cost
        
        elif action == 'sell':
            if self.position_type != 'short':
                # Close long position if exists
                if self.position_type == 'long':
                    self._close_position(price)
                
                # Open short position
                position_value = self.current_balance * 0.1  # Use 10% of balance
                self.position = position_value / price
                self.position_type = 'short'
                self.current_balance -= position_value * self.transaction_cost
        
        elif action == 'hold':
            # Do nothing
            pass
    
    def _close_position(self, price):
        """Close current position"""
        if self.position_type == 'long':
            # Close long position
            position_value = self.position * price
            self.current_balance += position_value * (1 - self.transaction_cost)
        
        elif self.position_type == 'short':
            # Close short position
            position_value = self.position * price
            # For short, profit when price goes down
            original_value = self.position * price  # This is simplified
            self.current_balance += original_value * (1 - self.transaction_cost)
        
        self.position = 0.0
        self.position_type = None
    
    def _calculate_reward(self, action, current_price, next_price):
        """Calculate reward for the action"""
        price_change = (next_price - current_price) / current_price
        
        # Base reward calculation
        if action == 'buy' and price_change > 0:
            reward = price_change * 100  # Positive reward for correct buy
        elif action == 'sell' and price_change < 0:
            reward = abs(price_change) * 100  # Positive reward for correct sell
        elif action == 'hold' and abs(price_change) < 0.01:
            reward = 1.0  # Small reward for holding in stable market
        else:
            reward = -abs(price_change) * 50  # Negative reward for wrong action
        
        # Add position-based reward
        if self.position_type == 'long' and price_change > 0:
            reward += price_change * self.position * 10
        elif self.position_type == 'short' and price_change < 0:
            reward += abs(price_change) * self.position * 10
        
        # Penalize excessive trading
        if action != 'hold':
            reward -= 0.5
        
        return reward
    
    def _generate_price_series(self, length=100):
        """Generate random price series for testing"""
        prices = [50000.0]  # Starting price
        
        for _ in range(length - 1):
            # Random walk with slight upward bias
            change = random.gauss(0.001, 0.02)  # Mean 0.1% increase, 2% volatility
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1000.0))  # Minimum price of $1000
        
        return prices
    
    def get_portfolio_value(self):
        """Get current portfolio value"""
        portfolio_value = self.current_balance
        
        if self.position_type == 'long':
            portfolio_value += self.position * self.current_price
        elif self.position_type == 'short':
            # For short positions, value decreases when price increases
            portfolio_value += self.position * self.current_price  # Simplified
        
        return portfolio_value
    
    def is_done(self):
        """Check if episode is finished"""
        return (self.step_count >= len(self.price_history) - 1 or 
                self.step_count >= self.max_steps or
                self.current_balance <= 0)
    
    def get_info(self):
        """Get environment information"""
        return {
            'step': self.step_count,
            'balance': self.current_balance,
            'position': self.position,
            'position_type': self.position_type,
            'portfolio_value': self.get_portfolio_value(),
            'total_return': (self.get_portfolio_value() - self.initial_balance) / self.initial_balance
        }
