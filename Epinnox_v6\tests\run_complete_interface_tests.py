#!/usr/bin/env python3
"""
Complete Interface Test Runner
Executes all interface tests and generates comprehensive operational report
"""

import sys
import os
import time
import logging
import subprocess
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteInterfaceTestRunner:
    """
    Comprehensive test runner for all interface components
    """
    
    def __init__(self):
        self.test_results = {}
        self.overall_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_suites': []
        }
        
    def run_test_suite(self, test_file, suite_name):
        """Run a specific test suite"""
        logger.info(f"🧪 Running {suite_name}...")
        
        try:
            # Import and run the test
            if test_file == "test_complete_interface_operations.py":
                from test_complete_interface_operations import run_complete_interface_tests
                success = run_complete_interface_tests()
                
            elif test_file == "test_real_interface_components.py":
                from test_real_interface_components import TestRealInterfaceComponents
                test_suite = TestRealInterfaceComponents()
                success = test_suite.run_all_tests()
                
            elif test_file == "test_gui_tabs_functionality.py":
                from test_gui_tabs_functionality import TestGUITabsFunctionality
                test_suite = TestGUITabsFunctionality()
                success = test_suite.run_all_tab_tests()
                
            else:
                logger.error(f"Unknown test file: {test_file}")
                return False
            
            self.test_results[suite_name] = {
                'success': success,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            if success:
                logger.info(f"   ✅ {suite_name}: PASSED")
            else:
                logger.error(f"   ❌ {suite_name}: FAILED")
                
            return success
            
        except Exception as e:
            logger.error(f"   ❌ {suite_name}: ERROR - {e}")
            self.test_results[suite_name] = {
                'success': False,
                'error': str(e),
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            return False
    
    def test_interface_imports(self):
        """Test that all interface modules can be imported"""
        logger.info("🧪 Testing Interface Module Imports...")
        
        import_tests = [
            ("gui.main_window", "Main Window"),
            ("gui.live_trading_tab", "Live Trading Tab"),
            ("gui.auto_trader_tab", "Auto Trader Tab"),
            ("gui.manual_trader_tab", "Manual Trader Tab"),
            ("gui.performance_dashboard_tab", "Performance Dashboard Tab"),
            ("gui.scalping_scanner_tab", "Scalping Scanner Tab"),
            ("gui.settings_tab", "Settings Tab"),
            ("launch_epinnox", "Main Interface"),
        ]
        
        import_results = []
        
        for module_name, description in import_tests:
            try:
                __import__(module_name)
                import_results.append(f"✅ {description}: Import successful")
                logger.info(f"   ✅ {description}: Import successful")
            except Exception as e:
                import_results.append(f"❌ {description}: Import failed - {e}")
                logger.error(f"   ❌ {description}: Import failed - {e}")
        
        return import_results
    
    def test_critical_dependencies(self):
        """Test critical dependencies are available"""
        logger.info("🧪 Testing Critical Dependencies...")
        
        dependency_tests = [
            ("PyQt5", "PyQt5 GUI Framework"),
            ("numpy", "NumPy"),
            ("pandas", "Pandas"),
            ("requests", "HTTP Requests"),
            ("websocket", "WebSocket Client"),
            ("ccxt", "CCXT Exchange Library"),
        ]
        
        dependency_results = []
        
        for module_name, description in dependency_tests:
            try:
                __import__(module_name)
                dependency_results.append(f"✅ {description}: Available")
                logger.info(f"   ✅ {description}: Available")
            except ImportError as e:
                dependency_results.append(f"❌ {description}: Missing - {e}")
                logger.error(f"   ❌ {description}: Missing - {e}")
        
        return dependency_results
    
    def test_configuration_files(self):
        """Test that configuration files exist and are valid"""
        logger.info("🧪 Testing Configuration Files...")
        
        config_files = [
            ("config/autonomous_deployment.yaml", "Autonomous Deployment Config"),
            ("credentials.py", "API Credentials"),
            ("config/trading_config.yaml", "Trading Configuration"),
            ("config/risk_management.yaml", "Risk Management Config"),
        ]
        
        config_results = []
        
        for file_path, description in config_files:
            full_path = project_root / file_path
            try:
                if full_path.exists():
                    if file_path.endswith('.py'):
                        # Test Python file can be imported
                        spec = __import__(file_path.replace('.py', '').replace('/', '.'))
                        config_results.append(f"✅ {description}: Valid Python file")
                    elif file_path.endswith('.yaml'):
                        # Test YAML file can be parsed
                        import yaml
                        with open(full_path, 'r') as f:
                            yaml.safe_load(f)
                        config_results.append(f"✅ {description}: Valid YAML file")
                    else:
                        config_results.append(f"✅ {description}: File exists")
                    
                    logger.info(f"   ✅ {description}: Valid")
                else:
                    config_results.append(f"⚠️ {description}: File not found")
                    logger.warning(f"   ⚠️ {description}: File not found")
                    
            except Exception as e:
                config_results.append(f"❌ {description}: Invalid - {e}")
                logger.error(f"   ❌ {description}: Invalid - {e}")
        
        return config_results
    
    def run_all_tests(self):
        """Run all interface tests"""
        logger.info("🚀 Starting Complete Interface Testing Suite...")
        
        start_time = time.time()
        
        # Test 1: Module imports
        import_results = self.test_interface_imports()
        
        # Test 2: Dependencies
        dependency_results = self.test_critical_dependencies()
        
        # Test 3: Configuration files
        config_results = self.test_configuration_files()
        
        # Test 4: Direct interface functionality
        direct_success = True
        try:
            from test_interface_functionality_direct import DirectInterfaceTest
            direct_test = DirectInterfaceTest()
            direct_success = direct_test.run_all_tests()
            self.test_results["Direct Interface"] = {
                'success': direct_success,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            logger.warning(f"Direct interface test failed: {e}")
            direct_success = False

        # Test 5: PyQt GUI components
        pyqt_success = True
        try:
            from test_gui_components_pyqt import TestGUIComponentsPyQt
            pyqt_test = TestGUIComponentsPyQt()
            pyqt_success = pyqt_test.run_all_tests()
            self.test_results["PyQt GUI Components"] = {
                'success': pyqt_success,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            logger.warning(f"PyQt GUI test failed: {e}")
            pyqt_success = False

        # Test 6: Interface operations (mock-based)
        operations_success = True
        try:
            operations_success = self.run_test_suite(
                "test_complete_interface_operations.py",
                "Interface Operations"
            )
        except Exception as e:
            logger.warning(f"Interface operations test failed: {e}")
            operations_success = False
        
        end_time = time.time()
        test_duration = end_time - start_time
        
        # Generate comprehensive report
        report = self.generate_comprehensive_report(
            import_results, dependency_results, config_results,
            direct_success, pyqt_success, operations_success,
            test_duration
        )

        print(report)

        # Save report to file
        report_file = project_root / "tests" / f"interface_test_report_{int(time.time())}.txt"
        with open(report_file, 'w') as f:
            f.write(report)

        logger.info(f"📄 Test report saved to: {report_file}")

        # Return overall success
        return all([direct_success, pyqt_success, operations_success])
    
    def generate_comprehensive_report(self, import_results, dependency_results,
                                    config_results, direct_success,
                                    pyqt_success, operations_success, test_duration):
        """Generate comprehensive test report"""
        
        total_suites = 3
        passed_suites = sum([direct_success, pyqt_success, operations_success])
        suite_pass_rate = (passed_suites / total_suites * 100)
        
        # Count individual results
        import_passed = len([r for r in import_results if r.startswith('✅')])
        import_total = len(import_results)
        
        dependency_passed = len([r for r in dependency_results if r.startswith('✅')])
        dependency_total = len(dependency_results)
        
        config_passed = len([r for r in config_results if r.startswith('✅')])
        config_total = len(config_results)
        
        report = f"""
🧪 COMPLETE INTERFACE TESTING REPORT
{'='*80}
Test Date: {time.strftime('%Y-%m-%d %H:%M:%S')}
Test Duration: {test_duration:.2f} seconds
Overall Status: {'🎉 ALL SYSTEMS OPERATIONAL' if passed_suites == total_suites else '⚠️ SOME SYSTEMS NEED ATTENTION'}

📊 TEST SUITE SUMMARY:
Test Suites Passed: {passed_suites}/{total_suites} ({suite_pass_rate:.1f}%)
   {'✅' if direct_success else '❌'} Direct Interface Functionality: {'PASSED' if direct_success else 'FAILED'}
   {'✅' if pyqt_success else '❌'} PyQt GUI Components: {'PASSED' if pyqt_success else 'FAILED'}
   {'✅' if operations_success else '❌'} Interface Operations: {'PASSED' if operations_success else 'FAILED'}

🔧 SYSTEM PREREQUISITES:
Module Imports: {import_passed}/{import_total} passed
{chr(10).join([f"   {result}" for result in import_results])}

Dependencies: {dependency_passed}/{dependency_total} available
{chr(10).join([f"   {result}" for result in dependency_results])}

Configuration Files: {config_passed}/{config_total} valid
{chr(10).join([f"   {result}" for result in config_results])}

🎯 INTERFACE COMPONENTS TESTED:
   🔘 Menu Bar Operations: Layout, Settings, About menus
   🔘 LLM Orchestrator Controls: Toggle, Emergency Stop, Analysis cycles
   🔘 Trading Controls: Buy, Sell, Cancel, Close operations
   🔘 Data Refresh Controls: Positions, Orders, Balance, Market data
   🔘 Input Controls: Combos, Spinboxes, Checkboxes, Text inputs
   🔘 Status Displays: System status, Balance, Mode, Time displays
   🔘 Button Responsiveness: Click handling and state management
   🔘 Timer Operations: Auto-refresh and periodic updates
   🔘 GUI Tabs: Live Trading, Auto Trader, Manual Trader, Performance, Scanner, Settings

🚀 OPERATIONAL READINESS:
   {'✅' if import_passed == import_total else '❌'} All GUI modules can be imported
   {'✅' if dependency_passed >= dependency_total * 0.8 else '❌'} Critical dependencies available
   {'✅' if config_passed >= config_total * 0.8 else '❌'} Configuration files valid
   {'✅' if direct_success else '❌'} Direct interface functionality operational
   {'✅' if pyqt_success else '❌'} PyQt GUI components functional
   {'✅' if operations_success else '❌'} Interface operations accessible

📋 RECOMMENDATIONS:
{self.generate_recommendations(import_passed, import_total, dependency_passed, dependency_total, config_passed, config_total, direct_success, pyqt_success, operations_success)}

🎉 INTERFACE STATUS: {'FULLY OPERATIONAL - READY FOR DEPLOYMENT' if passed_suites == total_suites and import_passed == import_total and dependency_passed >= dependency_total * 0.8 else 'NEEDS ATTENTION - REVIEW FAILED COMPONENTS'}
"""
        return report
    
    def generate_recommendations(self, import_passed, import_total, dependency_passed,
                               dependency_total, config_passed, config_total,
                               direct_success, pyqt_success, operations_success):
        """Generate recommendations based on test results"""
        recommendations = []
        
        if import_passed < import_total:
            recommendations.append("   🔧 Fix module import issues before deployment")
        
        if dependency_passed < dependency_total:
            recommendations.append("   📦 Install missing dependencies: pip install -r requirements.txt")
        
        if config_passed < config_total:
            recommendations.append("   ⚙️ Review and fix configuration files")
        
        if not direct_success:
            recommendations.append("   🔨 Debug direct interface functionality and method implementations")

        if not pyqt_success:
            recommendations.append("   🧩 Verify PyQt GUI component functionality and widget interactions")

        if not operations_success:
            recommendations.append("   📑 Review interface operations and mock implementations")
        
        if not recommendations:
            recommendations.append("   🎉 All systems operational - interface ready for production use!")
        
        return '\n'.join(recommendations)

def main():
    """Main test execution"""
    print("🚀 EPINNOX v6 COMPLETE INTERFACE TESTING SUITE")
    print("=" * 60)
    
    test_runner = CompleteInterfaceTestRunner()
    success = test_runner.run_all_tests()
    
    if success:
        print("\n🎉 ALL INTERFACE TESTS PASSED - SYSTEM READY FOR DEPLOYMENT!")
        return 0
    else:
        print("\n⚠️ SOME TESTS FAILED - REVIEW RESULTS AND FIX ISSUES")
        return 1

if __name__ == "__main__":
    sys.exit(main())
