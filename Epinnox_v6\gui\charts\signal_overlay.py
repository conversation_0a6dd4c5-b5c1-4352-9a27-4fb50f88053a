"""
Signal Overlay for Live Chart Widget
Displays ML/LLM trading signals as visual overlays on the candlestick chart
"""

import pyqtgraph as pg
import numpy as np
from PyQt5.QtGui import QColor, QPen, QBrush, QFont
from PyQt5.QtCore import QRectF, QPointF
from typing import List, Optional, Dict
from datetime import datetime

from ..matrix_theme import MatrixTheme
from ...ml.signal_generator import TradingSignal, SignalType, SignalStrength


class SignalArrow(pg.GraphicsObject):
    """Custom arrow for displaying trading signals"""
    
    def __init__(self, signal: TradingSignal, timestamp: float, price: float):
        super().__init__()
        self.signal = signal
        self.timestamp = timestamp
        self.price = price
        self.arrow_size = 20
        
        # Determine colors based on signal type
        if signal.signal_type == SignalType.BUY or signal.signal_type == SignalType.STRONG_BUY:
            self.color = QColor(MatrixTheme.GREEN)
            self.arrow_direction = 'up'
        elif signal.signal_type == SignalType.SELL or signal.signal_type == SignalType.STRONG_SELL:
            self.color = QColor(MatrixTheme.RED)
            self.arrow_direction = 'down'
        else:  # HOLD
            self.color = QColor(MatrixTheme.YELLOW)
            self.arrow_direction = 'right'
        
        # Adjust opacity based on confidence
        alpha = int(255 * signal.confidence)
        self.color.setAlpha(alpha)
        
        # Adjust size based on strength
        if signal.strength == SignalStrength.VERY_STRONG:
            self.arrow_size = 30
        elif signal.strength == SignalStrength.STRONG:
            self.arrow_size = 25
        elif signal.strength == SignalStrength.MODERATE:
            self.arrow_size = 20
        else:  # WEAK
            self.arrow_size = 15
    
    def boundingRect(self):
        """Return bounding rectangle"""
        return QRectF(-self.arrow_size/2, -self.arrow_size/2, self.arrow_size, self.arrow_size)
    
    def paint(self, painter, option, widget):
        """Paint the signal arrow"""
        try:
            painter.setRenderHint(painter.Antialiasing, True)
            
            # Set pen and brush
            pen = QPen(self.color, 2)
            brush = QBrush(self.color)
            painter.setPen(pen)
            painter.setBrush(brush)
            
            # Draw arrow based on direction
            if self.arrow_direction == 'up':
                # Buy arrow (pointing up)
                points = [
                    QPointF(0, -self.arrow_size/2),      # Top point
                    QPointF(-self.arrow_size/3, 0),      # Left point
                    QPointF(-self.arrow_size/6, 0),      # Left inner
                    QPointF(-self.arrow_size/6, self.arrow_size/2),  # Left bottom
                    QPointF(self.arrow_size/6, self.arrow_size/2),   # Right bottom
                    QPointF(self.arrow_size/6, 0),       # Right inner
                    QPointF(self.arrow_size/3, 0),       # Right point
                ]
            elif self.arrow_direction == 'down':
                # Sell arrow (pointing down)
                points = [
                    QPointF(0, self.arrow_size/2),       # Bottom point
                    QPointF(-self.arrow_size/3, 0),      # Left point
                    QPointF(-self.arrow_size/6, 0),      # Left inner
                    QPointF(-self.arrow_size/6, -self.arrow_size/2), # Left top
                    QPointF(self.arrow_size/6, -self.arrow_size/2),  # Right top
                    QPointF(self.arrow_size/6, 0),       # Right inner
                    QPointF(self.arrow_size/3, 0),       # Right point
                ]
            else:  # right (hold)
                # Hold arrow (pointing right)
                points = [
                    QPointF(self.arrow_size/2, 0),       # Right point
                    QPointF(0, -self.arrow_size/3),      # Top point
                    QPointF(0, -self.arrow_size/6),      # Top inner
                    QPointF(-self.arrow_size/2, -self.arrow_size/6), # Left top
                    QPointF(-self.arrow_size/2, self.arrow_size/6),  # Left bottom
                    QPointF(0, self.arrow_size/6),       # Bottom inner
                    QPointF(0, self.arrow_size/3),       # Bottom point
                ]
            
            painter.drawPolygon(points)
            
        except Exception as e:
            print(f"Error painting signal arrow: {e}")


class ConfidenceBar(pg.GraphicsObject):
    """Confidence bar indicator"""
    
    def __init__(self, confidence: float, timestamp: float, price: float):
        super().__init__()
        self.confidence = confidence
        self.timestamp = timestamp
        self.price = price
        self.bar_width = 40
        self.bar_height = 100
        
        # Color based on confidence level
        if confidence >= 0.8:
            self.color = QColor(MatrixTheme.GREEN)
        elif confidence >= 0.6:
            self.color = QColor(MatrixTheme.YELLOW)
        else:
            self.color = QColor(MatrixTheme.RED)
        
        # Set alpha based on confidence
        alpha = int(100 + (155 * confidence))  # 100-255 range
        self.color.setAlpha(alpha)
    
    def boundingRect(self):
        """Return bounding rectangle"""
        return QRectF(-self.bar_width/2, 0, self.bar_width, self.bar_height)
    
    def paint(self, painter, option, widget):
        """Paint the confidence bar"""
        try:
            painter.setRenderHint(painter.Antialiasing, True)
            
            # Background bar (gray)
            bg_pen = QPen(QColor(50, 50, 50), 1)
            bg_brush = QBrush(QColor(30, 30, 30, 100))
            painter.setPen(bg_pen)
            painter.setBrush(bg_brush)
            painter.drawRect(-self.bar_width/2, 0, self.bar_width, self.bar_height)
            
            # Confidence bar
            conf_height = self.bar_height * self.confidence
            conf_pen = QPen(self.color, 1)
            conf_brush = QBrush(self.color)
            painter.setPen(conf_pen)
            painter.setBrush(conf_brush)
            painter.drawRect(-self.bar_width/2, self.bar_height - conf_height, 
                           self.bar_width, conf_height)
            
            # Confidence text
            painter.setPen(QPen(QColor(255, 255, 255), 1))
            font = QFont("Arial", 8)
            painter.setFont(font)
            text = f"{self.confidence:.0%}"
            painter.drawText(-self.bar_width/2, -5, self.bar_width, 15, 0x84, text)
            
        except Exception as e:
            print(f"Error painting confidence bar: {e}")


class PriceTargetLine(pg.InfiniteLine):
    """Price target line with label"""
    
    def __init__(self, price: float, label: str, color: str, line_style=pg.QtCore.Qt.DashLine):
        super().__init__(
            pos=price,
            angle=0,
            pen=pg.mkPen(color, width=2, style=line_style),
            label=label,
            labelOpts={
                'position': 0.95,
                'color': color,
                'fill': (0, 0, 0, 100),
                'movable': False
            }
        )


class SignalOverlay:
    """Main signal overlay manager for the chart"""
    
    def __init__(self, chart_widget):
        self.chart_widget = chart_widget
        self.signal_arrows = []
        self.confidence_bars = []
        self.price_target_lines = []
        self.max_signals = 20  # Maximum number of signals to display
        
    def add_signal(self, signal: TradingSignal, timestamp: float, price: float):
        """Add a new trading signal to the chart"""
        try:
            # Create signal arrow
            arrow = SignalArrow(signal, timestamp, price)
            arrow.setPos(timestamp, price)
            self.chart_widget.addItem(arrow)
            self.signal_arrows.append(arrow)
            
            # Create confidence bar (positioned to the right of the arrow)
            if signal.confidence > 0.6:  # Only show for moderate+ confidence
                conf_bar = ConfidenceBar(signal.confidence, timestamp, price)
                # Position slightly to the right and above/below based on signal type
                offset_y = 20 if signal.signal_type in [SignalType.BUY, SignalType.STRONG_BUY] else -120
                conf_bar.setPos(timestamp + 300, price + offset_y)  # 300 seconds to the right
                self.chart_widget.addItem(conf_bar)
                self.confidence_bars.append(conf_bar)
            
            # Add price target lines
            self._add_price_targets(signal)
            
            # Limit number of signals displayed
            self._cleanup_old_signals()
            
            print(f"Added {signal.signal_type.value} signal at {price:.6f} with {signal.confidence:.1%} confidence")
            
        except Exception as e:
            print(f"Error adding signal overlay: {e}")
    
    def _add_price_targets(self, signal: TradingSignal):
        """Add price target lines for the signal"""
        try:
            # Add take profit line
            if signal.take_profit:
                tp_line = PriceTargetLine(
                    signal.take_profit, 
                    f"TP: {signal.take_profit:.6f}",
                    MatrixTheme.GREEN,
                    pg.QtCore.Qt.DotLine
                )
                self.chart_widget.addItem(tp_line)
                self.price_target_lines.append(tp_line)
            
            # Add stop loss line
            if signal.stop_loss:
                sl_line = PriceTargetLine(
                    signal.stop_loss,
                    f"SL: {signal.stop_loss:.6f}",
                    MatrixTheme.RED,
                    pg.QtCore.Qt.DotLine
                )
                self.chart_widget.addItem(sl_line)
                self.price_target_lines.append(sl_line)
            
            # Add price target line
            if signal.price_target:
                target_color = MatrixTheme.GREEN if signal.signal_type in [SignalType.BUY, SignalType.STRONG_BUY] else MatrixTheme.RED
                pt_line = PriceTargetLine(
                    signal.price_target,
                    f"Target: {signal.price_target:.6f}",
                    target_color,
                    pg.QtCore.Qt.DashDotLine
                )
                self.chart_widget.addItem(pt_line)
                self.price_target_lines.append(pt_line)
                
        except Exception as e:
            print(f"Error adding price targets: {e}")
    
    def _cleanup_old_signals(self):
        """Remove old signals to prevent chart clutter"""
        try:
            # Remove old arrows
            while len(self.signal_arrows) > self.max_signals:
                old_arrow = self.signal_arrows.pop(0)
                self.chart_widget.removeItem(old_arrow)
            
            # Remove old confidence bars
            while len(self.confidence_bars) > self.max_signals:
                old_bar = self.confidence_bars.pop(0)
                self.chart_widget.removeItem(old_bar)
            
            # Remove old price target lines (keep only recent ones)
            while len(self.price_target_lines) > self.max_signals * 3:  # 3 lines per signal max
                old_line = self.price_target_lines.pop(0)
                self.chart_widget.removeItem(old_line)
                
        except Exception as e:
            print(f"Error cleaning up old signals: {e}")
    
    def clear_all_signals(self):
        """Clear all signal overlays from the chart"""
        try:
            # Remove all arrows
            for arrow in self.signal_arrows:
                self.chart_widget.removeItem(arrow)
            self.signal_arrows.clear()
            
            # Remove all confidence bars
            for bar in self.confidence_bars:
                self.chart_widget.removeItem(bar)
            self.confidence_bars.clear()
            
            # Remove all price target lines
            for line in self.price_target_lines:
                self.chart_widget.removeItem(line)
            self.price_target_lines.clear()
            
            print("Cleared all signal overlays")
            
        except Exception as e:
            print(f"Error clearing signal overlays: {e}")
    
    def update_signal_visibility(self, show_arrows: bool = True, show_confidence: bool = True, 
                                show_targets: bool = True):
        """Update visibility of signal components"""
        try:
            # Toggle arrow visibility
            for arrow in self.signal_arrows:
                arrow.setVisible(show_arrows)
            
            # Toggle confidence bar visibility
            for bar in self.confidence_bars:
                bar.setVisible(show_confidence)
            
            # Toggle price target visibility
            for line in self.price_target_lines:
                line.setVisible(show_targets)
                
        except Exception as e:
            print(f"Error updating signal visibility: {e}")
    
    def get_signal_at_position(self, timestamp: float, price: float, tolerance: float = 100) -> Optional[TradingSignal]:
        """Get signal near the specified position"""
        try:
            for arrow in self.signal_arrows:
                if (abs(arrow.timestamp - timestamp) < tolerance and 
                    abs(arrow.price - price) < price * 0.01):  # 1% price tolerance
                    return arrow.signal
            return None
            
        except Exception as e:
            print(f"Error getting signal at position: {e}")
            return None
