#!/usr/bin/env python3
"""
Configuration Validation Script for Autonomous Trading System
Validates and tests configuration management
"""

import sys
import os
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_config_management():
    """Test configuration management system"""
    logger.info("🧪 Testing Configuration Management...")
    
    results = {}
    
    # Test 1: Import configuration manager
    try:
        from config.autonomous_config import AutonomousConfigManager, config_manager
        results['ConfigManager_Import'] = "✅ PASS"
        logger.info("✅ Configuration manager imported successfully")
    except Exception as e:
        results['ConfigManager_Import'] = f"❌ FAIL: {e}"
        logger.error(f"❌ Configuration manager import failed: {e}")
        return results
    
    # Test 2: Default configuration creation
    try:
        config = AutonomousConfigManager()
        results['Default_Config'] = "✅ PASS"
        logger.info("✅ Default configuration created successfully")
    except Exception as e:
        results['Default_Config'] = f"❌ FAIL: {e}"
        logger.error(f"❌ Default configuration creation failed: {e}")
    
    # Test 3: Configuration validation
    try:
        validation_result = config.validate_config()
        if validation_result['valid']:
            results['Config_Validation'] = "✅ PASS"
            logger.info("✅ Configuration validation passed")
        else:
            results['Config_Validation'] = f"❌ FAIL: {validation_result['issues']}"
            logger.error(f"❌ Configuration validation failed: {validation_result['issues']}")
    except Exception as e:
        results['Config_Validation'] = f"❌ FAIL: {e}"
        logger.error(f"❌ Configuration validation failed: {e}")
    
    # Test 4: Configuration access methods
    try:
        trading_config = config.get_trading_config()
        risk_config = config.get_risk_config()
        ml_config = config.get_ml_config()
        all_config = config.get_all_config()
        
        if all(isinstance(cfg, dict) for cfg in [trading_config, risk_config, ml_config, all_config]):
            results['Config_Access'] = "✅ PASS"
            logger.info("✅ Configuration access methods work correctly")
        else:
            results['Config_Access'] = "❌ FAIL: Invalid config format"
    except Exception as e:
        results['Config_Access'] = f"❌ FAIL: {e}"
        logger.error(f"❌ Configuration access failed: {e}")
    
    # Test 5: Configuration updates
    try:
        original_balance = config.trading.initial_balance
        config.update_trading_config(initial_balance=5000.0)
        
        if config.trading.initial_balance == 5000.0:
            results['Config_Update'] = "✅ PASS"
            logger.info("✅ Configuration update works correctly")
            
            # Restore original value
            config.update_trading_config(initial_balance=original_balance)
        else:
            results['Config_Update'] = "❌ FAIL: Update not applied"
    except Exception as e:
        results['Config_Update'] = f"❌ FAIL: {e}"
        logger.error(f"❌ Configuration update failed: {e}")
    
    # Test 6: Configuration file operations
    try:
        test_config_file = 'configs/test_config.yaml'
        config.config_file = test_config_file
        config.save_config()
        
        # Create new instance and load
        test_config = AutonomousConfigManager(test_config_file)
        
        if os.path.exists(test_config_file):
            results['Config_File_Ops'] = "✅ PASS"
            logger.info("✅ Configuration file operations work correctly")
            
            # Cleanup
            try:
                os.remove(test_config_file)
            except:
                pass
        else:
            results['Config_File_Ops'] = "❌ FAIL: File not created"
    except Exception as e:
        results['Config_File_Ops'] = f"❌ FAIL: {e}"
        logger.error(f"❌ Configuration file operations failed: {e}")
    
    return results

def display_current_config():
    """Display current configuration"""
    logger.info("📋 Current Configuration:")
    logger.info("=" * 50)
    
    try:
        from config.autonomous_config import config_manager
        
        config = config_manager.get_all_config()
        
        for section, settings in config.items():
            logger.info(f"\n[{section.upper()}]")
            for key, value in settings.items():
                logger.info(f"  {key}: {value}")
    
    except Exception as e:
        logger.error(f"❌ Failed to display configuration: {e}")

def validate_production_config():
    """Validate configuration for production use"""
    logger.info("🔍 Validating Production Configuration...")
    
    try:
        from config.autonomous_config import config_manager
        
        validation = config_manager.validate_config()
        
        if validation['valid']:
            logger.info("✅ Configuration is valid for production")
        else:
            logger.error("❌ Configuration has issues:")
            for issue in validation['issues']:
                logger.error(f"  - {issue}")
        
        if validation['warnings']:
            logger.warning("⚠️  Configuration warnings:")
            for warning in validation['warnings']:
                logger.warning(f"  - {warning}")
        
        return validation['valid']
        
    except Exception as e:
        logger.error(f"❌ Configuration validation failed: {e}")
        return False

def main():
    """Run all configuration tests"""
    logger.info("🚀 Starting Configuration Management Tests")
    logger.info("=" * 60)
    
    # Test configuration management
    test_results = test_config_management()
    
    # Display current configuration
    display_current_config()
    
    # Validate for production
    production_ready = validate_production_config()
    
    # Summary
    logger.info("=" * 60)
    logger.info("📊 CONFIGURATION TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results.items():
        logger.info(f"{test_name}: {result}")
        if "✅ PASS" in result:
            passed += 1
        else:
            failed += 1
    
    logger.info("=" * 60)
    logger.info(f"✅ PASSED: {passed}")
    logger.info(f"❌ FAILED: {failed}")
    logger.info(f"📈 SUCCESS RATE: {passed/(passed+failed)*100:.1f}%")
    logger.info(f"🏭 PRODUCTION READY: {'YES' if production_ready else 'NO'}")
    
    if failed == 0 and production_ready:
        logger.info("🎉 ALL CONFIGURATION TESTS PASSED!")
        return 0
    else:
        logger.info("⚠️  CONFIGURATION ISSUES DETECTED")
        return 1

if __name__ == "__main__":
    sys.exit(main())
