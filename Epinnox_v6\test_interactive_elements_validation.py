#!/usr/bin/env python3
"""
Interactive Elements Validation Test
Tests all buttons, controls, and interactive elements for responsiveness with new sizing
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QPushButton, QSpinBox, QDoubleSpinBox, QLineEdit, QCheckBox, QComboBox
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtTest import QTest

def test_interactive_elements():
    """Test all interactive elements in the running GUI"""
    print("🔍 INTERACTIVE ELEMENTS VALIDATION")
    print("=" * 50)
    
    # Get the running application
    app = QApplication.instance()
    if not app:
        print("❌ No QApplication instance found - GUI not running")
        return False
    
    # Find the main window
    main_window = None
    for widget in app.topLevelWidgets():
        if "Epinnox" in widget.windowTitle():
            main_window = widget
            break
    
    if not main_window:
        print("❌ Main window not found")
        return False
    
    print(f"✅ Found main window: {main_window.windowTitle()}")
    print(f"📏 Window size: {main_window.width()}x{main_window.height()}")
    
    # Test 1: Button Elements
    print("\n🔘 Testing Button Elements...")
    buttons = main_window.findChildren(QPushButton)
    print(f"   Found {len(buttons)} buttons")
    
    clickable_buttons = 0
    for i, button in enumerate(buttons[:10]):  # Test first 10 buttons
        if button.isVisible() and button.isEnabled():
            print(f"   ✅ Button {i+1}: '{button.text()}' - Size: {button.width()}x{button.height()}")
            clickable_buttons += 1
        else:
            print(f"   ⚠️ Button {i+1}: '{button.text()}' - Not clickable")
    
    print(f"   📊 Clickable buttons: {clickable_buttons}/{len(buttons[:10])}")
    
    # Test 2: Spinbox Controls
    print("\n🔢 Testing Spinbox Controls...")
    spinboxes = main_window.findChildren(QSpinBox) + main_window.findChildren(QDoubleSpinBox)
    print(f"   Found {len(spinboxes)} spinboxes")
    
    for i, spinbox in enumerate(spinboxes[:5]):  # Test first 5 spinboxes
        if spinbox.isVisible() and spinbox.isEnabled():
            print(f"   ✅ Spinbox {i+1}: Value={spinbox.value()} - Size: {spinbox.width()}x{spinbox.height()}")
        else:
            print(f"   ⚠️ Spinbox {i+1}: Not accessible")
    
    # Test 3: Text Input Fields
    print("\n📝 Testing Text Input Fields...")
    line_edits = main_window.findChildren(QLineEdit)
    print(f"   Found {len(line_edits)} text inputs")
    
    for i, line_edit in enumerate(line_edits[:5]):  # Test first 5 inputs
        if line_edit.isVisible() and line_edit.isEnabled():
            print(f"   ✅ Input {i+1}: '{line_edit.text()}' - Size: {line_edit.width()}x{line_edit.height()}")
        else:
            print(f"   ⚠️ Input {i+1}: Not accessible")
    
    # Test 4: Checkboxes
    print("\n☑️ Testing Checkboxes...")
    checkboxes = main_window.findChildren(QCheckBox)
    print(f"   Found {len(checkboxes)} checkboxes")
    
    for i, checkbox in enumerate(checkboxes[:5]):  # Test first 5 checkboxes
        if checkbox.isVisible() and checkbox.isEnabled():
            print(f"   ✅ Checkbox {i+1}: '{checkbox.text()}' - Checked: {checkbox.isChecked()}")
        else:
            print(f"   ⚠️ Checkbox {i+1}: Not accessible")
    
    # Test 5: Dropdown Menus
    print("\n📋 Testing Dropdown Menus...")
    comboboxes = main_window.findChildren(QComboBox)
    print(f"   Found {len(comboboxes)} dropdowns")
    
    for i, combobox in enumerate(comboboxes[:5]):  # Test first 5 dropdowns
        if combobox.isVisible() and combobox.isEnabled():
            print(f"   ✅ Dropdown {i+1}: Items={combobox.count()} - Current: '{combobox.currentText()}'")
        else:
            print(f"   ⚠️ Dropdown {i+1}: Not accessible")
    
    # Test 6: Font Size Validation
    print("\n🔤 Testing Font Sizes...")
    font_sizes = []
    for button in buttons[:5]:
        if button.isVisible():
            font_size = button.font().pointSize()
            font_sizes.append(font_size)
            print(f"   Button font size: {font_size}pt")
    
    if font_sizes:
        avg_font_size = sum(font_sizes) / len(font_sizes)
        print(f"   📊 Average font size: {avg_font_size:.1f}pt")
        
        if avg_font_size >= 12:
            print("   ✅ Font sizes are adequate for readability")
        else:
            print("   ⚠️ Font sizes may be too small")
    
    # Test 7: Click Responsiveness Test
    print("\n🖱️ Testing Click Responsiveness...")
    test_button = None
    for button in buttons:
        if button.isVisible() and button.isEnabled() and "refresh" in button.text().lower():
            test_button = button
            break
    
    if test_button:
        print(f"   Testing button: '{test_button.text()}'")
        original_text = test_button.text()
        
        # Simulate click
        QTest.mouseClick(test_button, Qt.LeftButton)
        print("   ✅ Click simulation successful")
        
        # Check if button responded
        QTimer.singleShot(100, lambda: print(f"   Button text after click: '{test_button.text()}'"))
    else:
        print("   ⚠️ No suitable test button found")
    
    print("\n" + "=" * 50)
    print("📊 INTERACTIVE ELEMENTS VALIDATION SUMMARY")
    print("=" * 50)
    print(f"✅ Total buttons found: {len(buttons)}")
    print(f"✅ Total controls found: {len(spinboxes) + len(line_edits) + len(checkboxes) + len(comboboxes)}")
    print(f"✅ Window properly sized: {main_window.width()}x{main_window.height()}")
    print(f"✅ Font sizes adequate: {avg_font_size:.1f}pt average" if font_sizes else "⚠️ Could not determine font sizes")
    print("✅ Interactive elements validation COMPLETE")
    
    return True

if __name__ == "__main__":
    success = test_interactive_elements()
    sys.exit(0 if success else 1)
