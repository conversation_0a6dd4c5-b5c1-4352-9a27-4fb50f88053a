"""
Auto Trade Settings Dialog
GUI dialog for configuring autonomous trading parameters
"""

try:
    from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                                QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox,
                                QPushButton, QLabel, QGroupBox, QSlider)
    from PyQt5.QtCore import Qt, pyqtSignal
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

import logging

logger = logging.getLogger(__name__)

class AutoTradeSettingsDialog:
    """
    Auto Trade Settings Dialog for configuring trading parameters
    Falls back to console input if PyQt5 is not available
    """
    
    def __init__(self, parent=None):
        self.parent = parent
        self.settings = {}
        
        if not PYQT_AVAILABLE:
            logger.warning("PyQt5 not available. Using console-based configuration.")
    
    def get_settings(self):
        """Get trading settings from user"""
        if PYQT_AVAILABLE:
            return self._get_settings_gui()
        else:
            return self._get_settings_console()
    
    def _get_settings_gui(self):
        """Get settings using PyQt5 GUI"""
        try:
            dialog = AutoTradeSettingsDialogGUI(self.parent)
            if dialog.exec_() == QDialog.Accepted:
                return dialog.get_settings()
            return None
        except Exception as e:
            logger.error(f"GUI dialog failed: {e}")
            return self._get_settings_console()
    
    def _get_settings_console(self):
        """Get settings using console input"""
        print("\n🔧 Auto Trade Settings Configuration")
        print("=" * 40)
        
        try:
            settings = {}
            
            # Basic settings
            settings['initial_balance'] = float(input("Initial Balance ($) [10000]: ") or "10000")
            settings['max_positions'] = int(input("Max Positions [3]: ") or "3")
            settings['min_confidence'] = float(input("Min Confidence (0-1) [0.65]: ") or "0.65")
            
            # Risk settings
            settings['max_portfolio_risk'] = float(input("Max Portfolio Risk (0-1) [0.20]: ") or "0.20")
            settings['max_position_size'] = float(input("Max Position Size (0-1) [0.10]: ") or "0.10")
            settings['max_leverage'] = float(input("Max Leverage [3.0]: ") or "3.0")
            
            # Trading settings
            settings['use_rl'] = input("Use RL Agent? (y/n) [n]: ").lower().startswith('y')
            settings['autonomous_mode'] = input("Autonomous Mode? (y/n) [y]: ").lower() != 'n'
            settings['paper_trading'] = input("Paper Trading? (y/n) [y]: ").lower() != 'n'
            
            # Symbols
            symbols_input = input("Trading Symbols (comma-separated) [BTC/USDT,ETH/USDT]: ")
            settings['symbols'] = [s.strip() for s in (symbols_input or "BTC/USDT,ETH/USDT").split(',')]
            
            print("\n✅ Settings configured successfully!")
            return settings
            
        except (ValueError, KeyboardInterrupt) as e:
            print(f"\n❌ Configuration failed: {e}")
            return None

if PYQT_AVAILABLE:
    class AutoTradeSettingsDialogGUI(QDialog):
        """PyQt5 GUI version of the settings dialog"""
        
        def __init__(self, parent=None):
            super().__init__(parent)
            self.setWindowTitle("Auto Trade Settings")
            self.setModal(True)
            self.resize(400, 600)
            
            self.setup_ui()
            self.load_defaults()
        
        def setup_ui(self):
            """Setup the user interface"""
            layout = QVBoxLayout(self)
            
            # Basic Settings Group
            basic_group = QGroupBox("Basic Settings")
            basic_layout = QFormLayout(basic_group)
            
            self.initial_balance = QDoubleSpinBox()
            self.initial_balance.setRange(100, 1000000)
            self.initial_balance.setValue(10000)
            self.initial_balance.setSuffix(" $")
            basic_layout.addRow("Initial Balance:", self.initial_balance)
            
            self.max_positions = QSpinBox()
            self.max_positions.setRange(1, 20)
            self.max_positions.setValue(3)
            basic_layout.addRow("Max Positions:", self.max_positions)
            
            self.min_confidence = QDoubleSpinBox()
            self.min_confidence.setRange(0.0, 1.0)
            self.min_confidence.setSingleStep(0.05)
            self.min_confidence.setValue(0.65)
            basic_layout.addRow("Min Confidence:", self.min_confidence)
            
            layout.addWidget(basic_group)
            
            # Risk Management Group
            risk_group = QGroupBox("Risk Management")
            risk_layout = QFormLayout(risk_group)
            
            self.max_portfolio_risk = QDoubleSpinBox()
            self.max_portfolio_risk.setRange(0.01, 1.0)
            self.max_portfolio_risk.setSingleStep(0.01)
            self.max_portfolio_risk.setValue(0.20)
            risk_layout.addRow("Max Portfolio Risk:", self.max_portfolio_risk)
            
            self.max_position_size = QDoubleSpinBox()
            self.max_position_size.setRange(0.01, 1.0)
            self.max_position_size.setSingleStep(0.01)
            self.max_position_size.setValue(0.10)
            risk_layout.addRow("Max Position Size:", self.max_position_size)
            
            self.max_leverage = QDoubleSpinBox()
            self.max_leverage.setRange(1.0, 20.0)
            self.max_leverage.setValue(3.0)
            risk_layout.addRow("Max Leverage:", self.max_leverage)
            
            layout.addWidget(risk_group)
            
            # Trading Options Group
            options_group = QGroupBox("Trading Options")
            options_layout = QFormLayout(options_group)
            
            self.use_rl = QCheckBox("Use Reinforcement Learning")
            options_layout.addRow(self.use_rl)
            
            self.autonomous_mode = QCheckBox("Autonomous Mode")
            self.autonomous_mode.setChecked(True)
            options_layout.addRow(self.autonomous_mode)
            
            self.paper_trading = QCheckBox("Paper Trading")
            self.paper_trading.setChecked(True)
            options_layout.addRow(self.paper_trading)
            
            layout.addWidget(options_group)
            
            # Symbols Group
            symbols_group = QGroupBox("Trading Symbols")
            symbols_layout = QVBoxLayout(symbols_group)
            
            self.btc_enabled = QCheckBox("BTC/USDT")
            self.btc_enabled.setChecked(True)
            symbols_layout.addWidget(self.btc_enabled)
            
            self.eth_enabled = QCheckBox("ETH/USDT")
            self.eth_enabled.setChecked(True)
            symbols_layout.addWidget(self.eth_enabled)
            
            self.ada_enabled = QCheckBox("ADA/USDT")
            symbols_layout.addWidget(self.ada_enabled)
            
            layout.addWidget(symbols_group)
            
            # Buttons
            button_layout = QHBoxLayout()
            
            self.ok_button = QPushButton("OK")
            self.ok_button.clicked.connect(self.accept)
            button_layout.addWidget(self.ok_button)
            
            self.cancel_button = QPushButton("Cancel")
            self.cancel_button.clicked.connect(self.reject)
            button_layout.addWidget(self.cancel_button)
            
            layout.addLayout(button_layout)
        
        def load_defaults(self):
            """Load default values"""
            pass
        
        def get_settings(self):
            """Get the configured settings"""
            symbols = []
            if self.btc_enabled.isChecked():
                symbols.append("BTC/USDT")
            if self.eth_enabled.isChecked():
                symbols.append("ETH/USDT")
            if self.ada_enabled.isChecked():
                symbols.append("ADA/USDT")
            
            return {
                'initial_balance': self.initial_balance.value(),
                'max_positions': self.max_positions.value(),
                'min_confidence': self.min_confidence.value(),
                'max_portfolio_risk': self.max_portfolio_risk.value(),
                'max_position_size': self.max_position_size.value(),
                'max_leverage': self.max_leverage.value(),
                'use_rl': self.use_rl.isChecked(),
                'autonomous_mode': self.autonomous_mode.isChecked(),
                'paper_trading': self.paper_trading.isChecked(),
                'symbols': symbols
            }

# For backward compatibility
AutoTradeSettingsDialog = AutoTradeSettingsDialog
