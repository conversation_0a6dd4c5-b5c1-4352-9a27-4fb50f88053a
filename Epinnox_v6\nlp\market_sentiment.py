"""
Market Sentiment Aggregator Module
This module combines sentiment from news, social media, and other sources to provide
overall market sentiment analysis for trading decisions.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import asyncio
from collections import defaultdict
import statistics

from .sentiment_analyzer import SentimentAnalyzer, SentimentScore, SentimentLabel
from .news_scraper import NewsScraperManager, NewsArticle
from .social_monitor import SocialMediaMonitor, SocialPost

logger = logging.getLogger(__name__)

class MarketSentimentLevel(Enum):
    """Overall market sentiment levels"""
    EXTREME_FEAR = "extreme_fear"
    FEAR = "fear"
    NEUTRAL = "neutral"
    GREED = "greed"
    EXTREME_GREED = "extreme_greed"

@dataclass
class MarketSentimentSignal:
    """Market sentiment signal for trading"""
    symbol: str
    overall_sentiment: MarketSentimentLevel
    confidence: float
    sentiment_score: float  # -1 to 1
    news_sentiment: float
    social_sentiment: float
    volume_sentiment: float
    technical_sentiment: float
    sources_count: Dict[str, int]
    reasoning: str
    generated_at: datetime
    valid_until: datetime
    
    # Detailed breakdowns
    news_articles: List[NewsArticle] = field(default_factory=list)
    social_posts: List[SocialPost] = field(default_factory=list)
    sentiment_scores: List[SentimentScore] = field(default_factory=list)

class SentimentWeights:
    """Weights for different sentiment sources"""
    
    def __init__(self):
        # Base weights for different data sources
        self.news_weight = 0.35
        self.social_weight = 0.25
        self.volume_weight = 0.20
        self.technical_weight = 0.20
        
        # Time decay factors
        self.time_decay_hours = 24
        self.max_age_hours = 72
        
        # Quality factors
        self.high_importance_multiplier = 1.5
        self.high_influence_multiplier = 1.3
        self.verified_source_multiplier = 1.2

class MarketSentimentAggregator:
    """Aggregates sentiment from multiple sources into trading signals"""
    
    def __init__(self, sentiment_analyzer: SentimentAnalyzer = None,
                 news_scraper: NewsScraperManager = None,
                 social_monitor: SocialMediaMonitor = None):
        """
        Initialize market sentiment aggregator
        
        Args:
            sentiment_analyzer: Sentiment analysis engine
            news_scraper: News scraping manager
            social_monitor: Social media monitor
        """
        self.sentiment_analyzer = sentiment_analyzer or SentimentAnalyzer()
        self.news_scraper = news_scraper
        self.social_monitor = social_monitor
        self.weights = SentimentWeights()
        
        # Cache for recent sentiment calculations
        self.sentiment_cache = {}
        self.cache_duration = timedelta(minutes=15)
        
        # Symbol mappings for different data sources
        self.symbol_mappings = {
            'DOGE': ['doge', 'dogecoin', 'doge/usdt', '$doge'],
            'BTC': ['btc', 'bitcoin', 'btc/usdt', '$btc'],
            'ETH': ['eth', 'ethereum', 'eth/usdt', '$eth'],
            'ADA': ['ada', 'cardano', 'ada/usdt', '$ada'],
            'SOL': ['sol', 'solana', 'sol/usdt', '$sol']
        }
    
    def normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol to standard format"""
        symbol_upper = symbol.upper().replace('/USDT', '').replace('$', '')
        
        # Handle common variations
        if symbol_upper in ['BITCOIN', 'BTC']:
            return 'BTC'
        elif symbol_upper in ['ETHEREUM', 'ETH']:
            return 'ETH'
        elif symbol_upper in ['DOGECOIN', 'DOGE']:
            return 'DOGE'
        elif symbol_upper in ['CARDANO', 'ADA']:
            return 'ADA'
        elif symbol_upper in ['SOLANA', 'SOL']:
            return 'SOL'
        
        return symbol_upper
    
    def get_symbol_variants(self, symbol: str) -> List[str]:
        """Get all variants of a symbol for filtering"""
        normalized = self.normalize_symbol(symbol)
        return self.symbol_mappings.get(normalized, [normalized.lower()])
    
    def calculate_time_decay(self, published_at: datetime) -> float:
        """
        Calculate time decay factor for sentiment data
        
        Args:
            published_at: When the content was published
            
        Returns:
            Decay factor (0.0 to 1.0)
        """
        age_hours = (datetime.now() - published_at).total_seconds() / 3600
        
        if age_hours > self.weights.max_age_hours:
            return 0.0
        
        if age_hours <= 1:  # Very recent
            return 1.0
        elif age_hours <= self.weights.time_decay_hours:
            # Linear decay over decay period
            return max(0.0, 1.0 - (age_hours / self.weights.time_decay_hours))
        else:
            # Exponential decay after decay period
            decay_factor = np.exp(-(age_hours - self.weights.time_decay_hours) / 24)
            return max(0.0, decay_factor)
    
    def analyze_news_sentiment(self, articles: List[NewsArticle], symbol: str) -> Tuple[float, List[SentimentScore]]:
        """
        Analyze sentiment from news articles
        
        Args:
            articles: List of news articles
            symbol: Target symbol
            
        Returns:
            Tuple of (weighted_sentiment_score, sentiment_scores)
        """
        symbol_variants = self.get_symbol_variants(symbol)
        relevant_articles = []
        
        # Filter articles for symbol
        for article in articles:
            if any(variant in article.symbols_mentioned or 
                  any(var in article.title.lower() or var in article.content.lower() 
                      for var in symbol_variants) 
                  for variant in symbol_variants):
                relevant_articles.append(article)
        
        if not relevant_articles:
            return 0.0, []
        
        sentiment_scores = []
        weighted_sum = 0.0
        total_weight = 0.0
        
        for article in relevant_articles:
            # Analyze sentiment
            content = f"{article.title} {article.content}"
            sentiment = self.sentiment_analyzer.analyze_sentiment(content, f"news_{article.source.value}")
            sentiment_scores.append(sentiment)
            
            # Calculate weight
            time_decay = self.calculate_time_decay(article.published_at)
            importance_weight = article.importance_score * self.weights.high_importance_multiplier
            
            weight = time_decay * importance_weight
            
            weighted_sum += sentiment.compound_score * weight
            total_weight += weight
        
        if total_weight == 0:
            return 0.0, sentiment_scores
        
        weighted_sentiment = weighted_sum / total_weight
        
        logger.info(f"News sentiment for {symbol}: {weighted_sentiment:.3f} from {len(relevant_articles)} articles")
        return weighted_sentiment, sentiment_scores
    
    def analyze_social_sentiment(self, posts: List[SocialPost], symbol: str) -> Tuple[float, List[SentimentScore]]:
        """
        Analyze sentiment from social media posts
        
        Args:
            posts: List of social media posts
            symbol: Target symbol
            
        Returns:
            Tuple of (weighted_sentiment_score, sentiment_scores)
        """
        symbol_variants = self.get_symbol_variants(symbol)
        relevant_posts = []
        
        # Filter posts for symbol
        for post in posts:
            if any(variant.upper() in post.symbols_mentioned or 
                  any(var in post.content.lower() for var in symbol_variants) 
                  for variant in symbol_variants):
                relevant_posts.append(post)
        
        if not relevant_posts:
            return 0.0, []
        
        sentiment_scores = []
        weighted_sum = 0.0
        total_weight = 0.0
        
        for post in relevant_posts:
            # Analyze sentiment
            sentiment = self.sentiment_analyzer.analyze_sentiment(post.content, f"social_{post.platform.value}")
            sentiment_scores.append(sentiment)
            
            # Calculate weight
            time_decay = self.calculate_time_decay(post.published_at)
            engagement_weight = post.engagement_score * 2.0  # Boost engagement
            influence_weight = post.influence_score * self.weights.high_influence_multiplier
            
            weight = time_decay * (engagement_weight + influence_weight)
            
            weighted_sum += sentiment.compound_score * weight
            total_weight += weight
        
        if total_weight == 0:
            return 0.0, sentiment_scores
        
        weighted_sentiment = weighted_sum / total_weight
        
        logger.info(f"Social sentiment for {symbol}: {weighted_sentiment:.3f} from {len(relevant_posts)} posts")
        return weighted_sentiment, sentiment_scores
    
    def calculate_volume_sentiment(self, ohlcv_data: pd.DataFrame) -> float:
        """
        Calculate sentiment based on volume patterns
        
        Args:
            ohlcv_data: OHLCV DataFrame
            
        Returns:
            Volume sentiment score (-1 to 1)
        """
        if len(ohlcv_data) < 20:
            return 0.0
        
        try:
            # Calculate volume indicators
            volume = ohlcv_data['volume'].iloc[-20:]  # Last 20 periods
            avg_volume = volume.mean()
            recent_volume = volume.iloc[-5:].mean()  # Last 5 periods
            
            # Volume trend
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0
            
            # Price change with volume
            close_prices = ohlcv_data['close'].iloc[-20:]
            price_change = (close_prices.iloc[-1] - close_prices.iloc[0]) / close_prices.iloc[0]
            
            # Volume sentiment calculation
            if volume_ratio > 1.5:  # High volume
                if price_change > 0:
                    volume_sentiment = min(1.0, volume_ratio * 0.3)  # Bullish with high volume
                else:
                    volume_sentiment = max(-1.0, -volume_ratio * 0.3)  # Bearish with high volume
            elif volume_ratio < 0.7:  # Low volume
                volume_sentiment = price_change * 0.2  # Muted sentiment with low volume
            else:
                volume_sentiment = price_change * 0.4  # Normal volume
            
            return float(np.clip(volume_sentiment, -1.0, 1.0))
            
        except Exception as e:
            logger.error(f"Error calculating volume sentiment: {e}")
            return 0.0
    
    def calculate_technical_sentiment(self, ohlcv_data: pd.DataFrame) -> float:
        """
        Calculate sentiment based on technical indicators
        
        Args:
            ohlcv_data: OHLCV DataFrame
            
        Returns:
            Technical sentiment score (-1 to 1)
        """
        if len(ohlcv_data) < 50:
            return 0.0
        
        try:
            closes = ohlcv_data['close']
            
            # Moving averages
            ma_20 = closes.rolling(20).mean()
            ma_50 = closes.rolling(50).mean()
            
            current_price = closes.iloc[-1]
            ma20_current = ma_20.iloc[-1]
            ma50_current = ma_50.iloc[-1]
            
            # Technical sentiment factors
            sentiment_factors = []
            
            # Price vs Moving Averages
            if current_price > ma20_current:
                sentiment_factors.append(0.3)
            else:
                sentiment_factors.append(-0.3)
            
            if current_price > ma50_current:
                sentiment_factors.append(0.2)
            else:
                sentiment_factors.append(-0.2)
            
            # Moving Average crossover
            if ma20_current > ma50_current:
                sentiment_factors.append(0.2)
            else:
                sentiment_factors.append(-0.2)
            
            # Price momentum (5-day change)
            if len(closes) >= 5:
                momentum = (current_price - closes.iloc[-5]) / closes.iloc[-5]
                sentiment_factors.append(np.clip(momentum * 2, -0.3, 0.3))
            
            technical_sentiment = sum(sentiment_factors)
            return float(np.clip(technical_sentiment, -1.0, 1.0))
            
        except Exception as e:
            logger.error(f"Error calculating technical sentiment: {e}")
            return 0.0
    
    def determine_market_sentiment_level(self, sentiment_score: float, confidence: float) -> MarketSentimentLevel:
        """
        Determine market sentiment level based on score and confidence
        
        Args:
            sentiment_score: Aggregate sentiment score (-1 to 1)
            confidence: Confidence level (0 to 1)
            
        Returns:
            MarketSentimentLevel
        """
        # Adjust thresholds based on confidence
        high_threshold = 0.6 if confidence > 0.8 else 0.7
        medium_threshold = 0.3 if confidence > 0.8 else 0.4
        
        if sentiment_score >= high_threshold:
            return MarketSentimentLevel.EXTREME_GREED
        elif sentiment_score >= medium_threshold:
            return MarketSentimentLevel.GREED
        elif sentiment_score <= -high_threshold:
            return MarketSentimentLevel.EXTREME_FEAR
        elif sentiment_score <= -medium_threshold:
            return MarketSentimentLevel.FEAR
        else:
            return MarketSentimentLevel.NEUTRAL
    
    async def aggregate_market_sentiment(self, symbol: str, 
                                       ohlcv_data: pd.DataFrame = None,
                                       news_articles: List[NewsArticle] = None,
                                       social_posts: List[SocialPost] = None) -> MarketSentimentSignal:
        """
        Aggregate sentiment from all sources for a symbol
        
        Args:
            symbol: Trading symbol
            ohlcv_data: OHLCV price data (optional)
            news_articles: News articles (optional, will fetch if not provided)
            social_posts: Social media posts (optional, will fetch if not provided)
            
        Returns:
            MarketSentimentSignal
        """
        normalized_symbol = self.normalize_symbol(symbol)
        
        # Check cache
        cache_key = f"{normalized_symbol}_{datetime.now().strftime('%Y%m%d_%H%M')}"
        if cache_key in self.sentiment_cache:
            cached_time, cached_signal = self.sentiment_cache[cache_key]
            if datetime.now() - cached_time < self.cache_duration:
                logger.info(f"Using cached sentiment for {normalized_symbol}")
                return cached_signal
        
        logger.info(f"Aggregating market sentiment for {normalized_symbol}")
        
        # Collect sentiment data
        news_sentiment = 0.0
        social_sentiment = 0.0
        volume_sentiment = 0.0
        technical_sentiment = 0.0
        
        all_sentiment_scores = []
        filtered_articles = []
        filtered_posts = []
        sources_count = defaultdict(int)
        
        # Analyze news sentiment
        if news_articles:
            news_sentiment, news_scores = self.analyze_news_sentiment(news_articles, normalized_symbol)
            all_sentiment_scores.extend(news_scores)
            filtered_articles = [a for a in news_articles if any(
                var in a.symbols_mentioned or var in a.title.lower() + a.content.lower()
                for var in self.get_symbol_variants(normalized_symbol)
            )]
            sources_count['news'] = len(filtered_articles)
        
        # Analyze social sentiment
        if social_posts:
            social_sentiment, social_scores = self.analyze_social_sentiment(social_posts, normalized_symbol)
            all_sentiment_scores.extend(social_scores)
            filtered_posts = [p for p in social_posts if any(
                var.upper() in p.symbols_mentioned or var in p.content.lower()
                for var in self.get_symbol_variants(normalized_symbol)
            )]
            sources_count['social'] = len(filtered_posts)
        
        # Calculate volume and technical sentiment
        if ohlcv_data is not None and not ohlcv_data.empty:
            volume_sentiment = self.calculate_volume_sentiment(ohlcv_data)
            technical_sentiment = self.calculate_technical_sentiment(ohlcv_data)
            sources_count['technical'] = 1
            sources_count['volume'] = 1
        
        # Calculate weighted aggregate sentiment
        sentiment_components = [
            (news_sentiment, self.weights.news_weight),
            (social_sentiment, self.weights.social_weight),
            (volume_sentiment, self.weights.volume_weight),
            (technical_sentiment, self.weights.technical_weight)
        ]
        
        # Calculate weighted average
        total_weight = 0.0
        weighted_sum = 0.0
        
        for sentiment, weight in sentiment_components:
            if abs(sentiment) > 0.001:  # Only include non-zero sentiments
                weighted_sum += sentiment * weight
                total_weight += weight
        
        if total_weight > 0:
            aggregate_sentiment = weighted_sum / total_weight
        else:
            aggregate_sentiment = 0.0
        
        # Calculate confidence based on data availability and agreement
        confidence_factors = []
        
        # Data availability factor
        data_availability = (
            (1.0 if abs(news_sentiment) > 0.001 else 0.0) +
            (1.0 if abs(social_sentiment) > 0.001 else 0.0) +
            (1.0 if abs(volume_sentiment) > 0.001 else 0.0) +
            (1.0 if abs(technical_sentiment) > 0.001 else 0.0)
        ) / 4.0
        
        confidence_factors.append(data_availability)
        
        # Agreement factor (how much do sources agree?)
        non_zero_sentiments = [s for s, _ in sentiment_components if abs(s) > 0.001]
        if len(non_zero_sentiments) > 1:
            sentiment_std = np.std(non_zero_sentiments)
            agreement_factor = max(0.0, 1.0 - sentiment_std)
            confidence_factors.append(agreement_factor)
        
        # Sample size factor
        total_samples = sum(sources_count.values())
        sample_factor = min(1.0, total_samples / 50.0)  # Normalize to 50 samples
        confidence_factors.append(sample_factor)
        
        overall_confidence = np.mean(confidence_factors) if confidence_factors else 0.5
        
        # Determine sentiment level
        sentiment_level = self.determine_market_sentiment_level(aggregate_sentiment, overall_confidence)
        
        # Generate reasoning
        reasoning_parts = []
        reasoning_parts.append(f"Aggregate sentiment: {aggregate_sentiment:.3f}")
        
        if abs(news_sentiment) > 0.001:
            reasoning_parts.append(f"News: {news_sentiment:.3f} ({sources_count['news']} articles)")
        
        if abs(social_sentiment) > 0.001:
            reasoning_parts.append(f"Social: {social_sentiment:.3f} ({sources_count['social']} posts)")
        
        if abs(volume_sentiment) > 0.001:
            reasoning_parts.append(f"Volume: {volume_sentiment:.3f}")
        
        if abs(technical_sentiment) > 0.001:
            reasoning_parts.append(f"Technical: {technical_sentiment:.3f}")
        
        reasoning = " | ".join(reasoning_parts)
        
        # Create sentiment signal
        signal = MarketSentimentSignal(
            symbol=normalized_symbol,
            overall_sentiment=sentiment_level,
            confidence=overall_confidence,
            sentiment_score=aggregate_sentiment,
            news_sentiment=news_sentiment,
            social_sentiment=social_sentiment,
            volume_sentiment=volume_sentiment,
            technical_sentiment=technical_sentiment,
            sources_count=dict(sources_count),
            reasoning=reasoning,
            generated_at=datetime.now(),
            valid_until=datetime.now() + timedelta(hours=1),
            news_articles=filtered_articles,
            social_posts=filtered_posts,
            sentiment_scores=all_sentiment_scores
        )
        
        # Cache the result
        self.sentiment_cache[cache_key] = (datetime.now(), signal)
        
        logger.info(f"Market sentiment for {normalized_symbol}: {sentiment_level.value} "
                   f"(score: {aggregate_sentiment:.3f}, confidence: {overall_confidence:.3f})")
        
        return signal
    
    def get_sentiment_trend(self, symbol: str, hours: int = 24) -> Dict[str, Any]:
        """
        Get sentiment trend over time
        
        Args:
            symbol: Trading symbol
            hours: Number of hours to look back
            
        Returns:
            Dictionary with trend analysis
        """
        # This would require storing historical sentiment data
        # For now, return placeholder
        return {
            'symbol': symbol,
            'trend_direction': 'neutral',
            'trend_strength': 0.0,
            'hours_analyzed': hours,
            'data_points': 0
        }
    
    def compare_symbols_sentiment(self, symbols: List[str]) -> Dict[str, Any]:
        """
        Compare sentiment across multiple symbols
        
        Args:
            symbols: List of symbols to compare
            
        Returns:
            Dictionary with comparative analysis
        """
        # This would require running sentiment analysis for multiple symbols
        # For now, return placeholder
        return {
            'symbols': symbols,
            'most_bullish': None,
            'most_bearish': None,
            'sentiment_scores': {},
            'confidence_scores': {}
        }

# Example usage
async def main():
    """Example usage of the market sentiment aggregator"""
    
    # Initialize components
    from .sentiment_analyzer import SentimentAnalyzer
    from .news_scraper import NewsScraperManager, NewsSource
    from .social_monitor import SocialMediaMonitor, SocialPlatform
    
    sentiment_analyzer = SentimentAnalyzer(use_transformer=False)
    
    # Initialize aggregator
    aggregator = MarketSentimentAggregator(sentiment_analyzer=sentiment_analyzer)
    
    # Mock some data for testing
    mock_ohlcv = pd.DataFrame({
        'close': np.random.randn(100).cumsum() + 100,
        'volume': np.random.randint(1000, 10000, 100),
        'high': np.random.randn(100).cumsum() + 102,
        'low': np.random.randn(100).cumsum() + 98,
        'open': np.random.randn(100).cumsum() + 99
    })
    
    # Aggregate sentiment for DOGE
    sentiment_signal = await aggregator.aggregate_market_sentiment(
        symbol='DOGE',
        ohlcv_data=mock_ohlcv
    )
    
    print(f"Market Sentiment Analysis for {sentiment_signal.symbol}:")
    print(f"Overall Sentiment: {sentiment_signal.overall_sentiment.value}")
    print(f"Confidence: {sentiment_signal.confidence:.2%}")
    print(f"Sentiment Score: {sentiment_signal.sentiment_score:.3f}")
    print(f"News Sentiment: {sentiment_signal.news_sentiment:.3f}")
    print(f"Social Sentiment: {sentiment_signal.social_sentiment:.3f}")
    print(f"Volume Sentiment: {sentiment_signal.volume_sentiment:.3f}")
    print(f"Technical Sentiment: {sentiment_signal.technical_sentiment:.3f}")
    print(f"Sources: {sentiment_signal.sources_count}")
    print(f"Reasoning: {sentiment_signal.reasoning}")

if __name__ == "__main__":
    asyncio.run(main())
