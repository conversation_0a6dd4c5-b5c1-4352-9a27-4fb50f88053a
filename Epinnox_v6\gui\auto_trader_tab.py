"""
Auto Trader Tab for Epinnox v6 Trading System
Automated trading controls and strategy management
"""

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
except ImportError:
    print("PyQt5 not installed. Install with: pip install PyQt5")
    raise

from .base_tab import BaseTab
from .matrix_theme import MatrixTheme
import logging

logger = logging.getLogger(__name__)

class AutoTraderTab(BaseTab):
    """Auto trader tab for automated trading"""
    
    # Signals
    auto_trading_started = pyqtSignal(dict)  # config
    auto_trading_stopped = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def setup_ui(self):
        """Setup the Auto Trader tab UI"""
        layout = QVBoxLayout(self)
        
        # Auto trading controls
        controls_group = self.create_trading_controls()
        
        # Strategy queue
        queue_group = self.create_strategy_queue()
        
        # Auto trading log
        log_group = self.create_trading_log()
        
        layout.addWidget(controls_group)
        layout.addWidget(queue_group)
        layout.addWidget(log_group)
    
    def create_trading_controls(self):
        """Create auto trading controls"""
        group = self.create_matrix_group_box("Automated Trading Controls")
        layout = QHBoxLayout(group)
        
        self.auto_trading_enabled = QCheckBox("Enable Auto Trading")
        self.auto_trading_symbols = QLineEdit("DOGE/USDT")
        self.auto_trading_interval = QSpinBox()
        self.auto_trading_interval.setRange(10, 3600)
        self.auto_trading_interval.setValue(30)
        self.auto_trading_interval.setSuffix(" seconds")
        
        self.start_auto_button = self.create_matrix_button("START AUTO TRADING", self.start_auto_trading)
        self.stop_auto_button = self.create_matrix_button("STOP AUTO TRADING", self.stop_auto_trading, False)
        
        layout.addWidget(self.auto_trading_enabled)
        layout.addWidget(QLabel("Symbols:"))
        layout.addWidget(self.auto_trading_symbols)
        layout.addWidget(QLabel("Interval:"))
        layout.addWidget(self.auto_trading_interval)
        layout.addWidget(self.start_auto_button)
        layout.addWidget(self.stop_auto_button)
        
        return group
    
    def create_strategy_queue(self):
        """Create strategy queue table"""
        group = self.create_matrix_group_box("Strategy Queue")
        layout = QVBoxLayout(group)
        
        self.strategy_queue_table = QTableWidget(0, 6)
        self.strategy_queue_table.setHorizontalHeaderLabels([
            "Symbol", "Strategy", "Status", "Next Execution", "Last Result", "Actions"
        ])
        self.apply_matrix_table_styling(self.strategy_queue_table)
        
        layout.addWidget(self.strategy_queue_table)
        return group
    
    def create_trading_log(self):
        """Create auto trading log"""
        group = self.create_matrix_group_box("Auto Trading Log")
        layout = QVBoxLayout(group)
        
        self.auto_trading_log = QTextEdit()
        self.auto_trading_log.setReadOnly(True)
        self.auto_trading_log.setMaximumHeight(150)
        
        layout.addWidget(self.auto_trading_log)
        return group
    
    def start_auto_trading(self):
        """Start automated trading"""
        if not self.auto_trading_enabled.isChecked():
            self.show_error_message("Auto Trading", "Please enable auto trading first")
            return
        
        config = {
            'symbols': self.auto_trading_symbols.text(),
            'interval': self.auto_trading_interval.value(),
            'enabled': True
        }
        
        self.start_auto_button.setEnabled(False)
        self.stop_auto_button.setEnabled(True)
        
        self.auto_trading_started.emit(config)
        self.log_message("Auto trading started")
    
    def stop_auto_trading(self):
        """Stop automated trading"""
        self.start_auto_button.setEnabled(True)
        self.stop_auto_button.setEnabled(False)
        
        self.auto_trading_stopped.emit()
        self.log_message("Auto trading stopped")
