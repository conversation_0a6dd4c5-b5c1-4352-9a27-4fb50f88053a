"""
Trading Environment for Reinforcement Learning
OpenAI Gym compatible environment for training RL trading agents
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

# Try to import gym, fallback to mock if not available
try:
    import gym
    from gym import spaces
    GYM_AVAILABLE = True
except ImportError:
    logger.warning("OpenAI Gym not available. Using mock implementation.")
    GYM_AVAILABLE = False

    # Mock gym classes
    class MockSpace:
        def __init__(self, *args, **kwargs):
            pass

    class spaces:
        Box = MockSpace

    class gym:
        class Env:
            def __init__(self):
                pass

class TradingEnvironment(gym.Env):
    """
    Custom trading environment for RL agent
    """
    
    def __init__(self, data_fetcher, initial_balance=1000.0, max_steps=1000):
        super(TradingEnvironment, self).__init__()
        
        self.data_fetcher = data_fetcher
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.max_steps = max_steps
        
        # Action space: [direction, position_size, leverage]
        # direction: 0=WAIT, 1=LONG, 2=SHORT
        # position_size: 0.0-1.0 (percentage of balance)
        # leverage: 1.0-10.0
        self.action_space = spaces.Box(
            low=np.array([0, 0.0, 1.0]),
            high=np.array([2, 1.0, 10.0]),
            dtype=np.float32
        )
        
        # Observation space: market features
        # [price, volume, volatility, rsi, macd, bb_upper, bb_lower, sentiment, ...]
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf, shape=(50,), dtype=np.float32
        )
        
        self.reset()
    
    def reset(self) -> np.ndarray:
        """Reset environment to initial state"""
        self.current_balance = self.initial_balance
        self.position = 0.0  # Current position size
        self.position_entry_price = 0.0
        self.trade_history = []
        self.step_count = 0
        self.current_price = 0.1  # Default price
        
        # Get initial market state
        return self._get_observation()
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """Execute one step in the environment"""
        direction = int(np.clip(action[0], 0, 2))
        position_size = float(np.clip(action[1], 0.0, 1.0))
        leverage = float(np.clip(action[2], 1.0, 10.0))
        
        # Get current market data
        obs = self._get_observation()
        current_price = self._get_current_price()
        
        # Calculate reward based on action
        reward = self._calculate_reward(direction, position_size, leverage, current_price)
        
        # Update portfolio
        self._update_portfolio(direction, position_size, leverage, current_price)
        
        # Check if episode is done
        done = self._is_done()
        
        # Additional info
        info = {
            'balance': self.current_balance,
            'position': self.position,
            'trade_count': len(self.trade_history),
            'step': self.step_count,
            'price': current_price
        }
        
        self.step_count += 1
        return obs, reward, done, info
    
    def _calculate_reward(self, direction: int, position_size: float, leverage: float, price: float) -> float:
        """Calculate reward for the action taken"""
        base_reward = 0.0
        
        # Profit/Loss from current position
        if self.position != 0 and self.position_entry_price > 0:
            price_change = (price - self.position_entry_price) / self.position_entry_price
            position_pnl = self.position * price_change * leverage
            base_reward += position_pnl * 100  # Scale reward
        
        # Penalty for excessive leverage
        if leverage > 5.0:
            base_reward -= (leverage - 5.0) * 0.1
        
        # Penalty for large position sizes in uncertain conditions
        if position_size > 0.5:  # More than 50% of balance
            base_reward -= (position_size - 0.5) * 0.2
        
        # Reward for maintaining balance
        balance_ratio = self.current_balance / self.initial_balance
        if balance_ratio > 1.0:
            base_reward += (balance_ratio - 1.0) * 10
        else:
            base_reward -= (1.0 - balance_ratio) * 20  # Higher penalty for losses
        
        return base_reward
    
    def _get_observation(self) -> np.ndarray:
        """Get current market observation"""
        # This should return the same features used in your current system
        # For now, return dummy data - implement with real market features
        obs = np.random.random(50).astype(np.float32)
        
        # Add some basic portfolio state
        obs[0] = self.current_balance / self.initial_balance  # Balance ratio
        obs[1] = self.position  # Current position
        obs[2] = self.current_price  # Current price
        obs[3] = len(self.trade_history) / 100.0  # Trade count normalized
        
        return obs
    
    def _get_current_price(self) -> float:
        """Get current market price"""
        # Simulate price movement
        if hasattr(self, 'current_price'):
            # Add some random walk
            change = np.random.normal(0, 0.001)
            self.current_price *= (1 + change)
            self.current_price = max(0.001, self.current_price)  # Prevent negative prices
        else:
            self.current_price = 0.1
        
        return self.current_price
    
    def _update_portfolio(self, direction: int, position_size: float, leverage: float, price: float):
        """Update portfolio based on action"""
        if direction == 0:  # WAIT
            return
        
        # Close existing position if switching directions
        if (direction == 1 and self.position < 0) or (direction == 2 and self.position > 0):
            self._close_position(price)
        
        # Open new position
        if direction == 1:  # LONG
            self.position = position_size * leverage
            self.position_entry_price = price
        elif direction == 2:  # SHORT
            self.position = -position_size * leverage
            self.position_entry_price = price
    
    def _close_position(self, price: float):
        """Close current position"""
        if self.position != 0:
            if self.position > 0:  # Long position
                pnl = self.position * (price - self.position_entry_price) / self.position_entry_price
            else:  # Short position
                pnl = abs(self.position) * (self.position_entry_price - price) / self.position_entry_price
            
            self.current_balance += pnl
            self.trade_history.append({
                'entry_price': self.position_entry_price,
                'exit_price': price,
                'position_size': self.position,
                'pnl': pnl
            })
            self.position = 0.0
            self.position_entry_price = 0.0
    
    def _is_done(self) -> bool:
        """Check if episode should end"""
        # End if balance drops too low or too many steps
        return (self.current_balance < self.initial_balance * 0.5 or 
                self.step_count >= self.max_steps)
    
    def render(self, mode='human'):
        """Render the environment"""
        if mode == 'human':
            print(f"Step: {self.step_count}, Balance: ${self.current_balance:.2f}, "
                  f"Position: {self.position:.2f}, Price: ${self.current_price:.4f}")
    
    def get_portfolio_summary(self) -> Dict:
        """Get current portfolio summary"""
        return {
            'balance': self.current_balance,
            'position': self.position,
            'entry_price': self.position_entry_price,
            'current_price': self.current_price,
            'trade_count': len(self.trade_history),
            'step_count': self.step_count,
            'total_return': (self.current_balance - self.initial_balance) / self.initial_balance
        }
